package api

import (
	"asdsec.com/asec/platform/app/console/app/module_switch/dto"
	"asdsec.com/asec/platform/app/console/app/module_switch/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

var featureSwitchService = service.NewFeatureSwitchService()

func GetFeatureSwitches(c *gin.Context) {
	moduleKey := c.Param("module_key")
	flagStr := c.Query("flag")
	flag, err := strconv.Atoi(flagStr)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//entityId := c.Param("entity_id")
	data, err := featureSwitchService.GetFeatureSwitches(c, moduleKey, flag)
	if err != nil {
		global.SysLog.Error("GetFeatureSwitches err", zap.Error(err))
		common.Fail(c, common.UpsetAgentModuleSwitchErr)
		return
	}
	common.OkWithData(c, data)
}

func UpdateFeatureSwitches(c *gin.Context) {
	var req dto.UpdateFeatureSwitchReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := featureSwitchService.UpdateFeatureSwitches(c, req)
	if err != nil {
		global.SysLog.Error("UpdateFeatureSwitches err", zap.Error(err))
		common.Fail(c, common.UpsetAgentModuleSwitchErr)
		return
	}
	common.Ok(c)
}
