package oprlog

import (
	oprlogapi "asdsec.com/asec/platform/app/console/app/oprlog/api"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	validator "github.com/go-playground/validator/v10"
)

/*
OprlogApi
操作日志域路由
*/
func OprlogApi(r *gin.RouterGroup) {
	// 注册针对操作日志ip查询的自定义验证规则
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		v.RegisterValidation("ipip", oprlogapi.IpAndipSegfunc)
	}
	// 路由组
	v := r.Group("/v1/operate_log")
	{
		v.POST("/list", oprlogapi.ReadOprLogList)
		v.GET("/types", oprlogapi.OprResourceTypes)
		v.POST("/export", oprlogapi.Export)
		v.POST("/resource_type", oprlogapi.CreateResourceType)
		//v.POST("/write", oprlogapi.WriteOprlog) //内部其他程序使用
	}
}
