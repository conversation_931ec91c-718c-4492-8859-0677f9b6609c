package model

type UpdateRiskLevelReq struct {
	Id            int    `gorm:"column:id" json:"id"`
	MinScore      int    `gorm:"column:min_score" json:"min_score"`
	MaxScore      int    `gorm:"column:max_score" json:"max_score"`
	RiskLevelName string `gorm:"risk_level_name" json:"risk_level_name"`
	RiskLevel     int    `gorm:"risk_level" json:"risk_level"`
}

type UpdateRiskScoreSettingReq struct {
	Id    int `json:"id" binding:"required"`
	Score int `json:"score"`
}

type CascadeCreatRiskScoreSettingReq struct {
	IndicatorType      int    `gorm:"column:indicator_type" json:"indicator_type"`
	IndicatorSubType   int    `gorm:"column:indicator_sub_type" json:"indicator_sub_type"`
	Indicator          string `gorm:"column:indicator" json:"indicator"`
	IndicatorGroupName string `gorm:"column:indicator_group_name" json:"indicator_group_name"`
	IndicatorLevel     string `gorm:"column:indicator_level" json:"indicator_level"`
	Score              int    `gorm:"column:score" json:"score"`
	TypeName           string `gorm:"column:type_name" json:"type_name"`
	CorpId             string `gorm:"column:corp_id" db:"corp_id" json:"corp_id"`
}
