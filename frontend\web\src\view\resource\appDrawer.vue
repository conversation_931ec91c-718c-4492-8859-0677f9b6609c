<template>
  <div class="appDraw">

    <t-form ref="appForm" :data="formData" label-align="top" :rules="rules" :label-width="60">
      <t-form-item v-show="addAppType !=='portal'" label="应用类型" name="app_type">
        <div style="display:flex;gap:16px;min-width:max-content;">
          <!-- 隧道应用 -->
          <div class="app-type-card" :class="{ tunCardNo: formData.app_type === 'web' }" data-value="tun"
               @click="selectAppType('tun')"
               style="position:relative;flex:1;padding:12px;border:2px solid #2563eb;border-radius:8px;cursor:pointer;transition:all 0.2s ease;background:#f8fafc;">
            <input type="radio" name="app-type" value="tun" checked
                   style="position:absolute;opacity:0;pointer-events:none;">
            <div style="display:flex;align-items:center;gap:12px;">
              <div
                  style="width:36px;height:36px;background:linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);border-radius:8px;display:flex;align-items:center;justify-content:center;flex-shrink:0;">
                <svg viewBox="0 0 24 24" fill="white">
                  <use xlink:href="#icon-suidaoyingyong"/>
                </svg>

              </div>
              <div style="flex:1;">
                <div style="font-weight:600;color:#1f2937;font-size:12px;margin-bottom:2px;">隧道应用</div>
                <div style="color:#6b7280;font-size:10px;line-height:1.4;">
                  通过客户端访问
                </div>
              </div>
            </div>
            <div class="check-indicator"
                 style="position:absolute;top:8px;right:8px;width:16px;height:16px;border:2px solid #2563eb;border-radius:50%;background:#2563eb;display:flex;align-items:center;justify-content:center;">
              <div style="width:6px;height:6px;background:white;border-radius:50%;"></div>
            </div>
          </div>

          <!-- WEB应用 -->
          <div class="app-type-card" data-value="web" :class="{ webCard: formData.app_type === 'web' }"
               @click="selectAppType('web')"
               style="position:relative;flex:1;padding:12px;border:2px solid #e5e7eb;border-radius:8px;cursor:pointer;transition:all 0.2s ease;background:#fff;">
            <input type="radio" name="app-type" value="web" style="position:absolute;opacity:0;pointer-events:none;">
            <div style="display:flex;align-items:center;gap:12px;">
              <div
                  style="width:36px;height:36px;background:linear-gradient(135deg, #10b981 0%, #059669 100%);border-radius:8px;display:flex;align-items:center;justify-content:center;flex-shrink:0;">
                <svg viewBox="0 0 24 24" fill="white">
                  <use xlink:href="#icon-Webyingyong"/>
                </svg>
              </div>
              <div style="flex:1;">
                <div style="font-weight:600;color:#1f2937;font-size:12px;margin-bottom:2px;">WEB应用</div>
                <div style="color:#6b7280;font-size:10px;line-height:1.4;">
                  浏览器直接访问
                </div>
              </div>
            </div>
            <div class="check-indicator"
                 style="position:absolute;top:8px;right:8px;width:16px;height:16px;border:2px solid #d1d5db;border-radius:50%;background:transparent;display:none;align-items:center;justify-content:center;">
              <div style="width:6px;height:6px;background:#10b981;border-radius:50%;"></div>
            </div>
          </div>
        </div>
      </t-form-item>

      <t-form-item label="应用名称" name="app_name">
        <t-input v-model="formData.app_name" style="width: 528px" placeholder="请输入应用名称"></t-input>
      </t-form-item>

      <t-form-item label="应用状态" name="app_status">
        <t-radio-group v-model="formData.app_status">
          <t-radio :value="1">启用</t-radio>
          <t-radio :value="3">禁用</t-radio>
          <t-radio :value="2">维护</t-radio>
        </t-radio-group>
      </t-form-item>

      <t-form-item v-show="addAppType !=='portal'" label="发布网关" name="sdp">
        <t-select
            style="width: 528px"
            clearable
            v-model="formData.sdp"
            :min-collapsed-num="4"
        >
          <t-option v-for="item in sdpOptions" :key="item.appliance_id" :value="item.appliance_id" :label="item.name"/>
        </t-select>
      </t-form-item>

      <t-form-item label="应用标签" name="group_ids">
        <t-select
            style="width: 528px"
            multiple
            clearable
            v-model="formData.group_ids"
            :min-collapsed-num="4"
        >
          <t-option v-for="item in props.groupOptions.child_group" :key="item.id" :value="item.id" :label="item.name"/>
          <template #panelBottomContent>
            <div class="select-panel-footer" style="color: #94949A">
              如需创建新的应用标签，请
              <t-link theme="primary" style="font-size: 12px" @click="tagVisible = true">
                &nbsp;点击添加
              </t-link>
            </div>
          </template>
        </t-select>
        <t-dialog
            class="asd-dialog"
            :visible="tagVisible"
            attach="body"
            draggable
        >
          <template #header>添加标签</template>
          <template #body>
            <t-input v-model="tagValue" placeholder="输入标签名称"/>
          </template>
          <template #footer>
            <div>
              <t-button variant="outline" class="asdc-default-but" style="float: left"
                        @click="tagValue = ''; tagVisible = false">
                取消
              </t-button>
              <t-button theme="primary" class="asdc-primary-but" style="float: right" @click="addGroup">
                确定
              </t-button>
            </div>
          </template>
        </t-dialog>
      </t-form-item>


      <!-- 高级配置标签页 -->
      <div class="advanced-tabs-container" style="margin-top:20px;width: 528px">
        <div class="advanced-tabs"
             style="display:flex;border-bottom:2px solid #e5e7eb;margin-bottom:20px;background:#fafbfc;border-radius:8px 8px 0 0;padding:4px;">
          <div class="tab-item" v-show="addAppType !=='portal'" :class="{ active: configTab === 'basic' }"
               data-tab="basic" @click="switchAdvancedTab('basic')"
               :style="{...getTabItemCommonStyle(), ...getTabItemActiveStyle(configTab === 'basic')}"
          >
            基础配置
          </div>
          <div class="tab-item" :class="{ active: configTab === 'portal' }" data-tab="portal"
               @click="switchAdvancedTab('portal')"
               :style="{...getTabItemCommonStyle(), ...getTabItemActiveStyle(configTab === 'portal')}"
          >
            {{ addAppType ==='portal' ? '应用展示' : '门户配置' }}
          </div>

          <div class="tab-item" v-show="addAppType ==='portal'" :class="{ active: configTab === 'opentype' }"
               data-tab="portal" @click="switchAdvancedTab('opentype')"
               :style="{...getTabItemCommonStyle(), ...getTabItemActiveStyle(configTab === 'opentype' )}"
          >
            应用打开方式
          </div>

          <div class="tab-item" v-show="addAppType !=='portal'" :class="{ active: configTab === 'sso' }" data-tab="sso"
               @click="switchAdvancedTab('sso')"
               :style="{...getTabItemCommonStyle(), ...getTabItemActiveStyle(configTab === 'sso')}"
          >
            单点登录
          </div>
          <div v-if="formData.app_type === 'web'" v-show="addAppType !=='portal'" class="tab-item webvpn-only"
               :class="{ active: configTab === 'compatibility' }" data-tab="compatibility"
               @click="switchAdvancedTab('compatibility')"
               :style="{...getTabItemCommonStyle(), ...getTabItemActiveStyle(configTab === 'compatibility')}"
          >
            应用兼容性
          </div>
          <div class="tab-item" data-tab="advanced" v-show="addAppType !=='portal'"
               :class="{ active: configTab === 'advanced' }" @click="switchAdvancedTab('advanced')"
               :style="{...getTabItemCommonStyle(), ...getTabItemActiveStyle(configTab === 'advanced')}"
          >
            高级设置
          </div>
        </div>
      </div>
      <div v-show="configTab === 'basic' && addAppType !=='portal'">
        <div v-if="formData.app_type ==='web'"> <!--web应用基础配置-->
          <t-form-item name="server_address">
            <template #label>
              服务器地址
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  目的应用地址，支持填写 IP 或域名
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                  <use xlink:href="#icon-shuoming"/>
                </svg>
              </el-tooltip>
            </template>
            <t-select
                v-model="formData.server_schema"
                placeholder="请选择"
                style="width: 116px;float: left"
            >
              <t-option key="http" value="http" label="HTTP"/>
              <t-option key="https" value="https" label="HTTPS"/>
            </t-select>
            <t-input
                v-model="formData.server_address"
                placeholder="www.domain.com:443或*************"
                :maxlength="128"
                style="width: calc(100% - 125px);float: right"
            >
            </t-input>
          </t-form-item>
          <t-form-item name="publish_address">
            <template #label>
              发布地址
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  发布地址示例：www.test.com:7443<br/>
                  说明：<br/>
                  1.若使用 http 协议，对应的代理端口为 80<br/>
                  2.若使用 https 协议，对应的代理端口为 7443<br/>
                  3.填写时，请使用代理端口映射后的实际访问端口
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                  <use xlink:href="#icon-shuoming"/>
                </svg>
              </el-tooltip>
            </template>
            <t-select
                v-model="formData.publish_schema"
                placeholder="请选择"
                style="width: 116px;float: left"
            >
              <t-option key="http" value="http" label="HTTP"/>
              <t-option key="https" value="https" label="HTTPS"/>
            </t-select>
            <t-input
                v-model="formData.publish_address"
                placeholder="请输入发布地址"
                :maxlength="128"
                style="width: calc(100% - 125px);float: right"
            >
            </t-input>
          </t-form-item>
          
          <!-- SSL证书选择字段 -->
          <t-form-item name="certificate_id" v-if="formData.publish_schema === 'https'">
            <template #label>
              SSL证书
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  使用https协议时，请务必选择与应用域名相匹配的证书，否则可能会导致业务访问受到影响。
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                  <use xlink:href="#icon-shuoming" />
                </svg>
              </el-tooltip>
            </template>
            <div style="width: 100%;">
              <div style="display: flex; gap: 12px; align-items: center;">
                <t-select
                    v-model="formData.certificate_id"
                    placeholder="请选择SSL证书, 如不选择则自动匹配域名证书, 匹配不上则无法访问"
                    style="flex: 1"
                    clearable
                    :loading="certificateLoading"
                    @visible-change="handleCertificateSelectOpen"
                    @clear="handleCertificateClear"
                    @change="handleCertificateChange"
                >
                  <t-option 
                    v-for="cert in certificateOptions" 
                    :key="cert.id" 
                    :value="cert.id.toString()" 
                    :label="cert.name"
                    :title="'域名: ' + (cert.domain && cert.domain.length > 0 ? cert.domain.join(', ') : '无')"
                  >
                    {{ cert.name }}
                  </t-option>
                </t-select>
                <t-button variant="outline" @click="handleUploadCertificate">
                  上传证书
                </t-button>
              </div>
            </div>
          </t-form-item>
          
          <t-form-item label="访问配置">
            <t-space>
              <t-checkbox v-model="formData.isPath">应用路径
                <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    <span style="font-weight: bold">应用路径</span><br/>
                    绝对匹配： 完整匹配指定的 URI（如 /foo/bar）。<br/>
                    前缀匹配： 匹配以指定路径开头的 URI（如 /foo* 匹配 /foo/、/foo/a、/foo/b 等）。<br/>
                    匹配优先级： 优先进行绝对匹配；若未命中，则尝试前缀匹配。
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming"/>
                  </svg>
                </el-tooltip>
              </t-checkbox>
              <t-checkbox v-model="formData.default_rule">URL智能重写
                <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    <span style="font-weight: bold">智能改写</span><br/>
                    适用于透明代理不能正常兼容的复杂站点或老旧站点（比如说站点中有大量URL绝对路径）。<br/>
                    （注：智能改写模式兼容性取决于网站规范化程度。如无特别必要，请发布为隧道资源（4层代理) ，以避免兼容性适配过程）
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming"/>
                  </svg>
                </el-tooltip>
              </t-checkbox>
              <t-checkbox v-model="formData.manual_rule">URL手动重写
                <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    <span style="font-weight: bold">URL手动重写</span><br/>
                    当资源URL替换错误时，支持手动修改替换规则。
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming"/>
                  </svg>
                </el-tooltip>
              </t-checkbox>
              <t-checkbox v-model="formData.depend_site">依赖站点
                <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    <span style="font-weight: bold">依赖站点</span><br/>
                    把依赖的原地址替换为统一后缀的地址，权限校验统一为该资源的权限。
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming"/>
                  </svg>
                </el-tooltip>
              </t-checkbox>
            </t-space>
          </t-form-item>
          <div class="dynamic-block" v-if="formData.isPath">
            <div class="dynamic-block-header">
              <span class="dynamic-block-title">应用路径</span>
              <button type='button' class='dynamic-block-delete' @click="formData.isPath = false">删除</button>
            </div>
            <input type='text' v-model="formData.uri" class='dynamic-block-input' placeholder='请输入应用路径'>

          </div>
          <div class="dynamic-block" v-if="formData.manual_rule">
            <div class="dynamic-block-header">
              <span class="dynamic-block-title">手动URL重写</span>
              <button type='button' class='dynamic-block-delete' @click='formData.manual_rule = false'>删除</button>
            </div>

            <div id='url-rewrite-list' v-for="item in formData.web_compatible_config.url_manual_rewrite"
                 :key="item.id">
              <div class="url-rewrite-row">
                <input type='text' placeholder='重写前地址' v-model="item.before" style="flex:1;">
                <span class="arrow">→</span>
                <input type='text' placeholder='重写后地址' v-model="item.after" style="flex:1;">
                <button type='button' class='row-delete-btn' @click='removeUrlRewriteRow(item)' title="删除此规则">×
                </button>
              </div>
            </div>
            <button type='button' class='add-url-rewrite-btn' @click='addUrlRewriteRow'>+ 更多地址</button>
          </div>
          <div class="dynamic-block" v-if="formData.depend_site">
            <div class="dynamic-block-header">
              <span class="dynamic-block-title">依赖站点
               <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    http(s)://www.***.com
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming"/>
                  </svg>
                </el-tooltip>
              </span>
              <button type='button' class='dynamic-block-delete' @click='formData.depend_site = false'>删除</button>
            </div>
            <textarea v-model="formData.web_compatible_config.depend_site.text" class='dynamic-block-input'
                      style='height:60px;'
                      placeholder='请输入依赖站点域名'></textarea>
          </div>
        </div>
        <div v-show="formData.app_type ==='tun'"> <!--隧道应用 基础配置-->
          <AddressTable ref="addressTable" :api-data="formData"></AddressTable>
        </div>
      </div>

      <div v-show="configTab === 'portal'">
        <t-form-item name="show_status">
          <t-checkbox :checked="formData.show_status === 1" @change="handleShowStatusChange">允许应用在门户可见
          </t-checkbox>
        </t-form-item>

        <AppIconUpload @syncSrc="syncSrc" :icon="formData.icon_url"/>
        <t-form-item :name=" formData.app_type ==='web' ? 'web_url_web' : 'web_url'">
          <template #label>
            <div style="display:inline-flex">
              应用中心首页地址
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  用于门户中心点击应用图标时的跳转地址，相对于应用地址的路径
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                  <use xlink:href="#icon-shuoming"/>
                </svg>
              </el-tooltip>
            </div>
          </template>
          <t-input v-if="formData.app_type ==='tun' || addAppType === 'portal'" v-model="formData.web_url"
                   placeholder="如：https://www.baicu.com/home.html"></t-input>
          <t-input-adornment v-else
                             :prepend="formData.publish_schema + '://' + (formData.publish_address ? formData.publish_address : '127.0.0.1' )">
            <t-input v-model="formData.web_url_web" placeholder="如：/home.html"/>
          </t-input-adornment>

        </t-form-item>

        <t-form-item name="portal_show_name">
          <template #label>
            <div style="display:inline-flex">
              门户显示名称
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  在用户门户中显示的应用名称，可与应用名称不同
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                  <use xlink:href="#icon-shuoming"/>
                </svg>
              </el-tooltip>
            </div>
          </template>
          <t-input v-model="formData.portal_show_name" placeholder="如不填写，将使用应用名称"></t-input>
        </t-form-item>

        <t-form-item name="portal_desc" style="margin-bottom: 16px">
          <template #label>
            <div style="display:inline-flex">
              应用描述
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  将在门户中显示，帮助用户了解应用功能
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                  <use xlink:href="#icon-shuoming"/>
                </svg>
              </el-tooltip>
            </div>
          </template>
          <t-textarea v-model="formData.portal_desc" placeholder="简要描述应用的功能和用途"/>
        </t-form-item>
      </div>


      <div v-show="configTab === 'opentype' || (addAppType !== 'portal' && configTab === 'portal')">
        <t-form-item name="open_config">
          <t-checkbox
              v-model="formData.open_config.enabled"
              @change="handleOpenConfigEnabledChange"
          >
            启用应用打开方式
          </t-checkbox>
        </t-form-item>

        <t-form-item name="open_config_detail" v-show="formData.open_config.enabled">
          <div class="open-config-layout"  ref="openConfigRef">
            <!-- 打开方式选择 -->
            <div class="open-type-selection">
              <div class="button-group"
                   style="display:flex;background:#f1f5f9;border-radius:8px;padding:4px;gap:2px;width:100%;">
                <button
                    type="button"
                    class="button-group-item"
                    :class="{ active: formData.open_config.open_type === 'browser' }"
                    @click="handleOpenTypeChange('browser')"
                    style="padding:10px 14px;border:none;border-radius:6px;background:transparent;color:#6b7280;font-size:14px;font-weight:500;cursor:pointer;transition:all 0.2s ease;white-space:nowrap;display:flex;flex-direction:row;align-items:center;gap:8px;flex:1;justify-content:center;"
                >
                  <span style="font-size:16px;">🌐</span>
                  <span>浏览器</span>
                </button>
                <button
                    type="button"
                    class="button-group-item"
                    :class="{ active: formData.open_config.open_type === 'client' }"
                    @click="handleOpenTypeChange('client')"
                    style="padding:10px 14px;border:none;border-radius:6px;background:transparent;color:#6b7280;font-size:14px;font-weight:500;cursor:pointer;transition:all 0.2s ease;white-space:nowrap;display:flex;flex-direction:row;align-items:center;gap:8px;flex:1;justify-content:center;"
                >
                  <span style="font-size:16px;">💻</span>
                  <span>指定程序</span>
                </button>
                <button
                    type="button"
                    class="button-group-item"
                    :class="{ active: formData.open_config.open_type === 'system' }"
                    @click="handleOpenTypeChange('system')"
                    style="padding:10px 14px;border:none;border-radius:6px;background:transparent;color:#6b7280;font-size:14px;font-weight:500;cursor:pointer;transition:all 0.2s ease;white-space:nowrap;display:flex;flex-direction:row;align-items:center;gap:8px;flex:1;justify-content:center;"
                >
                  <span style="font-size:16px;">⚙️</span>
                  <span>系统应用</span>
                </button>
              </div>


            </div>

            <!-- 浏览器配置 -->
            <div v-if="formData.open_config.open_type === 'browser'" class="config-section">
              <!-- 浏览器配置列表 -->
              <div v-for="(browser, index) in formData.open_config.browser_configs" :key="index" class="config-card">
                <div class="card-header">
                  <div class="card-title">
                    <span>浏览器 {{ index + 1 }}</span>
                  </div>
                  <t-button
                      v-if="formData.open_config.browser_configs.length > 1"
                      size="small"
                      variant="text"
                      @click="removeBrowserConfig(index)"
                      class="remove-button"
                  >
                    删除
                  </t-button>
                </div>

                <div class="card-content">
                  <!-- 浏览器类型选择 -->
                  <div class="field-group">
                    <div class="field-label">浏览器类型</div>
                    <t-select
                        v-model="browser.type"
                        placeholder="选择浏览器"
                        style="width: 100%;"
                    >
                      <t-option value="Default" label="系统默认浏览器"/>
                      <t-option value="Google Chrome" label="Google Chrome"/>
                      <t-option value="Microsoft Edge" label="Microsoft Edge"/>
                      <t-option value="Firefox" label="Firefox"/>
                      <t-option value="Safari" label="Safari"/>
                      <t-option value="Internet Explorer" label="Internet Explorer"/>
                      <t-option value="360安全浏览器" label="360安全浏览器"/>
                      <t-option value="360极速浏览器" label="360极速浏览器"/>
                      <t-option value="QQ浏览器" label="QQ浏览器"/>
                      <t-option value="搜狗浏览器" label="搜狗浏览器"/>
                    </t-select>
                  </div>

                  <!-- 启动参数 -->
                  <div class="field-group">
                    <div class="field-label">启动参数</div>
                    <t-input
                        v-model="browser.params"
                        placeholder="可选，如：--disable-web-security"
                    />
                  </div>

                  <!-- IE警告 -->
                  <div v-if="browser.type === 'ie'" class="warning-box">
                    <div class="warning-content">
                      <span class="warning-text">IE浏览器已停止支持，建议使用现代浏览器</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 添加按钮 -->
              <div class="add-button-container">
                <t-button size="small" variant="outline" @click="addBrowserConfig" class="add-browser-button">
                  + 添加
                </t-button>
              </div>
            </div>

            <!-- 指定程序配置 -->
            <div v-if="formData.open_config.open_type === 'client'" class="config-section">
              <!-- 程序配置列表 -->
              <div v-for="(program, index) in formData.open_config.program_configs" :key="index" class="config-card">
                <div class="card-header">
                  <div class="card-title">
                    <span>程序 {{ index + 1 }}</span>
                  </div>
                  <t-button
                      v-if="formData.open_config.program_configs.length > 1"
                      size="small"
                      variant="text"
                      @click="removeProgramConfig(index)"
                      class="remove-button"
                  >
                    删除
                  </t-button>
                </div>

                <div class="card-content">
                  <!-- 操作系统选择 -->
                  <div class="field-group">
                    <div class="field-label">操作系统</div>
                    <t-select
                        v-model="program.os"
                        placeholder="选择操作系统"
                        style="width: 100%;"
                        @change="(newVal) => handleOSChange(index, newVal)"
                    >
                      <t-option
                          v-for="osOption in getAvailableOSOptions(index)"
                          :key="osOption.value"
                          :value="osOption.value"
                          :label="osOption.label"
                          :disabled="osOption.disabled"
                      />
                    </t-select>
                  </div>

                  <!-- Windows/Linux 配置 -->
                  <div v-if="program.os === 'windows' || program.os === 'linux'">
                    <div class="field-group">
                      <div class="field-label">程序名称</div>
                      <t-input
                          v-model="program.name"
                          placeholder="如：notepad.exe、firefox"
                      />
                    </div>
                    <div class="field-group">
                      <div class="field-label">程序路径</div>
                      <t-input
                          v-model="program.path"
                          :placeholder="program.os === 'windows' ? '可选，如：C:\\Windows\\System32\\notepad.exe' : '可选，如：/usr/bin/firefox'"
                      />
                    </div>
                  </div>

                  <!-- macOS 配置 -->
                  <div v-if="program.os === 'macos'">
                    <div class="field-group">
                      <div class="field-label">程序Bundle ID</div>
                      <t-input
                          v-model="program.bundleId"
                          placeholder="如：com.apple.TextEdit"
                      />
                    </div>
                  </div>

                  <!-- 更多选项展开/折叠 -->
                  <div class="field-group more-options-toggle">
                    <div
                        class="more-options-btn"
                        @click="program.showMoreConfig = !program.showMoreConfig"
                    >
                      <span class="toggle-icon">{{ program.showMoreConfig ? '▼' : '▶' }}</span>
                      <span class="toggle-text">更多选项</span>
                    </div>
                  </div>

                  <!-- 高级配置选项 - 直接插入字段列表 -->
                  <template v-if="program.showMoreConfig">
                    <!-- 启动参数（所有系统通用） -->
                    <div class="field-group">
                      <div class="field-label">启动参数</div>
                      <t-input
                          v-model="program.params"
                          placeholder="可选"
                      />
                    </div>

                    <!-- 未找到程序提示语 -->
                    <div class="field-group">
                      <div class="field-label">未找到程序提示语</div>
                      <t-input
                          v-model="program.notFoundMessage"
                          placeholder="可选，如：此应用需要安装XXX程序才能正常使用"
                      />
                    </div>
                  </template>
                </div>
              </div>


              <!-- 添加按钮 -->
              <div class="add-button-container" v-if="formData.open_config.program_configs.length < 3">
                <t-button size="small" variant="outline" @click="addProgramConfig" class="add-program-button">
                  + 添加
                </t-button>
              </div>
            </div>

            <!-- 系统应用配置 -->
            <div v-if="formData.open_config.open_type === 'system'" class="config-section">
              <!-- 系统应用配置列表 -->
              <div v-for="(app, index) in formData.open_config.system_app_configs" :key="index" class="config-card">
                <div class="card-header">
                  <div class="card-title">
                    <span>系统应用 {{ index + 1 }}</span>
                  </div>
                  <t-button
                      v-if="formData.open_config.system_app_configs.length > 1"
                      size="small"
                      variant="text"
                      @click="removeSystemAppConfig(index)"
                      class="remove-button"
                  >
                    删除
                  </t-button>
                </div>

                <div class="card-content">
                  <!-- 操作系统选择 -->
                  <div class="field-group">
                    <div class="field-label">操作系统</div>
                    <t-select
                        v-model="app.os"
                        placeholder="选择操作系统"
                        style="width: 100%;"
                        @change="(newVal) => handleSystemAppOSChange(index, newVal)"
                    >
                      <t-option
                          v-for="osOption in getSystemAppAvailableOSOptions(index)"
                          :key="osOption.value"
                          :value="osOption.value"
                          :label="osOption.label"
                          :disabled="osOption.disabled"
                      />
                    </t-select>
                  </div>

                  <!-- 系统应用选择 -->
                  <div class="field-group">
                    <div class="field-label">系统应用</div>
                    <t-select
                        v-model="app.type"
                        placeholder="选择系统应用"
                        style="width: 100%;"
                    >
                      <!-- Windows 系统应用 -->
                      <template v-if="app.os === 'windows'">
                        <t-option value="remote_desktop" label="远程桌面连接 (mstsc)"/>
                        <t-option value="file_explorer" label="文件资源管理器 (explorer)"/>
                        <t-option value="notepad" label="记事本 (notepad)"/>
                        <t-option value="calculator" label="计算器 (calc)"/>
                        <t-option value="paint" label="画图 (mspaint)"/>
                        <t-option value="cmd" label="命令提示符 (cmd)"/>
                        <t-option value="powershell" label="PowerShell"/>
                        <t-option value="control_panel" label="控制面板 (control)"/>
                        <t-option value="task_manager" label="任务管理器 (taskmgr)"/>
                      </template>

                      <!-- macOS 系统应用 -->
                      <template v-if="app.os === 'macos'">
                        <t-option value="finder" label="访达 (Finder)"/>
                        <t-option value="terminal" label="终端 (Terminal)"/>
                        <t-option value="textedit" label="文本编辑 (TextEdit)"/>
                        <t-option value="calculator" label="计算器 (Calculator)"/>
                        <t-option value="activity_monitor" label="活动监视器 (Activity Monitor)"/>
                        <t-option value="system_preferences" label="系统偏好设置 (System Preferences)"/>
                        <t-option value="console" label="控制台 (Console)"/>
                        <t-option value="disk_utility" label="磁盘工具 (Disk Utility)"/>
                      </template>

                      <!-- Linux 系统应用 -->
                      <template v-if="app.os === 'linux'">
                        <t-option value="file_manager" label="文件管理器 (nautilus/dolphin)"/>
                        <t-option value="terminal" label="终端 (gnome-terminal/konsole)"/>
                        <t-option value="text_editor" label="文本编辑器 (gedit/kate)"/>
                        <t-option value="calculator" label="计算器 (gnome-calculator)"/>
                        <t-option value="system_monitor" label="系统监视器 (gnome-system-monitor)"/>
                        <t-option value="settings" label="系统设置 (gnome-control-center)"/>
                      </template>
                    </t-select>
                  </div>
                </div>
              </div>

              <!-- 添加按钮 -->
              <div class="add-button-container" v-if="formData.open_config.system_app_configs.length < 3">
                <t-button size="small" variant="outline" @click="addSystemAppConfig" class="add-system-button">
                  + 添加
                </t-button>
              </div>
            </div>
          </div>
        </t-form-item>
      </div>

      <div v-show="configTab === 'sso' && addAppType !=='portal'">
        <SingleSignOn :key="componentKey" :app-type="formData.app_type" :api-data="formData" ref="sso"></SingleSignOn>
      </div>

      <div v-show="configTab === 'compatibility' && addAppType !=='portal'">
        <AppCompatible ref="appCompatible" :api-data="formData"></AppCompatible>
      </div>


      <div v-show="configTab === 'advanced' && addAppType !=='portal'">
        <AdvancedConf ref="advancedConf" :app-type="formData.app_type" :api-data="formData"></AdvancedConf>
      </div>


    </t-form>

    <!-- 证书上传抽屉 -->
    <t-drawer 
      v-model:visible="certificateDrawerVisible" 
      size="600px"
      :destroy-on-close="true"
      @close="resetCertificateForm"
      placement="right"
      :z-index="2000"
      attach="body"
      :show-overlay="true"
      :close-on-overlay-click="true"
    >
      <template #header>
        <div style="font-size: 16px; font-weight: 600;">上传SSL证书</div>
      </template>
      
      <div style="padding: 20px;">
        <CertificateUpload 
          ref="certificateUploadRef"
          :modelValue="certificateFormData"
          @update:modelValue="handleCertificateDataUpdate"
        />
      </div>
      
      <template #footer>
        <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 20px; border-top: 1px solid #e5e6eb;">
          <t-button variant="outline" @click="certificateDrawerVisible = false">取消</t-button>
          <t-button theme="primary" @click="submitCertificate" :loading="certificateSubmitting">确定</t-button>
        </div>
      </template>
    </t-drawer>

  </div>
</template>

<script>
export default {
  name: 'AppDrawer'
}
</script>
<script setup>
import {reactive, ref, watch, onMounted, nextTick} from 'vue'
import {ElMessage, ElTooltip} from "element-plus";
import {createGroup} from '@/api/resource'
import {getCertificateList, addDomainSSL} from '@/api/certificate'

import AppIconUpload from "../../components/app/appIconUpload.vue";
import AddressTable from "../../components/app/addressTable.vue";
import SingleSignOn from "../../components/app/singleSignOn.vue";
import AppCompatible from "../../components/app/appCompatible.vue";
import AdvancedConf from "../../components/app/advancedConf.vue";
import CertificateUpload from '@/components/certificate/CertificateUpload.vue';
import {MessagePlugin} from "tdesign-vue-next";

const props = defineProps({
  sdpOptions: { //网关列表
    type: Array,
    required: true,
    default: []
  },
  addAppType: {
    type: String,
    required: true,
    default: ''
  },
  groupOptions: { //标签列表
    type: Object,
    required: true,
    default: {
      id: 0,
      child_group: []
    }
  },
  apiData: {
    type: Object,
    required: false,
    default: {}
  }
});
const addressTable = ref()
const sso = ref()
const appCompatible = ref()
const advancedConf = ref()
const openConfigRef = ref() // 应用打开方式配置区域的引用

// 计算标签页通用样式
const getTabItemCommonStyle = () => {
  return {
    padding: '10px 18px',
    cursor: 'pointer',
    borderBottom: '2px solid transparent',
    color: '#6b7280',
    fontWeight: '500',
    background: 'transparent',
    borderRadius: '6px',
    marginRight: '2px',
    fontSize: '12px',
    transition: 'all 0.3s ease',
    position: 'relative'
  };
};

// 计算标签页激活样式
const getTabItemActiveStyle = (isActive) => {
  if (isActive) {
    return {
      borderBottomColor: '#2563eb',
      color: '#2563eb',
      fontWeight: '600',
      background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
      boxShadow: '0 2px 4px rgba(37,99,235,0.1)',
      transform: 'translateY(-2px)'
    };
  }
  return {};
};

// 证书相关的响应式数据
const certificateOptions = ref([])
const certificateLoading = ref(false)

// 证书上传抽屉相关
const certificateDrawerVisible = ref(false)
const certificateSubmitting = ref(false)
const certificateUploadRef = ref()
const certificateFormData = reactive({
  name: '',
  certificate: '',
  private_key: ''
})

const handleShowStatusChange = (isChecked) => {
  formData.show_status = isChecked ? 1 : 0;
}

// 修改操作系统选择时的处理函数
const handleOSChange = (index, newOS) => {
  // 检查是否已有相同操作系统的配置（除了当前正在修改的）
  const existingOS = formData.open_config.program_configs
      .filter((_, i) => i !== index)
      .map(config => config.os);

  if (existingOS.includes(newOS)) {
    ElMessage.warning('该操作系统已存在配置，请选择其他操作系统！');
    // 恢复为原来的值
    return;
  }
  // 更新操作系统
  formData.open_config.program_configs[index].os = newOS;
  // 根据操作系统重置相关字段
  const program = formData.open_config.program_configs[index];
  program.name = '';
  program.path = '';
  program.bundleId = '';
}

// 获取可用的操作系统选项
const getAvailableOSOptions = (currentIndex) => {
  const allOS = [
    { value: 'windows', label: '🪟 Windows' },
    { value: 'macos', label: '🍎 macOS' },
    { value: 'linux', label: '🐧 Linux' }
  ];

  // 获取已选择的操作系统（排除当前正在编辑的项）
  const selectedOS = formData.open_config.program_configs
      .filter((_, index) => index !== currentIndex)
      .map(config => config.os);

  // 标记已被选择的操作系统为禁用
  return allOS.map(os => ({
    ...os,
    disabled: selectedOS.includes(os.value)
  }));
}
// 处理应用打开方式启用状态变化
const handleOpenConfigEnabledChange = (isChecked) => {
  formData.open_config.enabled = isChecked;
  // 如果禁用了，可以选择是否清空配置
  if (!isChecked) {
    // 保留配置，只是不启用
    console.log('应用打开方式已禁用，但保留配置');
  } else {
    console.log('应用打开方式已启用');
    // 启用时自动滚动到配置区域
    nextTick(() => {
      if (openConfigRef.value) {
        openConfigRef.value.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    });
  }
}

// 处理系统应用操作系统变更
const handleSystemAppOSChange = (index, newOS) => {
  // 检查是否已有相同操作系统的配置（除了当前正在修改的）
  const existingOS = formData.open_config.system_app_configs
      .filter((_, i) => i !== index)
      .map(config => config.os);

  if (existingOS.includes(newOS)) {
    ElMessage.warning('该操作系统已存在配置，请选择其他操作系统！');
    return;
  }
  // 更新操作系统
  formData.open_config.system_app_configs[index].os = newOS;

  // 重置系统应用类型，确保与操作系统匹配
  formData.open_config.system_app_configs[index].type =
      newOS === 'windows' ? 'remote_desktop' :
          newOS === 'macos' ? 'finder' :
              'file_manager'; // linux默认
}

// 获取系统应用可用的操作系统选项
const getSystemAppAvailableOSOptions = (currentIndex) => {
  const allOS = [
    { value: 'windows', label: '🪟 Windows' },
    { value: 'macos', label: '🍎 macOS' },
    { value: 'linux', label: '🐧 Linux' }
  ];

  // 获取已选择的操作系统（排除当前正在编辑的项）
  const selectedOS = formData.open_config.system_app_configs
      .filter((_, index) => index !== currentIndex)
      .map(config => config.os);

  // 标记已被选择的操作系统为禁用
  return allOS.map(os => ({
    ...os,
    disabled: selectedOS.includes(os.value)
  }));
}

// 处理打开方式变化
const handleOpenTypeChange = (value) => {
  // 设置新的打开方式
  formData.open_config.open_type = value;


  // 根据应用类型设置默认配置
  if (value === 'browser') {
    // 浏览器模式，确保browser_configs存在
    if (!formData.open_config.browser_configs || formData.open_config.browser_configs.length === 0) {
      formData.open_config.browser_configs = [
        {type: 'Default', params: ''}
      ];
    }
  } else if (value === 'client') {
    // 指定程序模式，确保program_configs存在
    if (!formData.open_config.program_configs || formData.open_config.program_configs.length === 0) {
      formData.open_config.program_configs = [createDefaultProgramConfig()];
    }
  } else if (value === 'system') {
    // 系统应用模式，确保system_app_configs存在
    if (!formData.open_config.system_app_configs || formData.open_config.system_app_configs.length === 0) {
      formData.open_config.system_app_configs = [
        {os: 'windows', type: 'remote_desktop'}
      ];
    }
  }

  // 自动滚动到配置区域
  setTimeout(() => {
    scrollToConfigSection();
  }, 100);
}

// 添加浏览器配置
const addBrowserConfig = async () => {
  formData.open_config.browser_configs.push({
    type: 'Default',
    params: ''
  });

  // 等待DOM更新后滚动到新添加的配置项
  await nextTick();
  scrollToNewConfig('browser', formData.open_config.browser_configs.length - 1);
}

// 移除浏览器配置
const removeBrowserConfig = (index) => {
  if (formData.open_config.browser_configs.length > 1) {
    formData.open_config.browser_configs.splice(index, 1);
  }
}

// 创建默认程序配置对象的辅助函数
const createDefaultProgramConfig = (os = 'windows') => ({
  os,
  name: '',
  path: '',
  bundleId: '',
  params: '',
  notFoundMessage: '',
  showMoreConfig: false
});

// 添加程序配置
const addProgramConfig = async () => {
  if (formData.open_config.program_configs.length >= 3){
    ElMessage.warning('最多添加3个程序配置，每种操作系统只能添加一个！');
    return;
  }
  // 获取已存在的操作系统列表
  const existingOS = formData.open_config.program_configs.map(config => config.os);

  // 确定下一个要添加的操作系统（选择尚未添加的操作系统）
  let nextOS = 'windows'; // 默认从 Windows 开始
  const allOS = ['windows', 'macos', 'linux'];

  // 查找尚未添加的操作系统
  for (const os of allOS) {
    if (!existingOS.includes(os)) {
      nextOS = os;
      break;
    }
  }

  // 如果所有操作系统都已添加（理论上不应该发生，但我们还是要检查）
  if (existingOS.includes(nextOS)) {
    ElMessage.warning('每种操作系统只能添加一个配置！');
    return;
  }

  formData.open_config.program_configs.push(createDefaultProgramConfig(nextOS));

  // 等待DOM更新后滚动到新添加的配置项
  await nextTick();
  scrollToNewConfig();
}

// 移除程序配置
const removeProgramConfig = (index) => {
  if (formData.open_config.program_configs.length > 1) {
    formData.open_config.program_configs.splice(index, 1);
  }
}

// 添加系统应用配置
const addSystemAppConfig = async () => {

  if (formData.open_config.system_app_configs.length >= 3){
    ElMessage.warning('最多添加3个系统应用配置，每种操作系统只能添加一个！');
    return;
  }

  // 获取已存在的操作系统列表
  const existingOS = formData.open_config.system_app_configs.map(config => config.os);

  // 确定下一个要添加的操作系统（选择尚未添加的操作系统）
  let nextOS = 'windows'; // 默认从 Windows 开始
  const allOS = ['windows', 'macos', 'linux'];

  // 查找尚未添加的操作系统
  for (const os of allOS) {
    if (!existingOS.includes(os)) {
      nextOS = os;
      break;
    }
  }

  // 如果所有操作系统都已添加
  if (existingOS.includes(nextOS)) {
    ElMessage.warning('每种操作系统只能添加一个配置！');
    return;
  }

  // 添加新配置
  const defaultType =
      nextOS === 'windows' ? 'remote_desktop' :
          nextOS === 'macos' ? 'finder' :
              'file_manager'; // linux默认

  formData.open_config.system_app_configs.push({
    os: nextOS,
    type: defaultType
  });

  // 等待DOM更新后滚动到新添加的配置项
  await nextTick();
  scrollToNewConfig('system', formData.open_config.system_app_configs.length - 1);
}

// 滚动到新添加的配置项
const scrollToNewConfig = () => {
  try {
    // 查找当前激活的配置区域
    const activeConfigSection = document.querySelector('.config-section');
    if (!activeConfigSection) return;

    // 查找所有配置卡片
    const configCards = activeConfigSection.querySelectorAll('.config-card');

    // 获取最后一个配置卡片（新添加的）
    const newConfigElement = configCards[configCards.length - 1];

    if (newConfigElement) {
      // 平滑滚动到新配置项
      newConfigElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });
    }
  } catch (error) {
    console.warn('滚动到新配置项失败:', error);
  }
}

// 滚动到标签页内容区域
const scrollToTabContent = (tabName) => {
  try {
    // 根据标签页名称查找对应的内容区域
    let targetElement = null;

    if (tabName === 'portal') {
      // 门户配置：查找应用打开方式区域
      targetElement = document.querySelector('.config-section') ||
          document.querySelector('[data-tab="portal"]') ||
          document.querySelector('div[v-show="configTab === \'portal\'"]');
    } else {
      // 其他标签页：查找对应的内容区域
      targetElement = document.querySelector(`[data-tab="${tabName}"]`) ||
          document.querySelector(`div[v-show="configTab === '${tabName}'"]`);
    }

    if (targetElement) {
      // 平滑滚动到目标区域
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  } catch (error) {
    console.warn('滚动到标签页内容失败:', error);
  }
}

// 滚动到配置区域
const scrollToConfigSection = () => {
  try {
    // 查找当前激活的配置区域
    const activeConfigSection = document.querySelector('.config-section');

    if (activeConfigSection) {
      // 平滑滚动到配置区域
      activeConfigSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    } else {
      // 如果没有找到配置区域，滚动到应用打开方式按钮组
      const buttonGroup = document.querySelector('.button-group');
      if (buttonGroup) {
        buttonGroup.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    }
  } catch (error) {
    console.warn('滚动到配置区域失败:', error);
  }
}

// 移除系统应用配置
const removeSystemAppConfig = (index) => {
  if (formData.open_config.system_app_configs.length > 1) {
    formData.open_config.system_app_configs.splice(index, 1);
  }
}

let formData = reactive({
  app_type: 'tun', //应用类型
  app_name: '', //应用名称
  app_status: 1, //应用状态
  sdp: '', //发布网关
  sdp_list: [],
  group_ids: [], //应用标签
  app_sites: [], //应用地址  只有tun应用才需要填写
  //下面的基础配置 只有web应用需要填写
  server_address: '', //服务器地址
  server_schema: 'https', //服务器协议
  publish_address: '', //应用发布地址
  publish_schema: 'https', //应用发布协议
  certificate_id: '', //SSL证书ID
  depend_site: false,  //是否开启依赖站点
  isPath: true, //应用路径开关
  uri: "/*", //应用路径
  manual_rule: false, //手动重写开关
  default_rule: false, //智能重写开关
  form_fill_enabled: 0, //表单代填启用标志

  // 下面的基础配置 只有web应用需要填写 end
  //门户配置
  icon_url: '', //应用图标
  show_status: 1,//门户可见
  web_url: '', //应用中心首页地址
  web_url_web: "", //web应用的首页地址
  portal_show_name: '',//门户显示名称
  portal_desc: '',//应用描述
  open_config: { //应用打开方式配置
    enabled: false, //是否启用应用打开方式
    open_type: 'browser', //打开方式：browser(浏览器)、client(指定程序)、system(系统应用)
    // 浏览器配置
    browser_configs: [
      {type: 'Default', params: ''}
    ],
    // 指定程序配置
    program_configs: [createDefaultProgramConfig()],
    system_app_configs:[],
    supported_os: ['windows', 'macos', 'linux'], //支持的操作系统
    per_os_config: false, //是否按操作系统分别配置
    // 未找到程序提示
    show_not_found_tip: false, //是否显示未找到程序提示配置
    not_found_message: '' //未找到程序时的提示语
  },
  health_config: {
    enable: "0",
    config: {
      protocol: 'https',//协议
      timeout: 3, //响应超时时间
      path: '/index.html', //探测路径
      interval_time: 15, //检测频率
      health_intervals: 5, //健康间隔
      health_code: [200], //相应状态码
      success_num: 3, //异常次数
      un_health_intervals: 5,
      fail_nums: 3
    }
  },
  web_compatible_config: {
    default_rule: [],
    url_control: {
      type: 'free_auth',
      text: ''
    },
    header_config: [],
    depend_site: {
      type: 'assign',
      text: ''
    },
    hosts: '',
    source_ip_get: {
      trust_ips: "***********,10.0.0.0/8",
      source: "",
      recursive: "1"
    },
    source_ip_insert: {
      header: "X-Forwarded-For",
      direction: "left",
      separator: ",",
      position: 1
    },
    url_manual_rewrite: [
      {id: Symbol(), before: '', after: ''}
    ],//手动改写规则
    single_sign_on: {
      type: '',
      config: {}
    }, //单点登录配置
  },
})

// 证书相关方法
// 获取证书列表
const fetchCertificateList = async () => {
  try {
    certificateLoading.value = true
    const response = await getCertificateList({ limit: 100, offset: 0 })
    if (response.status === 200 && response.data.code === 0) {
      certificateOptions.value = response.data.data.data || []
    } else {
      ElMessage.error('获取证书列表失败')
    }
  } catch (error) {
    console.error('获取证书列表失败:', error)
    ElMessage.error('获取证书列表失败')
  } finally {
    certificateLoading.value = false
  }
}

// 处理证书下拉框打开事件
const handleCertificateSelectOpen = (visible) => {
  if (visible && certificateOptions.value.length === 0) {
    fetchCertificateList()
  }
}

// 处理证书清空事件
const handleCertificateClear = () => {
  formData.certificate_id = ''
}

// 处理证书选择变化事件
const handleCertificateChange = (value) => {
  formData.certificate_id = value || ''
}

// 处理上传证书按钮点击
const handleUploadCertificate = () => {
  certificateDrawerVisible.value = true
}

// 重置证书表单
const resetCertificateForm = () => {
  certificateFormData.name = ''
  certificateFormData.certificate = ''
  certificateFormData.private_key = ''
}

// 处理证书数据更新
const handleCertificateDataUpdate = (newData) => {
  Object.assign(certificateFormData, newData)
}

// 提交证书
const submitCertificate = async () => {
  try {
    const valid = await certificateUploadRef.value.validate()
    if (!valid) return

    certificateSubmitting.value = true
    const response = await addDomainSSL(certificateFormData)
    
    if (response.status === 200 && response.data.code === 0) {
      MessagePlugin.success('证书上传成功')
      certificateDrawerVisible.value = false
      resetCertificateForm()
      // 重新获取证书列表
      await fetchCertificateList()
      // 自动选择新上传的证书
      if (response.data.data && response.data.data.id) {
        formData.certificate_id = response.data.data.id.toString()
      }
    } else {
      MessagePlugin.error(response.data.message || '证书上传失败')
    }
  } catch (error) {
    console.error('证书上传失败:', error)
    MessagePlugin.error('证书上传失败')
  } finally {
    certificateSubmitting.value = false
  }
}

watch(
    () => props.apiData,
    (newVal) => {
      if (newVal.app_type === "portal") {
        formData.app_type = newVal.app_type
      }
      if (newVal && newVal.id !== undefined && newVal.id !== null && newVal.id !== '') {
        formData.id = newVal.id
        const formDataKeys = Object.keys(formData)
        // 遍历 formData 的 keys
        formDataKeys.forEach(key => {
          // 如果 newVal 中也存在相同的 key，则进行赋值
          if (key in newVal) {
            formData[key] = newVal[key];
          }
        })

        if (formData.app_type !== 'portal') {
          formData.sdp = newVal.sdp_list[0]
          if (newVal.app_type === 'tun') {
            formData.app_sites = newVal.app_addresses
          }
        }


        // 确保open_config有默认值
        if (!formData.open_config || typeof formData.open_config !== 'object') {
          formData.open_config = {
            open_type: 'browser',
            // 浏览器配置（多个浏览器，每个有自己的参数）
            browser_configs: [
              {type: 'Default', params: ''}
            ],
            // 指定程序配置（多个程序，按操作系统区分）
            program_configs: [createDefaultProgramConfig()],
            // 未找到程序提示
            show_not_found_tip: false,
            not_found_message: '',
            // 系统应用配置（按操作系统区分）
            system_app_configs: [
              {os: 'windows', type: 'remote_desktop'}
            ]
          };
        } else {
          // 确保新增字段有默认值（用于兼容旧数据）
          // 浏览器配置兼容性处理
          if (!formData.open_config.browser_configs) {
            // 如果是旧的browser_type格式，转换为新格式
            const oldBrowserType = formData.open_config.browser_type || 'Default';
            const oldBrowserParams = formData.open_config.browser_params || '';
            formData.open_config.browser_configs = [
              {type: oldBrowserType, params: oldBrowserParams}
            ];
          }

          // 指定程序配置兼容性处理
          if (!formData.open_config.program_configs) {
            // 如果是旧的单程序格式，转换为新格式
            const oldProgramName = formData.open_config.program_name || '';
            const oldProgramPath = formData.open_config.program_path || '';
            const oldLaunchParams = formData.open_config.launch_params || '';
            formData.open_config.program_configs = [
              {
                os: 'windows',
                name: oldProgramName,
                path: oldProgramPath,
                bundleId: '',
                params: oldLaunchParams,
                notFoundMessage: '',
                showMoreConfig: false
              }
            ];
          }

          // 确保每个程序配置都有 notFoundMessage 和 showMoreConfig 字段
          if (formData.open_config.program_configs) {
            formData.open_config.program_configs.forEach(program => {
              if (program.notFoundMessage === undefined) {
                program.notFoundMessage = '';
              }
              if (program.showMoreConfig === undefined) {
                program.showMoreConfig = false;
              }
            });
          }

          if (formData.open_config.show_not_found_tip === undefined) {
            formData.open_config.show_not_found_tip = false;
          }
          if (!formData.open_config.not_found_message) {
            formData.open_config.not_found_message = '';
          }
          // 系统应用配置兼容性
          if (!formData.open_config.system_app_configs) {
            // 如果是旧的system_app_type格式，转换为新格式
            const oldSystemAppType = formData.open_config.system_app_type || 'remote_desktop';
            formData.open_config.system_app_configs = [
              {os: 'windows', type: oldSystemAppType}
            ];
          }

          if (formData.open_config.open_type === ''){
            formData.open_config.open_type = 'browser'
          }
        }
        
        // 如果是Web应用且使用https协议，自动加载证书列表
        if (newVal.app_type === 'web' && newVal.publish_schema === 'https') {
          // 加载证书列表（用于编辑时显示选项）
          if (certificateOptions.value.length === 0) {
            fetchCertificateList()
          }
        }
      } else {
        // 新增应用的情况：如果是Web应用且默认为https协议，也要加载证书列表
        if (formData.app_type === 'web' && formData.publish_schema === 'https') {
          if (certificateOptions.value.length === 0) {
            fetchCertificateList()
          }
        }
      }
    },
    {immediate: true}
);

// 监听发布协议变化，当切换到https时自动加载证书列表
watch(
    () => formData.publish_schema,
    (newSchema, oldSchema) => {
      // 只有当从非https切换到https时才加载（避免重复加载）
      if (newSchema === 'https' && oldSchema !== 'https' && formData.app_type === 'web') {
        if (certificateOptions.value.length === 0) {
          fetchCertificateList()
        }
      }
    }
);

// 监听应用类型变化，当切换到Web应用且使用https时自动加载证书列表
watch(
    () => formData.app_type,
    (newType, oldType) => {
      // 只有当从非web切换到web时才加载（避免重复加载）
      if (newType === 'web' && oldType !== 'web' && formData.publish_schema === 'https') {
        if (certificateOptions.value.length === 0) {
          fetchCertificateList()
        }
      }
    }
);


//获取抽屉数据
const getDrawerData = () => {
  let ssoConf = sso.value.ssoConf
  let appCompatibleData
  if (formData.app_type !== 'web') {
    formData.app_sites = addressTable.value.returnAppSites()
  } else {
    formData.app_sites = []
    formData.web_url = formData.publish_schema + "://" + formData.publish_address + formData.web_url_web
  }

  if (formData.app_type === 'web') {
    appCompatibleData = appCompatible.value.appCompatible

    if (appCompatibleData.CrossDomain) { //允许跨域
      formData.web_compatible_config.default_rule.push("cross_domain")//添加
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "cross_domain")
    }
    if (appCompatibleData.smartRedirect) { //智能跳转
      formData.web_compatible_config.default_rule.push("http2https")//添加
    }else{
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "http2https")
    }
    if (appCompatibleData.requestResponseRewrite) { //允许请求响应改写
      formData.web_compatible_config.default_rule.push("header_config")//添加
      formData.web_compatible_config.header_config = appCompatibleData.header.filter(obj => obj.key && (obj.value || obj.operation === 'remove'))
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "header_config")
      formData.web_compatible_config.header_config = []
    }

    if (formData.isPath) {
      formData.web_compatible_config.default_rule.push("uri")
    } else {
      formData.uri = "/*"
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "uri")
    }

    if (appCompatibleData.customHost) { //允许自定义host
      formData.web_compatible_config.default_rule.push("hosts")//添加
      formData.web_compatible_config.hosts = appCompatibleData.customHostValue
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "hosts")
      formData.web_compatible_config.hosts = ''
    }

    //source_ip_get
    if (appCompatibleData.ipExtract) {//IP提取
      formData.web_compatible_config.default_rule.push("source_ip_get")//添加
      formData.web_compatible_config.source_ip_get.source = appCompatibleData.ipExtractValue
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "source_ip_get")
      formData.web_compatible_config.source_ip_get.source = ''
    }

    //source_ip_insert
    if (appCompatibleData.ipInsert) {//源IP插入
      formData.web_compatible_config.default_rule.push("source_ip_insert")//添加
      formData.web_compatible_config.source_ip_insert = {
        header: appCompatibleData.ipInsertConfig.header,
        direction: appCompatibleData.ipInsertConfig.direction,
        separator: appCompatibleData.ipInsertConfig.separator,
        position: appCompatibleData.ipInsertConfig.position
      }
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "source_ip_insert")
      formData.web_compatible_config.source_ip_insert = null
    }

    //error_response
    if (appCompatibleData.async) { //异步请求响应处理
      formData.web_compatible_config.default_rule.push("error_response")//添加
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "error_response")
    }
    //
    if (appCompatibleData.routing) { //前端路由增强控制
      formData.web_compatible_config.default_rule.push("front_url_control")//添加
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "front_url_control")
    }
  }

  let advancedConfData = advancedConf.value.conf

  if (formData.app_type !== 'portal') {


    if (formData.default_rule) //开启了智能改写
    {
      formData.web_compatible_config.default_rule.push("url_smart_rewrite")//添加
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "url_smart_rewrite")
    }

    if (formData.manual_rule) { //开启了手动改写
      formData.web_compatible_config.default_rule.push("url_manual_rewrite")//添加
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "url_manual_rewrite")
    }

    if (formData.depend_site) { //开启了依赖站点
      formData.web_compatible_config.default_rule.push("depend_site")//添加
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "depend_site")
    }

    if (ssoConf.enableSso) { //开启了单点登录
      formData.web_compatible_config.default_rule.push("single_sign_on")//添加
      formData.web_compatible_config.single_sign_on.type = ssoConf.oauthType
      if (ssoConf.oauthType === "micro_application") { //微应用、办公应用
        formData.web_compatible_config.single_sign_on.config = {
          idp_id: '',
          idp_type: ssoConf.officeApp
        }
      } else if (ssoConf.oauthType === "oauth2") {
        formData.web_compatible_config.single_sign_on.config = {
          app_id: ssoConf.appid,
          app_secret: ssoConf.appSecret,
          callback: ssoConf.callback
        }
      } else if (ssoConf.oauthType === "fill_forms") { //表单代填
        // 创建配置副本，避免修改原始响应式数据
        const fillFormsConfig = { ...ssoConf }
        
        //删除不需要的字段
        delete fillFormsConfig.appid
        delete fillFormsConfig.appSecret
        delete fillFormsConfig.callback
        delete fillFormsConfig.officeApp
        delete fillFormsConfig.enableSso
        delete fillFormsConfig.oauthType
        
        // 转换布尔值为字符串，确保后端能正确解析
        if (typeof fillFormsConfig.auto_login === 'boolean') {
          fillFormsConfig.auto_login = fillFormsConfig.auto_login ? '1' : '0'
        }
        if (typeof fillFormsConfig.auto_login_ped === 'number') {
          fillFormsConfig.auto_login_ped = fillFormsConfig.auto_login_ped.toString()
        }
        
        formData.web_compatible_config.single_sign_on.config = fillFormsConfig
        // 设置表单代填启用标志
        formData.form_fill_enabled = 1
      }
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "single_sign_on")
      // 未启用单点登录时，确保表单代填也未启用
      formData.form_fill_enabled = 0
    }

    formData.health_config.enable = advancedConfData.healthCheck ? "1" : "0"
    if (advancedConfData.healthCheck) { //健康检查
      formData.health_config.config = advancedConfData.healthCheckConfig
    }

    // 应用bypass处理
    if (advancedConfData.bypassSwitch) {
      formData.web_compatible_config.default_rule.push("bypass")
    } else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "bypass")
    }

    // URL路径白名单处理
    if (advancedConfData.whitelistSwitch) {
      formData.web_compatible_config.default_rule.push("url_control")
      formData.web_compatible_config.url_control.type = "free_auth"
      formData.web_compatible_config.url_control.text = advancedConfData.urlWhiteList
    }
    // URL路径黑名单处理
    else if (advancedConfData.blacklistSwitch) {
      formData.web_compatible_config.default_rule.push("url_control")
      formData.web_compatible_config.url_control.type = "forbid_access"
      formData.web_compatible_config.url_control.text = advancedConfData.urlBlackList
    }
    // 都未启用时清除url_control相关配置
    else {
      formData.web_compatible_config.default_rule = formData.web_compatible_config.default_rule.filter(item => item !== "url_control")
      formData.web_compatible_config.url_control.type = ""
      formData.web_compatible_config.url_control.text = ""
    }

  }


  // 当切换到非浏览器模式时，清空浏览器相关配置
  if (formData.open_config.open_type !== 'browser') {
    formData.open_config.browser_configs = [
      {type: 'Default', params: ''}
    ];
  }

  // 当切换到非指定程序模式时，清空指定程序相关配置
  if (formData.open_config.open_type !== 'client') {
    formData.open_config.program_configs = [createDefaultProgramConfig()];
    formData.open_config.show_not_found_tip = false;
    formData.open_config.not_found_message = '';
  }

  // 当切换到非系统应用模式时，清空系统应用相关配置
  if (formData.open_config.open_type !== 'system') {
    formData.open_config.system_app_configs = [
      {os: 'windows', type: 'remote_desktop'}
    ];
  }



  formData.web_compatible_config.default_rule = [...new Set(formData.web_compatible_config.default_rule)]
  
  // 确保证书ID字段能正确处理空值（用于取消证书绑定）
  if (formData.app_type === 'web' && formData.publish_schema === 'https') {
    // 如果是空字符串或者为空，明确设置为null或空字符串
    // 确保后端能接收到取消证书绑定的信号
    if (!formData.certificate_id || formData.certificate_id === '') {
      formData.certificate_id = ''  // 明确设置为空字符串
    }
  }
  
  // 创建返回数据对象，确保所有字段都被明确包含
  const returnData = { ...formData }
  
  // 对于Web应用，明确包含证书相关字段，即使是空值
  if (returnData.app_type === 'web') {
    // 确保证书ID字段在返回数据中明确存在
    returnData.certificate_id = formData.certificate_id || ''
  }
  
  return returnData
}

const rules = reactive({
  app_name: [{required: true, message: '请输入应用名称', type: 'error', trigger: 'blur'}],
  web_url: [
    {
      url: {
        protocols: ['http', 'https'],
        require_protocol: true,
      },
      message: '请输入正确的地址链接',
    },
  ],
  app_status: [{required: true, message: '请输选择应用状态', type: 'error', trigger: 'change'}],
  sdp: [{required: true, message: '请选择网关', type: 'error', trigger: 'change'}],
})


const appForm = ref()


const validate = async () => {
  let validateR = true
  const formInstance = appForm.value
  if (!formInstance) return false
  if (formData.manual_rule) { //开启了手动改写
    for (const item of formData.web_compatible_config.url_manual_rewrite) {
      if (!item.after || !item.before) {
        await MessagePlugin.error("手动重写配置填写不完整");
        return
      }
    }
  }
  if (formData.depend_site) { //开启了依赖站点
    if (formData.web_compatible_config.depend_site.text === '') {
      ElMessage.error('依赖站点不能为空')
      return false
    }
    //写一个正则 判断依赖站点必须要是一个网址
    const reg = /^(https?|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/;
    if (!reg.test(formData.web_compatible_config.depend_site.text)) {
      ElMessage.error('依赖站点必须是合法的地址')
      return false
    }

  }
  if (formData.app_type === 'web') {
    let appCompatibleData
    appCompatibleData = appCompatible.value.appCompatible

    if (appCompatibleData.customHost) { //允许自定义host
      const regex = /^(http|https)/;
      if (regex.test(appCompatibleData.customHostValue)) {
        configTab.value = 'compatibility'
        switchAdvancedTab('compatibility')
        ElMessage.error('自定义host不能以http或者https开头')
        return false
      }
    }
  }

  try {
    const r = await formInstance.validate();
    if (r !== true) {
      configTab.value = props.addAppType === "portal" ? 'portal' : 'basic'
      switchAdvancedTab(props.addAppType === "portal" ? 'portal' : 'basic')
      return false
    } else if (props.addAppType === "portal") {
      return true
    }
  } catch (error) {
    console.error('校验异常:', error)
    return false
  }
  if (formData.app_type === 'tun') { //tun应用 校验应用地址
    validateR = await addressTable.value?.validate()
    if (!validateR) {
      return false
    }
  }

  validateR = await sso.value?.validate() //单点登录校验
  if (!validateR) {
    configTab.value = 'sso'
    switchAdvancedTab('sso')
    return false
  }
  validateR = await advancedConf.value?.validate()
  if (!validateR) {
    configTab.value = 'advanced'
    switchAdvancedTab('advanced')
    return false
  }


  return validateR
}
defineExpose({validate, getDrawerData})


const configTab = ref(props.addAppType === 'portal' ? 'portal' : 'basic') //当前选中的选项卡


const tagValue = ref('') //添加标签值

const tagVisible = ref(false)

const emit = defineEmits(['refreshGroup'])
//添加应用标签
const addGroup = async () => {
  const res = await createGroup({
    group_name: tagValue.value.replace(/(^\s*)|(\s*$)/g, ''),
  })
  if (res.status === 200 && res.data.code !== -1) {
    emit('refreshGroup')
    tagValue.value = ''
    tagVisible.value = false
  } else {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
  }
}
const componentKey = ref(0)
//设置应用类型 选中的样式
const selectAppType = async (type) => {
  if (type === 'portal') {
    return
  }
  formData.app_type = type
  await nextTick() // 确保 DOM 已经更新
  const cardElement = document.querySelector(`.app-type-card[data-value="${type}"]`);
  // 重置所有卡片样式
  const allCards = document.querySelectorAll('.app-type-card');
  allCards.forEach(card => {
    card.classList.remove('selected');
    card.style.borderColor = '#e5e7eb';
    card.style.background = '#fff';
    card.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
    // 隐藏选中指示器
    const indicator = card.querySelector('.check-indicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  });

  // 设置选中卡片样式
  cardElement.classList.add('selected');
  if (type === 'tun') {
    cardElement.style.borderColor = '#2563eb';
    cardElement.style.background = 'linear-gradient(135deg, #f8fafc 0%, #eff6ff 100%)';
  } else {
    cardElement.style.borderColor = '#10b981';
    cardElement.style.background = 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%)';
  }
  // 显示选中指示器
  const selectedIndicator = cardElement.querySelector('.check-indicator');
  if (selectedIndicator) {
    selectedIndicator.style.display = 'flex';
    selectedIndicator.style.borderColor = type === 'web' ? '#10b981' : '#2563eb';
    selectedIndicator.style.background = type === 'web' ? '#10b981' : '#2563eb';
  }
  // 选中对应的radio按钮
  const radioButton = cardElement.querySelector('input[type="radio"]');
  if (radioButton) {
    radioButton.checked = true;
  }

}

watch(
    () => formData.app_type,
    (newVal) => {

      if (newVal === 'tun' && configTab.value === 'compatibility') {
        configTab.value = 'basic'
        switchAdvancedTab('basic')
      }
      if ( props.addAppType !== 'portal'){
        if (newVal === 'web') {
          if ((formData.publish_address === '' || formData.server_address) && configTab.value === "portal") {
            ElMessage({
              type: 'error',
              message: "请先填写完整基础配置",
            })
            switchAdvancedTab('basic')
            return false
          }

          formData.web_url = formData.publish_schema + "://" + formData.publish_address + '/' + formData.web_url_web
          rules.server_address = [
            {required: true, message: '服务器地址必填', type: 'error', trigger: 'blur'}
          ];
          rules.publish_address = [
            {required: true, message: '应用发布地址必填', type: 'error', trigger: 'blur'}
          ];
          formData.server_schema = formData.server_schema ? formData.server_schema : 'https'
          formData.publish_schema = formData.publish_schema ? formData.publish_schema : 'https'
          if (props.apiData.id === '') {
            formData.web_compatible_config.single_sign_on.type = 'micro_application'
          }
        } else {
          rules.publish_address = [];
          rules.server_address = [];
          formData.web_compatible_config.single_sign_on.type = 'oauth2'
        }
      }
      componentKey.value += 1
      selectAppType(newVal)
    },
    {immediate: true}
);


//应用图标上传后的src
const syncSrc = (src) => {
  formData.icon_url = src
}


const addUrlRewriteRow = async () => {
  for (const item of formData.web_compatible_config.url_manual_rewrite) {
    if (!item.after || !item.before) {
      await MessagePlugin.error("上一行数据填写不完整");
      return
    }
  }
  formData.web_compatible_config.url_manual_rewrite.push({id: Symbol(), before: '', after: ''})
}

const removeUrlRewriteRow = (item) => {
  if (formData.web_compatible_config.url_manual_rewrite.length === 1) {
    return
  }
  const idx = formData.web_compatible_config.url_manual_rewrite.findIndex(i => i.id === item.id);
  if (idx > -1) {
    formData.web_compatible_config.url_manual_rewrite.splice(idx, 1);
  }
}


// 标签页切换功能
const switchAdvancedTab = (tabName) => {
  // 基础验证
  if (tabName === 'portal' && formData.app_type === 'web' && (!formData.publish_address || !formData.server_address)) {
    ElMessage({
      type: 'error',
      message: "请先填写完整基础配置",
    })
    // 如果是portal类型且基础配置未完成，切换到基础配置
    if (props.addAppType !== 'portal') {
      configTab.value = 'basic'
    }
    return false
  }

  // 设置当前标签页
  configTab.value = tabName

  // 重置所有标签样式
  const tabItems = document.querySelectorAll('.tab-item');
  tabItems.forEach(item => {
    item.style.borderBottomColor = 'transparent';
    item.style.color = '#6b7280';
    item.style.background = 'transparent';
    item.style.fontWeight = '500';
    item.style.boxShadow = 'none';
    item.style.transform = 'translateY(0)';
    item.classList.remove('active');
  });

  // 设置选中标签的样式
  const selectedTab = document.querySelector(`[data-tab="${tabName}"]`);
  if (selectedTab) {
    selectedTab.style.borderBottomColor = '#2563eb';
    selectedTab.style.color = '#2563eb';
    selectedTab.style.background = 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)';
    selectedTab.style.fontWeight = '600';
    selectedTab.style.boxShadow = '0 2px 4px rgba(37,99,235,0.1)';
    selectedTab.style.transform = 'translateY(-2px)';
    selectedTab.classList.add('active');
  }

  // 自动滚动到标签页内容区域
  setTimeout(() => {
    scrollToTabContent(tabName);
  }, 100);
}

// 监听发布协议变化，当切换到https时自动加载证书列表
watch(
  () => formData.publish_schema,
  (newVal) => {
    if (newVal === 'https' && certificateOptions.value.length === 0) {
      fetchCertificateList()
    }
  }
);

// 监听证书ID变化，确保空值能被正确处理
watch(
    () => formData.certificate_id,
    (newVal, oldVal) => {
      // 确保空值被明确设置
      if (newVal === null || newVal === undefined) {
        formData.certificate_id = ''
      }
    }
);
watch(
    () => props.addAppType,
    (newVal) => {
      if (newVal && newVal === 'portal') {
        switchAdvancedTab('portal')
        //删掉 sdp
        delete rules.sdp
      }
    },
    {immediate: true, deep: true}
);

</script>
<style>

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.t-input-adornment {
  width: 100% !important;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modal-footer .default,
.modal-footer .primary {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-footer .default {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.modal-footer .default:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.modal-footer .primary {
  background: #2563eb;
  color: white;
  border: 1px solid #2563eb;
}

.modal-footer .primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
}


/* 错误输入框样式 */
.appDraw .error_input {
  border-color: var(--td-error-color) !important;
}

/* 表单标签样式 */
.appDraw .t-form__label {
  font-weight: 600;
  font-size: 12px;
  color: #1f2937;
}

/* 应用类型容器 */
.appDraw .app-type-container {
  display: flex;
  gap: 16px;
  width: 100%;
  min-width: fit-content;
}

/* 卡片容器通用样式 */
.appDraw .app-type-card {
  flex: 1 1 auto;
  min-width: 256px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

/* 鼠标悬停效果 */
.appDraw .app-type-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-3px);
}

/* 点击动画 */
.appDraw .app-type-card:active {
  transform: translateY(-1px);
}

/* 未选中卡片淡化效果 */
.appDraw .app-type-card:not(.selected) {
  opacity: 0.7;
  filter: grayscale(0.2);
}

/* 选中卡片高亮效果 */
.appDraw .app-type-card.selected {
  opacity: 1;
  filter: none;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

/* 选中指示器显示 */
.appDraw .app-type-card.selected .check-indicator {
  display: flex !important;
  border-color: #10b981 !important;
  background: #10b981 !important;
}

/* 卡片选中动画 */
@keyframes cardGlow {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
}

.appDraw .app-type-card.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05));
  pointer-events: none;
  opacity: 0;
  animation: cardGlow 0.6s ease-out forwards;
}

/* URL重写按钮样式 */
.appDraw .add-url-rewrite-btn {
  background: none;
  border: none;
  color: #2563eb;
  font-size: 12px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-top: 4px;
}

.appDraw .add-url-rewrite-btn:hover {
  background: #eff6ff;
  color: #1d4ed8;
}

/* Web应用卡片样式 */
.appDraw .webCard {
  flex: 1 1 0% !important;
  padding: 12px !important;
  border-radius: 8px !important;
  border: 2px solid rgb(16, 185, 129) !important;
  cursor: pointer !important;
  background: linear-gradient(135deg, rgb(240, 253, 244) 0%, rgb(236, 253, 245) 100%);
}

/* 隧道应用非选中卡片样式 */
.appDraw .tunCardNo {
  flex: 1 1 0% !important;
  padding: 12px !important;
  border: 2px solid rgb(229, 231, 235) !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  transition: 0.2s !important;
  background: rgb(255, 255, 255) !important;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 2px 4px !important;
}

/* 动态配置块样式 */
.appDraw .dynamic-block {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  padding: 20px 24px;
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
  position: relative;
}

.appDraw .dynamic-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.appDraw .dynamic-block-title {
  font-weight: 600;
  color: #2563eb;
  font-size: 12px;
}

.appDraw .dynamic-block-delete {
  background: none;
  border: none;
  color: #2563eb;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.appDraw .dynamic-block-delete:hover {
  background: #eff6ff;
  color: #1d4ed8;
}

.appDraw .dynamic-block-input,
.appDraw .dynamic-block textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 12px;
  color: #1f2937;
  resize: vertical;
  transition: all 0.2s ease;
}

.appDraw .dynamic-block-input:focus,
.appDraw .dynamic-block textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #eff6ff;
  outline: none;
}

/* 表单控件样式优化 */
.appDraw input[type="checkbox"],
.appDraw input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: #2563eb;
  cursor: pointer;
}

/* 星号样式 */
.appDraw span[style*="color:#f5222d"] {
  color: #ef4444 !important;
  font-size: 10px !important;
  font-weight: 500 !important;
  margin-right: 4px !important;
  vertical-align: top !important;
  line-height: 1 !important;
  position: relative !important;
  top: 1px !important;
}

/* 标签页样式优化 */
.appDraw .advanced-tabs {
  position: relative;
}

.appDraw .tab-item {
  position: relative;
  overflow: hidden;
}

.appDraw .tab-item:hover:not(.active) {
  background: rgba(59, 130, 246, 0.05) !important;
  color: #374151 !important;
  transform: translateY(-1px);
}

.appDraw .tab-item.active {
  transform: translateY(-2px) !important;
  z-index: 10;
}

.appDraw .tab-item.active::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
  border-radius: 2px 2px 0 0;
}

/* 标签页切换动画 */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.appDraw .tab-content {
  animation: fadeInUp 0.4s ease-out;
}

/* URL重写行样式 */
.appDraw .url-rewrite-row {
  display: flex;
  gap: 8px;
  margin-bottom: 6px;
  align-items: center;
  padding: 6px 10px;
  background: #f8fafc;
  border-radius: 6px;
  font-size: 12px;
}

.appDraw .url-rewrite-row input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  transition: all 0.2s ease;
}

.appDraw .url-rewrite-row input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #eff6ff;
  outline: none;
}

.appDraw .row-delete-btn {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 14px;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  transition: all 0.2s ease;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

.appDraw .row-delete-btn:hover {
  background: #fef2f2;
  color: #b91c1c;
}

/* 输入框单位样式 */
.appDraw .input-with-unit input {
  width: 60px;
}

.appDraw .input-with-unit span {
  color: #6b7280;
  font-size: 12px;
  white-space: nowrap;
}

/* 应用打开方式配置样式 */
.appDraw .open-config-layout {
  transition: all 0.3s ease;
}

/* 整体说明区域 */
.appDraw .open-config-intro {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.appDraw .intro-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.appDraw .intro-icon {
  font-size: 18px;
}

.appDraw .intro-desc {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

/* 打开方式选择区域 */
.appDraw .open-type-selection {
  margin-bottom: 24px;
  width: 100% !important;
}

.appDraw .open-config-layout {
  width: 100% !important;
}

/* 按钮组样式 - 仅针对门户配置中的应用打开方式 */
.appDraw .open-config-layout .button-group {
  position: relative;
  margin-bottom: 16px;
  display: flex !important;
  width: 100% !important;
  max-width: 400px !important;
  gap: 2px !important; /* 稍微增加间距，让按钮更清晰 */
  background: #f1f5f9 !important; /* 更浅的背景色 */
  border-radius: 8px !important;
  padding: 3px !important;
}

.appDraw .open-config-layout .button-group-item {
  position: relative;
  z-index: 1;
  flex: 1 !important;
  background: transparent !important; /* 完全透明背景，更加简洁 */
  border: none !important; /* 完全无边框设计 */
  transition: all 0.2s ease !important; /* 平滑过渡效果 */
}

/* 第一个按钮的圆角 */
.appDraw .open-config-layout .button-group-item:first-child {
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}

/* 最后一个按钮的圆角 */
.appDraw .open-config-layout .button-group-item:last-child {
  border-top-right-radius: 6px !important;
  border-bottom-right-radius: 6px !important;
}

.appDraw .open-config-layout .button-group-item:hover:not(.active) {
  background: rgba(59, 130, 246, 0.08) !important; /* 微妙的蓝色背景 */
  color: #374151 !important;
  border-radius: 6px !important; /* 悬停时添加圆角 */
  transform: translateY(-0.5px) !important; /* 轻微的上移效果 */
}

.appDraw .open-config-layout .button-group-item.active {
  background: #2563eb !important;
  color: white !important;
  border-radius: 6px !important; /* 选中时的圆角 */
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.25) !important; /* 更明显的阴影 */
  transform: translateY(-1px) !important;
}

.appDraw .open-config-layout .button-group-item.active:hover {
  background: #1d4ed8 !important;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important; /* 悬停时更强的阴影 */
}

/* 选择说明 */
.appDraw .selection-desc {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
}

.appDraw .desc-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #475569;
}

.appDraw .desc-icon {
  font-size: 16px;
}

/* 配置区域 */
.appDraw .config-section {
  margin-top: 24px;
}

/* 添加按钮容器 */
.appDraw .add-button-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 32px;
  padding-top: 16px;
  padding-bottom: 16px;
  padding-right: 16px;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  position: relative;
}

.appDraw .config-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.appDraw .config-icon {
  font-size: 18px;
}

.appDraw .add-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 添加按钮统一蓝色主题 - 轻量化样式 */
.appDraw .add-browser-button,
.appDraw .add-program-button,
.appDraw .add-system-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #64748b !important;
  border: 1px dashed #cbd5e1 !important;
  background: transparent !important;
  font-size: 13px !important;
  padding: 6px 12px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.appDraw .add-browser-button:hover,
.appDraw .add-program-button:hover,
.appDraw .add-system-button:hover {
  color: #2563eb !important;
  border-color: #2563eb !important;
  border-style: solid !important;
  background: #f1f5f9 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 1px 4px rgba(37, 99, 235, 0.1) !important;
}

/* 配置卡片 */
.appDraw .config-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.appDraw .config-card:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.appDraw .card-header {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.appDraw .card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.appDraw .card-icon {
  font-size: 16px;
}

.appDraw .remove-button {
  color: #2563eb !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.appDraw .remove-button:hover {
  background: #eff6ff !important;
  color: #1d4ed8 !important;
}

.appDraw .card-content {
  padding: 16px;
}

.appDraw .field-group {
  margin-bottom: 16px;
}

.appDraw .field-group:last-child {
  margin-bottom: 0;
}

.appDraw .field-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.appDraw .field-hint {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

/* 警告框 */
.appDraw .warning-box {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
}

.appDraw .warning-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #92400e;
}

.appDraw .warning-icon {
  font-size: 14px;
}

.appDraw .open-config-layout .t-checkbox-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.appDraw .open-config-layout .t-checkbox {
  margin-right: 0;
}

/* 更多选项样式 - 简化版 */
.appDraw .more-options-toggle {
  margin-top: 8px;
  margin-bottom: 12px;
}

.appDraw .more-options-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 0;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  color: #64748b;
  font-size: 13px;
  border-radius: 4px;
}

.appDraw .more-options-btn:hover {
  background: #f1f5f9;
  color: #374151;
}

.appDraw .more-options-btn .toggle-icon {
  font-size: 11px;
  transition: transform 0.2s ease;
  width: 10px;
  text-align: center;
}

.appDraw .more-options-btn .toggle-text {
  font-weight: 400;
}


</style>
