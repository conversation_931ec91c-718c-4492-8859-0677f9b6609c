package oprlog

import (
	"asdsec.com/asec/platform/app/console/app/read_event/api"
	"github.com/gin-gonic/gin"
)

func ReadEventApi(r *gin.RouterGroup) {
	v := r.Group("/v1/read")
	{
		v.POST("/condition", api.GetReadEventCondition)
		v.POST("/event", api.GetReadEventList)
		v.PUT("/filter", api.UpdateReadEventFilter)
		v.GET("/filter", api.GetReadEventFilter)
		v.GET("/event/detail", api.GetReadEventDetail)
		v.POST("/process", api.ExcludeProcess)
		v.POST("/process_list", api.GetProcessList)
		v.POST("/clean", api.CleanReadEvent)
	}
}
