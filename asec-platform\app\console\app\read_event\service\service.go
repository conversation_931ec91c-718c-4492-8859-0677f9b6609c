package service

import (
	"asdsec.com/asec/platform/app/console/app/read_event/constants"
	"asdsec.com/asec/platform/app/console/app/read_event/dto"
	"asdsec.com/asec/platform/app/console/app/read_event/repository"
	userDto "asdsec.com/asec/platform/app/console/app/user/dto"
	userService "asdsec.com/asec/platform/app/console/app/user/service"
	global "asdsec.com/asec/platform/app/console/global"
	readEvent "asdsec.com/asec/platform/pkg/model/read_event"
	"context"
	"gorm.io/gorm"
	"sync"
	"time"
)

var EventFilterServiceImpl EventFilterService

// EventFilterServiceInit 单例对象
var EventFilterServiceInit sync.Once

type EventFilterService interface {
	GetReadEventCondition(ctx context.Context, req dto.GetReadEventCommonReq) (dto.GetReadEventConditionRsp, error)
	GetReadEventList(ctx context.Context, req dto.GetReadEventReq) (dto.GetReadEventListRsp, error)
	UpdateReadEventFilter(ctx context.Context, req dto.UpdateEventFilterReq) error
	GetReadEventFilter(ctx context.Context) (dto.EventFilterRsp, error)
	GetReadEventDetail(ctx context.Context, uuid string) (readEvent.FileEvents, error)
	ExcludeProcess(ctx context.Context, process string) error
	GetReadEventProcessList(ctx context.Context, req dto.GetReadEventReq) (dto.GetProcessListRsp, error)
	DelCkReadEventPartition(ctx context.Context, reserverDay int) error
	CleanReadEvent(ctx context.Context) error
}

type eventFilterService struct {
	db repository.EventFilterRepository
}

const (
	queryPartitionSql = `select partition from system.parts where database='asec' and  table='tb_read_file_events' group by partition order by partition desc;`
	delPartitionSql   = `alter table tb_read_file_events drop partition ?`
)

func (e eventFilterService) GetReadEventProcessList(ctx context.Context, req dto.GetReadEventReq) (dto.GetProcessListRsp, error) {
	return e.db.GetReadEventProcessList(ctx, req)
}

func (e eventFilterService) ExcludeProcess(ctx context.Context, process string) error {
	filter, err := e.GetReadEventFilter(ctx)
	if err != nil {
		return err
	}
	processList := filter.Process
	processList = append(processList, process)
	updateFilterReq := dto.UpdateEventFilterReq{
		Id:         filter.Id,
		FilterType: constants.GlobalAgentFilter,
		Process:    processList, FileTypeCode: filter.FileTypeCode,
		UserGroupIds:  filter.UserGroupIds,
		UserIds:       filter.UserIds,
		EnableAllUser: filter.EnableAllUser,
		ScanContent:   filter.ScanContent,
		ReserveDay:    filter.ReserveDay,
	}
	return e.UpdateReadEventFilter(ctx, updateFilterReq)
}

func (e eventFilterService) GetReadEventDetail(ctx context.Context, uuid string) (readEvent.FileEvents, error) {
	return e.db.GetReadEventDetail(ctx, uuid)
}

func (e eventFilterService) GetReadEventCondition(ctx context.Context, req dto.GetReadEventCommonReq) (dto.GetReadEventConditionRsp, error) {
	return e.db.GetReadEventCondition(ctx, req)
}

func (e eventFilterService) GetReadEventList(ctx context.Context, req dto.GetReadEventReq) (dto.GetReadEventListRsp, error) {
	return e.db.GetReadEventList(ctx, req)
}

func (e eventFilterService) UpdateReadEventFilter(ctx context.Context, req dto.UpdateEventFilterReq) error {
	err := e.db.UpdateReadEventFilter(ctx, req)
	if err != nil {
		return err
	}
	go func() {
		err := e.DelCkReadEventPartition(ctx, req.ReserveDay)
		if err != nil {
			global.SysLog.Sugar().Errorf("DelCkReadEventPartition failed. err=%v", err)
		}
	}()
	return nil
}

func (e eventFilterService) GetReadEventFilter(ctx context.Context) (dto.EventFilterRsp, error) {
	data, err := e.db.GetReadEventFilter(ctx)
	if err != nil {
		return dto.EventFilterRsp{}, err
	}
	infoRsp, err := userService.GetUserService().UserComponentEcho(
		ctx,
		userDto.UserComponentEchoReq{UserIds: data.UserIds, GroupIds: data.UserGroupIds},
	)
	data.UserInfo = infoRsp.UserEcho
	data.GroupInfo = infoRsp.GroupEcho
	return data, nil
}

func (e eventFilterService) CleanReadEvent(ctx context.Context) error {
	return e.DelCkReadEventPartition(ctx, -1)
}

func (e eventFilterService) DelCkReadEventPartition(ctx context.Context, reserverDay int) error {
	db, err := global.GetCkClient(ctx)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetCkDb failed. err=%v", err)
		return err
	}
	date := time.Now().AddDate(0, 0, -1*reserverDay).Format("20060102")
	var delPartitions []string
	err = db.Raw(queryPartitionSql).Find(&delPartitions).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("GetReadEventPartition failed. err=%v", err)
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		for _, v := range delPartitions {
			if v < date {
				err = tx.Exec(delPartitionSql, v).Error
				if err != nil {
					global.SysLog.Sugar().Errorf("DelPartition failed. err=%v, partition=%v", err, v)
					return err
				}
				global.SysLog.Sugar().Infof("DelPartition success. partition=%v", v)
			}
		}
		return nil
	})
}
func GetEventFilterService() EventFilterService {
	EventFilterServiceInit.Do(func() {
		EventFilterServiceImpl = &eventFilterService{db: repository.NewEventFilterRepository()}
	})
	return EventFilterServiceImpl
}
