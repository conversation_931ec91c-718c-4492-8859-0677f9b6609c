package repository

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"strconv"
	"sync"

	"asdsec.com/asec/platform/app/console/app/form_fill/model"
	global "asdsec.com/asec/platform/app/console/global"
	"gorm.io/gorm"
)

var FormFillRepositoryImpl FormFillRepository
var FormFillRepositoryInit sync.Once

// 加密密钥 - 在生产环境中应该从配置文件或环境变量获取
// 这里使用一个示例密钥，生产环境中应该替换为真实的密钥
const encryptionKey = "wbiGLjBMSXjlqbV9zaAdjqhBSsfvrOEa" // 32字节用于AES-256

// encryptPassword 加密密码
func encryptPassword(password string) (string, error) {
	block, err := aes.NewCipher([]byte(encryptionKey))
	if err != nil {
		return "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	// 加密
	ciphertext := gcm.Seal(nonce, nonce, []byte(password), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptPassword 解密密码
func decryptPassword(encryptedPassword string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encryptedPassword)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher([]byte(encryptionKey))
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// FormFillRepository 表单代填仓储接口
type FormFillRepository interface {
	// 获取用户应用账户
	GetAccountByAppIDAndUserID(ctx context.Context, appID, userID string) (*model.ApplicationAccount, error)

	// 获取解密后的用户应用账户
	GetDecryptedAccountByAppIDAndUserID(ctx context.Context, appID, userID string) (*model.ApplicationAccount, error)

	// 创建或更新用户应用账户
	UpsertAccount(ctx context.Context, corpID int64, appID string, userID, username, password string) error

	// 获取用户基本信息
	GetUserInfo(ctx context.Context, userID string) (*model.UserInfo, error)
}

// formFillRepository 表单代填仓储实现
type formFillRepository struct {
}

// GetFormFillRepository 获取表单代填仓储实例
func GetFormFillRepository() FormFillRepository {
	FormFillRepositoryInit.Do(func() {
		FormFillRepositoryImpl = &formFillRepository{}
	})
	return FormFillRepositoryImpl
}

// GetAccountByAppIDAndUserID 根据应用ID和用户ID获取账户
func (r *formFillRepository) GetAccountByAppIDAndUserID(ctx context.Context, appID, userID string) (*model.ApplicationAccount, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	// 将appID从字符串转换为int64
	appIDInt64, err := strconv.ParseInt(appID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid app_id: %s", appID)
	}

	var account model.ApplicationAccount
	err = db.Where("app_id = ? AND local_user_id = ?", appIDInt64, userID).First(&account).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {

			return nil, nil // 账户不存在
		}

		return nil, err
	}

	return &account, nil
}

// GetDecryptedAccountByAppIDAndUserID 根据应用ID和用户ID获取解密后的账户
func (r *formFillRepository) GetDecryptedAccountByAppIDAndUserID(ctx context.Context, appID, userID string) (*model.ApplicationAccount, error) {
	// 先获取加密的账户信息
	account, err := r.GetAccountByAppIDAndUserID(ctx, appID, userID)
	if err != nil || account == nil {
		return account, err
	}

	// 解密密码
	decryptedPassword, err := decryptPassword(account.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt password: %v", err)
	}

	// 创建新的账户对象，包含解密后的密码
	decryptedAccount := *account
	decryptedAccount.Password = decryptedPassword

	return &decryptedAccount, nil
}

// UpsertAccount 创建或更新用户应用账户
func (r *formFillRepository) UpsertAccount(ctx context.Context, corpID int64, appID string, userID, username, password string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	// 将appID从字符串转换为int64
	appIDInt64, err := strconv.ParseInt(appID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid app_id: %s", appID)
	}

	// 加密密码
	encryptedPassword, err := encryptPassword(password)
	if err != nil {
		return fmt.Errorf("failed to encrypt password: %v", err)
	}

	// 查找现有记录
	var account model.ApplicationAccount
	err = db.Where("app_id = ? AND local_user_id = ?", appIDInt64, userID).First(&account).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		account = model.ApplicationAccount{
			CorpID:      corpID,
			AppID:       appIDInt64,
			LocalUserID: userID,
			Username:    username,
			Password:    encryptedPassword, // 存储加密后的密码
		}
		return db.Create(&account).Error
	} else if err != nil {
		return err
	}

	// 更新现有记录
	return db.Model(&account).Updates(map[string]interface{}{
		"username": username,
		"password": encryptedPassword, // 存储加密后的密码
	}).Error
}

// GetUserInfo 获取用户基本信息
func (r *formFillRepository) GetUserInfo(ctx context.Context, userID string) (*model.UserInfo, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	// 查询用户基本信息 (phone, email, name)
	var userInfo model.UserInfo
	err = db.Table("tb_user_entity").
		Select("id, name, phone, email").
		Where("id = ?", userID).
		First(&userInfo).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return &userInfo, nil
}
