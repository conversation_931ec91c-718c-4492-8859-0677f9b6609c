package repository

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	"asdsec.com/asec/platform/app/console/app/notification/dto"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	pageModel "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/notification_model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"strconv"
	"strings"
)

type NotificationRepository interface {
	UpdateNotification(ctx context.Context, req dto.NotificationReq) error
	GetNotificationList(ctx context.Context, req dto.ListReq) (dto.NotificationListResp, error)
	CreateNotification(ctx context.Context, req dto.NotificationReq) error
	DeleteNotification(ctx context.Context, id string) error
	GetNotificationDetail(ctx context.Context, id string) (notification_model.Notification, error)
	BatchDeleteNotification(ctx context.Context, req vo.DelReq) error
}

// NewNotificationRepository 创建接口实现接口实现
func NewNotificationRepository() NotificationRepository {
	return &notificationRepository{}
}

type notificationRepository struct {
}

func (n notificationRepository) BatchDeleteNotification(ctx context.Context, req vo.DelReq) error {
	db, err := global.GetDBClient(ctx)
	return db.Transaction(func(tx *gorm.DB) error {
		err = db.Model(notification_model.Notification{}).Where("id in ?", req.Ids).Delete(notification_model.Notification{}).Error
		if err != nil {
			return err
		}
		for _, id := range req.Ids {
			err = conf_center.ConfChange(conf_center.ConfChangeReq{
				ConfBizId:  id,
				ConfType:   dto.NotificationConfType,
				Tx:         tx,
				RedisCli:   global.SysRedisClient,
				ChangeType: conf_center.DelConf})
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (n notificationRepository) DeleteNotification(ctx context.Context, id string) error {
	db, err := global.GetDBClient(ctx)
	return db.Transaction(func(tx *gorm.DB) error {
		err = db.Model(notification_model.Notification{}).Where("id = ?", id).Delete(notification_model.Notification{}).Error
		if err != nil {
			return err
		}
		err = conf_center.ConfChange(conf_center.ConfChangeReq{
			ConfBizId:  id,
			ConfType:   dto.NotificationConfType,
			Tx:         tx,
			RedisCli:   global.SysRedisClient,
			ChangeType: conf_center.DelConf})
		if err != nil {
			return err
		}
		return nil
	})
}

func (n notificationRepository) GetNotificationList(ctx context.Context, req dto.ListReq) (dto.NotificationListResp, error) {
	db, err := global.GetDBClient(ctx)
	resp := dto.NotificationListResp{}
	if err != nil {
		return resp, err
	}
	//var pagination = model.Pagination{Limit: req.Limit, Offset: req.Offset, Search: req.Search, SearchColumns: []string{"name", "title"}}
	search := strings.ToLower(req.Search)
	var pagination = model.Pagination{
		Limit:         req.Limit,
		Offset:        req.Offset,
		Search:        search,
		SearchColumns: []string{"LOWER(name)", "LOWER(title)"},
	}
	var _l []notification_model.Notification
	paginate, err := pageModel.Paginate(&_l, &pagination, db)
	if err != nil {
		return resp, err
	}
	resp.TotalNum = paginate.TotalRows
	resp.NotificationList = _l
	return resp, nil
}

func (n notificationRepository) CreateNotification(ctx context.Context, req dto.NotificationReq) error {
	data := notification_model.Notification{}
	copier.Copy(&data, &req)
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return err
	}
	data.Id = strconv.FormatUint(id, 10)
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(notification_model.Notification{}).Create(&data).Error
		if err != nil {
			return err
		}
		err = commonApi.PushCommonAgentConf(
			tx,
			data,
			dto.NotificationConfType,
			data.Id,
			conf_center.AddConf,
			conf_center.GlobalConf,
			nil, nil, nil, nil)
		if err != nil {
			return err
		}
		return nil
	})
}

func (n notificationRepository) GetNotificationDetail(ctx context.Context, id string) (notification_model.Notification, error) {
	resp := notification_model.Notification{}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return resp, err
	}
	err = db.Model(notification_model.Notification{}).First(&resp, "id = ?", id).Error
	if err != nil {
		return resp, err
	}
	return resp, nil
}

func (n notificationRepository) UpdateNotification(ctx context.Context, req dto.NotificationReq) error {
	data := notification_model.Notification{}
	copier.Copy(&data, &req)
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(notification_model.Notification{}).Where("id = ?", data.Id).Updates(&data).Error
		if err != nil {
			return err
		}
		err = commonApi.PushCommonAgentConf(
			tx,
			data,
			dto.NotificationConfType,
			data.Id,
			conf_center.UpdateConf,
			conf_center.GlobalConf,
			nil, nil, nil, nil)
		if err != nil {
			return err
		}
		return nil
	})
}
