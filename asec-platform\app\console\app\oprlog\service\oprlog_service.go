package service

import (
	"asdsec.com/asec/platform/app/console/app/oprlog/dto"
	"asdsec.com/asec/platform/app/console/app/oprlog/repository"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/app/console/utils/web"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"sync"
	"time"
)

var OprlogServiceImpl OprlogService

// OprlogServiceInit 单例对象
var OprlogServiceInit sync.Once

type OprlogService interface {
	GetById(ctx context.Context, id string) model.Oprlog
	DeleteById(ctx context.Context, id string)
	Create(ctx *gin.Context, oprlog model.Oprlog) (string, error)
	OprLogList(ctx context.Context, req dto.ListOperateLogReq) (model.Pagination, error)
	ClearLog(ctx context.Context) error
	CreateOptResource(ctx context.Context, req dto.OptResourceTypeReq) error
	GetOptResourceList(ctx context.Context) (dto.OptResourceTypeList, error)
}
type oprlogService struct {
	db repository.OprlogRepository
}

func (u *oprlogService) CreateOptResource(ctx context.Context, req dto.OptResourceTypeReq) error {
	return u.db.CreateOptResource(ctx, req)
}

func (u *oprlogService) GetOptResourceList(ctx context.Context) (dto.OptResourceTypeList, error) {
	return u.db.GetOptResourceList(ctx)
}

func GetOprlogService() OprlogService {
	OprlogServiceInit.Do(func() {
		OprlogServiceImpl = &oprlogService{db: repository.NewOprlogRepository()}
	})
	return OprlogServiceImpl
}

func (u *oprlogService) GetById(ctx context.Context, id string) model.Oprlog {
	return u.db.GetById(ctx, id)
}

func (u *oprlogService) DeleteById(ctx context.Context, id string) {
	u.db.DeleteById(ctx, id)
}

func (u *oprlogService) Create(ctx *gin.Context, oprlog model.Oprlog) (string, error) {
	oprlog.AdminEventTime = time.Now().UnixMilli()
	oprlog.Id = uuid.New().String()
	oprlog.IpAddress = common.GetClientIp(ctx)
	oprlog.AuthUserID = web.GetCurrentAdminId(ctx)
	oprlog.CorpId = web.GetAdminCorpId(ctx)

	//参数检查
	return u.db.Create(ctx, oprlog)
}

func (u *oprlogService) OprLogList(ctx context.Context, req dto.ListOperateLogReq) (model.Pagination, error) {
	return u.db.OprLogList(ctx, req)
}

func (u *oprlogService) ClearLog(ctx context.Context) error {
	return u.db.ClearLog(ctx)
}
