package admin

import (
	"asdsec.com/asec/platform/app/console/app/auth/admin/api"
	"github.com/gin-gonic/gin"
)

func AdminApi(r *gin.RouterGroup) {
	grp := r.Group("/v1/admin")
	{
		grp.POST("", api.CreateAdmin)
		grp.DELETE("", api.DeleteAdmin)
		grp.PUT("", api.UpdateAdmin)
		grp.GET("list", api.GetAdminList)
		grp.GET("", api.GetAdminById)
		grp.PUT("password", api.UpdateAdminPwd)
		grp.POST("logout", api.AdminLogOut)
		grp.GET("/login/authorization", api.GetLicenseInfo)
		grp.PUT("/auth/config", api.UpdateAdminAuthConfig)
		grp.GET("/auth/config", api.GetAdminAuthConfig)
	}
}
