package dto

type TimeReq struct {
	StartTime string `form:"start_time" json:"start_time" binding:"required"`
	EndTime   string `form:"end_time" json:"end_time" binding:"required"`
}

type ProcessReq struct {
	TimeReq
	IncidentId string `form:"incident_id" json:"incident_id" binding:"required"`
	AgentId    string `form:"agent_id" json:"agent_id" binding:"omitempty,min=1"`
}

type ContextReq struct {
	TimeReq         //默认显示告警事件触发的前12小时和后12小时
	UserId   string `form:"user_id" json:"user_id" binding:"required"`
	Activity string `form:"activity" json:"activity" binding:"required,oneof=send access"`
}

type NameReq struct {
	TimeReq            //默认显示告警事件触发的前12小时和后12小时
	UserId      string `form:"user_id" json:"user_id" binding:"required"`
	IncidentsId string `form:"incidents_id" json:"incidents_id" binding:"required"`
}
