<template>
  <div class="application">
    <t-tabs v-model="application_tabs" class="one-tabs">
      <t-tab-panel :value="1" label="应用管理" :destroy-on-hide="false" style="background: #EEEEEE">
        <div :class="isAction?'top-title-d':'top-title'"/>
        <div class="content" :style="isAction?'margin-top: -1px;height: calc(100vh - 112px)': ''">
          <div
              class="shrink"
              :style="isAction?'top: -12px':''"
              style="display: none"
              @click="upshrink"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
          >
            <svg class="icon" style="font-size: 12px" aria-hidden="true">
              <use :xlink:href="shrink_icon"/>
            </svg>
          </div>
          <t-layout class="frame" style="height: 100%">
            <t-aside v-if="!showAside"
                     style="border-right: 1px solid #EBEBEB;border-radius: 4px 0 0 4px;width: 33px;height: 100%;margin: 0px"
                     class="zk">
              <t-popup content="展开" :show-arrow="true" placement="bottom">
                <div class="fold-aside-content" @click="showAside = true">
                  <i class="iconfont icon-fenyefuyou" style="font-size: 12px"/>
                </div>
              </t-popup>
            </t-aside>
            <t-aside v-else style="border-right: 1px solid #EBEBEB;border-radius: 4px 0 0 4px;width: 250px">
              <AppAsideMenu
                  :data="groupOptions"
                  :policy-group-data="policyGroupData"
                  :add="addGroup"
                  :edit="editGroup"
                  :del="deleGroup"
                  :move="handleMoveGroup"
                  :update-group-sort="updateGroupSort"
                  :get-data="getGroupList"
                  :tree-item-click="selectGroup"
                  :keys="{ value: 'id', label: 'name', children: 'child_group' }"
                  :fold="flodClick"
              />
            </t-aside>
            <t-layout>
              <t-header class="asdc-header">
                <!-- 新增应用统一下拉按钮 -->
                <div class="add-app-unified" v-if="isEdit">
                  <t-dropdown
                    trigger="click"
                    placement="bottom-left"
                    :popup-props="{ overlayClassName: 'unified-dropdown-menu' }"
                    @click="handleAddApp"
                  >
                    <div class="unified-button">
                      <div class="main-section" @click.stop="formDataInit">
                        <add-icon class="add-icon" />
                        <span class="button-text">新增应用</span>
                      </div>
                      <div class="divider-line"></div>
                      <div class="dropdown-section" @click="handleDropdownButtonClick">
                        <svg class="dropdown-icon" style="font-size: 12px" aria-hidden="true">
                          <use xlink:href="#icon-jiantouxia"/>
                        </svg>
                      </div>
                    </div>
                    <t-dropdown-menu>
                      <t-dropdown-item value="navigation">门户应用</t-dropdown-item>
                    </t-dropdown-menu>
                  </t-dropdown>
                </div>

                <t-button variant="outline" class="asdc-default-but" @click="exportApp" v-if="isEdit">
                  <template #icon>
                    <i class="iconfont icon-daochu"/>
                  </template>
                  导出
                </t-button>

                <t-button variant="outline" class="asdc-default-but" @click="router.push('importApp')" v-if="isEdit">
                  <template #icon>
                    <i class="iconfont icon-xiazai"/>
                  </template>
                  导入
                </t-button>

                <t-button variant="outline" class="asdc-default-but" @click="router.push('certificate')" v-if="isEdit">
                  <template #icon>
                    <i class="iconfont icon-zhengshuguanli"/>
                  </template>
                  应用证书
                </t-button>
                <t-button variant="outline" class="asdc-default-but" @click="getDataList">
                  <template #icon>
                    <i class="iconfont icon-shuaxin"/>
                  </template>
                  刷新
                </t-button>
                <el-input
                    v-model="search"
                    clearable
                    placeholder="请输入关键字搜索"
                    class="asdc-search"
                    style="margin-top: 17px;width: 289px"
                    @enter="getDataList"
                    @clear="getDataList"
                >
                  <template #prepend>
                    <el-select v-model="search_select" placeholder="全部" style="width: 95px;background: #ffffff">
                      <el-option label="全部" value="all"/>
                      <el-option label="应用名称" value="APP_NAME"/>
                      <el-option label="应用地址" value="SERVER_ADDRESS"/>
                      <el-option label="发布地址" value="PUBLISH_ADDRESS"/>
                      <el-option label="应用网关" value="SDP"/>
                    </el-select>
                  </template>
                  <template #append>
                    <el-button type="primary" @click="getDataList"
                               style="
                               background: #0052D9;
                               border-top-right-radius: 4px;
                               border-bottom-right-radius: 4px;
                               border-bottom-left-radius: 0px;
                               border-top-left-radius: 0px;
                                color: #FFFFFF"
                               :icon="Search" />
                  </template>
                </el-input>
                <el-select
                    v-model="application"
                    class="m-2"
                    placeholder="全部应用"
                    size="small"
                    style="width: 100px;margin-right: 10px;float: right;margin-top: 17px"
                    @change="getDataList"
                >
                  <el-option label="全部应用" value="all"/>
                  <el-option label="Web应用" value="web"/>
                  <el-option label="隧道应用" value="tun"/>
                </el-select>
              </t-header>
              <t-content
                  style="background: #ffffff;padding: 0px 18px 0px 18px;display: inline-flex; "
                  :style="isAction?'height: calc(100vh - 200px);width:100%':'height: calc(100vh - 400px)'"
              >
                <t-table
                    height="calc(100% - 69px)"
                    vertical-align="top"
                    row-key="id"
                    :sort="sort"
                    resizable
                    hover
                    :loading="loading"
                    disable-data-page="true"
                    :columns="columns"
                    :data="data"
                    :selected-row-keys="selectedRowKeys"
                    :pagination="pagination"
                    :pagination-affixed-bottom="true"
                    :reserve-selected-row-on-paginate="true"
                    row-class-name="table-row"
                    @page-change="onPageChange"
                    @select-change="onSelectChange"
                >
                  <template #empty>
                    <div class="t-table-em" style="text-align: center;">
                      <img class="empty-img" src="@/assets/empty.png">
                      <div>暂无数据</div>
                    </div>
                  </template>
                  <template #name="{row}">
                    <svg class="icon" style="font-size: 12px;margin-right: 5px" aria-hidden="true">
                      <use :xlink:href="row.app_type === 'web'?'#icon-webyingyong':'#icon-yingyong1'"/>
                    </svg>
                    {{ row.name }}
                  </template>
                  <template #application_address="{row}">
                    <div
                        style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;margin-right: 5px;float: left;"
                        :style="row.application_address?.length > 1 ? 'max-width: calc(100% - 55px)':''"
                    >
                      <t-popup class="placement bottom center" placement="bottom" show-arrow destroy-on-close>
                        <template #content>
                          <div style="padding: 8px 16px;font-size: 12px">{{ row.application_address?.[0] || '-' }}</div>
                        </template>
                        {{ row.application_address?.[0] || '-' }}
                      </t-popup>
                    </div>
                    <div style="width: 50px;float: left">
                      <t-popup class="placement bottom center" placement="bottom" show-arrow destroy-on-close>
                        <template #content>
                          <div style="padding: 8px 16px;font-size: 12px">
                            <div v-for="(item,index) in row.application_address" :key="index" style="font-size: 12px">
                              {{ item }}
                            </div>
                          </div>
                        </template>
                        <t-tag v-if="row.application_address?.length > 1" style="background: #F4F4F4">
                          +{{ row.application_address?.length - 1 }}
                        </t-tag>
                      </t-popup>
                    </div>
                  </template>
                  <template #publish_endpoint="{row}">
                    <div class="copy" style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                        <svg v-if="row.publish_endpoint && row.publish_endpoint !== '-'"
                             @click="copy(row.publish_endpoint)" class="icon" aria-hidden="true"
                             style="position: relative;font-size: 14px;float: right;margin-right: 20px;top: 5px;cursor: pointer;">
                          <use xlink:href="#icon-fuzhi"></use>
                        </svg>
                      {{ row.publish_endpoint }}
                    </div>
                  </template>

                  <template #access_strategy="{row}">
                    <t-popup v-if="row.access_strategy?.length > 0" class="placement bottom center" placement="bottom"
                             show-arrow destroy-on-close>
                      <template #content>
                        <div style="padding: 8px 16px;font-size: 12px">
                          <div v-for="(item,index) in row.access_strategy" :key="index">
                            <t-link class="font-class" theme="primary" target="_blank"
                                    :href="`/#/layout/security-policy/realTimeRules?name=${item.name}`"
                                    style="font-size: 12px;">{{ item.name }}
                            </t-link>
                          </div>
                        </div>
                      </template>
                      <t-tag style="background: #E1E9F7;color: #0052D9;cursor: pointer"> {{
                          row.access_strategy?.length
                        }}
                      </t-tag>
                    </t-popup>
                    <div v-else>
                      <t-tag style="background: #E1E9F7;color: #0052D9;cursor: pointer"> 0</t-tag>
                    </div>
                  </template>
                  <template #operation="{ row }">
                    <t-link
                        theme="primary"
                        hover="color"
                        class="font-class operation"
                        style="padding: 0px 5px;margin-right: 5px;border-radius: 4px;font-size: 12px"
                        @click="getAppDetails(row.id)"
                    >
                      详情
                    </t-link>
                    <t-dropdown
                        v-if="isEdit"
                        trigger="click"
                        :options="
                        [
                          {content: '编辑', value: 'update', onClick: () => upData(row)},
                          {content: '删除', value: 'delete', onClick: () => delData([row.id], row.name)}
                        ]
                      "
                        :popup-props="{placement: 'bottom'}"
                    >
                      <t-link hover="color" class="font-class operation more" style="padding: 0px 5px" theme="primary"/>
                    </t-dropdown>
                  </template>
                </t-table>
              </t-content>
            </t-layout>
          </t-layout>
        </div>
      </t-tab-panel>
    </t-tabs>
    <!--    详情展示抽屉-->
    <t-drawer v-model:visible="visible" size="600" destroy-on-close :close-btn="true">
      <template #header>
        <div style="font-size: 14px;font-width: 500;">应用详情</div>
      </template>
      <el-card class="box-card">
        <el-form ref="form" label-position="left" label-width="120px">
          <el-form-item  v-for="(item,index) in details_columns"  :key="index" :label="item.label">
              <div v-if="item.type === 'status'" style="color: #252631;word-wrap: break-word;">
              <span v-if="item.value ==='app_type' ">
                {{appDetail[item.value] === 'web' ? "WEB应用" : ( appDetail[item.value] === 'tun'?  "隧道应用" : '门户应用')}}
              </span>
                <span v-else-if="item.value ==='app_status'">
                  {{appDetail[item.value] ==1 ? "启用" : (appDetail[item.value] == 2 ? "维护中":"禁用") }}
              </span>
                <div v-else>
                  <div style="margin-top: 13px;float: left;width: 6px;height: 6px;border-radius: 10px"
                       :style="appDetail[item.value] === 'web'?'background: #00A870':'background: #9F9F9F'"/>
                  <div style="float: left;margin-left: 10px">{{ appDetail[item.value] === 'web' ? '开启' : '关闭' }}</div>
                </div>
              </div>
              <div v-else-if="item.value === 'server_address'">
                {{ appDetail[item.value] ? appDetail.server_schema + '://' + appDetail[item.value] : '-' }}
              </div>
              <div v-else-if="item.value === 'publish_address'">
                {{ appDetail[item.value] ? appDetail.publish_schema + '://' + appDetail[item.value] : '-' }}
              </div>
              <div v-else-if="item.value === 'default_rule'" style="color: #252631;word-wrap: break-word;">
                <div style="margin-top: 13px;float: left;width: 6px;height: 6px;border-radius: 10px"
                     :style="appDetail.web_compatible_config.default_rule.includes('url_smart_rewrite') ?'background: #00A870':'background: #9F9F9F'"/>
                <div style="float: left;margin-left: 10px">
                  {{ appDetail.web_compatible_config.default_rule.includes('url_smart_rewrite') ? '开启' : '关闭' }}
                </div>
              </div>
            <div v-else-if="item.value === 'url_manual_rewrite'" style="color: #252631;word-wrap: break-word;">
              <div style="margin-top: 13px;float: left;width: 6px;height: 6px;border-radius: 10px"
                   :style="appDetail.web_compatible_config.default_rule.includes('url_manual_rewrite') ?'background: #00A870':'background: #9F9F9F'"/>
              <div style="float: left;margin-left: 10px">
                {{ appDetail.web_compatible_config.default_rule.includes('url_manual_rewrite') ? '开启' : '关闭' }}
              </div>
            </div>
            <div v-else-if="item.value === 'url_control'" style="color: #252631;word-wrap: break-word;">
              {{ appDetail.web_compatible_config.url_control.text ? appDetail.web_compatible_config.url_control.text : '-' }}

            </div>
            <div v-else-if="item.value === 'depend_site'" style="color: #252631;word-wrap: break-word;">
              <div style="margin-top: 13px;float: left;width: 6px;height: 6px;border-radius: 10px"
                   :style="appDetail.web_compatible_config.default_rule.includes('depend_site') ?'background: #00A870':'background: #9F9F9F'"/>
              <div style="float: left;margin-left: 10px">
                {{ appDetail.web_compatible_config.default_rule.includes('depend_site') ? '开启' : '关闭' }}
              </div>
            </div>

              <div v-else-if="item.value === 'header_config'" style="color: #252631;word-wrap: break-word;">
                <div
                    v-for="(h,index) in appDetail?.web_compatible_config?.header_config"
                    :key="index"
                    :style="{ 'margin-bottom': index !== appDetail?.web_compatible_config?.header_config.length - 1 ? '10px': '' }"
                >
                  <t-tag style="background: #f4f4f4">
                    {{ h.field === 'request_header' ? '请求头' : '响应头' }}，{{ 
                      h.operation === 'add' ? '添加' : 
                      h.operation === 'set' ? '修改' : 
                      h.operation === 'update' ? '更新' : 
                      h.operation === 'remove' ? '删除' : '未知操作'
                    }}，{{ h.key || '-' }}{{ h.operation !== 'remove' ? '，' + (h.value || '-') : '' }}
                  </t-tag>
                </div>
              </div>
              <div v-else-if="item.value === 'bind_se'" style="color: #252631;word-wrap: break-word;">
                {{ appDetail?.bind_se?.map(sdp => sdp.appliance_name).join('，')  ? appDetail?.bind_se?.map(sdp => sdp.appliance_name).join('，')  : '-'}}
              </div>
              <div v-else-if="item.value === 'group_relationship'" style="color: #252631;word-wrap: break-word;">
                <div v-if="appDetail?.group_relationship?.length > 0">
                  <t-tag v-for="(group,index) in appDetail?.group_relationship" :key="index"
                         style="margin-right: 12px;background: #F4F4F4">
                    {{ group.name }}
                  </t-tag>
                </div>
                <div v-else>-</div>
              </div>
              <div v-else-if="item.value === 'app_addresses'" style="color: #252631;word-wrap: break-word;">
                <t-tag v-for="(add,index) in appDetail?.app_addresses" :key="index"
                       style="margin-right: 12px;margin-bottom: 8px;background: #F4F4F4">
                  {{ `${add.protocol}:${add.address}:${add.port}` }}
                </t-tag>
              </div>
              <div v-else-if="item.value === 'port'" style="color: #252631;word-wrap: break-word;">
                <div>TCP: {{ appDetail.tcp_port || '-' }}</div>
                <div>UDP: {{ appDetail.udp_port || '-' }}</div>
              </div>
              <div v-else-if="item.value === 'single_sign_on' ">

                <div style="margin-top: 13px;float: left;width: 6px;height: 6px;border-radius: 10px"
                     :style="appDetail.web_compatible_config.default_rule.includes('single_sign_on') ?'background: #00A870':'background: #9F9F9F'"/>
                <div style="float: left;margin-left: 10px">{{ appDetail.web_compatible_config.default_rule.includes("single_sign_on")  ? '开启' : '关闭' }}</div>

              </div>
            <div v-else-if="item.value === 'health_config' ">

              <div style="margin-top: 13px;float: left;width: 6px;height: 6px;border-radius: 10px"
                   :style="appDetail.health_config.enable === '1' ?'background: #00A870':'background: #9F9F9F'"/>
              <div style="float: left;margin-left: 10px">{{ appDetail.health_config.enable === '1'  ? '开启' : '关闭' }}</div>

            </div>
              <div v-else style="color: #252631;word-wrap: break-word;">{{ appDetail[item.value] || '-' }}</div>
          </el-form-item>
        </el-form>
      </el-card>
      <template #footer>
        <t-button variant="outline" style="width: 80px" @click="visible = false"> 取消</t-button>
      </template>
    </t-drawer>

    <!--    新增编辑抽屉-->
    <t-drawer v-model:visible="opvisible" size="600" destroy-on-close :close-btn="true">
      <template #header>
        <div style="font-size: 14px;font-width: 500;">{{ formData.id === '' ? '新增应用' : '编辑应用' }}</div>
      </template>
      <t-space direction="vertical" style="width: calc(100% - 16px);margin-left: 9px">

        <AppDrawer
            @refreshGroup="getGroupList"
            :key="componentKey"
            :sdp-options="sdpOptions"
            :api-data="formData" ref="appDraw"
            :group-options="groupOptions[0]"
            :add-app-type="addAppType"
        ></AppDrawer>
      </t-space>
      <template #footer>
        <t-button class="asdc-primary-but"  :disabled="submitting" style="float: right;width: 80px" @click="onSubmit">确定</t-button>
        <t-button
            style="margin-right: 5px;background: #ffffff;float: right;width: 125px;height: 32px;border: 1px solid #0052D9;color: #0052D9;border-radius: 4px"
            :disabled="submitting"
            @click="onSubmit('skip')"
        >保存并添加策略
        </t-button>
        <t-button variant="outline" style="width: 80px;" @click.stop="opvisible = false"> 取消</t-button>
      </template>
    </t-drawer>
  </div>
</template>

<script>
export default {
  name: 'ApplicationManagement'
}
</script>
<script setup>
import {h, reactive, ref, watch, computed, onMounted, nextTick} from 'vue'
import { AddIcon} from 'tdesign-icons-vue-next'
import { Search } from '@element-plus/icons-vue'
import {useRouter, useRoute} from 'vue-router'
import {DialogPlugin} from 'tdesign-vue-next'
import useClipboard from "vue-clipboard3";
import {getIdpList} from "@/api/auth";
import AppDrawer from "./appDrawer.vue";
import { downLoadXls } from '@/utils/excel'
import {
  detailscolumnsconfig,
  tableColumnsConfig,
  formColumnsConfig,
  webformColumnsConfig
} from './applicationConfig'
import AppAsideMenu from '@/components/asideMenu/appAsideMenu.vue'
import {
  addApp,
  getApplicationList,
  getResourceGroup,
  upApp,
  getApp,
  delApp,
  createGroup,
  updateGroup,
  delGroup,
  exportApp as appExport,
  MoveApplicationGroup,
  UpdateGroupSort
} from '@/api/resource'
import {ElMessage} from 'element-plus'
import {getAgentsList} from '@/api/agents'
import {MessagePlugin} from 'tdesign-vue-next'
import { isEditVal, isViewVal } from '@/utils/limite'
const route = useRoute()
const isEdit = isEditVal(route.meta.title)
const router = useRouter()
const application_tabs = ref(1)
const opvisible = ref(false)
const search = ref('')
const search_select = ref('all')
const application = ref('all')
let formData = reactive({
})
const componentKey = ref(0)
const sort = ref({
  sortBy: 'status',
  descending: true,
})

const isAction = ref(true)
const shrink_icon = ref('#icon-heiseshang')
const upshrink = () => {
  isAction.value = !isAction.value
  shrink_icon.value = isAction.value ? '#icon-heisexia' : '#icon-heiseshang'
}

const handleMouseEnter = () => {
  if (isAction.value) {
    shrink_icon.value = '#icon-jiantouxia'
  } else {
    shrink_icon.value = '#icon-jiantoushang'
  }
}



const handleMouseLeave = () => {
  if (isAction.value) {
    shrink_icon.value = '#icon-heisexia'
  } else {
    shrink_icon.value = '#icon-heiseshang'
  }
}

const loading = ref(false)
const data = ref([])
const selectedRowKeys = ref([])
const columns = tableColumnsConfig()
const details_columns = ref([])
const form_config = ref([])

const total = ref(0)
const pagination = reactive({
  current: 1,
  pageSize: 50,
  total: total,
  pageSizeOptions: [50, 100, 150, 200],
  paginationAffixedBottom: true,
  showJumper: true,
  totalContent: () => {
    return [
      h('div', {id: 'foo', class: 't-pagination__total', innerHTML: `共${total.value}条`}, []),
      h('button', {
        id: 'delete',
        class: [(!isEdit || selectedRowKeys.value.length < 1) ? 't-pagination__total_delete_disabled' : 't-pagination__total_delete'],
        disabled: (!isEdit || selectedRowKeys.value.length < 1) ? 'disabled' : null,
        innerHTML: `批量删除`,
        onClick: () => delData('')
      }, []),
    ]
  }
})



const onPageChange = (pageInfo, context) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
  getDataList('next')
}

const onSelectChange = (value) => {
  selectedRowKeys.value = value
}

const selectGroupId = ref(0)
const getDataList = async (type) => {
  if (type !== 'next') {
    pagination.current = 1
    pagination.pageSize = 50
  }
  loading.value = true
  const query = {
    limit: pagination.pageSize,
    offset: (pagination.current - 1) * pagination.pageSize,
    search: search.value.trim(),
    group_id: Number(selectGroupId.value)
  }
  if (application.value !== 'all') {
    query.app_type = application.value
  }
  if (search_select.value !== 'all') {
    query.search_columns = [search_select.value]
  }
  const res = await getApplicationList(query)
  if (res.status === 200 && res.data.code !== -1) {
    data.value = res.data.data.data || []
    total.value = res.data.data.total_num || 0
    loading.value = false
  } else {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
    loading.value = false
  }
}

getDataList()

const setInitialFormData = () => {
  formData= {}
  formData.id = ''
  addAppType.value = ''
}

// 获取标签列表
const groupOptions = ref([])
const policyGroupData = computed(() => {
  return groupOptions.value?.[0]?.child_group?.filter(item => 
    item.name !== '全部标签' && item.name !== '默认分类'
  ) || []
})

const getGroupList = async (name) => {
  const res = await getResourceGroup({need_default: true, name: name})
  if (res.status === 200) {
    groupOptions.value = res.data.data
    console.log('分组数据已更新:', groupOptions.value)
  } else {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
  }
}

// 监听 groupOptions 的变化
watch(groupOptions, (newVal) => {
  console.log('groupOptions updated:', newVal)
  // 当 groupOptions 更新时，policyGroupData 会自动更新
}, { deep: true })

// 组件挂载时获取数据
onMounted(() => {
  getGroupList()
})

const addGroup = async (value) => {
  if (!value) {
    ElMessage({
      customClass: 'hint',
      type: 'error',
      message: '标签名称不能为空',
    })
    return true
  }
  const res = await createGroup({
    group_name: value.replace(/(^\s*)|(\s*$)/g, ''),
  })
  if (res.status === 200 && res.data.code !== -1) {
    await getGroupList()
    return false
  } else {
    ElMessage({
      customClass: 'hint',
      type: 'error',
      message: res.data.msg,
    })
    return true
  }
}

const editGroup = async (value, row) => {
  const res = await updateGroup({
    id: row.id,
    group_name: value.replace(/(^\s*)|(\s*$)/g, ''),
  })
  if (res.status === 200 && res.data.code !== -1) {
    await getGroupList()
    return false
  } else {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
    return true
  }
}

const a = ref(1)
const deleGroup = async (value, row) => {
  a.value++
  const res = await delGroup({
    id: row.id,
  })
  if (res.status === 200 && res.data.code !== -1) {
    await getGroupList()
    return false
  } else {
    await MessagePlugin.error(res.data.msg)
    return true
  }
}

// applicationManagement.vue
const handleMoveGroup = async ( position, groupId, targetId ) => {
  try {
    // 参数转换映射
    const params = {
      group_id: Number(groupId),          // 接口要求的参数名
      target_id: Number(targetId),  // 接口要求的参数名
      position: position // 根据接口文档映射值
    }

    const res = await MoveApplicationGroup(params)

    if (res.data.code === 0) {
      await MessagePlugin.success('移动成功')
      await getGroupList()
      await getDataList()
    } else {
      throw new Error(res.data.msg)
    }
  } catch (err) {
    await MessagePlugin.error(err.message || '移动失败')
    throw err
  }
}

const updateGroupSort = async (groups) => {
  try {
    const res = await UpdateGroupSort({ groups })
    if (res.data.code === 0) {
      getGroupList()
    } else {
      throw new Error(res.data.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存分组排序失败:', error)
    MessagePlugin.error('保存分组排序失败')
    throw error
  }
}
// 在打开移动对话框时处理参数

const selectGroup = (value) => {
  pagination.current = 1
  pagination.pageSize = 50
  selectGroupId.value = value.node.value
  getDataList()
}

// 获取网关列表
const getSDPList = async () => {
  const query = {
    limit: 500,
    offset: 0,
  }
  const res = await getAgentsList(query)
  if (res.data.code === 0) {
    return res.data?.data?.rows
  }
  return []
}

const exportApp = async () => {
  const query = {
    limit: total.value,
    offset: 0,
    search: search.value.trim(),
    group_id: Number(selectGroupId.value)
  }
  if (application.value !== 'all') {
    query.app_type = application.value
  }
  if (search_select.value !== 'all') {
    query.search_columns = [search_select.value]
  }

  const res = await appExport(query)
  downLoadXls(res)
}

const sdpOptions = ref([])

// 新增应用下拉选项配置
const addAppOptions = ref([
  {
    content: '门户应用',
    value: 'navigation',
    onClick: () => formDataInitNavigation()
  }
])

const addAppType = ref('')

const formDataInit = async (type = "") => {

  opvisible.value = false
  setInitialFormData()
  if (type !== ""){
    formData.app_type = type
    addAppType.value = type
  }
  await get_idp_list()
  // 获取网关列表
  sdpOptions.value = await getSDPList()
  if (selectGroupId.value > 1) {
    formData.group_ids = [selectGroupId.value]
  }
  form_config.value = formData.web_access ? webformColumnsConfig(sdpOptions, groupOptions.value[0].child_group, idp_list?.value?.assistIdpList) : formColumnsConfig(sdpOptions, groupOptions.value[0].child_group)
  opvisible.value = true

}

// 新增导航应用初始化函数
const formDataInitNavigation = async () => {
  await formDataInit("portal")
}

// 下拉按钮点击处理函数
const handleDropdownButtonClick = () => {
  // 延迟执行样式修复，确保下拉菜单已经显示
  setTimeout(() => {
    fixTDesignDropdownStyles()
  }, 50)
}

// TDesign Dropdown点击处理函数
const handleAddApp = (data) => {
  console.log('下拉选择:', data.value)

  if (data.value === 'navigation') {
    formDataInitNavigation()
  }
}

// 修复TDesign下拉菜单宽度的函数
const fixTDesignDropdownStyles = () => {
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    const button = document.querySelector('.unified-button')
    const dropdown = document.querySelector('.t-popup.unified-dropdown-menu')
    const popupContent = dropdown?.querySelector('.t-popup__content')
    const dropdownMenu = dropdown?.querySelector('.t-dropdown__menu')
    const menuWrapper = dropdownMenu?.querySelector('div')
    const item = document.querySelector('.t-dropdown__item')

    if (button && dropdown && item) {
      const buttonWidth = button.offsetWidth // 获取按钮实际宽度

      // 设置所有相关容器的宽度为按钮宽度
      dropdown.style.width = buttonWidth + 'px'
      dropdown.style.minWidth = buttonWidth + 'px'
      dropdown.style.maxWidth = buttonWidth + 'px'

      // 设置 .t-popup__content 的宽度
      if (popupContent) {
        popupContent.style.width = buttonWidth + 'px'
        popupContent.style.minWidth = buttonWidth + 'px'
        popupContent.style.maxWidth = buttonWidth + 'px'
      }

      // 设置 .t-dropdown__menu 的宽度
      if (dropdownMenu) {
        dropdownMenu.style.width = buttonWidth + 'px'
        dropdownMenu.style.minWidth = buttonWidth + 'px'
        dropdownMenu.style.maxWidth = buttonWidth + 'px'
      }

      // 设置菜单包装div的宽度
      if (menuWrapper) {
        menuWrapper.style.width = buttonWidth + 'px'
        menuWrapper.style.minWidth = buttonWidth + 'px'
        menuWrapper.style.maxWidth = buttonWidth + 'px'
      }

      // 设置菜单项的宽度
      item.style.width = buttonWidth + 'px'
      item.style.minWidth = buttonWidth + 'px'
      item.style.maxWidth = buttonWidth + 'px'
      item.style.boxSizing = 'border-box'

      console.log('TDesign下拉菜单宽度已修复:', {
        buttonWidth,
        dropdownWidth: dropdown.offsetWidth,
        popupContentWidth: popupContent?.offsetWidth,
        dropdownMenuWidth: dropdownMenu?.offsetWidth,
        menuWrapperWidth: menuWrapper?.offsetWidth,
        itemWidth: item.offsetWidth
      })
    }
  })
}


const appDraw = ref()
const submitting = ref(false)
const onSubmit = async (type) => {
  if (submitting.value) return // 防止重复点击
  try {
    submitting.value = true
    // 调用子组件的 validate 方法
    const isSubValid = await appDraw.value?.validate()
    if (isSubValid !== true) {
      console.log('数据校验失败')
      return false
    }
    // 获取子组件中的 formData
    const childFormData = appDraw.value.getDrawerData()
    console.log(childFormData)

    childFormData.sdp_list = [childFormData.sdp]

    let res = ''
    res = formData.id ? await upApp(childFormData) : await addApp(childFormData)
    if (res.data.code !== 0) {
      await MessagePlugin.error(res.data.msg)
      return
    }
    await MessagePlugin.info(formData.id ? '修改成功!' : '新增成功')
    if (type === 'skip') {
      await router.push({ name: 'realTimeRules' })
      return
    }
    opvisible.value = false
    await getGroupList()
    await getDataList()
  } catch (error) {
    console.error(error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

const idp_list = ref([])
const get_idp_list = async(id) => {
  const res = await getIdpList({ rootGroupId: id })
  if (res?.status === 200) {
    idp_list.value = res.data
  }
}

const upData = async (row) => {
  opvisible.value  = false
  componentKey.value += 1
  setInitialFormData()
  await get_idp_list()
  sdpOptions.value = await getSDPList()

  const res = await getApp({id: row.id})
  if (res.data.code !== 0) {
    await MessagePlugin.error(res.data.msg)
    return
  }
  formData = res.data.data
  formData.id = row.id.toString()
  if (formData.app_type === 'portal'){
    formData.sdp_list = []//网关回显
    addAppType.value = 'portal'
  }else{
    addAppType.value = ''
    formData.sdp_list = formData.bind_se.map(obj => obj.appliance_id) //网关回显
  }

  formData.group_ids = formData.group_relationship?.map(obj => obj.id)
  formData.isPath = formData.uri !== ''
  if (formData.app_type === 'web'){
    formData.default_rule = formData.web_compatible_config.default_rule.includes('url_smart_rewrite')
    formData.manual_rule = formData.web_compatible_config.default_rule.includes('url_manual_rewrite')
    formData.depend_site = formData.web_compatible_config.default_rule.includes('depend_site')
    formData.isPath = formData.web_compatible_config.default_rule.includes('uri')
    if (!formData.isPath){
      formData.uri = '/*'
    }
    const weburl = formData.publish_schema + '://' + formData.publish_address
    formData.web_url_web = formData.web_url.replace(weburl, '')
    // 处理表单代填启用标志回显
    if (formData.web_compatible_config.default_rule.includes('single_sign_on') && 
        formData.web_compatible_config.single_sign_on.type === 'fill_forms') {
      formData.form_fill_enabled = 1
    } else {
      formData.form_fill_enabled = 0
    }
    
    // 处理证书ID回显
    if (formData.certificate_id) {
      // 确保certificate_id是字符串格式，与前端select组件的value类型匹配
      formData.certificate_id = formData.certificate_id.toString()
    }
  }
  opvisible.value = true
}

const delData = async (ids, names) => {
  let name = ''
  if (!ids) {
    let nameValue = []
    ids = selectedRowKeys.value
    data.value.forEach(item => {
      if (selectedRowKeys.value.includes(item.id)) {
        nameValue.push(item.name)
      }
    })
    name = nameValue.join(',')
  } else {
    name = names
  }
  const query = {
    ids: ids,
    name: name
  }

  const confirmDialog = DialogPlugin.confirm({
    header: '删除应用',
    theme: 'warning',
    body: '删除应用后将无法继续访问该应用，是否继续删除？',
    className: 'dle',
    confirmBtn: {
      content: '确定',
      theme: 'primary',
      loading: false,
    },
    onConfirm: async () => {
      const res = await delApp(query)
      if (res.data.code === 0) {
        await MessagePlugin.info('删除成功！')
        selectedRowKeys.value = []
        await getGroupList()
        await getDataList()
        confirmDialog.hide()
      } else {
        await MessagePlugin.error(res.data.msg)
      }
    },
  })
}
const visible = ref(false)
const appDetail = ref({})
const getAppDetails = async (id) => {
  const res = await getApp({id: id})
  if (res.data.code !== 0) {
    await MessagePlugin.error('获取信息失败！')
  } else {
    appDetail.value = res.data.data
    visible.value = true
    details_columns.value = detailscolumnsconfig(res.data.data.app_type, res.data.data?.web_compatible_config?.default_rule?.[0] === 'url_smart_rewrite')
  }
}

const {toClipboard} = useClipboard()
const copy = async (value) => {
  if (!value) {
    ElMessage.error({
      message: '卸载密码未生成，请重试！',
    })
    return
  }
  try {
    await toClipboard(value.toString())
    ElMessage.success({
      message: '复制成功',
    })
  } catch (e) {
    ElMessage.error({
      message: '复制失败请重试',
    })
  }
}

const showAside = ref(true)
// 侧边栏折叠
const flodClick = () => {
  showAside.value = false
}

watch(() => formData.web_access, () => {
  get_idp_list()
  form_config.value = formData.web_access ? webformColumnsConfig(sdpOptions, groupOptions.value[0]?.child_group, idp_list?.value?.assistIdpList) : formColumnsConfig(sdpOptions, groupOptions.value[0]?.child_group)
}, {immediate: true})
</script>
<style lang="scss" scoped>
.application {
  padding: 0px 0px !important;

  .top-title {
    height: 222px;
    width: 100%;
    margin-bottom: 10px;
    background: #ffffff;
  }
  .el-form-item{
   margin-bottom: 9px;
  }
  .top-title-d {
    margin-bottom: 12px;
  }

  .t-tabs {
    .t-tabs__content {
      overflow: visible;
    }
  }

  .content {
    margin: 0px 12px;
    border-radius: 4px;
    border: 1px solid #EBEBEB;
    overflow: initial;
    height: calc(100vh - 330px);

    .shrink {
      position: absolute;
      font-size: 10px;
      z-index: 999;
      width: 28px;
      height: 28px;
      border-radius: 100px;
      border: 1px solid #EBEBEB;
      left: 49%;
      line-height: 24px;
      background: #ffffff;
      top: 202px;
      text-align: center;

      .icon {
        vertical-align: -0.2em
      }
    }

    .shrink:hover {
      color: var(--main-color-theme);
      z-index: 9;
      border: 1px solid var(--main-color-theme);
      background-color: var(--main-color-theme);
    }

    .icon-zhengshuguanli:hover {
      color: #0052D9;
    }
  }

  .operation {
    display: block;
    text-align: center;
    float: left;
    line-height: 32px;
  }

  .operation:hover {
    background: #EBEBEB;
  }

  .more {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    width: 20px;
    height: 32px;
    border-radius: 4px;
  }

  .more::after {
    content: "...";
    position: absolute;
    right: 20%;
    transform: translateY(-10%);
  }

  .t-popup__content {
    font-size: 12px;
  }
}

.icon-xinzeng {
  font-size: 10px;
  margin-right: 4px;
}

.hint {
  z-index: 9999 !important;
}

.copy {
  svg {
    display: none;
  }
}

.copy:hover svg {
  top: -5px;
  display: block;
}

.t-form-item__web_access {
  margin-bottom: 5px !important;
}

/* 新增应用统一下拉按钮样式 - 修复垂直对齐 */
.add-app-unified {
  display: inline-block;
  position: relative;
  margin-right: 12px; /* 添加右边距，与其他按钮保持间距 */
  vertical-align: middle; /* 确保垂直对齐 */
  height: 32px; /* 固定高度与其他按钮一致 */
  line-height: 32px; /* 行高对齐 */
}

/* 修复TDesign dropdown的垂直对齐 */
.add-app-unified .t-dropdown {
  display: inline-block !important;
  vertical-align: middle !important;
  height: 32px !important;
  line-height: 32px !important;
}

.unified-dropdown {
  display: inline-block;
}

.unified-button {
  display: flex;
  align-items: center;
  background: #0052d9;
  border: 1px solid #0052d9;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  height: 32px; /* 与TDesign按钮高度保持一致 */
  overflow: hidden;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
  box-sizing: border-box; /* 确保边框计算正确 */
}

.unified-button:hover {
  background: #1a5cd9;
  border-color: #1a5cd9;
  box-shadow: 0 2px 8px rgba(0, 82, 217, 0.3);
}

.main-section {
  display: flex;
  align-items: center;
  padding: 0 4px 0 8px;
  flex: 1;
  height: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.main-section:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.add-icon {
  font-size: 14px;
  margin-right: 6px;
}

.button-text {
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
}

/* 完全移除分隔线 */
.divider-line {
  display: none;
}

.dropdown-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  height: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-width: 24px;
  width: 24px;
}

.dropdown-section:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.dropdown-icon {
  font-size: 10px;
  color: white;
}

/* 确保下拉菜单正确显示 */
.unified-dropdown .t-dropdown__trigger {
  display: inline-block;
  width: 100%;
}


</style>
