#!/usr/bin/env lua

-- response-rewrite 插件测试脚本
-- 用于测试 JSON 配置文件中的测试用例

local json = require("cjson")
local io = require("io")

-- 模拟实际插件的智能替换功能(asec-response-rewrite.lua插件里的函数)
local function smart_port_aware_replace(body, filter)
    local regex = filter.regex
    local replace = filter.replace
    local scope = filter.scope or "once"
    
    -- 检查是否是带转义符的URL（如 https:\/\/）
    local has_https = regex:find("https?:") ~= nil
    local is_escaped_url = has_https and regex:find(":\\/\\/") ~= nil
    
    -- 检查是否是标准URL（如 https://）
    local is_standard_url = has_https and regex:find("://") ~= nil and regex:find(":\\/\\/") == nil
    
    -- 如果既不是转义符URL也不是标准URL，使用基本逻辑
    if not is_escaped_url and not is_standard_url then
        -- 基本正则表达式替换
        local ok, result = pcall(function()
            return body:gsub(regex, replace)
        end)
        if ok then
            return result
        else
            return body
        end
    end
    
    -- URL 替换逻辑
    local result = body
    local pos = 1
    local replacements = 0
    local max_replacements = (scope == "once") and 1 or math.huge
    
    while pos <= #result and replacements < max_replacements do
        local start_pos, end_pos = result:find(regex, pos, true)  -- 精确文本匹配
        if not start_pos then
            break
        end
        
        -- 对于转义符URL，需要检查是否会导致端口重复
        if is_escaped_url then
            -- 检查匹配后面是否有端口号
            local next_char_pos = end_pos + 1
            local should_replace = true
            local replace_end_pos = end_pos
            
            if next_char_pos <= #result and result:sub(next_char_pos, next_char_pos) == ":" then
                -- 后面有冒号，检查是否是端口号
                local port_match_end = result:find("[^0-9]", next_char_pos + 1)
                if not port_match_end then
                    port_match_end = #result + 1
                end
                local port_str = result:sub(next_char_pos + 1, port_match_end - 1)
                
                if port_str and port_str:match("^%d+$") then
                    local port_num = tonumber(port_str)
                    -- 确定协议默认端口
                    local default_port = 80
                    if regex:match("^https") then
                        default_port = 443
                    end
                    
                    -- 原文有端口号，检查是否是默认端口
                    if port_num == default_port then
                        -- 是默认端口，可以替换，并且要包含端口部分
                        replace_end_pos = port_match_end - 1
                        should_replace = true
                    elseif replace:match(":%d+") then
                        -- 非默认端口，且替换字符串也有端口，避免重复，不进行替换
                        should_replace = false
                    end
                end
            end
            
            if should_replace then
                result = result:sub(1, start_pos - 1) .. replace .. result:sub(replace_end_pos + 1)
                pos = start_pos + #replace
                replacements = replacements + 1
            else
                pos = end_pos + 1
            end
        else
            -- 标准URL的智能替换逻辑
            local next_char_pos = end_pos + 1
            local should_replace = true
            
            -- 如果匹配后面还有字符，检查是否构成完整URL
            if next_char_pos <= #result then
                local next_char = result:sub(next_char_pos, next_char_pos)
                
                if next_char == ":" then
                    -- 后面有冒号，检查端口号
                    local port_match_end = result:find("[^0-9]", next_char_pos + 1)
                    if not port_match_end then
                        port_match_end = #result + 1
                    end
                    local port_str = result:sub(next_char_pos + 1, port_match_end - 1)
                    
                    if port_str and port_str:match("^%d+$") then
                        local port_num = tonumber(port_str)
                        -- 检查是否是配置中指定的端口
                        local config_port = regex:match(":(%d+)$")
                        if config_port then
                            local config_port_num = tonumber(config_port)
                            if port_num == config_port_num then
                                -- 端口完全匹配，正常替换
                                should_replace = true
                                replace_end_pos = end_pos
                            else
                                -- 端口不匹配，跳过替换
                                should_replace = false
                            end
                        else
                            -- 配置没有端口，但文本有端口
                            if port_num == default_port then
                                -- 默认端口，一起替换掉
                                replace_end_pos = port_match_end - 1
                            else
                                -- 非默认端口，跳过替换
                                should_replace = false
                            end
                        end
                    else
                        -- 冒号后面不是纯数字，跳过替换
                        should_replace = false
                    end
                elseif next_char:match("[%w%.%-]") then
                    -- 后面是字母、数字、点或连字符，说明是部分匹配，跳过
                    should_replace = false
                else
                    -- 后面是其他字符（如/、空格、引号等），正常替换
                    should_replace = true
                end
            else
                -- 匹配到了字符串结尾，正常替换
                should_replace = true
            end
            
            if should_replace then
                result = result:sub(1, start_pos - 1) .. replace .. result:sub(end_pos + 1)
                pos = start_pos + #replace
                replacements = replacements + 1
            else
                pos = end_pos + 1
            end
        end
    end
    
    return result
end

-- 模拟实际插件的头部 URL 替换功能
local function smart_header_url_replace(header_value, origin_url, replace_url)
    -- 检查是否是转义符URL（如 https:\/\/）
    local has_https = origin_url:find("https?:") ~= nil
    local is_escaped_url = has_https and origin_url:find(":\\/\\/") ~= nil
    -- 检查是否是标准URL（如 https://）
    local is_standard_url = has_https and origin_url:find("://") ~= nil and origin_url:find(":\\/\\/") == nil
    -- 检查是否是URL编码的URL（如 https%3A%2F%2F）
    local is_encoded_url = origin_url:find("https?%%3A%%2F%%2F") ~= nil
    
    -- 如果不是URL类型，使用原有逻辑
    if not is_escaped_url and not is_standard_url and not is_encoded_url then
        local ok, result = pcall(function()
            return header_value:gsub(origin_url, replace_url)
        end)
        if ok then
            return result
        else
            return header_value
        end
    end
    
    -- 确定协议默认端口
    local default_port = 80
    if origin_url:match("^https") then
        default_port = 443
    end

    local result = header_value
    local pos = 1
    
    while true do
        local start_pos, end_pos = result:find(origin_url, pos, true)  -- 精确文本匹配
        if not start_pos then
            break
        end
        
        local next_char_pos = end_pos + 1
        local should_replace = true
        local replace_end_pos = end_pos
        
        -- 检查前面的字符，确保是完整URL匹配
        if start_pos > 1 then
            local prev_char = result:sub(start_pos - 1, start_pos - 1)
            if prev_char:match("[%w%.%-]") then
                should_replace = false
            end
        end
        
        -- 对于URL编码的URL，需要特殊处理
        if should_replace and is_encoded_url then
            -- 检查匹配后面是否有编码的端口号（%3A表示冒号:）
            if next_char_pos <= #result and result:sub(next_char_pos, next_char_pos + 2) == "%3A" then
                -- 后面有编码的冒号，检查是否是端口号
                local port_start = next_char_pos + 3
                local port_end = result:find("%%2F", port_start) -- 查找 %2F (/)
                if not port_end then
                    port_end = result:find("[^0-9]", port_start)
                end
                if not port_end then
                    port_end = #result + 1
                end
                local port_str = result:sub(port_start, port_end - 1)
                
                if port_str and port_str:match("^%d+$") then
                    local port_num = tonumber(port_str)
                    
                    -- 检查替换URL是否已经包含端口（包括编码和非编码形式）
                    if replace_url:match(":%d+") or replace_url:match("%%3A%d+") then
                        -- 替换URL已经有端口，不应该替换，避免双端口
                        should_replace = false
                    end
                end
            end
        elseif should_replace and is_escaped_url then
            -- 检查匹配后面是否有端口号
            if next_char_pos <= #result and result:sub(next_char_pos, next_char_pos) == ":" then
                -- 后面有冒号，检查是否是端口号
                local port_match_end = result:find("[^0-9]", next_char_pos + 1)
                if not port_match_end then
                    port_match_end = #result + 1
                end
                local port_str = result:sub(next_char_pos + 1, port_match_end - 1)
                
                if port_str and port_str:match("^%d+$") then
                    local port_num = tonumber(port_str)
                    
                    -- 原文有端口号，检查是否是默认端口
                    if port_num == default_port then
                        -- 是默认端口，可以替换，并且要包含端口部分
                        replace_end_pos = port_match_end - 1
                        should_replace = true
                    elseif replace_url:match(":%d+") then
                        -- 非默认端口，且替换字符串也有端口，避免重复，不进行替换
                        should_replace = false
                    end
                end
            end
        elseif should_replace and is_standard_url then
            -- 标准URL的智能替换逻辑
            if next_char_pos <= #result and result:sub(next_char_pos, next_char_pos) == ":" then
                -- 后面跟着冒号，可能是端口，需要谨慎处理
                local port_end = result:find("[^0-9]", next_char_pos + 1)
                if not port_end then
                    port_end = #result + 1
                end
                local port_str = result:sub(next_char_pos + 1, port_end - 1)
                
                if port_str:match("^%d+$") then
                    -- 后面确实是端口号
                    local port_num = tonumber(port_str)
                    
                    if port_num == default_port then
                        -- 是默认端口，一起替换掉
                        replace_end_pos = port_end - 1
                    elseif port_num ~= default_port and replace_url:match(":%d+") then
                        -- 非默认端口，且替换字符串也有端口，跳过替换
                        should_replace = false
                    end
                end
            elseif next_char_pos <= #result and result:sub(next_char_pos, next_char_pos + 2) == "%3A" then
                -- 后面跟着URL编码的冒号，检查是否是端口号
                local port_start = next_char_pos + 3
                local port_end = result:find("%%2F", port_start) -- 查找 %2F (/)
                if not port_end then
                    port_end = result:find("[^0-9]", port_start)
                end
                if not port_end then
                    port_end = #result + 1
                end
                local port_str = result:sub(port_start, port_end - 1)
                
                if port_str and port_str:match("^%d+$") then
                    -- 后面确实是端口号
                    local port_num = tonumber(port_str)
                    
                    -- 检查替换URL是否已经包含端口（包括编码和非编码形式）
                    if replace_url:match(":%d+") or replace_url:match("%%3A%d+") then
                        -- 替换URL已经有端口，不应该替换，避免双端口
                        should_replace = false
                    end
                end
            else
                -- 检查后面的字符，确保是完整URL匹配
                if should_replace and next_char_pos <= #result then
                    local next_char = result:sub(next_char_pos, next_char_pos)
                    -- 如果后面是字母、数字、点、连字符，说明不是完整匹配
                    if next_char:match("[%w%.%-]") then
                        should_replace = false
                    end
                end
            end
        end
        
        if should_replace then
            result = result:sub(1, start_pos - 1) .. replace_url .. result:sub(replace_end_pos + 1)
            pos = start_pos + #replace_url
        else
            pos = end_pos + 1
        end
        
        if pos > #result then
            break
        end
    end
    
    return result
end

-- 运行单个测试用例
local function run_test_case(test_case, test_type)
    local success = true
    local error_msg = ""
    
    if test_type == "body" then
        local result = test_case.originbody
        
        if test_case.regex and test_case.replace then
            -- 单规则测试
            local filter = {
                regex = test_case.regex,
                replace = test_case.replace,
                scope = test_case.scope or "global"  -- 默认为 global 以匹配测试预期
            }
            result = smart_port_aware_replace(result, filter)
        elseif test_case.rewriterules then
            -- 多规则测试
            for _, rule in ipairs(test_case.rewriterules) do
                local filter = {
                    regex = rule.regex,
                    replace = rule.replace,
                    scope = rule.scope or "global"  -- 默认为 global 以匹配测试预期
                }
                result = smart_port_aware_replace(result, filter)
            end
        end
        
        if result ~= test_case.expect then
            success = false
            error_msg = string.format("Expected: %s, Got: %s", test_case.expect, result)
        end
        
    elseif test_type == "header" then
        local result = test_case.originheader
        
        if test_case.regex and test_case.replace then
            -- 单规则测试
            result = smart_header_url_replace(result, test_case.regex, test_case.replace)
        elseif test_case.rewriterules then
            -- 多规则测试
            for _, rule in ipairs(test_case.rewriterules) do
                result = smart_header_url_replace(result, rule.regex, rule.replace)
            end
        end
        
        if result ~= test_case.expect then
            success = false
            error_msg = string.format("Expected: %s, Got: %s", test_case.expect, result)
        end
    end
    
    return success, error_msg
end

-- 加载测试文件
local function load_test_file(filename)
    local file = io.open(filename, "r")
    if not file then
        print("Error: Cannot open file " .. filename)
        return nil
    end
    
    local content = file:read("*all")
    file:close()
    
    local ok, data = pcall(json.decode, content)
    if not ok then
        print("Error: Invalid JSON in file " .. filename)
        return nil
    end
    
    return data
end

-- 运行所有测试
local function run_tests(test_files)
    local total_tests = 0
    local passed_tests = 0
    
    for _, filename in ipairs(test_files) do
        print("=== 运行测试文件: " .. filename .. " ===")
        
        local test_data = load_test_file(filename)
        if not test_data then
            goto continue
        end
        
        -- 测试 body_tests
        if test_data.body_tests then
            print("\n--- Body Tests ---")
            for i, test_case in ipairs(test_data.body_tests) do
                total_tests = total_tests + 1
                local success, error_msg = run_test_case(test_case, "body")
                
                if success then
                    passed_tests = passed_tests + 1
                    print(string.format("✓ [%d] %s", i, test_case.name))
                else
                    print(string.format("✗ [%d] %s", i, test_case.name))
                    print("  " .. error_msg)
                end
            end
        end
        
        -- 测试 header_tests
        if test_data.header_tests then
            print("\n--- Header Tests ---")
            for i, test_case in ipairs(test_data.header_tests) do
                total_tests = total_tests + 1
                local success, error_msg = run_test_case(test_case, "header")
                
                if success then
                    passed_tests = passed_tests + 1
                    print(string.format("✓ [%d] %s (Header: %s)", i, test_case.name, test_case.header_key or "N/A"))
                else
                    print(string.format("✗ [%d] %s (Header: %s)", i, test_case.name, test_case.header_key or "N/A"))
                    print("  " .. error_msg)
                end
            end
        end
        
        ::continue::
    end
    
    print("\n=== 测试结果汇总 ===")
    print(string.format("总测试数: %d", total_tests))
    print(string.format("通过测试: %d", passed_tests))
    print(string.format("失败测试: %d", total_tests - passed_tests))
    print(string.format("通过率: %.2f%%", (passed_tests / total_tests) * 100))
    
    return passed_tests == total_tests
end

-- 主程序
local function main()
    local test_files = {
        "test_rules.json",
        "test_rules_enhanced.json"
    }
    
    print("Response-Rewrite 插件测试工具")
    print("==============================")
    
    local success = run_tests(test_files)
    
    if success then
        print("\n🎉 所有测试通过!")
        os.exit(0)
    else
        print("\n❌ 部分测试失败!")
        os.exit(1)
    end
end

-- 如果直接运行此脚本
if arg and arg[0] and arg[0]:match("test_response_rewrite%.lua$") then
    main()
end

-- 导出模块供其他脚本使用
return {
    smart_port_aware_replace = smart_port_aware_replace,
    smart_header_url_replace = smart_header_url_replace,
    run_test_case = run_test_case,
    run_tests = run_tests
}
