package repository

import (
	"asdsec.com/asec/platform/app/console/app/scan_stg/constants"
	"asdsec.com/asec/platform/app/console/app/scan_stg/dto"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/auth_model"
	"asdsec.com/asec/platform/pkg/model/scan_stg"
	"asdsec.com/asec/platform/pkg/pb_conf"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils"
	"asdsec.com/asec/platform/pkg/utils/slice"
	"encoding/json"
	"sort"
	"strings"

	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type ScanTaskRepository interface {
	AddTaskByStgAndDate(ctx context.Context, stgId, date string) error
	StopTaskByStgAndDate(ctx context.Context, stgId, date, taskId string) error
	CheckTaskByStgAndDate(ctx context.Context, stgId, date string) (bool, error)

	GetScanTaskList(ctx context.Context, req dto.GetScanTaskListReq) (dto.GetScanTaskListRsp, error)
	GetTaskResultDetail(ctx context.Context, id string) (dto.GetScanTaskDetailRsp, error)
	GetTaskResultUser(ctx context.Context, req dto.GetScanTaskUserReq) (dto.GetScanTaskUserRsp, error)
	GetCleanTaskList(ctx context.Context) ([]dto.CleanTask, error)
	DoTaskByStgAndDate(ctx context.Context, db *gorm.DB, stgId, date string) error
	DoDelTaskTx(db *gorm.DB, stgId, date string) error
}

// NewScanTaskRepository 创建接口实现接口实现
func NewScanTaskRepository() ScanTaskRepository {
	return &scanTaskRepository{}
}

type scanTaskRepository struct {
}

func (s scanTaskRepository) GetCleanTaskList(ctx context.Context) ([]dto.CleanTask, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	var res []dto.CleanTask
	err = db.Model(scan_stg.ScanTask{}).
		Select("task_id,task_date,strategy_id as stg_id,tb_scan_strategy.enable_all_user,user_ids,user_group_ids").
		Joins("LEFT JOIN tb_scan_strategy on tb_scan_task.strategy_id = tb_scan_strategy.id").
		Where("task_status in ?", []string{constants.ScanTaskProcess, constants.ScanTaskFileScanning}).
		Group("task_id,task_date,strategy_id,tb_scan_strategy.enable_all_user,user_ids,user_group_ids").Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (s scanTaskRepository) GetScanTaskList(ctx context.Context, req dto.GetScanTaskListReq) (dto.GetScanTaskListRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetScanTaskListRsp{}, err
	}
	pageReq := model.Pagination{Limit: req.Limit, Offset: req.Offset, Search: req.Search}
	if pageReq.Search != "" {
		pageReq.SearchColumns = []string{"tb_scan_strategy.strategy_name"}
	}
	if req.StartTime != "" && req.EndTime != "" {
		db = db.Where("tb_scan_task.start_time >= ? AND tb_scan_task.start_time <= ?", req.StartT, req.EndT)
	}
	db = db.Model(scan_stg.ScanTask{}).
		Select("task_id,task_date,tb_scan_strategy.strategy_name,tb_scan_strategy.id as strategy_id,MIN(tb_scan_task.start_time) as start_time,MAX(tb_scan_task.end_time) as end_time," +
			"SUM(l1_file_count) as l1_file_count,SUM(l2_file_count) as l2_file_count,SUM(l3_file_count) as l3_file_count,SUM(l4_file_count) as l4_file_count," +
			"SUM(l1_file_count)+SUM(l2_file_count)+SUM(l3_file_count)+SUM(l4_file_count) as sensitive_file_count," +
			"COUNT(DISTINCT CASE WHEN task_status IN ('process','file_scanning') THEN tb_scan_task.id END) AS process_task," +
			"COUNT(DISTINCT CASE WHEN task_status='finished' THEN tb_scan_task.id END) AS finish_task," +
			"COUNT(DISTINCT tb_scan_task.id) as all_task").
		Joins("INNER JOIN tb_scan_strategy on tb_scan_strategy.id = tb_scan_task.strategy_id").
		Group("task_id,task_date,tb_scan_strategy.strategy_name,tb_scan_strategy.id")
	var taskList []dto.ScanTask
	pageReq, err = model.Paginate(&taskList, &pageReq, db)
	if err != nil {
		return dto.GetScanTaskListRsp{}, err
	}
	sort.Slice(taskList, func(i, j int) bool {
		return strings.Compare(taskList[i].StartTime.String(), taskList[j].StartTime.String()) > 0
	})
	return dto.GetScanTaskListRsp{
		CommonPage: model.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page},
		ScanTask:   taskList,
	}, nil
}

func (s scanTaskRepository) GetTaskResultDetail(ctx context.Context, id string) (dto.GetScanTaskDetailRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetScanTaskDetailRsp{}, err
	}
	var res dto.GetScanTaskDetailRsp
	err = db.Model(scan_stg.ScanTask{}).
		Select("task_id,task_date,tb_scan_strategy.id as strategy_id,MIN(tb_scan_task.start_time) as start_time,MAX(tb_scan_task.end_time) as end_time,"+
			"SUM(l1_file_count) as l1_file_count,SUM(l2_file_count) as l2_file_count,SUM(l3_file_count) as l3_file_count,SUM(l4_file_count) as l4_file_count,"+
			"COUNT(DISTINCT CASE WHEN task_status='finished' THEN tb_scan_task.id END) AS finish_task,COUNT(DISTINCT tb_scan_task.id) as all_task,"+
			"jsonb_agg(distinct jsonb_build_object('user_id',tb_scan_task.user_id,'user_name',tb_scan_task.user_name)) as user_info_byte,scan_conf").
		Joins("LEFT JOIN tb_scan_strategy on tb_scan_strategy.id = tb_scan_task.strategy_id").
		Where("task_id = ?", id).
		Group("task_id,task_date,tb_scan_strategy.id,scan_conf").
		Find(&res).Error
	if len(res.UserInfoByte) > 0 {
		err = json.Unmarshal(res.UserInfoByte, &res.UserInfo)
		if err != nil {
			return dto.GetScanTaskDetailRsp{}, err
		}
	}
	var scanConf dto.TaskStgConf
	if res.ScanConf != "" {
		err = json.Unmarshal([]byte(res.ScanConf), &scanConf)
		if err != nil {
			return dto.GetScanTaskDetailRsp{}, err
		}
	}
	res.TaskStgConf = scanConf
	return res, nil
}

func (s scanTaskRepository) GetTaskResultUser(ctx context.Context, req dto.GetScanTaskUserReq) (dto.GetScanTaskUserRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetScanTaskUserRsp{}, err
	}
	pageReq := model.Pagination{Limit: req.Limit, Offset: req.Offset, Search: req.Search}
	if pageReq.Search != "" {
		pageReq.SearchColumns = []string{"tb_scan_task.user_name", "tb_scan_task.agent_name"}
	}
	if req.TaskStatus != "" {
		db = db.Where("task_status = ?", req.TaskStatus)
	}
	db = db.Model(scan_stg.ScanTask{}).
		Select("start_time,agent_name,user_name,task_status,l1_file_count,l2_file_count,l3_file_count,l4_file_count,scan_count,"+
			"l1_file_count,l2_file_count,l3_file_count,l4_file_count,l1_file_count+l2_file_count+l3_file_count+l4_file_count as sensitive_file_count").
		Where("task_id = ?", req.TaskId).Group("id").Order("start_time desc")
	var res []dto.UserTask
	pageReq, err = model.Paginate(&res, &pageReq, db)
	if err != nil {
		return dto.GetScanTaskUserRsp{}, err
	}
	return dto.GetScanTaskUserRsp{
		CommonPage: model.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page},
		UserTask:   res,
	}, nil
}

func (s scanTaskRepository) CheckTaskByStgAndDate(ctx context.Context, stgId, date string) (bool, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return false, err
	}
	var count int64
	err = db.Model(scan_stg.ScanTask{}).Where("strategy_id = ? AND task_date = ?", stgId, date).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s scanTaskRepository) AddTaskByStgAndDate(ctx context.Context, stgId, date string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return s.DoTaskByStgAndDate(ctx, db, stgId, date)
}

func (s scanTaskRepository) DoTaskByStgAndDate(ctx context.Context, db *gorm.DB, stgId, date string) error {
	var stg dto.TaskStgRsp
	err := db.Model(scan_stg.ScanStrategy{}).
		Select("tb_scan_strategy.*,"+
			"COALESCE (array_agg(distinct tb_file_type.name)filter(where tb_file_type.name is not null)) AS file_type").
		Where("tb_scan_strategy.id = ? AND strategy_status = ?", stgId, constants.DefaultStatus).
		Joins("LEFT JOIN tb_file_type ON tb_file_type.code = any(tb_scan_strategy.file_type_code)").
		Group("tb_scan_strategy.id").Limit(1).
		Find(&stg).Error
	if err != nil {
		return err
	}
	minFileSize, err := utils.ConvertSizeToString(fmt.Sprintf("%d%s", stg.MinFileSize, stg.MinFileUnit))
	if err != nil {
		return err
	}
	maxFileSize, err := utils.ConvertSizeToString(fmt.Sprintf("%d%s", stg.MaxFileSize, stg.MaxFileUnit))
	if err != nil {
		return err
	}
	if minFileSize == 0 {
		minFileSize = 1
	}
	strategyConf := dto.TaskStgConf{
		FileType:            stg.FileType,
		ScanPosition:        stg.ScanPosition,
		ScanExcludePosition: stg.ScanExcludePosition,
		EnableAllUser:       stg.EnableAllUser,
		FileSizeRange:       fmt.Sprintf("%d%s~%d%s", stg.MinFileSize, stg.MinFileUnit, stg.MaxFileSize, stg.MaxFileUnit),
	}
	strategyConfByte, err := json.Marshal(strategyConf)
	if err != nil {
		return err
	}
	taskId, _ := snowflake.Sf.GetId()
	// 下发扫描任务pb初始化
	taskReq := pb_conf.ScanTask{
		MinuteCountLimit: int32(stg.ScanFileCount),
		FileTypeCode:     stg.FileTypeCode,
		ScanMinFileSize:  minFileSize,
		ScanMaxFileSize:  maxFileSize,
		TaskEnable:       constants.DefaultStatus,
		TaskPriority:     constants.DefaultStatus,
		TaskId:           strconv.FormatUint(taskId, 10),
		DailyStartTime:   time.Now().Format("2006-01-02 15:04:05"),
		DailyEndTime:     fmt.Sprintf("%s %s", time.Now().Format("2006-01-02"), constants.ScanTaskEndTime),
		AllDayEnable:     true,
	}
	if taskReq.FileTypeCode == nil {
		taskReq.FileTypeCode = make([]int64, 0)
	}
	// 组装平台留存task
	taskList := make([]scan_stg.ScanTask, 0)
	userIds := make([]string, 0)
	userItems := make([]auth_model.TbUserEntity, 0)
	confGranularity := conf_center.NonGlobalConf
	if stg.EnableAllUser != constants.AllUserEnable {
		confGranularity = conf_center.NonGlobalConf
		tmpUserIds, err := commonApi.GetUserIdByGroups(ctx, stg.UserGroupIds)
		if err != nil {
			return err
		}
		userIds = append(userIds, tmpUserIds...)
		userIds = append(userIds, stg.UserIds...)
		userIds = slice.Unique(userIds)
	}
	if len(userIds) > 0 {
		db = db.Where("id in ?", userIds)
	}
	err = db.Model(auth_model.TbUserEntity{}).Find(&userItems).Error
	if err != nil {
		return err
	}
	for _, v := range userItems {
		id, _ := snowflake.Sf.GetId()
		name := v.DisplayName
		if v.DisplayName == "" {
			name = v.Name
		}
		taskList = append(taskList, scan_stg.ScanTask{
			Id:         strconv.FormatUint(id, 10),
			StrategyId: stgId,
			StartTime:  time.Now(),
			EndTime:    time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 22, 0, 0, 0, time.Now().Location()),
			TaskStatus: constants.ScanTaskProcess,
			TaskDate:   date,
			TaskId:     strconv.FormatUint(taskId, 10),
			UserId:     v.ID,
			UserName:   name,
			ScanConf:   string(strategyConfByte),
		})
	}

	// 判断扫描条件 空闲/充电
	for _, v := range stg.ScanCondition {
		switch v {
		case constants.IdleScanType:
			taskReq.IdleScanSwitch = true
			break
		case constants.ChargingScanType:
			taskReq.ChargingScanSwitch = true
			break
		}
	}
	// 判断扫描位置是否指定
	taskReq.ScanDirectory = make([]string, 0)
	taskReq.ScanExcludeDirectory = make([]string, 0)
	taskReq.ScanPositionType = constants.DefaultStatus
	if len(stg.ScanPosition) > 0 {
		taskReq.ScanDirectory = stg.ScanPosition
		taskReq.ScanPositionType = constants.ScanPositionType
	}
	if len(stg.ScanExcludePosition) > 0 {
		taskReq.ScanExcludeDirectory = stg.ScanExcludePosition
	}
	newDb, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	if len(taskList) > 0 {
		return newDb.Transaction(func(tx *gorm.DB) error {
			if len(taskList) > 0 {
				err = tx.Model(scan_stg.ScanTask{}).Create(&taskList).Error
				if err != nil {
					return err
				}
				err = commonApi.PushCommonAgentConf(
					tx,
					taskReq,
					constants.ScanTaskAgentType,
					fmt.Sprintf("%s%s", stgId, date),
					conf_center.AddConf,
					confGranularity,
					nil, stg.UserIds, stg.UserGroupIds, nil)
				if err != nil {
					return err
				}
			}
			return nil
		})
	}
	return nil
}

func (s scanTaskRepository) StopTaskByStgAndDate(ctx context.Context, stgId, date, taskId string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	var stg scan_stg.ScanStrategy
	err = db.Model(scan_stg.ScanStrategy{}).Where("id = ?", stgId, constants.DefaultStatus).Find(&stg).Error
	if err != nil {
		return err
	}
	taskReq := pb_conf.ScanTask{
		TaskEnable:           constants.ScanTaskDisable,
		ScanExcludeDirectory: make([]string, 0),
		ScanDirectory:        make([]string, 0),
		FileTypeCode:         make([]int64, 0),
	}

	confGranularity := conf_center.NonGlobalConf
	userIds := make([]string, 0)
	userGroupIds := make([]string, 0)
	if stg.EnableAllUser != constants.AllUserEnable {
		confGranularity = conf_center.NonGlobalConf
		userIds = append(userIds, stg.UserIds...)
		userGroupIds = append(userGroupIds, stg.UserGroupIds...)
	}
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(scan_stg.ScanTask{}).
			Where("task_id = ? AND task_status != ?", taskId, constants.ScanTaskSuccess).
			Updates(&scan_stg.ScanTask{TaskStatus: constants.ScanTaskStop}).Error
		if err != nil {
			return err
		}
		err = commonApi.PushCommonAgentConf(
			tx,
			taskReq,
			constants.ScanTaskAgentType,
			fmt.Sprintf("%s%s", stgId, date),
			conf_center.UpdateConf,
			confGranularity,
			nil, userIds, userGroupIds, nil)
		if err != nil {
			return err
		}
		return nil
	})

	return nil
}

func (s scanTaskRepository) DoDelTaskTx(db *gorm.DB, stgId, date string) error {
	return db.Transaction(func(tx *gorm.DB) error {
		var err error
		allTask := make([]string, 0)
		if date != "" {
			allTask = append(allTask, fmt.Sprintf("%s%s", stgId, date))
			err = tx.Where("strategy_id = ? AND task_date = ?", stgId, date).Delete(scan_stg.ScanTask{}).Error
			if err != nil {
				return err
			}
		} else {
			err = tx.Model(scan_stg.ScanTask{}).Select("strategy_id || task_date").Where("strategy_id = ?", stgId).
				Group("strategy_id,task_date").Find(&allTask).Error
			if err != nil {
				return err
			}
			err = tx.Where("strategy_id = ?", stgId).Delete(scan_stg.ScanTask{}).Error
			if err != nil {
				return err
			}
		}

		for _, bizId := range allTask {
			err = conf_center.ConfChange(conf_center.ConfChangeReq{
				ConfBizId:  bizId,
				ConfType:   constants.ScanTaskAgentType,
				Tx:         tx,
				RedisCli:   global.SysRedisClient,
				ChangeType: conf_center.DelConf})
			if err != nil {
				return err
			}
		}
		return err
	})
}
