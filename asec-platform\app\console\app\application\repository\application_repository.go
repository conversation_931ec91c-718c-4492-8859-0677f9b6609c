package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/console/app/application/constants"
	"asdsec.com/asec/platform/app/console/app/application/dto"
	"asdsec.com/asec/platform/app/console/common"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/basedefine"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/auth_model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"github.com/jinzhu/copier"
	"github.com/lib/pq"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ApplicationRepository 接口定义
type ApplicationRepository interface {
	GetById(ctx context.Context, id uint64) (dto.ApplicationDetailRsp, error)
	AddApplication(ctx context.Context, req *dto.ApplicationReq) (uint64, error)
	AddApplicationGroup(ctx context.Context, app *model.AppGroup) (uint64, aerrors.AError)
	ListApplicationGroup(ctx context.Context, req dto.GetApplicationGroupListReq) ([]dto.GetAppGroupListRsp, error)
	DeleteAppByID(ctx context.Context, id []uint64) error
	DeleteAppGroupByID(ctx context.Context, id uint64) error
	UpdateApplication(ctx context.Context, app *dto.UpdateApplicationReq) (uint64, error)
	UpdateAppGroup(ctx context.Context, group *model.AppGroup) aerrors.AError
	GetMyApp(ctx context.Context, userId string) ([]model.Application, error)
	GetGroupById(ctx context.Context, id uint64) (model.AppGroup, error)
	Count(ctx context.Context) (dto.ApplicationCount, error)
	AppCountByName(ctx context.Context, appName string, id uint64) (int64, error)
	AddWebApplication(ctx context.Context, req dto.AddWebAppReq) (uint64, aerrors.AError)
	AddPortalApplication(ctx context.Context, req dto.PortalAppReq) (uint64, aerrors.AError)
	ImportWebApp(ctx context.Context, req [][]string) (uint64, aerrors.AError)
	ImportPortalApp(ctx context.Context, req [][]string) (uint64, aerrors.AError)
	ImportTunApp(ctx context.Context, req [][]string) (uint64, aerrors.AError)
	UpdateWebApplication(ctx context.Context, req dto.AddWebAppReq, id uint64) (uint64, aerrors.AError)
	UpdatePortalApplication(ctx context.Context, req dto.PortalAppReq, id uint64) (uint64, aerrors.AError)
	GetApplicationList(ctx context.Context, req dto.GetApplicationListReq) (dto.GetApplicationListRsp, error)
	GetAppIdGroupIds(ctx context.Context, appIds []string) ([]string, error)
	GatewayNames(ctx context.Context) ([]string, error)
	UpdateGroupSort(ctx context.Context, groups []dto.GroupSort) error
	MoveGroup(ctx context.Context, req dto.GroupMoveReq) error
	GetApplicationByCategory(ctx context.Context, userId string) ([]dto.CategoryApplications, error)
	ResetAppCache(ctx context.Context) error
	BindDomainToCertificate(ctx context.Context, domain string, certificateIds []string, appId uint64) error
}

const agentAppCacheKey = "agent:app:cache"

// 删除这个未使用的结构体
// type AppRepository struct {
// }

// 将 ResetAppCache 方法移动到 applicationRepository
func (u *applicationRepository) ResetAppCache(ctx context.Context) error {
	rdb, err := global.GetRedisClient(ctx)
	if err != nil {
		return err
	}
	return rdb.Del(ctx, agentAppCacheKey).Err()
}

// NewAppRepository 创建接口实现
func NewAppRepository() ApplicationRepository {
	return &applicationRepository{}
}

// ParseURL 解析 URL 字符串，返回协议和地址
func ParseURL(rawURL string) (scheme, host string, err error) {
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return "", "", err
	}

	scheme = parsedURL.Scheme // 获取协议，例如 "https"
	host = parsedURL.Host     // 获取主机和端口，例如 "127.0.0.1:80"

	return scheme, host, nil
}

type applicationRepository struct {
}

func (u *applicationRepository) GatewayNames(ctx context.Context) ([]string, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]string, 0)
	var gateway []model.ApplianceInstall
	err = db.Model(model.ApplianceInstall{}).Select("name").Find(&gateway).Error
	if err != nil {
		return nil, err
	}
	for _, v := range gateway {
		res = append(res, v.Name)
	}
	return res, nil
}

func (u *applicationRepository) GetAppIdGroupIds(ctx context.Context, appIds []string) ([]string, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]string, 0)
	var appList []model.Application
	err = db.Model(model.Application{}).Select("group_ids").Where("id IN ?", appIds).Find(&appList).Error
	if err != nil {
		return nil, err
	}
	for _, v := range appList {
		for _, id := range v.GroupIds {
			res = append(res, strconv.FormatInt(id, 10))
		}
	}
	return res, nil
}

func (u *applicationRepository) GetApplicationList(ctx context.Context, req dto.GetApplicationListReq) (dto.GetApplicationListRsp, error) {
	pagination := req.Pagination
	if pagination.Search != "" {
		if len(req.SearchColumns) == 0 {
			pagination.SearchColumns = []string{
				"tb_application.app_name",
				"tb_application.publish_address",
				"tb_application.server_address",
				"taa.addresses",
				"tb_appliance.app_name"}
		} else {
			searchColumns := make([]string, 0)
			for _, v := range req.SearchColumns {
				switch v {
				case constants.AppName:
					searchColumns = append(searchColumns, "tb_application.app_name")
					break
				case constants.ServerAddress:
					searchColumns = append(searchColumns, []string{"tb_application.server_address", "taa.addresses"}...)
					break
				case constants.PublishAddress:
					searchColumns = append(searchColumns, "tb_application.publish_address")
					break
				case constants.Sdp:
					searchColumns = append(searchColumns, "tb_appliance.app_name")
					break
				default:
					global.SysLog.Sugar().Errorf("get not support column: %v", req.SearchColumns)
					return dto.GetApplicationListRsp{}, fmt.Errorf("get not support column: %v", req.SearchColumns)
				}
			}
			pagination.SearchColumns = searchColumns
		}
	}

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetApplicationListRsp{}, err
	}

	seSqlQuery := `SELECT app_id,array (select unnest (array_agg(distinct tb_se_app_relation.se_id))) AS se_list FROM tb_se_app_relation GROUP BY app_id`
	tunAddressQuery := `SELECT app_id,(protocol || ':' || address || ':' || port) as addresses FROM tb_app_address GROUP BY protocol,app_id,address,port`
	db = db.Model(model.Application{}).
		Select("tb_application.id,CASE  WHEN  tb_application.portal_show_name != '' THEN tb_application.portal_show_name  ELSE tb_application.app_name  END AS name,tb_application.group_ids,tb_application.app_type," +
			"tb_application.app_status," +
			"tb_application.show_status,portal_show_name,portal_desc,uri,web_compatible_config,tb_application.certificate_id::text as certificate_id," +
			"CASE WHEN tb_application.app_type = 'web' then publish_schema || '://' || publish_address || REGEXP_REPLACE(tb_application.uri, '\\*', '') ELSE '-' END AS publish_endpoint," +
			"array (select unnest (array_agg(distinct tb_appliance.app_name)filter(where tb_appliance.app_name is not null))) AS sdp_list," +
			"array (select unnest (array_agg(distinct jsonb_build_object('id',tas.id::text,'name',tas.strategy_name)) filter(where tas.id is not null))) as access_strategy_byte," +
			"array (select unnest (array_agg(distinct Case When tb_application.app_type = 'web' then server_schema || '://' || server_address || REGEXP_REPLACE(tb_application.uri, '\\*', '') WHEN tb_application.app_type = 'portal' then '-' ELSE taa.addresses END) )) AS application_address").
		Joins(fmt.Sprintf("LEFT JOIN (%s) ses ON ses.app_id = tb_application.id", seSqlQuery)).
		Joins("LEFT JOIN tb_appliance ON tb_appliance.appliance_id = any(ses.se_list)").
		Joins(fmt.Sprintf("LEFT JOIN (%s) taa ON taa.app_id = tb_application.id", tunAddressQuery)).
		Joins("LEFT JOIN tb_access_strategy tas ON (tb_application.id = any(tas.app_ids) OR tas.app_group_ids && tb_application.group_ids)").
		Group("tb_application.id").
		Order("tb_application.created_at desc")
	if req.GroupId > 0 {
		db = db.Where("? = any(tb_application.group_ids) ", req.GroupId)
	}
	if req.AppType != "" {
		db = db.Where("tb_application.app_type = ? ", req.AppType)
	}

	var appItem []dto.ApplicationGroupItem
	pagination, err = model.Paginate(&appItem, &pagination, db)
	if err != nil {
		return dto.GetApplicationListRsp{}, err
	}
	//聚合数据
	groupAppMap := make(map[int64][]dto.ApplicationGroupItem)
	for k, v := range appItem {
		var accessStg []dto.AccessStrategy
		for _, data := range v.AccessStrategyByte.Elements {
			var dataStg dto.AccessStrategy
			err = json.Unmarshal(data.Bytes, &dataStg)
			if err != nil {
				return dto.GetApplicationListRsp{}, err
			}
			accessStg = append(accessStg, dataStg)
		}

		//兼容性配置反序列
		var webCompatibleConfig dto.CompatibleConfig
		if v.WebCompatibleConfigByte != nil {
			err = json.Unmarshal(v.WebCompatibleConfigByte, &webCompatibleConfig)
			if err != nil {
				return dto.GetApplicationListRsp{}, err
			}
		}
		appItem[k].WebCompatibleConfig = webCompatibleConfig
		appItem[k].AccessStrategy = accessStg
		appItem[k].Type = constants.AppType
		if len(v.GroupIds) == 0 {
			if _, ok := groupAppMap[constants.DefaultGroupId]; ok {
				groupAppMap[constants.DefaultGroupId] = append(groupAppMap[constants.DefaultGroupId], v)
			} else {
				groupAppMap[constants.DefaultGroupId] = []dto.ApplicationGroupItem{v}
			}
		}
		for _, groupId := range v.GroupIds {
			if _, ok := groupAppMap[groupId]; ok {
				groupAppMap[groupId] = append(groupAppMap[groupId], v)
			} else {
				groupAppMap[groupId] = []dto.ApplicationGroupItem{v}
			}
		}
	}
	//分情况处理
	var res []dto.ApplicationGroupItem
	res = append(res, appItem...)
	return dto.GetApplicationListRsp{
		CommonPage: model.CommonPage{CurrentPage: pagination.Page, PageSize: pagination.Limit, TotalNum: int(pagination.TotalRows)},
		Data:       res,
	}, nil
}
func (u *applicationRepository) UpdatePortalApplication(ctx context.Context, req dto.PortalAppReq, id uint64) (uint64, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	//开启事务更新
	err = db.Transaction(func(tx *gorm.DB) error {
		var application model.Application
		err = copier.Copy(&application, req)
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		application.ID = id
		application.AppStatus = 1
		application.AppType = constants.PortalAppType
		application.IconURL = req.IconURL
		application.WebUrl = req.WebUrl
		application.AppStatus = req.AppStatus
		var webC dto.CompatibleConfig
		webCompatibleConfigByte, err := json.Marshal(webC)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
			return err
		}
		var HealthConfig dto.HealthConfig
		healthConfigByte, err := json.Marshal(HealthConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal HealthConfig failed. err=%v", err)
			return err
		}
		openConfigB, err := json.Marshal(req.OpenConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal openConfig failed. err=%v", err)
			return err
		}

		application.WebCompatibleConfig = webCompatibleConfigByte
		application.HealthConfig = healthConfigByte
		application.OpenConfig = openConfigB

		//更新应用
		err = tx.Model(application).Updates(application).Error
		if err != nil {
			return err
		}

		err = tx.Model(application).Updates(map[string]interface{}{
			"portal_desc":      application.PortalDesc,
			"show_status":      application.ShowStatus,
			"web_url":          application.WebUrl,
			"portal_show_name": application.PortalShowName, // 显式更新 PortalShowName 字段
		}).Error
		if err != nil {
			return err
		}

		return err
	})
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return id, nil
}
func (u *applicationRepository) UpdateWebApplication(ctx context.Context, req dto.AddWebAppReq, id uint64) (uint64, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	// 发布地址重复校验
	var data model.Application
	err = db.Model(model.Application{}).Where("publish_address = ? and uri = ?", req.PublishAddress, req.Uri).Find(&data).Error
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	if data.ID > 0 && data.ID != id {
		return 0, aerrors.New("publish address and uri duplicate", constants.AppPublishAddressDuplicateErr)
	}

	//开启事务更新
	err = db.Transaction(func(tx *gorm.DB) error {
		var application model.Application
		err = copier.Copy(&application, req)
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		application.ID = id
		application.AppStatus = 1
		application.AppType = constants.WebAppType
		application.IconURL = req.IconURL
		application.AppStatus = req.AppStatus
		webCompatibleConfigByte, err := json.Marshal(req.WebCompatibleConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
			return err
		}
		HealthConfig, err := json.Marshal(req.HealthConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal HealthConfig failed. err=%v", err)
			return err
		}
		openConfigB, err := json.Marshal(req.OpenConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal openConfig failed. err=%v", err)
			return err
		}
		application.OpenConfig = openConfigB
		application.HealthConfig = HealthConfig
		application.WebCompatibleConfig = webCompatibleConfigByte
		//更新应用和网关的关系
		err = tx.Where("app_id = ?", id).Delete(&model.SeAppRelation{}).Error
		if err != nil {
			return err
		}
		//隧道应用和web应用类型更换
		err = tx.Where("app_id = ? ", id).Delete(&model.AppAddress{}).Error
		if err != nil {
			return err
		}

		err = updateSeApp(tx, req.SeApp, id)
		if err != nil {
			global.SysLog.Sugar().Errorf("update se_relationship failed. err=%v", err)
			return err
		}
		//更新应用
		err = tx.Model(application).Updates(application).Error
		if err != nil {
			return err
		}

		err = tx.Model(application).Updates(map[string]interface{}{
			"portal_desc":      application.PortalDesc,
			"show_status":      application.ShowStatus,
			"portal_show_name": application.PortalShowName, // 显式更新 PortalShowName 字段
		}).Error
		if err != nil {
			return err
		}
		err = tx.Model(application).UpdateColumn("idp_id", req.IdpId).Error
		if err != nil {
			return err
		}

		// 同步OAuth2客户端配置
		if err := u.syncOAuth2Client(ctx, &application, req.WebCompatibleConfig); err != nil {
			global.SysLog.Sugar().Errorf("sync oauth2 client failed. err=%v", err)
			return err
		}

		return err
	})
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}

	// 处理证书绑定（在事务外执行，因为是独立的操作）
	if req.PublishSchema == "https" {
		domain := req.PublishAddress
		if req.CertificateId != "" {
			// 绑定新证书
			if err := u.BindDomainToCertificate(ctx, domain, []string{req.CertificateId}, id); err != nil {
				global.SysLog.Sugar().Errorf("bind domain to certificate failed after app update. app_id: %d, domain: %s, certificate_id: %s, err: %v", id, domain, req.CertificateId, err)
				// 这里不返回错误，因为应用已经更新成功，证书绑定失败只记录日志
			}
		} else {
			// 取消证书绑定（传入空的证书ID列表）
			if err := u.BindDomainToCertificate(ctx, domain, []string{}, id); err != nil {
				global.SysLog.Sugar().Errorf("unbind domain from certificate failed after app update. app_id: %d, domain: %s, err: %v", id, domain, err)
				// 这里不返回错误，因为应用已经更新成功，证书解绑失败只记录日志
			}
		}
	}

	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return id, nil
}
func (u *applicationRepository) AddPortalApplication(ctx context.Context, req dto.PortalAppReq) (uint64, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		var application model.Application
		err = copier.Copy(&application, req)
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		application.ID = id
		application.AppType = constants.PortalAppType
		var webC dto.CompatibleConfig
		webCompatibleConfigByte, err := json.Marshal(webC)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
			return err
		}
		var HealthConfig dto.HealthConfig
		healthConfigByte, err := json.Marshal(HealthConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal HealthConfig failed. err=%v", err)
			return err
		}
		openConfigB, err := json.Marshal(req.OpenConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal openConfig failed. err=%v", err)
			return err
		}

		application.WebCompatibleConfig = webCompatibleConfigByte
		application.HealthConfig = healthConfigByte
		application.OpenConfig = openConfigB
		// 门户应用不需要证书，明确设置为 nil
		application.CertificateID = nil

		//创建应用
		err = tx.Model(model.Application{}).Create(&application).Error
		if err != nil {
			global.SysLog.Sugar().Errorf("create application failed. err=%v", err)
			return err
		}

		return err
	})
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}

	return id, nil
}
func (u *applicationRepository) AddWebApplication(ctx context.Context, req dto.AddWebAppReq) (uint64, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}

	//地址重复校验
	var data model.Application
	err = db.Model(model.Application{}).Where("publish_address = ? and uri = ?", req.PublishAddress, req.Uri).Find(&data).Error
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	if data.ID > 0 {
		return 0, aerrors.New("publish address and uri duplicate", constants.AppPublishAddressDuplicateErr)
	}
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		var application model.Application
		err = copier.Copy(&application, req)
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		application.ID = id
		//application.AppStatus =
		application.AppType = constants.WebAppType
		webCompatibleConfigByte, err := json.Marshal(req.WebCompatibleConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
			return err
		}
		HealthConfig, err := json.Marshal(req.HealthConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal HealthConfig failed. err=%v", err)
			return err
		}
		OpenConfig, err := json.Marshal(req.OpenConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal HealthConfig failed. err=%v", err)
			return err
		}
		application.OpenConfig = OpenConfig
		application.WebCompatibleConfig = webCompatibleConfigByte
		application.HealthConfig = HealthConfig
		//创建应用
		err = tx.Model(model.Application{}).Create(&application).Error
		if err != nil {
			global.SysLog.Sugar().Errorf("create application failed. err=%v", err)
			return err
		}
		//更新关系表
		err = updateSeApp(tx, req.SeApp, id)
		if err != nil {
			global.SysLog.Sugar().Errorf("update se_relationship failed. err=%v", err)
			return err
		}

		// 同步OAuth2客户端配置
		if err := u.syncOAuth2Client(ctx, &application, req.WebCompatibleConfig); err != nil {
			global.SysLog.Sugar().Errorf("sync oauth2 client failed. err=%v", err)
			return err
		}

		return err
	})
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}

	// 处理证书绑定（在事务外执行，因为是独立的操作）
	if req.PublishSchema == "https" && req.CertificateId != "" {
		domain := req.PublishAddress
		if err := u.BindDomainToCertificate(ctx, domain, []string{req.CertificateId}, id); err != nil {
			global.SysLog.Sugar().Errorf("bind domain to certificate failed after app creation. app_id: %d, domain: %s, certificate_id: %s, err: %v", id, domain, req.CertificateId, err)
			// 这里不返回错误，因为应用已经创建成功，证书绑定失败只记录日志
		}
	}

	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return id, nil
}

func (u *applicationRepository) GetMyApp(ctx context.Context, userId string) ([]model.Application, error) {
	var res []model.Application
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	var user auth_model.TbUserEntity
	err = db.Table(auth_model.TableNameTbUserEntity).First(&user, "id= ? ", userId).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return res, err
	}
	// 收集所有相关策略，但需要检查排除用户逻辑
	strategyTable := model.AccessStrategy{}.TableName()

	// 1. 查询所有可能相关的策略（包括用户直接、用户组、角色）
	var userIdStrategy []model.AccessStrategy
	err = db.Table(strategyTable).Where("(user_ids @> ? or enable_all_user = 1) and strategy_status = 1", pq.StringArray{userId}).Find(&userIdStrategy).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return res, err
	}

	// 1.1 查询用户角色的策略
	var roleIds []string
	var roleStrategy []model.AccessStrategy
	roleMappingTable := auth_model.TableNameTbUserRole
	err = db.Table(roleMappingTable).Select("role_id").Where("user_id = ?", userId).Find(&roleIds).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return res, err
	}
	if len(roleIds) > 0 {
		for _, v := range roleIds {
			var tmp []model.AccessStrategy
			err = db.Table(strategyTable).Where("?=any(role_ids) and strategy_status = 1", v).Find(&tmp).Error
			if err != nil {
				global.SysLog.Error(err.Error())
				continue
			}
			roleStrategy = append(roleStrategy, tmp...)
		}
	}

	// 2. 查询用户所在组的策略
	var allGroups []string
	var groupStrategy []model.AccessStrategy
	groups := u.GetParentInfo(ctx, user.GroupID)
	for _, v := range groups {
		if v.ID != "" {
			allGroups = append(allGroups, v.ID)
		}
	}
	for _, v := range allGroups {
		var tmp []model.AccessStrategy
		err = db.Table(strategyTable).Where("?=any(user_group_ids) and strategy_status = 1", v).Find(&tmp).Error
		if err != nil {
			global.SysLog.Error(err.Error())
			continue
		}
		groupStrategy = append(groupStrategy, tmp...)
	}
	// 3. 从有效策略中获取应用ID和应用分组ID
	var appId []int64
	var appGroup []int64
	appTable := model.Application{}.TableName()
	var allAppId []int64
	err = db.Table(appTable).Select("id").Where("app_status in (1,2) and show_status = 1").Find(&allAppId).Error
	if err != nil {
		global.SysLog.Error(err.Error())
		return res, nil
	}
	// 4. 检查是否有策略启用了"全部应用"
	enableAllApp := false

	// 处理用户直接策略
	for _, v := range userIdStrategy {
		if !u.isUserExcludedFromStrategy(ctx, userId, roleIds, allGroups, v) {
			if v.EnableAllApp == 1 {
				appId = allAppId
				enableAllApp = true
				break
			}
			appId = append(appId, v.AppIds...)
			appGroup = append(appGroup, v.AppGroupIds...)
		}
	}

	// 如果还没有启用全部应用，继续处理用户组策略
	if !enableAllApp {
		for _, v := range groupStrategy {
			if !u.isUserExcludedFromStrategy(ctx, userId, roleIds, allGroups, v) {
				if v.EnableAllApp == 1 {
					appId = allAppId
					enableAllApp = true
					break
				}
				appId = append(appId, v.AppIds...)
				appGroup = append(appGroup, v.AppGroupIds...)
			}
		}
	}

	// 如果还没有启用全部应用，继续处理角色策略
	if !enableAllApp {
		for _, v := range roleStrategy {
			if !u.isUserExcludedFromStrategy(ctx, userId, roleIds, allGroups, v) {
				if v.EnableAllApp == 1 {
					appId = allAppId
					enableAllApp = true
					break
				}
				appId = append(appId, v.AppIds...)
				appGroup = append(appGroup, v.AppGroupIds...)
			}
		}
	}
	// 获取分组中所有的app
	if len(appGroup) > 0 {
		var application []int64
		err = db.Table(appTable).Select("Distinct(id)").Where("group_ids && ?", pq.Array(appGroup)).Find(&application).Error
		if err != nil {
			global.SysLog.Error(err.Error())
			return res, err
		}
		appId = append(appId, application...)
	}

	appStatus := []int64{1, 2}
	if len(appId) > 0 {
		//获取所有应用
		webSwitch, err := commonApi.GetSpecialConfig(ctx, constants.SwitchSpecialType, constants.WebApplicationSpecialKey)
		if err != nil {
			return res, err
		}
		if webSwitch == constants.FalseSpecialValue {
			db = db.Where("web_url != ''")
		}
		err = db.Model(&model.Application{}).Preload("AppAddresses").Where("id in (?)  and app_status in (?) and show_status = 1", appId, appStatus).Find(&res).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return res, err
		}
	}
	return res, nil
}

// isUserExcludedFromStrategy 检查用户是否被策略排除
func (u *applicationRepository) isUserExcludedFromStrategy(ctx context.Context, userId string, userRoleIds []string, userGroupIds []string, strategy model.AccessStrategy) bool {
	// 1. 检查用户是否直接被排除
	for _, excludeUserId := range strategy.ExcludeUserIds {
		if excludeUserId == userId {
			return true
		}
	}

	// 2. 检查用户的角色是否被排除
	for _, userRoleId := range userRoleIds {
		for _, excludeRoleId := range strategy.ExcludeUserRoleIds {
			if excludeRoleId == userRoleId {
				return true
			}
		}
	}

	// 3. 检查用户的用户组是否被排除
	for _, userGroupId := range userGroupIds {
		for _, excludeGroupId := range strategy.ExcludeUserGroupIds {
			if excludeGroupId == userGroupId {
				return true
			}
		}
	}

	return false
}

func (u *applicationRepository) GetParentInfo(ctx context.Context, pg string) []auth_model.TbUserGroup {
	var tmp auth_model.TbUserGroup
	var kg []auth_model.TbUserGroup
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil
	}
	err = db.Table(auth_model.TableNameTbUserGroup).Where("id = ?", pg).Find(&tmp).Error
	if err != nil {
		return kg
	}
	kg = append(kg, tmp)
	if tmp.ParentGroupID != "" && tmp.ParentGroupID != basedefine.RootGroupID {
		tt := u.GetParentInfo(ctx, tmp.ParentGroupID)
		kg = append(kg, tt...)
	}
	return kg
}

func (u *applicationRepository) Count(ctx context.Context) (count dto.ApplicationCount, err error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return
	}
	err = db.Model(&model.Application{}).
		Select("SUM(CASE WHEN app_status = 1 THEN 1 ELSE 0 END) as online_count, count(*) as total_count").Find(&count).Error
	return
}

func (u *applicationRepository) GetGroupById(ctx context.Context, id uint64) (model.AppGroup, error) {
	var group model.AppGroup
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return group, err
	}

	err = db.Model(&group).Find(&group, id).Error
	return group, err
}

func (u *applicationRepository) UpdateAppGroup(ctx context.Context, group *model.AppGroup) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	var data model.AppGroup
	err = db.Model(model.AppGroup{}).Where("group_name = ? and id != ?", group.GroupName, group.ID).Find(&data).Error
	if err != nil {
		return aerrors.NewWithError(err, common.AddAppErr)
	}
	if data.GroupName != "" {
		return aerrors.New("application group name duplicate", constants.AppGroupNameDuplicateErr)
	}
	err = db.Updates(group).Error
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return nil
}

func (u *applicationRepository) UpdateApplication(ctx context.Context, req *dto.UpdateApplicationReq) (uint64, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, err
	}
	var app model.Application
	copier.Copy(&app, &req)
	app.AppAddresses = req.AppSites
	app.AppType = constants.TunAppType
	app.IconURL = req.IconURL
	app.AppStatus = req.AppStatus
	webCompatibleConfigByte, err := json.Marshal(req.WebCompatibleConfig)
	if err != nil {
		global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
		return 0, err
	}
	HealthConfig, err := json.Marshal(req.HealthConfig)
	if err != nil {
		global.SysLog.Sugar().Errorf("marshal HealthConfig failed. err=%v", err)
		return 0, err
	}
	config, err := json.Marshal(req.OpenConfig)
	if err != nil {
		global.SysLog.Sugar().Errorf("marshal OpenConfig failed. err=%v", err)
		return 0, err
	}
	app.OpenConfig = config
	app.HealthConfig = HealthConfig
	app.WebCompatibleConfig = webCompatibleConfigByte

	err = db.Transaction(func(tx *gorm.DB) error {
		err = tx.Where("app_id = ? ", app.ID).Delete(&model.AppAddress{}).Error
		if err != nil {
			return err
		}

		err = tx.Where("app_id = ?", app.ID).Delete(&model.SeAppRelation{}).Error
		if err != nil {
			return err
		}

		err = updateSeApp(tx, req.SeApp, app.ID)
		if err != nil {
			return err
		}
		err = tx.Model(model.Application{}).Where("id = ?", app.ID).Updates(map[string]interface{}{
			"web_url":          req.WebUrl,
			"portal_desc":      req.PortalDesc,
			"portal_show_name": req.PortalShowName,
			"show_status":      req.ShowStatus,
		}).Error
		if err != nil {
			return err
		}
		return tx.Model(app).Updates(app).Error
	})
	if err != nil {
		return 0, err
	}

	// 同步OAuth2客户端配置（在事务外执行，因为需要读取完整的应用信息）
	var fullApp model.Application
	if err := db.First(&fullApp, app.ID).Error; err != nil {
		global.SysLog.Sugar().Errorf("get application for oauth2 sync failed. err=%v", err)
	} else {
		if err := u.syncOAuth2Client(ctx, &fullApp, req.WebCompatibleConfig); err != nil {
			global.SysLog.Sugar().Errorf("sync oauth2 client failed. err=%v", err)
		}
	}

	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return req.ID, err
}

func updateSeApp(tx *gorm.DB, seApps []dto.SeApps, appId uint64) error {
	var relations []model.SeAppRelation
	if len(seApps) <= 0 {
		return nil
	}

	for _, seApp := range seApps {
		var seAppRelation model.SeAppRelation
		if appId <= 0 {
			seAppRelation.AppId = seApp.AppId
		} else {
			seAppRelation.AppId = appId
		}
		connectorId, _ := strconv.ParseUint(seApp.ConnectorId, 10, 64)
		seAppRelation.ConnectorId = connectorId
		seId, _ := strconv.ParseUint(seApp.SeId, 10, 64)
		seAppRelation.SeId = seId
		if v1.ApplianceType(seApp.AppType) == v1.ApplianceType_CONNECTOR {
			var appliance model.Appliance
			tx.Find(&appliance, "appliance_id", connectorId)
			if appliance.BindSeID > 0 {
				//如果是连接器,关系表中se_id为connector连接的se的id.
				seAppRelation.SeId = appliance.BindSeID
			}
		}
		relations = append(relations, seAppRelation)
	}
	return tx.Create(relations).Error
}

func (u *applicationRepository) DeleteAppGroupByID(ctx context.Context, id uint64) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		//delete app group
		err = tx.Delete(&model.AppGroup{}, id).Error
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		//get tenant default group
		var defaultGroup model.AppGroup
		err = tx.Model(&model.AppGroup{}).First(&defaultGroup, "is_default = ?", true).Error
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		// delete application group in application
		sql := "UPDATE tb_application SET group_ids=array_remove(group_ids, $1) WHERE $2 = any(group_ids)"
		err = tx.Exec(sql, id, id).Error
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		// move application to default group
		upSql := fmt.Sprintf("UPDATE tb_application SET group_ids='{%d}' where group_ids IS null or group_ids ='{}'", constants.DefaultGroupId)
		err = tx.Exec(upSql).Error
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return nil
}

func (u *applicationRepository) DeleteAppByID(ctx context.Context, id []uint64) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	// 获取应用信息
	var apps []model.Application
	if err := db.Where("id in ?", id).Find(&apps).Error; err != nil {
		return err
	}

	// 删除图标文件
	for _, app := range apps {
		if app.IconURL != "" && !strings.HasPrefix(app.IconURL, "http") {
			iconPath := filepath.Join("/opt/asdsec/icon", filepath.Base(app.IconURL))
			_ = os.Remove(iconPath)
		}
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		//删除application并且删除外键关联的地址
		err = tx.Where("app_id in ?", id).Delete(&model.AppAddress{}).Error
		if err != nil {
			return err
		}

		err = tx.Where("id in ?", id).Delete(&model.Application{}).Error
		if err != nil {
			return err
		}

		err = tx.Where("app_id in ?", id).Delete(&model.SeAppRelation{}).Error
		if err != nil {
			return err
		}

		// 删除对应的OAuth2客户端记录
		for _, app := range apps {
			if u.shouldDeleteOAuth2Client(app) {
				if err := tx.Where("name = ?", app.AppName).Delete(&model.OAuth2Client{}).Error; err != nil {
					global.SysLog.Sugar().Errorf("delete oauth2 client failed for app %s. err=%v", app.AppName, err)
					// 不中断删除流程，只记录错误
				}
			}
		}

		return nil
	})
	if err != nil {
		return err
	}
	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return nil
}

func (u *applicationRepository) ListApplicationGroup(ctx context.Context, req dto.GetApplicationGroupListReq) ([]dto.GetAppGroupListRsp, error) {
	var groups []dto.GetAppGroupListRsp
	itemDb, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	itemDb = itemDb.Model(&model.AppGroup{}).
		Select("tb_app_group.id,tb_app_group.group_name as name,is_default,Count(tb_application.id) as app_count").
		Joins("LEFT JOIN tb_application ON tb_app_group.id = any(tb_application.group_ids)").
		Group("tb_app_group.id").
		Order("tb_app_group.sort_order desc")
	if req.Name != "" {
		keyword := "%" + dbutil.EscapeForLike(req.Name) + "%"
		itemDb = itemDb.Where("group_name like ?", keyword)
	}
	err = itemDb.Find(&groups).Error
	if err != nil {
		return nil, err
	}
	if req.NeedDefault {
		var res []dto.GetAppGroupListRsp
		res = append(res, dto.GetAppGroupListRsp{Id: "0", Name: "全部标签", IsDefault: true, ChildGroup: groups})
		return res, nil
	}
	return groups, nil
}

func (u *applicationRepository) AddApplicationGroup(ctx context.Context, group *model.AppGroup) (uint64, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.AddAppErr)
	}
	var data model.AppGroup
	err = db.Model(model.AppGroup{}).Where("group_name = ?", group.GroupName).Find(&data).Error
	if err != nil {
		return 0, aerrors.NewWithError(err, common.AddAppErr)
	}
	if data.GroupName != "" {
		return 0, aerrors.New("application group name duplicate", constants.AppGroupNameDuplicateErr)
	}
	// 在创建分组前，设置 sort_order 默认值
	if group.SortOrder == 0 {
		// 获取最大的 sort_order
		var maxOrder struct {
			MaxOrder int `gorm:"column:max_order"`
		}
		if err := db.Model(&model.AppGroup{}).
			Select("COALESCE(MAX(sort_order), 0) as max_order").
			Scan(&maxOrder).Error; err != nil {
			return 0, aerrors.NewWithError(err, common.OperateError)
		}

		// 设置为最大值 + 100
		group.SortOrder = maxOrder.MaxOrder + 100
	}
	err = db.Create(&group).Error
	if err != nil {
		return 0, aerrors.NewWithError(err, common.AddAppErr)
	}
	var res model.AppGroup
	err = db.Model(model.AppGroup{}).Where("group_name = ?", group.GroupName).Find(&res).Error
	if err != nil {
		return 0, aerrors.NewWithError(err, common.AddAppErr)
	}
	return res.ID, nil
}

func (u *applicationRepository) GetById(ctx context.Context, id uint64) (dto.ApplicationDetailRsp, error) {
	var res dto.ApplicationDetailRsp

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return res, err
	}
	err = db.Model(model.Application{}).
		Select("tb_application.*,"+
			"array (select unnest (array_agg(distinct jsonb_build_object('address',tb_app_address.address::text,'port',tb_app_address.port,'protocol',tb_app_address.protocol)) filter(where tb_app_address.address is not null))) AS app_addresses,"+
			"array (select unnest (array_agg(distinct jsonb_build_object('id',tb_app_group.id::text,'name',tb_app_group.group_name)) filter(where tb_app_group.id is not null))) as group_info,"+
			"tb_application.certificate_id::text as certificate_id").
		Joins("LEFT JOIN  tb_app_address ON tb_app_address.app_id = tb_application.id").
		Joins("LEFT JOIN  tb_app_group ON tb_app_group.id = any(tb_application.group_ids)").
		Where("tb_application.id = ?", id).Group("tb_application.id").Find(&res).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return res, err
	}
	//兼容性配置反序列
	var webCompatibleConfig dto.CompatibleConfig
	if res.WebCompatibleConfigByte != nil {
		err = json.Unmarshal(res.WebCompatibleConfigByte, &webCompatibleConfig)
		if err != nil {
			return dto.ApplicationDetailRsp{}, err
		}
	}

	var healthConfig dto.HealthConfig
	if res.HealthConfigByte != nil {
		err = json.Unmarshal(res.HealthConfigByte, &healthConfig)
		if err != nil {
			return dto.ApplicationDetailRsp{}, err
		}
	}

	var openConfig dto.OpenConfig
	if res.OpenConfigByte != nil {
		err = json.Unmarshal(res.OpenConfigByte, &openConfig)
		if err != nil {
			return dto.ApplicationDetailRsp{}, err
		}
	}

	res.WebCompatibleConfig = webCompatibleConfig
	res.HealthConfig = healthConfig
	res.OpenConfig = openConfig
	//组关系反序列
	var groupRelationship []dto.GroupInfo
	for _, data := range res.GroupInfo.Elements {
		var groupInfo dto.GroupInfo
		err = json.Unmarshal(data.Bytes, &groupInfo)
		if err != nil {
			return dto.ApplicationDetailRsp{}, err
		}
		groupRelationship = append(groupRelationship, groupInfo)
	}

	res.GroupRelationship = groupRelationship

	// app_address
	var appAddress []dto.AppAddress
	for _, data := range res.AppAddressesByte.Elements {
		var address dto.AppAddress
		err = json.Unmarshal(data.Bytes, &address)
		if err != nil {
			return dto.ApplicationDetailRsp{}, err
		}
		appAddress = append(appAddress, address)
	}
	res.AppAddresses = appAddress
	var seApps []dto.SEInfo

	err = db.Model(&model.SeAppRelation{}).
		Select("case WHEN connector_id >0 then connector_id else se_id end as appliance_id,tb_appliance.app_name").
		Joins("LEFT JOIN tb_appliance ON tb_appliance.appliance_id = se_id").
		Where("app_id = ?", id).Scan(&seApps).Error
	if err != nil {
		return res, err
	}
	res.BindSE = seApps

	return res, nil
}

func (u *applicationRepository) ImportWebApp(ctx context.Context, req [][]string) (uint64, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	insetRow := 0
	err = db.Transaction(func(tx *gorm.DB) error {
		for _, v := range req {
			insetRow += 1
			id, err := snowflake.Sf.GetId()
			if err != nil {
				return err
			}
			var applicationRow model.Application
			applicationRow.AppName = v[common.ImportAppName]
			applicationRow.ID = id
			applicationRow.AppType = constants.WebAppType             //应用类型
			applicationRow.WebUrl = v[common.ImportAppPublishAddress] //应用中心首页地址 就等于发布地址
			applicationRow.AppStatus = 1                              //应用状态
			if v[common.ImportAppStatus] == "停用" {
				applicationRow.AppStatus = 3
			} else if v[common.ImportAppStatus] == "维护" {
				applicationRow.AppStatus = 2
			}
			//服务器地址
			protocol, address, err := ParseURL(v[common.ImportAppServerAddress])
			if err != nil {
				return err
			}
			applicationRow.ServerSchema = protocol
			applicationRow.ServerAddress = address
			//发布地址
			pubProtocol, pubAddress, err := ParseURL(v[common.ImportAppPublishAddress])
			if err != nil {
				return err
			}
			applicationRow.PublishSchema = pubProtocol
			applicationRow.PublishAddress = pubAddress
			groupIds := pq.Int64Array{} //默认标签
			groupIds = append(groupIds, int64(constants.DefaultGroupId))
			applicationRow.GroupIds = groupIds

			applicationRow.ShowStatus = 1 //门户是否展示
			if v[common.ImportAppShowStatus] == "否" {
				applicationRow.ShowStatus = 0
			}
			applicationRow.PortalShowName = v[common.ImportAppPortalShowName]
			applicationRow.PortalDesc = v[common.ImportAppPortalDesc]
			var WebCompatibleConfig dto.CompatibleConfig
			applicationRow.Uri = v[common.ImportAppUri] //应用路径
			if v[common.ImportAppUri] != "" {
				WebCompatibleConfig.DefaultRule = append(WebCompatibleConfig.DefaultRule, "uri")
			}
			if v[common.ImportAppSmartRewrite] == "是" { //智能改写
				WebCompatibleConfig.DefaultRule = append(WebCompatibleConfig.DefaultRule, "url_smart_rewrite")
			}
			//依赖站点
			WebCompatibleConfig.DependSite.Type = "assign"
			WebCompatibleConfig.DependSite.Text = v[common.ImportAppDependSite]
			if v[common.ImportAppDependSite] != "" {
				WebCompatibleConfig.DefaultRule = append(WebCompatibleConfig.DefaultRule, "depend_site")
			}

			webCompatibleConfigByte, err := json.Marshal(WebCompatibleConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
				return err
			}
			var HealthConfig dto.HealthConfig
			healthConfigByte, err := json.Marshal(HealthConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal healthConfig failed. err=%v", err)
				return err
			}
			var openConfig dto.OpenConfig
			openConfigB, err := json.Marshal(openConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal openConfig failed. err=%v", err)
				return err
			}
			applicationRow.OpenConfig = openConfigB
			applicationRow.WebCompatibleConfig = webCompatibleConfigByte //web应用兼容性
			applicationRow.HealthConfig = healthConfigByte               //健康检查
			//创建应用
			err = tx.Model(model.Application{}).Create(&applicationRow).Error
			if err != nil {
				global.SysLog.Sugar().Errorf("create application failed. err=%v", err)
				return err
			}

			var seApp []dto.SeApps
			var cmd model.ApplianceInstall
			db, err := global.GetDBClient(ctx)
			if err != nil {
				return err
			}
			err = db.Where("name= ? and expire_time > ?", v[common.ImportAppGateway], time.Now()).Find(&cmd).Error //根据网关名查询网关ID
			if err != nil || errors.Is(err, gorm.ErrRecordNotFound) {
				global.SysLog.Error(err.Error())
				return err
			}
			gateWayId := cmd.ApplianceID
			seApp = append(seApp, dto.SeApps{SeId: strconv.FormatUint(gateWayId, 10), AppType: constants.GatewayAppType})

			//网关 与 应用关联
			err = updateSeApp(tx, seApp, id)
			if err != nil {
				global.SysLog.Sugar().Errorf("update se_relationship failed. err=%v", err)
				return err
			}
		}
		return err
	})
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return uint64(insetRow), nil
}

func (u *applicationRepository) ImportPortalApp(ctx context.Context, req [][]string) (uint64, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	insetRow := 0
	err = db.Transaction(func(tx *gorm.DB) error {
		fmt.Println(len(req))
		for _, v := range req {
			insetRow += 1
			fmt.Println(insetRow)
			id, err := snowflake.Sf.GetId()
			if err != nil {
				return err
			}
			var app model.Application
			app.AppType = constants.PortalAppType
			app.ID = id
			app.AppName = v[common.ImportAppName]
			app.AppStatus = 1 //应用状态
			if v[common.ImportAppStatus] == "停用" {
				app.AppStatus = 3
			} else if v[common.ImportAppStatus] == "维护" {
				app.AppStatus = 2
			}
			app.ShowStatus = 1 //门户是否展示
			if v[common.ImportAppShowStatus] == "否" {
				app.ShowStatus = 0
			}
			app.PortalShowName = v[common.ImportAppPortalShowName]
			app.PortalDesc = v[common.ImportAppPortalDesc]
			groupIds := pq.Int64Array{} //默认标签
			groupIds = append(groupIds, int64(constants.DefaultGroupId))
			app.GroupIds = groupIds

			//都给个默认值
			var HealthConfig dto.HealthConfig
			var WebCompatibleConfig dto.CompatibleConfig
			WebCompatibleConfig.DefaultRule = []string{}
			WebCompatibleConfig.HeaderConfig = []dto.HeaderConfig{}
			WebCompatibleConfig.UrlManualRewrite = []dto.UrlManualRewrite{}

			webCompatibleConfigByte, err := json.Marshal(WebCompatibleConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
				return err
			}
			healthConfigByte, err := json.Marshal(HealthConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal healthConfig failed. err=%v", err)
				return err
			}
			var openConfig dto.OpenConfig
			openConfigB, err := json.Marshal(openConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal openConfig failed. err=%v", err)
				return err
			}
			app.OpenConfig = openConfigB
			app.WebCompatibleConfig = webCompatibleConfigByte //web应用兼容性
			app.HealthConfig = healthConfigByte               //健康检查
			err = tx.Model(&app).Create(&app).Error
			if err != nil {
				global.SysLog.Error(err.Error())
				return err
			}
		}
		return nil
	})

	return uint64(insetRow), nil
}

func (u *applicationRepository) ImportTunApp(ctx context.Context, req [][]string) (uint64, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	insetRow := 0
	err = db.Transaction(func(tx *gorm.DB) error {
		for _, v := range req {
			insetRow += 1
			id, err := snowflake.Sf.GetId()
			if err != nil {
				return err
			}
			var app model.Application
			app.AppType = constants.TunAppType
			app.ID = id
			app.AppName = v[common.ImportAppName]
			app.AppStatus = 1 //应用状态
			if v[common.ImportAppStatus] == "停用" {
				app.AppStatus = 3
			} else if v[common.ImportAppStatus] == "维护" {
				app.AppStatus = 2
			}
			app.ShowStatus = 1 //门户是否展示
			if v[common.ImportAppShowStatus] == "否" {
				app.ShowStatus = 0
			}
			app.PortalShowName = v[common.ImportAppPortalShowName]
			app.PortalDesc = v[common.ImportAppPortalDesc]
			groupIds := pq.Int64Array{} //默认标签
			groupIds = append(groupIds, int64(constants.DefaultGroupId))
			app.GroupIds = groupIds

			var address []model.AppAddress
			//对v[3] 以","逗号为分割符号 转化成切片
			appSite := strings.Split(v[common.ImportAppServerAddress], ",")
			for _, site := range appSite {
				item := strings.Split(site, ":")
				if len(item) != 3 {
					return aerrors.NewWithError(err, common.ParamInvalidError) //参数错误
				}
				address = append(address, model.AppAddress{
					Address:  item[1],
					AppId:    id,
					Port:     item[2],
					Protocol: item[0],
				})
			}
			app.AppAddresses = address

			//都给个默认值
			var HealthConfig dto.HealthConfig
			var WebCompatibleConfig dto.CompatibleConfig
			WebCompatibleConfig.DefaultRule = []string{}
			WebCompatibleConfig.HeaderConfig = []dto.HeaderConfig{}
			WebCompatibleConfig.UrlManualRewrite = []dto.UrlManualRewrite{}

			webCompatibleConfigByte, err := json.Marshal(WebCompatibleConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
				return err
			}
			healthConfigByte, err := json.Marshal(HealthConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal healthConfig failed. err=%v", err)
				return err
			}
			var openConfig dto.OpenConfig
			openConfigB, err := json.Marshal(openConfig)
			if err != nil {
				global.SysLog.Sugar().Errorf("marshal openConfig failed. err=%v", err)
				return err
			}
			app.OpenConfig = openConfigB
			app.WebCompatibleConfig = webCompatibleConfigByte //web应用兼容性
			app.HealthConfig = healthConfigByte               //健康检查
			err = tx.Model(&app).Create(&app).Error
			if err != nil {
				global.SysLog.Error(err.Error())
				return err
			}

			//更新网关关系表
			var seApp []dto.SeApps
			var cmd model.ApplianceInstall
			db, err := global.GetDBClient(ctx)
			if err != nil {
				return err
			}
			err = db.Where("name= ? and expire_time > ?", v[common.ImportAppGateway], time.Now()).Find(&cmd).Error //根据网关名查询网关ID
			if err != nil || errors.Is(err, gorm.ErrRecordNotFound) {
				global.SysLog.Error(err.Error())
				return err
			}
			gateWayId := cmd.ApplianceID
			seApp = append(seApp, dto.SeApps{SeId: strconv.FormatUint(gateWayId, 10), AppType: constants.GatewayAppType})

			err = updateSeApp(tx, seApp, id)
			if err != nil {
				global.SysLog.Error(err.Error())
				return err
			}
		}
		return nil
	})

	return uint64(insetRow), nil
}

func (u *applicationRepository) AddApplication(ctx context.Context, req *dto.ApplicationReq) (uint64, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, err
	}

	id, err := snowflake.Sf.GetId()

	err = db.Transaction(func(tx *gorm.DB) error {
		var app model.Application
		copier.Copy(&app, &req)
		app.AppAddresses = req.AppSites
		app.AppType = constants.TunAppType
		app.ID = id
		app.IconURL = req.IconURL
		app.AppStatus = req.AppStatus

		webCompatibleConfigByte, err := json.Marshal(req.WebCompatibleConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal webCompatibleConfig failed. err=%v", err)
			return err
		}
		HealthConfig, err := json.Marshal(req.HealthConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal HealthConfig failed. err=%v", err)
			return err
		}
		OpenConfig, err := json.Marshal(req.OpenConfig)
		if err != nil {
			global.SysLog.Sugar().Errorf("marshal HealthConfig failed. err=%v", err)
			return err
		}

		app.WebCompatibleConfig = webCompatibleConfigByte
		app.HealthConfig = HealthConfig
		app.OpenConfig = OpenConfig
		// 隧道应用不需要证书，明确设置为 nil
		app.CertificateID = nil

		for i := range req.AppSites {
			req.AppSites[i].AppId = id
		}
		err = tx.Model(&app).Create(&app).Error
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		err = updateSeApp(tx, req.SeApp, id)
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}

		// 同步OAuth2客户端配置
		if err := u.syncOAuth2Client(ctx, &app, req.WebCompatibleConfig); err != nil {
			global.SysLog.Sugar().Errorf("sync oauth2 client failed. err=%v", err)
			return err
		}

		return nil
	})
	if err != nil {
		return 0, err
	}
	// 添加清理缓存
	if err := u.ResetAppCache(ctx); err != nil {
		global.SysLog.Error("reset app cache failed", zap.Error(err))
	}
	return id, nil
}

func (u *applicationRepository) AppCountByName(ctx context.Context, appName string, id uint64) (int64, error) {
	var count int64
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return count, err
	}
	db = db.Model(&model.Application{}).Where("app_name = ?", appName)
	if id != 0 {
		db = db.Where("id != ?", id)
	}
	err = db.Count(&count).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("find app by name err:", zap.Error(err))
		return count, err
	}
	return count, nil
}

func (u *applicationRepository) UpdateGroupSort(ctx context.Context, groups []dto.GroupSort) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		for _, group := range groups {
			if err := tx.Model(&model.AppGroup{}).
				Where("id = ?", group.ID).
				Update("sort_order", group.Order).Error; err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		global.SysLog.Error("update group sort failed", zap.Error(err))
		return err
	}
	return nil
}

func (a *applicationRepository) MoveGroup(ctx context.Context, req dto.GroupMoveReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	return db.Transaction(func(tx *gorm.DB) error {
		// 获取全部分组并排序
		var groups []model.AppGroup
		if err := tx.Order("sort_order asc").Find(&groups).Error; err != nil {
			return err
		}

		// 查找目标索引
		var (
			currentIndex = -1
			targetIndex  = -1
		)
		for i, g := range groups {
			if g.ID == req.GroupID {
				currentIndex = i
			}
			if g.ID == req.TargetID {
				targetIndex = i
			}
		}

		// 校验参数有效性
		if currentIndex == -1 {
			return errors.New("当前分组不存在")
		}
		if req.TargetID != 0 && targetIndex == -1 {
			return errors.New("目标分组不存在")
		}

		// 从原位置移除
		movingGroup := groups[currentIndex]
		newGroups := append(groups[:currentIndex], groups[currentIndex+1:]...)

		// 计算新位置
		switch req.Position {
		case "first":
			newGroups = append([]model.AppGroup{movingGroup}, newGroups...)
		case "last":
			newGroups = append(newGroups, movingGroup)
		case "before":
			if targetIndex > currentIndex {
				targetIndex-- // 因为已经移除了当前元素
			}
			newGroups = append(newGroups[:targetIndex], append([]model.AppGroup{movingGroup}, newGroups[targetIndex:]...)...)
		case "after":
			if targetIndex > currentIndex {
				targetIndex-- // 因为已经移除了当前元素
			}
			newGroups = append(newGroups[:targetIndex+1], append([]model.AppGroup{movingGroup}, newGroups[targetIndex+1:]...)...)
		default:
			return errors.New("无效的位置参数")
		}

		// 重新生成排序值（间隔100）
		for i := range newGroups {
			newGroups[i].SortOrder = (i + 1) * 100
		}

		// 批量更新数据库
		return tx.Session(&gorm.Session{FullSaveAssociations: true}).Save(newGroups).Error
	})
}

func (u *applicationRepository) GetApplicationByCategory(ctx context.Context, userId string) ([]dto.CategoryApplications, error) {
	// 获取应用列表
	apps, err := u.GetMyApp(ctx, userId)
	if err != nil {
		return nil, err
	}

	// 获取所有组ID对应的组名
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	var groups []model.AppGroup
	if err := db.Order("sort_order").Find(&groups).Error; err != nil {
		return nil, err
	}

	// 创建组ID到组名的映射和排序信息
	groupMap := make(map[int64]string)
	groupOrder := make(map[string]int)
	for _, group := range groups {
		groupMap[int64(group.ID)] = group.GroupName
		groupOrder[group.GroupName] = group.SortOrder
	}

	// 按分类分组
	categorizedApps := make(map[string][]dto.AppBasicInfo)
	for _, app := range apps {
		var openConfig dto.OpenConfig

		// 处理空的或无效的OpenConfig JSON数据
		if len(app.OpenConfig) == 0 {
			// 如果OpenConfig为空，使用默认配置
			openConfig = dto.OpenConfig{
				Enable: true, // 默认启用
			}
			// global.SysLog.Warn("应用OpenConfig为空，使用默认配置", zap.String("appId", fmt.Sprintf("%d", app.ID)), zap.String("appName", app.AppName))
		} else {
			err := json.Unmarshal(app.OpenConfig, &openConfig)
			if err != nil {
				// JSON解析失败时，使用默认配置并记录警告日志，但不中断整个流程
				openConfig = dto.OpenConfig{
					Enable: true, // 默认启用
				}
				// global.SysLog.Warn("解析应用开放配置JSON失败，使用默认配置", zap.Error(err), zap.String("appId", fmt.Sprintf("%d", app.ID)), zap.String("appName", app.AppName), zap.ByteString("openConfig", app.OpenConfig))
			}
		}
		basicInfo := dto.AppBasicInfo{
			Name:            app.AppName,
			OpenConfig:      openConfig,
			AppType:         app.AppType,
			AppStatus:       app.AppStatus,
			Icon:            app.IconURL,
			WebUrl:          app.WebUrl,
			AppAddresses:    app.AppAddresses,
			ServerAddress:   app.ServerAddress,
			ID:              fmt.Sprintf("%d", app.ID), // 转换为字符串以避免JavaScript精度丢失
			PortalShowName:  app.PortalShowName,
			PortalDesc:      app.PortalDesc,
			FormFillEnabled: app.FormFillEnabled,
		}

		// 处理每个分组ID
		for _, groupId := range app.GroupIds {
			groupName := groupMap[groupId]
			if groupName == "" {
				groupName = "默认分类"
			}
			basicInfo.GroupName = groupName
			categorizedApps[groupName] = append(categorizedApps[groupName], basicInfo)
		}
	}

	// 构建返回结果
	var result []dto.CategoryApplications
	for category, apps := range categorizedApps {
		result = append(result, dto.CategoryApplications{
			Category: category,
			Apps:     apps,
		})
	}

	// 按分组排序顺序排序
	sort.Slice(result, func(i, j int) bool {
		orderI := groupOrder[result[i].Category]
		orderJ := groupOrder[result[j].Category]
		return orderI < orderJ
	})

	return result, nil
}

// containsString 检查字符串数组是否包含指定字符串
func containsString(arr []string, str string) bool {
	for _, s := range arr {
		if s == str {
			return true
		}
	}
	return false
}

// syncOAuth2Client 同步OAuth2客户端配置
func (u *applicationRepository) syncOAuth2Client(ctx context.Context, app *model.Application, config dto.CompatibleConfig) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	// 检查是否启用了OAuth2单点登录
	if !containsString(config.DefaultRule, "single_sign_on") || config.SingleSignOn.Type != "oauth2" {
		// 未启用OAuth2，删除可能存在的客户端记录
		return u.deleteOAuth2ClientByAppName(ctx, db, app.AppName)
	}

	oauth2Config := config.SingleSignOn.Config
	if oauth2Config.Appid == "" || oauth2Config.AppSecret == "" {
		// OAuth2配置不完整，删除可能存在的客户端记录
		return u.deleteOAuth2ClientByAppName(ctx, db, app.AppName)

	}

	// 处理回调地址：确保正确保存多个回调地址（换行符分隔）
	redirectURI := strings.TrimSpace(oauth2Config.Callback)
	if redirectURI == "" {
		// 回调地址为空，删除客户端记录
		return u.deleteOAuth2ClientByAppName(ctx, db, app.AppName)

	}

	// 验证并清理回调地址格式
	validatedCallbacks, err := u.validateAndNormalizeCallbacks(redirectURI)
	if err != nil {
		global.SysLog.Sugar().Errorf("回调地址格式验证失败: %v", err)
		return u.deleteOAuth2ClientByAppName(ctx, db, app.AppName)
	}

	// 构建OAuth2客户端记录
	appID := int64(app.ID)
	oauth2Client := model.OAuth2Client{
		CorpID:       int64(app.CorpID),
		AppID:        &appID,
		ClientID:     oauth2Config.Appid,
		ClientSecret: oauth2Config.AppSecret,
		Name:         app.AppName,
		RedirectURI:  validatedCallbacks,     // 使用验证后的回调地址
		Scope:        "openid profile email", // 默认scope
		Status:       1,                      // 启用状态
		UpdatedAt:    time.Now(),
	}

	// 检查客户端是否已存在
	var existing model.OAuth2Client
	err = db.Where("client_id = ?", oauth2Config.Appid).First(&existing).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if existing.ID > 0 {
		// 更新现有记录
		oauth2Client.ID = existing.ID
		oauth2Client.CreatedAt = existing.CreatedAt
		return db.Save(&oauth2Client).Error
	} else {
		// 创建新记录
		oauth2Client.CreatedAt = time.Now()
		return db.Create(&oauth2Client).Error
	}
}

// validateAndNormalizeCallbacks 验证并规范化回调地址
func (u *applicationRepository) validateAndNormalizeCallbacks(callbackStr string) (string, error) {
	if callbackStr == "" {
		return "", fmt.Errorf("回调地址不能为空")
	}

	// 按换行符分割并清理空行
	lines := strings.Split(callbackStr, "\n")
	var validCallbacks []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 验证URL格式
		if _, err := url.Parse(line); err != nil {
			return "", fmt.Errorf("无效的回调地址格式: %s", line)
		}

		// 检查协议
		if !strings.HasPrefix(line, "http://") && !strings.HasPrefix(line, "https://") {
			return "", fmt.Errorf("回调地址必须以http://或https://开头: %s", line)
		}

		validCallbacks = append(validCallbacks, line)
	}

	if len(validCallbacks) == 0 {
		return "", fmt.Errorf("没有有效的回调地址")
	}

	// 检查总长度是否超过数据库字段限制（255字符）
	result := strings.Join(validCallbacks, "\n")
	if len(result) > 255 {
		return "", fmt.Errorf("回调地址总长度超过255字符限制，当前长度: %d", len(result))
	}

	return result, nil
}

// deleteOAuth2ClientByAppName 根据应用名称删除OAuth2客户端记录
func (u *applicationRepository) deleteOAuth2ClientByAppName(ctx context.Context, db *gorm.DB, appName string) error {
	// 删除该应用对应的OAuth2客户端记录
	return db.Where("name = ?", appName).Delete(&model.OAuth2Client{}).Error
}

// shouldDeleteOAuth2Client 判断应用是否需要删除OAuth2客户端记录
func (u *applicationRepository) shouldDeleteOAuth2Client(app model.Application) bool {
	// 没有web兼容配置，不需要删除
	if app.WebCompatibleConfig == nil {
		return false
	}

	// 尝试解析配置
	var webConfig dto.CompatibleConfig
	if err := json.Unmarshal(app.WebCompatibleConfig, &webConfig); err != nil {
		// 配置解析失败，不删除（保守策略）
		return false
	}

	// 满足任一条件就需要删除：启用了单点登录规则 或 配置了OAuth2类型
	return containsString(webConfig.DefaultRule, "single_sign_on") || webConfig.SingleSignOn.Type == "oauth2"
}

// BindDomainToCertificate 绑定域名到证书，如果certificateIds为空则解绑证书
func (u *applicationRepository) BindDomainToCertificate(ctx context.Context, domain string, certificateIds []string, appId uint64) error {
	if domain == "" {
		return fmt.Errorf("domain cannot be empty")
	}

	// 去除域名中的端口号
	if strings.Contains(domain, ":") {
		domain = strings.Split(domain, ":")[0]
	}

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	// 如果证书ID列表为空，则解绑证书
	if len(certificateIds) == 0 {
		// 解绑操作：需要找到当前应用绑定的证书，并从其domain列表中移除域名
		if appId > 0 {
			// 先获取当前应用绑定的证书ID，使用指针类型处理NULL值
			var currentCertId *string
			err = db.Table("tb_application").Select("certificate_id").Where("id = ?", appId).Scan(&currentCertId).Error
			if err != nil {
				global.SysLog.Sugar().Errorf("get current certificate_id failed, app_id: %d, err: %v", appId, err)
				return err
			}

			// 如果当前有绑定证书，从证书的domain列表中移除域名
			if currentCertId != nil && *currentCertId != "" {
				// 获取当前证书的domain字段
				var cert struct {
					Domain pq.StringArray `gorm:"type:varchar[]"`
				}
				err = db.Table("tb_certificate").Select("domain").Where("id = ?", *currentCertId).Scan(&cert).Error
				if err != nil {
					global.SysLog.Sugar().Errorf("get certificate domain failed for unbinding, cert_id: %s, err: %v", *currentCertId, err)
				} else {
					// 从domain列表中移除当前域名
					var newDomains []string
					for _, existingDomain := range cert.Domain {
						if existingDomain != domain {
							newDomains = append(newDomains, existingDomain)
						}
					}

					// 更新证书的domain字段
					err = db.Table("tb_certificate").Where("id = ?", *currentCertId).Update("domain", pq.Array(newDomains)).Error
					if err != nil {
						global.SysLog.Sugar().Errorf("remove domain from certificate failed, cert_id: %s, domain: %s, err: %v", *currentCertId, domain, err)
					} else {
						global.SysLog.Sugar().Infof("successfully removed domain %s from certificate %s", domain, *currentCertId)
					}
				}
			}

			// 将应用的证书ID设置为NULL
			err = db.Table("tb_application").Where("id = ?", appId).Update("certificate_id", nil).Error
			if err != nil {
				global.SysLog.Sugar().Errorf("clear application certificate_id failed, app_id: %d, err: %v", appId, err)
				return err
			}
			global.SysLog.Sugar().Infof("successfully cleared certificate binding for application %d", appId)
		}
		return nil
	}

	// 绑定操作：对每个证书ID进行域名绑定
	for _, certId := range certificateIds {
		if certId == "" {
			global.SysLog.Sugar().Errorf("empty certificate ID")
			continue
		}

		// 获取当前证书的domain字段
		var cert struct {
			Domain pq.StringArray `gorm:"type:varchar[]"`
		}
		err = db.Table("tb_certificate").Select("domain").Where("id = ?", certId).Scan(&cert).Error
		if err != nil {
			global.SysLog.Sugar().Errorf("get certificate domain failed, cert_id: %s, err: %v", certId, err)
			continue
		}

		// 检查域名是否已存在，避免重复
		domainExists := false
		for _, existingDomain := range cert.Domain {
			if existingDomain == domain {
				domainExists = true
				break
			}
		}

		// 如果域名不存在，则添加
		if !domainExists {
			newDomains := append(cert.Domain, domain)
			err = db.Table("tb_certificate").Where("id = ?", certId).Update("domain", pq.Array(newDomains)).Error
			if err != nil {
				global.SysLog.Sugar().Errorf("bind domain to certificate failed, cert_id: %s, domain: %s, err: %v", certId, domain, err)
				return err
			}
			global.SysLog.Sugar().Infof("successfully bound domain %s to certificate %s", domain, certId)
		}

		// 更新应用表中的证书ID（只处理第一个证书ID，因为一个应用只能关联一个证书）
		if appId > 0 {
			err = db.Table("tb_application").Where("id = ?", appId).Update("certificate_id", certId).Error
			if err != nil {
				global.SysLog.Sugar().Errorf("update application certificate_id failed, app_id: %d, cert_id: %s, err: %v", appId, certId, err)
				return err
			}
			global.SysLog.Sugar().Infof("successfully updated application %d with certificate %s", appId, certId)
			// 只更新一次，因为一个应用只能关联一个证书
			break
		}
	}

	return nil
}
