package dto

import (
	"asdsec.com/asec/platform/pkg/model"
	"time"
)

type GetEventListRsp struct {
	model.CommonPage
	EvenList  []model.FileEvents `json:"even_list"` // 事件列表
	UserCount int                `json:"user_count"`
}

type Condition struct {
	UserTags          []CommonCondition `json:"user_tags"`
	SensitiveLevel    []CommonCondition `json:"sensitive_level"`
	SensitiveStrategy []CommonCondition `json:"sensitive_strategy"`
	FileCategoryIds   []CommonCondition `json:"file_category_ids"`
	GetActivity       []CommonCondition `json:"get_activity"`
	UseActivity       []CommonCondition `json:"use_activity"`
	ChannelList       []CommonCondition `json:"channel_list"`
	SourceList        []CommonCondition `json:"source_list"`
}

type CommonCondition struct {
	Id    string `gorm:"column:id" json:"id"`
	Name  string `gorm:"column:name" json:"name"`
	Count int    `gorm:"column:count" json:"count"`
}

type GetHistoryListRsp struct {
	Id         string    `gorm:"column:id" json:"id"`
	Name       string    `gorm:"column:name;type:varchar;comment:名称" json:"name"`
	Type       string    `gorm:"column:type;type:varchar;comment:类型" json:"type"`
	Condition  string    `gorm:"column:condition;type:varchar;comment:条件" json:"condition"`
	CreateTime time.Time `gorm:"column:create_time;type:timestamptz;comment:创建时间" json:"create_time"`
}

type GetActivityCategoryRsp struct {
	GetActivityList     []string `json:"get_activity_list"`
	UseActivityList     []string `json:"use_activity_list"`
	ChannelActivityList []string `json:"channel_activity_list"`
}

type GetUsersRsp struct {
	model.CommonPage
	UserList   []GetUserItem `json:"user_list"` // 用户视角
	EventCount int           `json:"event_count"`
}
type GetUserItem struct {
	UserId     string   `gorm:"column:user_id" json:"user_id,omitempty"`
	UserName   string   `gorm:"column:user_name" json:"user_name"`
	UserNames  []string `gorm:"column:user_names;type:[]varchar;" json:"-"`
	UserPath   string   `gorm:"-" json:"user_path"`
	TotalTags  []string `gorm:"column:total_tags;type:[]varchar;comment:用户标签" json:"total_tags"`
	EventCount int      `gorm:"column:event_count" json:"event_count"`
	UserFocus  bool     `gorm:"column:user_focus" json:"user_focus"`
}
