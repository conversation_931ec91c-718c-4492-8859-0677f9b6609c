package service

import (
	"context"
	"strings"
	"sync"

	"asdsec.com/asec/platform/app/console/app/application/dto"
	repository "asdsec.com/asec/platform/app/console/app/application/repository"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
)

var ApplicationServiceImpl ApplicationService

// ApplicationServiceInit 单例对象
var ApplicationServiceInit sync.Once

type ApplicationService interface {
	GetById(ctx context.Context, id uint64) (dto.ApplicationDetailRsp, error)
	AddApplication(ctx context.Context, req *dto.ApplicationReq) (uint64, error)
	AddApplicationGroup(ctx context.Context, group *model.AppGroup) (uint64, aerrors.AError)
	ListApplicationGroup(ctx context.Context, req dto.GetApplicationGroupListReq) ([]dto.GetAppGroupListRsp, error)
	DeleteApp(ctx context.Context, appID []uint64) error
	DeleteAppGroup(ctx context.Context, id uint64) error
	UpdateApplication(ctx context.Context, req *dto.UpdateApplicationReq) (uint64, error)
	UpdateAppGroup(ctx context.Context, group *model.AppGroup) aerrors.AError
	GetMyApp(ctx context.Context, userId string) ([]model.Application, error)
	GetGroupById(ctx context.Context, id uint64) (model.AppGroup, error)
	Count(ctx context.Context) (dto.ApplicationCount, error)
	AppCountByName(ctx context.Context, appName string, id uint64) (int64, error)
	AddWebApplication(ctx context.Context, req dto.AddWebAppReq) (uint64, aerrors.AError)
	AddPortalApplication(ctx context.Context, req dto.PortalAppReq) (uint64, aerrors.AError)
	ImportApp(ctx context.Context, req [][]string) (uint64, aerrors.AError)
	UpdateWebApplication(ctx context.Context, req dto.AddWebAppReq, id uint64) (uint64, aerrors.AError)
	UpdatePortalApplication(ctx context.Context, req dto.PortalAppReq, id uint64) (uint64, aerrors.AError)
	GetApplicationList(ctx context.Context, req dto.GetApplicationListReq) (dto.GetApplicationListRsp, error)
	AppDownload(ctx context.Context, data []dto.ApplicationGroupItem) ([]dto.AppDownload, error)
	Appliances(ctx context.Context) ([]string, error)
	GetAppIdGroupIds(ctx context.Context, appIds []string) ([]string, error)
	UpdateGroupSort(ctx context.Context, groups []dto.GroupSort) error
	MoveGroup(ctx context.Context, req dto.GroupMoveReq) error
	GetApplicationByCategory(ctx context.Context, userId string) ([]dto.CategoryApplications, error)
	BindDomainToCertificate(ctx context.Context, domain string, certificateIds []string, appId uint64) error
}

type applyService struct {
	db repository.ApplicationRepository
}

func (a *applyService) UpdatePortalApplication(ctx context.Context, req dto.PortalAppReq, id uint64) (uint64, aerrors.AError) {
	return a.db.UpdatePortalApplication(ctx, req, id)
}

func (a *applyService) AddPortalApplication(ctx context.Context, req dto.PortalAppReq) (uint64, aerrors.AError) {
	return a.db.AddPortalApplication(ctx, req)
}

func (a *applyService) ImportApp(ctx context.Context, req [][]string) (uint64, aerrors.AError) {
	var webReq [][]string
	var tunReq [][]string
	var portalReq [][]string
	//循环 req
	for _, v := range req {
		if v[common.ImportAppType] == "WEB应用" {
			webReq = append(webReq, v)
		} else if v[common.ImportAppType] == "门户应用" {
			portalReq = append(portalReq, v)
		} else {
			tunReq = append(tunReq, v)
		}
	}
	var webNum, tunNum, pN uint64
	var err aerrors.AError
	if len(webReq) > 0 {
		webNum, err = a.db.ImportWebApp(ctx, webReq)
		if err != nil {
			return webNum, err
		}
	}
	if len(tunReq) > 0 {
		tunNum, err = a.db.ImportTunApp(ctx, tunReq)
		if err != nil {
			return webNum, err
		}
	}
	if len(portalReq) > 0 {
		pN, err = a.db.ImportPortalApp(ctx, portalReq)
		if err != nil {
			return webNum + tunNum, err
		}
	}
	return webNum + tunNum + pN, nil
}

func (a *applyService) Appliances(ctx context.Context) ([]string, error) {
	return a.db.GatewayNames(ctx)
}

func (a *applyService) AppDownload(ctx context.Context, data []dto.ApplicationGroupItem) ([]dto.AppDownload, error) {
	//重新定义一个变量，用于存储数据、返回[]dto.ApplicationGroupItem
	var dataNew []dto.AppDownload
	for i := range data {
		status := "启用"
		if data[i].AppStatus == 3 {
			status = "禁用"
		} else if data[i].AppStatus == 2 {
			status = "维护"
		}
		SdpList := "未发布"
		if len(data[i].SdpList) > 0 {
			SdpList = strings.Join(data[i].SdpList, "、")
		}
		PublishEndpoint := ""
		if data[i].PublishEndpoint != "-" {
			//去除最后的  /
			PublishEndpoint = strings.TrimSuffix(data[i].PublishEndpoint, "/")
		}
		applicationAddress := strings.TrimSuffix(strings.Join(data[i].ApplicationAddress, ","), "/")
		showStatus := "是"
		if data[i].ShowStatus == 0 {
			showStatus = "否"
		}

		smartRewrite := "否"
		if data[i].WebCompatibleConfig.DefaultRule != nil {
			for _, v := range data[i].WebCompatibleConfig.DefaultRule {
				if v == "url_smart_rewrite" {
					smartRewrite = "是"
				}
			}
		}

		AppType := "WEB应用"
		if data[i].AppType == "tun" {
			AppType = "隧道应用"
			dataNew = append(dataNew, dto.AppDownload{
				AppType:        AppType,
				Name:           data[i].Name,
				AppStatus:      status,
				ServerAddress:  applicationAddress,
				ShowStatus:     showStatus,
				PortalShowName: data[i].PortalShowName,
				PortalDesc:     data[i].PortalDesc,
				SdpList:        SdpList,
			})
		} else if data[i].AppType == "portal" {
			AppType = "门户应用"
			dataNew = append(dataNew, dto.AppDownload{
				AppType:        AppType,
				Name:           data[i].Name,
				AppStatus:      status,
				ShowStatus:     showStatus,
				PortalShowName: data[i].PortalShowName,
				PortalDesc:     data[i].PortalDesc,
			})
		} else {
			dataNew = append(dataNew, dto.AppDownload{
				AppType:        AppType,
				Name:           data[i].Name,
				AppStatus:      status,
				ServerAddress:  applicationAddress,
				PublishAddress: PublishEndpoint,
				Uri:            data[i].Uri,
				ShowStatus:     showStatus,
				PortalShowName: data[i].PortalShowName,
				SmartRewrite:   smartRewrite,
				DependSite:     data[i].WebCompatibleConfig.DependSite.Text,
				PortalDesc:     data[i].PortalDesc,
				SdpList:        SdpList,
			})
		}
	}
	return dataNew, nil
}

func (a *applyService) GetAppIdGroupIds(ctx context.Context, appIds []string) ([]string, error) {
	return a.db.GetAppIdGroupIds(ctx, appIds)
}

func (a *applyService) GetApplicationList(ctx context.Context, req dto.GetApplicationListReq) (dto.GetApplicationListRsp, error) {
	return a.db.GetApplicationList(ctx, req)
}

func (a *applyService) UpdateWebApplication(ctx context.Context, req dto.AddWebAppReq, id uint64) (uint64, aerrors.AError) {
	return a.db.UpdateWebApplication(ctx, req, id)
}

func (a *applyService) AddWebApplication(ctx context.Context, req dto.AddWebAppReq) (uint64, aerrors.AError) {
	return a.db.AddWebApplication(ctx, req)
}

func (a *applyService) GetMyApp(ctx context.Context, userId string) ([]model.Application, error) {
	return a.db.GetMyApp(ctx, userId)
}

func (a *applyService) Count(ctx context.Context) (dto.ApplicationCount, error) {
	return a.db.Count(ctx)
}

func (a *applyService) GetGroupById(ctx context.Context, id uint64) (model.AppGroup, error) {
	return a.db.GetGroupById(ctx, id)
}

func (a *applyService) UpdateAppGroup(ctx context.Context, group *model.AppGroup) aerrors.AError {
	return a.db.UpdateAppGroup(ctx, group)
}

func (a *applyService) UpdateApplication(ctx context.Context, req *dto.UpdateApplicationReq) (uint64, error) {
	return a.db.UpdateApplication(ctx, req)
}

func (a *applyService) DeleteAppGroup(ctx context.Context, id uint64) error {
	return a.db.DeleteAppGroupByID(ctx, id)
}

func (a *applyService) DeleteApp(ctx context.Context, appID []uint64) error {
	return a.db.DeleteAppByID(ctx, appID)
}

func (a *applyService) ListApplicationGroup(ctx context.Context, req dto.GetApplicationGroupListReq) ([]dto.GetAppGroupListRsp, error) {
	return a.db.ListApplicationGroup(ctx, req)
}

func (a *applyService) AddApplicationGroup(ctx context.Context, group *model.AppGroup) (uint64, aerrors.AError) {
	return a.db.AddApplicationGroup(ctx, group)
}

func (a *applyService) GetById(ctx context.Context, id uint64) (dto.ApplicationDetailRsp, error) {
	return a.db.GetById(ctx, id)
}

func (a *applyService) AddApplication(ctx context.Context, req *dto.ApplicationReq) (uint64, error) {
	return a.db.AddApplication(ctx, req)
}

func (a *applyService) AppCountByName(ctx context.Context, appName string, id uint64) (int64, error) {
	return a.db.AppCountByName(ctx, appName, id)
}

func (a *applyService) UpdateGroupSort(ctx context.Context, groups []dto.GroupSort) error {
	return a.db.UpdateGroupSort(ctx, groups)
}

func (a *applyService) MoveGroup(ctx context.Context, req dto.GroupMoveReq) error {
	return a.db.MoveGroup(ctx, req)
}

func (a *applyService) GetApplicationByCategory(ctx context.Context, userId string) ([]dto.CategoryApplications, error) {
	return a.db.GetApplicationByCategory(ctx, userId)
}

func (a *applyService) BindDomainToCertificate(ctx context.Context, domain string, certificateIds []string, appId uint64) error {
	return a.db.BindDomainToCertificate(ctx, domain, certificateIds, appId)
}

func GetApplicationService() ApplicationService {
	ApplicationServiceInit.Do(func() {
		ApplicationServiceImpl = &applyService{db: repository.NewAppRepository()}
	})
	return ApplicationServiceImpl
}
