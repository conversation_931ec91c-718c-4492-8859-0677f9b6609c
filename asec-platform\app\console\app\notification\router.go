package notification

import (
	"asdsec.com/asec/platform/app/console/app/notification/api"
	"github.com/gin-gonic/gin"
)

func NotificationApi(r *gin.RouterGroup) {
	v := r.Group("/v1/notification")
	{
		v.POST("/template", api.CreateNotificationTemplate)
		v.POST("/list", api.GetNotificationList)
		v.PUT("/template", api.UpdateNotificationTemplate)
		v.DELETE("/template", api.DeleteNotificationTemplate)
		v.POST("/template/batch_del", api.BatchDeleteNotificationTemplate)
	}
}
