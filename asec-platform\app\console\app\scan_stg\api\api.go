package api

import (
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/app/scan_stg/dto"
	"asdsec.com/asec/platform/app/console/app/scan_stg/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetScanStgDetail godoc
// @Summary 获取策略详情
// @Schemes
// @Description 获取策略详情
// @Tags        risk_setting
// @Produce     application/json
// @Success     200
// @Router      /v1/scan_strategy/detail [GET]
// @success     200 {object} common.Response{} "ok"
func GetScanStgDetail(c *gin.Context) {
	id := c.Query("id")
	data, err := service.GetScanStgService().GetScanStgDetail(c, id)
	if err != nil {
		global.SysLog.Error("GetScanStgDetail err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetScanStgList godoc
// @Summary 获取扫描策略列表
// @Schemes
// @Description 获取扫描策略列表
// @Tags        risk_setting_list
// @Produce     application/json
// @Success     200
// @Router      /v1/scan_strategy/list [GET]
// @success     200 {object} common.Response{} "ok"
func GetScanStgList(c *gin.Context) {
	var req dto.GetCommonListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetScanStgService().GetScanStgList(c, req)
	if err != nil {
		global.SysLog.Error("GetScanStgList err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// CreateScanStg godoc
// @Summary 新增扫描策略
// @Schemes
// @Description 新增扫描策略
// @Tags        Application
// @Produce     application/json
// @Param       req body dto.CreateScanStgReq true "新增扫描策略"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/risk/scan_strategy [POST]
// @success     200 {object} common.Response{} "ok"
func CreateScanStg(c *gin.Context) {
	var req dto.CreateScanStgReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.ScanStrategyTYpe,
			OperationType:  common.OperateCreate,
			Representation: req.StrategyName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aeErr := service.GetScanStgService().CreateScanStg(c, req)
	if aeErr != nil {
		common.FailAError(c, aeErr)
		return
	}
	common.Ok(c)
}

// UpdateScanStg godoc
// @Summary 编辑扫描策略
// @Schemes
// @Description 编辑扫描策略
// @Tags        Application
// @Produce     application/json
// @Param       req body dto.CreateScanStgReq true "新增扫描策略"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/risk/scan_strategy [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateScanStg(c *gin.Context) {
	var req dto.UpdateScanStgReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.ScanStrategyTYpe,
			OperationType:  common.OperateUpdate,
			Representation: req.StrategyName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	aeErr := service.GetScanStgService().UpdateScanStg(c, req)
	if aeErr != nil {
		common.FailAError(c, aeErr)
		return
	}
	common.Ok(c)
}

// DeleteScanStg godoc
// @Summary 删除扫描策略
// @Schemes
// @Description 删除扫描策略
// @Tags        Application
// @Produce     application/json
// @Success     200
// @Router      /v1/scan_strategy [DELETE]
// @success     200 {object} common.Response{} "ok"
func DeleteScanStg(c *gin.Context) {
	id := c.Query("id")
	StrategyName := c.Query("name")
	err := service.GetScanStgService().DeleteScanStg(c, id)
	if err != nil {
		common.Fail(c, common.OperateError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.ScanStrategyTYpe,
			OperationType:  common.OperateDelete,
			Representation: StrategyName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	common.Ok(c)
}
