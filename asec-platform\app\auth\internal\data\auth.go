package data

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/pkg/utils"
	"gorm.io/gorm"

	"asdsec.com/asec/platform/app/auth/internal/dto"

	"asdsec.com/asec/platform/app/auth/internal/data/query"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"github.com/go-kratos/kratos/v2/log"
)

type authRepo struct {
	data       *Data
	log        *log.Helper
	licenseId  string
	licenseMux sync.RWMutex
}

func (a authRepo) GetGroupByAdServerAddr(ctx context.Context, serverAddr, entry, username string) (model.TbUserEntity, error) {
	u := query.Use(a.data.db).TbUserEntity
	g := query.Use(a.data.db).TbUserGroup
	idp := query.Use(a.data.db).TbIdentityProviderAttribute
	idpMapper := query.Use(a.data.db).TbIdpGroupMapper
	serverKeyword := "%" + utils.EscapeForLike(serverAddr) + "%"
	user, err := u.WithContext(ctx).
		Join(g, g.ID.EqCol(u.RootGroupID)).Join(idpMapper, g.ID.EqCol(idpMapper.GroupID)).Join(idp, idp.ProviderID.EqCol(idpMapper.ProviderID)).
		Where(idp.Key.Eq("ad_server_address"), idp.Value.Like(serverKeyword), u.DisplayName.Eq(username)).
		First()
	if err != nil {
		return model.TbUserEntity{}, err
	}
	return *user, nil
}

func (a authRepo) GetAdminEcdsaPublicKey(ctx context.Context) (dto.PublicKey, error) {
	var res string
	err := a.data.db.Table("tb_admin_component_config").Select("value").Where("name = ?", "ecdsaPublicKey").Find(&res).Error
	if err != nil {
		return dto.PublicKey{}, err
	}
	return dto.PublicKey{PublicKey: res}, err
}

func (a authRepo) QuerySecondaryAuth(ctx context.Context, policies []string) ([]*model.TbIdentityProvider, error) {
	apim := query.Use(a.data.db).TbAuthPolicyIdpMapper
	idp := query.Use(a.data.db).TbIdentityProvider
	return idp.WithContext(ctx).LeftJoin(apim, apim.IdpID.EqCol(idp.ID)).
		Where(apim.PolicyID.In(policies...), idp.Type.In(dto.AssistIDPType...)).
		Select(idp.ALL).Group(idp.ID).Find()
}

func (a authRepo) Set(c context.Context, key string, value string, expireTime time.Duration) error {
	err := a.data.rdb.Set(c, key, value, expireTime).Err()
	return err
}

func (a authRepo) SAdd(c context.Context, key string, value string) error {
	err := a.data.rdb.SAdd(c, key, value).Err()
	return err
}

func (a authRepo) GetSet(c context.Context, key string, value string) error {
	err := a.data.rdb.GetSet(c, key, value).Err()
	return err
}

func (a authRepo) HSet(c context.Context, key string, values ...interface{}) error {
	err := a.data.rdb.HSet(c, key, values).Err()
	return err
}

func (a authRepo) Get(c context.Context, key string) (string, error) {
	result, err := a.data.rdb.Get(c, key).Result()
	return result, err
}

func (a authRepo) SMembers(c context.Context, key string) ([]string, error) {
	result, err := a.data.rdb.SMembers(c, key).Result()
	return result, err
}

func (a authRepo) SPop(c context.Context, key string) error {
	err := a.data.rdb.SPop(c, key).Err()
	return err
}

func (a authRepo) SRem(c context.Context, key string, item []string) error {
	err := a.data.rdb.SRem(c, key, item).Err()
	return err
}

func (a authRepo) Del(c context.Context, key string) error {
	err := a.data.rdb.Del(c, key).Err()
	return err
}

func (a authRepo) LPop(c context.Context, key string) error {
	err := a.data.rdb.LPop(c, key).Err()
	return err
}

func (a authRepo) RPush(c context.Context, key string, value string) error {
	err := a.data.rdb.RPush(c, key, value).Err()
	return err
}

func (a authRepo) LRem(c context.Context, key string, count int64, value string) error {
	err := a.data.rdb.LRem(c, key, count, value).Err()

	return err
}

func (a authRepo) LLen(c context.Context, key string) (int64, error) {
	result, err := a.data.rdb.LLen(c, key).Result()
	return result, err
}

func (a authRepo) LRange(c context.Context, key string, startIndex, endIndex int64) ([]string, error) {
	result, err := a.data.rdb.LRange(c, key, startIndex, endIndex).Result()
	return result, err
}

func (a authRepo) SetNX(ctx context.Context, key string, val interface{}, ttl time.Duration) error {
	return a.data.rdb.SetNX(ctx, key, val, ttl).Err()
}

func (a authRepo) GetStringV(ctx context.Context, key string) (string, error) {
	res, err := a.data.rdb.Get(ctx, key).Result()
	return res, err
}

func (a authRepo) ExpireNX(ctx context.Context, key string, ttl time.Duration) (bool, error) {
	return a.data.rdb.ExpireNX(ctx, key, ttl).Result()
}

func (a authRepo) Expire(ctx context.Context, key string, ttl time.Duration) (bool, error) {
	return a.data.rdb.Expire(ctx, key, ttl).Result()
}

func (a authRepo) GetEcdsaPublicKeyByCorpId(ctx context.Context, corpId string) (dto.PublicKey, error) {
	q := query.Use(a.data.db)
	c := q.TbComponent
	cc := q.TbComponentConfig
	var result dto.PublicKey
	err := c.WithContext(ctx).LeftJoin(cc, cc.ComponentID.EqCol(c.ID)).
		Where(cc.Name.Eq("ecdsaPublicKey"), c.CorpID.Eq(corpId)).
		Select(cc.Value.As("PublicKey"), c.CorpID.As("CorpId")).Scan(&result)
	return result, err
}

func (a authRepo) GetEcdsaPublicKeys(ctx context.Context) ([]dto.PublicKey, error) {
	q := query.Use(a.data.db)
	c := q.TbComponent
	cc := q.TbComponentConfig
	var result []dto.PublicKey
	err := c.WithContext(ctx).LeftJoin(cc, cc.ComponentID.EqCol(c.ID)).
		Where(cc.Name.Eq("ecdsaPublicKey")).
		Select(cc.Value.As("PublicKey"), c.CorpID.As("CorpId")).Scan(&result)
	return result, err
}

func (a authRepo) GetEcdsaPrivateKey(ctx context.Context, corpId string) (string, error) {
	q := query.Use(a.data.db)
	c := q.TbComponent
	cc := q.TbComponentConfig
	var result string
	err := c.WithContext(ctx).LeftJoin(cc, cc.ComponentID.EqCol(c.ID)).
		Where(c.CorpID.Eq(corpId), cc.Name.Eq("ecdsaPrivateKey")).
		Select(cc.Value).Scan(&result)
	return result, err
}

func (a authRepo) GenerateAuthCode(ctx context.Context, user model.TbUserEntity) (code string, err error) {
	rdb := a.data.rdb
	authCode, err := common.GenerateUniqueAuthCode(ctx, rdb)
	if err != nil {
		return "", err
	}
	value, err := json.Marshal(user)
	if err != nil {
		return "", err
	}
	if err = rdb.Set(ctx, authCode, value, common.AuthCodeExpires).Err(); err != nil {
		return "", err
	}
	return authCode, nil
}

func (a authRepo) GetUserCache(ctx context.Context, key string) (model.TbUserEntity, error) {
	var value []byte
	if err := a.data.rdb.Get(ctx, key).Scan(&value); err != nil {
		return model.TbUserEntity{}, err
	}

	var result model.TbUserEntity
	if err := json.Unmarshal(value, &result); err != nil {
		return model.TbUserEntity{}, err
	}
	return result, nil
}

func (a authRepo) QueryUserByName(ctx context.Context, corpId, name string) (*model.TbUserEntity, error) {
	ue := query.Use(a.data.db).TbUserEntity
	result, err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.Name.Eq(name)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserEntity{}, pb.ErrorRecordNotFound("name=%v", name)
		}
		return &model.TbUserEntity{}, err
	}
	return result, nil
}

func (a authRepo) CreateLoginLog(ctx context.Context, param model.TbUserLoginLog) error {
	loginLog := query.Use(a.data.db).TbUserLoginLog
	return loginLog.WithContext(ctx).Create(&param)
}

func (a authRepo) GetSpecialConfig(key string) string {
	return a.data.specialConfig[key]
}

func (a authRepo) CheckUserIdExist(ctx context.Context, userId string, userType string) (bool, error) {
	var count int64
	var err error
	if userType == dto.AdminAuthType {
		err = a.data.db.Table("tb_admin_entity").Where("id = ?", userId).Count(&count).Error
		if err != nil {
			return false, err
		}
	} else {
		userT := query.Use(a.data.db).TbUserEntity
		count, err = userT.WithContext(ctx).Where(userT.ID.Eq(userId)).Count()
		if err != nil {
			return false, err
		}
	}
	return count > 0, err
}

func (a *authRepo) GetLicenseId(ctx context.Context) (string, error) {
	a.licenseMux.RLock()
	if a.licenseId != "" {
		a.licenseMux.RUnlock()
		return a.licenseId, nil
	}
	a.licenseMux.RUnlock()

	a.licenseMux.Lock()
	defer a.licenseMux.Unlock()
	// Double check
	if a.licenseId != "" {
		return a.licenseId, nil
	}

	var licenseId string
	if err := a.data.db.WithContext(ctx).Table("tb_lis_unique_identifiers").Select("uuid").Scan(&licenseId).Error; err != nil {
		return "", fmt.Errorf("查询第一行的 UUID 失败: %w", err)
	}
	if len(licenseId) != 32 {
		if len(licenseId) < 32 {
			// 填充到32字节
			licenseId = licenseId + string(make([]byte, 32-len(licenseId)))
		} else {
			// 截断到32字节
			licenseId = licenseId[:32]
		}
		a.log.Warnf("License ID长度不是32字节，已调整，原长度：%d，调整后长度：%d，licenseId：%s", len(licenseId), len(licenseId), licenseId)
	}
	a.licenseId = licenseId
	return a.licenseId, nil
}

func NewAuthRepo(data *Data, logger log.Logger) biz.AuthRepo {
	return &authRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
