package repository

import (
	riskC "asdsec.com/asec/platform/app/console/app/risk_setting/constants"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/common"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/constants"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"encoding/json"
	"fmt"
	"github.com/jackc/pgconn"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"
	"time"
)

type SensitiveCategoryRepository interface {
	GetSensitiveCategoryList(ctx context.Context, stgName string) ([]model.GetSensitiveCategoryListRsp, error)
	GetSensitiveCategoryByName(ctx context.Context, name string) (model.SensitiveCategory, error)
	UpdateSensitiveCategory(ctx context.Context, req model.UpdateSensitiveCategoryReq) error
	AddSensitiveCategory(ctx context.Context, req model.AddSensitiveCategoryReq) error
	DeleteSensitiveCategory(ctx context.Context, req model.DelSensitiveCategoryReq) error
	CheckDelCondition(ctx context.Context, id string, delType int) (bool, error)
	GetCategoryCondition(ctx context.Context, startTime time.Time, endTime time.Time) ([]model.GetSensitiveCategoryConditionRsp, error)
	GetStgByIds(ctx context.Context, ids []string) ([]model.SensitiveStrategyDB, error)
	CreateCtgAndStgBatch(ctx context.Context, categoryList []model.SensitiveCategory, strategyList map[string][]model.SensitiveStrategyDB) error
}

// NewScRepository 创建接口实现接口实现
func NewScRepository() SensitiveCategoryRepository {
	return &sensitiveCategoryRepository{}
}

type sensitiveCategoryRepository struct {
}

func (s sensitiveCategoryRepository) CreateCtgAndStgBatch(ctx context.Context, categoryList []model.SensitiveCategory, strategyList map[string][]model.SensitiveStrategyDB) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	for i := 0; i < 10000; i++ {
		err = db.Transaction(func(tx *gorm.DB) error {
			err = tx.Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "name"}, {Name: "built_in"}},
				DoNothing: true,
			}).Create(&categoryList).Error
			if err != nil {
				return err
			}
			stgReal := make([]model.SensitiveStrategyDB, 0)
			ddrScore := make([]modelTable.RiskScoreConfig, 0)
			confReqList := make([]conf_center.ConfChangeReq, 0)
			for cName, stgList := range strategyList {
				var cId string
				err = tx.Model(model.SensitiveCategory{}).Select("id").Where("name = ?", cName).Find(&cId).Error
				if err != nil {
					return err
				}
				for _, v := range stgList {
					var item model.SensitiveStrategyDB
					copier.Copy(&item, &v)
					id, err := snowflake.Sf.GetId()
					if err != nil {
						global.SysLog.Sugar().Errorf("generate snow flake id failed. err=%v", err)
						return err
					}
					if i > 0 {
						item.RuleName = fmt.Sprintf("%s(%d)", item.RuleName, i)
					}
					item.Id = strconv.FormatUint(id, 10)
					item.CategoryId = cId
					item.BuiltIn = constants.CustomBuiltInType
					item.CreateAt = time.Now()
					ddrScore = append(ddrScore, modelTable.RiskScoreConfig{
						TypeName:         riskC.DataScoreIndicatorTypeName,
						Indicator:        item.Id,
						IndicatorType:    1,
						IndicatorSubType: 1,
						Score:            int(item.SensitiveLevel),
					})
					stgReal = append(stgReal, item)
					var fileNameRule []common.SensitiveStrategyFileNameRule
					err = json.Unmarshal(item.FileNameRule, &fileNameRule)
					if err != nil {
						return err
					}

					var contentRule []common.SensitiveStrategyFileNameRule
					err = json.Unmarshal(item.ContentRule, &contentRule)
					if err != nil {
						return err
					}
					confReq, err := strategyToConfReq(item, fileNameRule, contentRule, tx, conf_center.AddConf)
					if err != nil {
						return err
					}
					confReqList = append(confReqList, confReq)
				}
			}
			err = tx.Create(&ddrScore).Error
			if err != nil {
				return err
			}
			err = tx.Create(&stgReal).Error
			if err != nil {
				return err
			}
			for _, v := range confReqList {
				err = conf_center.ConfChange(v)
				if err != nil {
					return err
				}
			}
			return err
		})

		if err == nil {
			break
		}
		pgErr, ok := err.(*pgconn.PgError)
		if ok && pgErr.Code == "23505" {
			continue
		}
		return err
	}
	return err
}

func (s sensitiveCategoryRepository) GetStgByIds(ctx context.Context, ids []string) ([]model.SensitiveStrategyDB, error) {
	var res []model.SensitiveStrategyDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return res, err
	}
	err = db.Model(model.SensitiveStrategyDB{}).Where("id in ?", ids).Find(&res).Error
	return res, nil
}
func (s sensitiveCategoryRepository) GetCategoryCondition(ctx context.Context, startTime time.Time, endTime time.Time) ([]model.GetSensitiveCategoryConditionRsp, error) {
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return nil, err
	}
	var sensitiveRuleIds []model.AlertEventCountRsp
	err = db.Model(&modelTable.AlertEvent{}).Select("sensitive_rule_id,count(uuid) as count").
		Where("occur_time >= ? AND occur_time <= ?", startTime.Unix(), endTime.Unix()).
		Group("sensitive_rule_id").Find(&sensitiveRuleIds).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get alert event failed. err=%v", err)
		return nil, err
	}
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	countMap := make(map[string]int)
	var sgIdList []string
	for _, v := range sensitiveRuleIds {
		countMap[v.SensitiveRuleId] = v.Count
		sgIdList = append(sgIdList, v.SensitiveRuleId)
	}
	var sgCategoryRs []model.SgCategoryRelation
	err = pgDb.Model(model.SensitiveCategory{}).
		Select("tb_sensitive_category.id as category_id,tb_sensitive_category.name as category_name,tb_sensitive_strategy.id as sensitive_rule_id,tb_sensitive_strategy.rule_name,tb_sensitive_strategy.sensitive_level").
		Joins("INNER JOIN tb_sensitive_strategy on tb_sensitive_strategy.category_id = tb_sensitive_category.id ").
		Where("tb_sensitive_strategy.id in ? AND tb_sensitive_strategy.built_in = ?", sgIdList, constants.CustomBuiltInType).Find(&sgCategoryRs).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get sensitive strategy category relationship failed. err=%v", err)
		return nil, err
	}
	retMap := make(map[string][]model.SensitiveStrategyItem)
	categoryNameMap := make(map[string]string)
	for _, v := range sgCategoryRs {
		value := retMap[v.CategoryId]
		categoryCount := countMap[v.CategoryId]
		value = append(value, model.SensitiveStrategyItem{
			Id:    v.SensitiveRuleId,
			Name:  v.RuleName,
			Level: v.SensitiveLevel,
			Count: countMap[v.SensitiveRuleId],
		})
		countMap[v.CategoryId] = categoryCount + countMap[v.SensitiveRuleId]
		retMap[v.CategoryId] = value
		categoryNameMap[v.CategoryId] = v.CategoryName
	}
	var ret []model.GetSensitiveCategoryConditionRsp
	for key, value := range retMap {
		temp := model.GetSensitiveCategoryConditionRsp{
			Id:                key,
			SensitiveStrategy: value,
			Name:              categoryNameMap[key],
			Count:             countMap[key],
		}
		ret = append(ret, temp)
	}
	return ret, nil
}

func (s sensitiveCategoryRepository) GetSensitiveCategoryByName(ctx context.Context, name string) (model.SensitiveCategory, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model.SensitiveCategory{}, err
	}
	var data model.SensitiveCategory
	err = db.Model(model.SensitiveCategory{}).Where("name = ? and built_in = ?", name, constants.CategoryBuiltInType).Find(&data).Error
	return data, err
}

func (s sensitiveCategoryRepository) GetSensitiveCategoryList(ctx context.Context, stgName string) ([]model.GetSensitiveCategoryListRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	if stgName != "" {
		keyword := "%" + dbutil.EscapeForLike(stgName) + "%"
		db = db.Where("tb_sensitive_strategy.rule_name like ?", keyword)
	}
	var ret []model.GetSensitiveCategoryListRsp
	err = db.Model(model.SensitiveCategory{}).
		Select("tb_sensitive_category.id,tb_sensitive_category.name,COUNT(tb_sensitive_strategy.id) AS count,tb_sensitive_category.is_default,icon_code").
		Joins("LEFT JOIN tb_sensitive_strategy on tb_sensitive_strategy.category_id = tb_sensitive_category.id  AND tb_sensitive_strategy.built_in = ?", constants.CustomBuiltInType).
		Group("tb_sensitive_category.id").
		Where("tb_sensitive_category.built_in = ? ", constants.CategoryBuiltInType).
		Order("tb_sensitive_category.created_at desc").Find(&ret).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get sensitive category list failed. err=%v", err)
		return nil, err
	}
	return ret, nil
}

func (s sensitiveCategoryRepository) UpdateSensitiveCategory(ctx context.Context, req model.UpdateSensitiveCategoryReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(model.SensitiveCategory{}).Where("id = ?", req.Id).Updates(model.SensitiveCategory{Id: req.Id, Name: req.Name, UpdatedAt: time.Now()}).Error
}

func (s sensitiveCategoryRepository) AddSensitiveCategory(ctx context.Context, req model.AddSensitiveCategoryReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	id, err := snowflake.Sf.GetId()
	if err != nil {
		global.SysLog.Sugar().Errorf("generate snow flake id failed. err=%v", err)
		return err
	}
	iconCode := req.IconCode
	if iconCode == "" {
		iconCode = constants.CustomIconCode
	}
	idStr := strconv.FormatUint(id, 10)
	err = db.Model(model.SensitiveCategory{}).Create(&model.SensitiveCategory{
		Id:       idStr,
		Name:     req.Name,
		IconCode: iconCode,
		BuiltIn:  constants.CategoryBuiltInType,
	}).Error
	return err
}

func (s sensitiveCategoryRepository) DeleteSensitiveCategory(ctx context.Context, req model.DelSensitiveCategoryReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	var data model.SensitiveCategory
	err = db.Model(model.SensitiveCategory{}).Where("id = ?", req.Id).Find(&data).Error
	if err != nil {
		return err
	}
	// 未分类组不让删除，并且内置分类不让删除
	if data.IsDefault || data.BuiltIn != constants.CategoryBuiltInType {
		return nil
	}
	return db.Transaction(func(tx *gorm.DB) error {
		confOpt := conf_center.UpdateConf
		var sensitiveIds []string
		err = tx.Model(model.SensitiveStrategyDB{}).Select("id").
			Where("category_id = ? and built_in = ?", req.Id, constants.CustomBuiltInType).Find(&sensitiveIds).Error
		if err != nil {
			return err
		}
		if req.Type == constants.DelSensitiveCategoryAndStrategy {
			confOpt = conf_center.DelConf
			//删除所有敏感数据
			err = tx.Where("category_id = ? and built_in = ?", req.Id, constants.CustomBuiltInType).Delete(model.SensitiveStrategyDB{}).Error
			if err != nil {
				return err
			}
			//删除敏感数据对应的评分
			err = tx.Where("indicator in ? and indicator_type = ? and indicator_sub_type = ?", sensitiveIds, constants.SgIndicatorType, constants.SgIndicatorSubType).
				Delete(modelTable.RiskScoreConfig{}).Error
			if err != nil {
				return err
			}
		} else {
			err = tx.Model(model.SensitiveStrategyDB{}).Select("id").Where("category_id = ? and built_in = ?", req.Id, constants.CustomBuiltInType).Find(&sensitiveIds).Error
			if err != nil {
				return err
			}
			//查询默认分组id
			var defaultCategoryId string
			err = tx.Model(model.SensitiveCategory{}).Select("id").Where("is_default").Find(&defaultCategoryId).Error
			if err != nil {
				return err
			}
			//移动分类下的敏感数据分类都改为未分类
			err = tx.Model(model.SensitiveStrategyDB{}).Where("id in ?", sensitiveIds).Update("category_id", defaultCategoryId).Error
			if err != nil {
				return err
			}
		}
		//删除配置中心的敏感数据的配置
		for _, v := range sensitiveIds {
			confReq, err := GetConfReq(v, confOpt, tx)
			if err != nil {
				return err
			}
			err = conf_center.ConfChange(confReq)
			if err != nil {
				return err
			}
		}
		// 删除敏感数据分类
		err = tx.Model(model.SensitiveCategory{}).Delete(model.SensitiveCategory{Id: req.Id}).Error
		return err
	})
}

func GetConfReq(id string, confType conf_center.ConfChangeType, tx *gorm.DB) (conf_center.ConfChangeReq, error) {
	if confType == conf_center.DelConf {
		return conf_center.ConfChangeReq{
			ConfBizId:  id,
			ConfType:   "sensitive_strategy",
			Tx:         tx,
			RedisCli:   global.SysRedisClient,
			ChangeType: conf_center.DelConf}, nil
	} else {
		temp := model.SensitiveStrategyDB{}
		err := tx.Model(&temp).Where("id=?", id).Find(&temp).Error
		if err != nil {
			return conf_center.ConfChangeReq{}, err
		}
		var fileNameRule []common.SensitiveStrategyFileNameRule
		err = json.Unmarshal(temp.FileNameRule, &fileNameRule)
		if err != nil {
			return conf_center.ConfChangeReq{}, err
		}
		var contentRule []common.SensitiveStrategyFileNameRule
		err = json.Unmarshal(temp.ContentRule, &contentRule)
		if err != nil {
			return conf_center.ConfChangeReq{}, err
		}
		changeReq, err := strategyToConfReq(temp, fileNameRule, contentRule, tx, conf_center.UpdateConf)
		if err != nil {
			return conf_center.ConfChangeReq{}, err
		}
		return changeReq, nil
	}
}

func (s sensitiveCategoryRepository) CheckDelCondition(ctx context.Context, id string, delType int) (bool, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return false, err
	}
	var data []modelTable.AlertRule

	if delType == constants.DelSensitiveCategoryAndStrategy {
		db = db.Or("tb_alert_rule.sensitive_ids && (select array (select unnest (array_agg(id))) as id from tb_sensitive_strategy where category_id = ?)", id)
	}
	err = db.Model(modelTable.AlertRule{}).Where("? = any (tb_alert_rule.sensitive_category)", id).Find(&data).Error
	if err != nil {
		return false, err
	}
	if len(data) > 0 {
		return false, nil
	}
	return true, nil
}
