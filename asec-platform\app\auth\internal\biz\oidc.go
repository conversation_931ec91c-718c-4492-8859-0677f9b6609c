package biz

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
)

// AppRepo 应用仓库接口（用于权限验证）
type AppRepo interface {
	GetMyApp(ctx context.Context, userId string) ([]Application, error)
}

// Application 应用信息结构（简化版，只包含权限验证需要的字段，后续可拓展）
type Application struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
}

// OIDCRepo OIDC数据仓库接口
type OIDCRepo interface {
	// OAuth2Client相关
	GetOAuth2ClientByID(ctx context.Context, clientID string) (*dto.OAuth2Client, error)
	CreateOAuth2Client(ctx context.Context, client *dto.OAuth2Client) error
	UpdateOAuth2Client(ctx context.Context, client *dto.OAuth2Client) error
	DeleteOAuth2Client(ctx context.Context, clientID string) error
	ListOAuth2Clients(ctx context.Context, corpID int64, limit, offset int) ([]*dto.OAuth2Client, int64, error)

	// 授权码相关
	CreateAuthorizationCode(ctx context.Context, code *dto.OIDCAuthorizationCode) error
	GetAuthorizationCode(ctx context.Context, code string) (*dto.OIDCAuthorizationCode, error)
	DeleteAuthorizationCode(ctx context.Context, code string) error
	CleanExpiredAuthorizationCodes(ctx context.Context) error

	// 访问令牌相关
	CreateAccessToken(ctx context.Context, token *dto.OIDCAccessToken) error
	GetAccessToken(ctx context.Context, tokenID string) (*dto.OIDCAccessToken, error)
	GetAccessTokenByToken(ctx context.Context, token string) (*dto.OIDCAccessToken, error)
	RevokeAccessToken(ctx context.Context, tokenID string) error
	RevokeAccessTokenByClientUser(ctx context.Context, clientID, userID string) error
	CleanExpiredAccessTokens(ctx context.Context) error

	// 刷新令牌相关
	CreateRefreshToken(ctx context.Context, token *dto.OIDCRefreshToken) error
	GetRefreshToken(ctx context.Context, tokenID string) (*dto.OIDCRefreshToken, error)
	GetRefreshTokenByToken(ctx context.Context, token string) (*dto.OIDCRefreshToken, error)
	RevokeRefreshToken(ctx context.Context, tokenID string) error
	RevokeRefreshTokenByClientUser(ctx context.Context, clientID, userID string) error
	CleanExpiredRefreshTokens(ctx context.Context) error

	// 事务操作
	RevokeAllTokensByClientUser(ctx context.Context, clientID, userID string) error
	CreateTokenPair(ctx context.Context, accessToken *dto.OIDCAccessToken, refreshToken *dto.OIDCRefreshToken) error
}

// OIDCUsecase OIDC业务逻辑
type OIDCUsecase struct {
	repo     OIDCRepo
	userRepo UserRepo // 使用现有的用户仓库获取用户信息
	appRepo  AppRepo  // 添加应用仓库依赖，用于权限验证
	log      *log.Helper

	// OIDC配置
	issuer     string
	baseURL    string
	jwtSecret  []byte
	signingKey interface{} // JWT签名密钥
}

// NewOIDCUsecase 创建OIDC业务逻辑实例
func NewOIDCUsecase(repo OIDCRepo, userRepo UserRepo, appRepo AppRepo, logger log.Logger) *OIDCUsecase {
	// TODO: 从配置文件读取这些值
	issuer := "https://auth.asec.com"
	baseURL := "https://auth.asec.com"
	jwtSecret := []byte("your-jwt-secret-key") // 应该从配置文件读取

	return &OIDCUsecase{
		repo:       repo,
		userRepo:   userRepo,
		appRepo:    appRepo,
		log:        log.NewHelper(logger),
		issuer:     issuer,
		baseURL:    baseURL,
		jwtSecret:  jwtSecret,
		signingKey: jwtSecret,
	}
}

// ValidateClient 验证OAuth2客户端
func (uc *OIDCUsecase) ValidateClient(ctx context.Context, clientID, clientSecret string) (*dto.OAuth2Client, error) {
	client, err := uc.repo.GetOAuth2ClientByID(ctx, clientID)
	if err != nil {
		return nil, err
	}
	if client == nil {
		return nil, errors.BadRequest("invalid_client", "客户端不存在")
	}

	// 验证客户端密钥（如果提供了的话）
	if clientSecret != "" && client.ClientSecret != clientSecret {
		return nil, errors.BadRequest("invalid_client", "客户端密钥错误")
	}

	return client, nil
}

// GetOAuth2ClientWithAppID 获取包含应用ID的OAuth2客户端信息
func (uc *OIDCUsecase) GetOAuth2ClientWithAppID(ctx context.Context, clientID string) (*dto.OAuth2Client, error) {
	client, err := uc.repo.GetOAuth2ClientByID(ctx, clientID)
	if err != nil {
		return nil, err
	}
	if client == nil {
		return nil, errors.BadRequest("invalid_client", "客户端不存在")
	}

	return client, nil
}

// ValidateUserAppPermission 验证用户是否有访问指定应用的权限
func (uc *OIDCUsecase) ValidateUserAppPermission(ctx context.Context, userID, appID string) (bool, error) {
	// uc.log.Debugf("开始验证用户应用权限: userID=%s, appID=%s", userID, appID)

	// 1. 调用应用仓库获取用户有权访问的所有应用
	userApps, err := uc.appRepo.GetMyApp(ctx, userID)
	if err != nil {
		uc.log.Errorf("获取用户应用列表失败: userID=%s, error=%v", userID, err)
		return false, fmt.Errorf("获取用户应用列表失败: %w", err)
	}

	// 2. 检查用户是否有权访问指定的应用
	appIDUint64, err := strconv.ParseUint(appID, 10, 64)
	if err != nil {
		uc.log.Errorf("应用ID格式错误: appID=%s, error=%v", appID, err)
		return false, fmt.Errorf("应用ID格式错误: %w", err)
	}

	// 3. 遍历用户有权访问的应用列表，查找匹配的应用ID
	for _, app := range userApps {
		if app.ID == appIDUint64 {
			uc.log.Debugf("用户权限验证通过: userID=%s, appID=%s, appName=%s", userID, appID, app.Name)
			return true, nil
		}
	}

	// 4. 未找到匹配的应用，拒绝访问
	uc.log.Warnf("用户权限验证失败: userID=%s, appID=%s, 用户无权访问该应用", userID, appID)
	return false, nil
}

// ValidateRedirectURI 验证重定向URI
func (uc *OIDCUsecase) ValidateRedirectURI(client *dto.OAuth2Client, redirectURI string) error {
	if redirectURI == "" {
		return errors.BadRequest("invalid_request", "redirect_uri参数缺失")
	}

	// 简单的URL格式验证
	reqURL, err := url.Parse(redirectURI)
	if err != nil {
		return errors.BadRequest("invalid_request", "redirect_uri格式无效")
	}

	// 解析客户端注册的URI
	clientURL, err := url.Parse(client.RedirectURI)
	if err != nil {
		uc.log.Errorf("客户端注册的redirect_uri格式无效: %s", client.RedirectURI)
		return errors.BadRequest("invalid_request", "客户端配置的redirect_uri格式无效")
	}

	// 验证协议和主机必须匹配
	if reqURL.Scheme != clientURL.Scheme {
		return errors.BadRequest("invalid_request", "redirect_uri协议不匹配")
	}

	if reqURL.Host != clientURL.Host {
		return errors.BadRequest("invalid_request", "redirect_uri主机不匹配")
	}

	// 验证路径必须以客户端注册的路径为前缀（允许子路径）
	if !strings.HasPrefix(reqURL.Path, clientURL.Path) {
		return errors.BadRequest("invalid_request", "redirect_uri路径不匹配")
	}

	return nil
}

// ValidateScope 验证权限范围
func (uc *OIDCUsecase) ValidateScope(scope string) ([]string, error) {
	if scope == "" {
		return []string{dto.ScopeOpenID}, nil
	}

	scopes := strings.Split(scope, " ")

	// 必须包含openid
	hasOpenID := false
	for _, s := range scopes {
		if s == dto.ScopeOpenID {
			hasOpenID = true
			break
		}
	}

	if !hasOpenID {
		return nil, errors.BadRequest("invalid_scope", "必须包含openid scope")
	}

	// 验证所有scope都是支持的
	supportedScopes := map[string]bool{
		dto.ScopeOpenID:  true,
		dto.ScopeProfile: true,
		dto.ScopeEmail:   true,
		dto.ScopePhone:   true,
		dto.ScopeAddress: true,
	}

	for _, s := range scopes {
		if !supportedScopes[s] {
			return nil, errors.BadRequest("invalid_scope", fmt.Sprintf("不支持的scope: %s", s))
		}
	}

	return scopes, nil
}

// ValidatePKCE 验证PKCE参数
func (uc *OIDCUsecase) ValidatePKCE(codeChallenge, codeChallengeMethod string) error {
	if codeChallenge == "" {
		return nil // PKCE是可选的
	}

	if codeChallengeMethod != "" && codeChallengeMethod != dto.CodeChallengeMethod {
		return errors.BadRequest("invalid_request", "不支持的code_challenge_method")
	}

	return nil
}

// GenerateAuthorizationCode 生成授权码
func (uc *OIDCUsecase) GenerateAuthorizationCode(ctx context.Context, req *AuthorizeRequest, userID string) (*dto.OIDCAuthorizationCode, error) {
	// 在生成新授权码前，清理过期的授权码（异步执行，不影响主流程）
	go func() {
		cleanCtx := context.Background() // 使用独立的上下文避免超时影响
		if err := uc.repo.CleanExpiredAuthorizationCodes(cleanCtx); err != nil {
			uc.log.Warnf("清理过期授权码失败: %v", err)
		}
	}()

	// 生成随机授权码
	codeBytes := make([]byte, 32)
	if _, err := rand.Read(codeBytes); err != nil {
		return nil, err
	}
	code := base64.URLEncoding.EncodeToString(codeBytes)

	// 创建授权码记录
	authCode := &dto.OIDCAuthorizationCode{
		Code:                code,
		ClientID:            req.ClientID,
		UserID:              userID,
		RedirectURI:         req.RedirectURI,
		Scope:               req.Scope,
		State:               req.State,
		Nonce:               req.Nonce,
		CodeChallenge:       req.CodeChallenge,
		CodeChallengeMethod: req.CodeChallengeMethod,
		ExpiresIn:           600, // 10分钟
		ExpiresAt:           time.Now().Add(10 * time.Minute),
	}

	if err := uc.repo.CreateAuthorizationCode(ctx, authCode); err != nil {
		return nil, err
	}

	return authCode, nil
}

// ExchangeCodeForTokens 用授权码交换令牌
func (uc *OIDCUsecase) ExchangeCodeForTokens(ctx context.Context, req *TokenRequest) (*TokenResponse, error) {
	// 在交换令牌前，异步清理所有过期令牌（不影响主流程性能）
	go func() {
		cleanCtx := context.Background()
		if err := uc.CleanExpiredTokens(cleanCtx); err != nil {
			uc.log.Warnf("清理过期令牌失败: %v", err)
		}
	}()

	// 获取授权码
	authCode, err := uc.repo.GetAuthorizationCode(ctx, req.Code)
	if err != nil {
		return nil, err
	}
	if authCode == nil {
		return nil, errors.BadRequest("invalid_grant", "无效的授权码")
	}

	// 验证授权码是否过期
	if authCode.IsExpired() {
		// 立即删除过期的授权码
		uc.repo.DeleteAuthorizationCode(ctx, req.Code)
		return nil, errors.BadRequest("invalid_grant", "授权码已过期")
	}

	// 二次检查：确保授权码在合理的时间范围内
	if time.Since(authCode.CreatedAt) > 15*time.Minute {
		// 如果授权码创建时间超过15分钟，也认为是无效的（额外的安全措施）
		uc.repo.DeleteAuthorizationCode(ctx, req.Code)
		return nil, errors.BadRequest("invalid_grant", "授权码已超时")
	}

	// 验证客户端ID
	if authCode.ClientID != req.ClientID {
		return nil, errors.BadRequest("invalid_grant", "客户端ID不匹配")
	}

	// 验证重定向URI
	if authCode.RedirectURI != req.RedirectURI {
		return nil, errors.BadRequest("invalid_grant", "重定向URI不匹配")
	}

	// 验证PKCE（如果使用了的话）
	if authCode.CodeChallenge != "" {
		if req.CodeVerifier == "" {
			return nil, errors.BadRequest("invalid_request", "缺少code_verifier")
		}

		// 验证code_verifier
		if !uc.verifyPKCE(authCode.CodeChallenge, req.CodeVerifier, authCode.CodeChallengeMethod) {
			return nil, errors.BadRequest("invalid_grant", "PKCE验证失败")
		}
	}

	// 获取用户信息
	user, err := uc.userRepo.QueryUserEntity(ctx, "0", authCode.UserID) // TODO: 处理corpId
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.BadRequest("invalid_grant", "用户不存在")
	}

	// 转换为DTO用户对象
	userDTO := &dto.User{
		ID:        user.ID,
		Name:      user.TrueName,
		Phone:     user.Phone,
		Email:     user.Email,
		GroupID:   user.GroupID,
		CorpID:    user.CorpID,
		SourceID:  user.SourceID,
		Identify:  user.Identify,
		AuthType:  user.AuthType,
		CreatedAt: user.CreatedAt,
	}

	// 生成访问令牌
	accessToken, err := uc.generateAccessToken(ctx, authCode.ClientID, authCode.UserID, authCode.Scope)
	if err != nil {
		return nil, err
	}

	// 生成刷新令牌
	refreshToken, err := uc.generateRefreshToken(ctx, authCode.ClientID, authCode.UserID, accessToken.TokenID)
	if err != nil {
		return nil, err
	}

	// 生成ID令牌
	idToken, err := uc.generateIDToken(ctx, authCode.ClientID, authCode.UserID, authCode.Nonce, userDTO)
	if err != nil {
		return nil, err
	}

	// 保存令牌到数据库
	if err := uc.repo.CreateTokenPair(ctx, accessToken, refreshToken); err != nil {
		return nil, err
	}

	// 删除已使用的授权码
	if err := uc.repo.DeleteAuthorizationCode(ctx, req.Code); err != nil {
		uc.log.Warnf("删除授权码失败: %v", err)
	}

	return &TokenResponse{
		AccessToken:  accessToken.Token,
		TokenType:    dto.TokenTypeBearer,
		ExpiresIn:    int64(accessToken.ExpiresIn),
		RefreshToken: refreshToken.Token,
		IDToken:      idToken,
		Scope:        authCode.Scope,
	}, nil
}

// GetUserInfo 获取用户信息
func (uc *OIDCUsecase) GetUserInfo(ctx context.Context, accessToken string) (*UserInfoResponse, error) {
	// 验证访问令牌
	token, err := uc.repo.GetAccessTokenByToken(ctx, accessToken)
	if err != nil {
		return nil, err
	}
	if token == nil {
		return nil, errors.Unauthorized("invalid_token", "无效的访问令牌")
	}

	// 检查令牌是否过期
	if token.IsExpired() {
		return nil, errors.Unauthorized("invalid_token", "访问令牌已过期")
	}

	// 获取用户信息
	user, err := uc.userRepo.QueryUserEntity(ctx, "0", token.UserID) // TODO: 处理corpId
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.Unauthorized("invalid_token", "用户不存在")
	}

	// 构建用户信息响应
	userInfo := &UserInfoResponse{
		Sub:                 user.ID,
		Name:                user.TrueName,
		Nickname:            user.DisplayName,
		PreferredUsername:   user.Name, // 使用登录名作为首选用户名
		Email:               user.Email,
		EmailVerified:       user.Email != "",
		PhoneNumber:         user.Phone,
		PhoneNumberVerified: user.Phone != "",
	}

	// 构建address字段（包含扩展信息）
	address := make(map[string]interface{})
	address["group_id"] = user.GroupID
	address["group_name"] = "" // TODO: 从group表获取group name
	address["corp_id"] = user.CorpID
	address["source_type"] = "" // TODO: 从source表获取source type
	address["source_name"] = "" // TODO: 从source表获取source name
	address["identifier"] = user.Identify
	address["auth_type"] = user.AuthType

	userInfo.Address = address
	userInfo.UpdatedAt = time.Now().Unix()

	return userInfo, nil
}

// RevokeToken 撤销令牌
func (uc *OIDCUsecase) RevokeToken(ctx context.Context, req *RevokeRequest) error {
	// 验证客户端
	_, err := uc.ValidateClient(ctx, req.ClientID, req.ClientSecret)
	if err != nil {
		return err
	}

	// 尝试作为访问令牌撤销
	accessToken, err := uc.repo.GetAccessTokenByToken(ctx, req.Token)
	if err == nil && accessToken != nil {
		return uc.repo.RevokeAccessToken(ctx, accessToken.TokenID)
	}

	// 尝试作为刷新令牌撤销
	refreshToken, err := uc.repo.GetRefreshTokenByToken(ctx, req.Token)
	if err == nil && refreshToken != nil {
		return uc.repo.RevokeRefreshToken(ctx, refreshToken.TokenID)
	}

	// 令牌不存在，按OIDC规范应该返回成功
	return nil
}

// GetDiscoveryDocument 获取发现文档
func (uc *OIDCUsecase) GetDiscoveryDocument() *dto.OIDCDiscoveryResponse {
	return &dto.OIDCDiscoveryResponse{
		Issuer:                           uc.issuer,
		AuthorizationEndpoint:            uc.baseURL + "/auth/login/v1/authorize/oidc/authorize",
		TokenEndpoint:                    uc.baseURL + "/auth/login/v1/authorize/oidc/token",
		UserinfoEndpoint:                 uc.baseURL + "/auth/login/v1/authorize/oidc/userinfo",
		JwksURI:                          uc.baseURL + "/auth/login/v1/authorize/oidc/jwks",
		RevocationEndpoint:               uc.baseURL + "/auth/login/v1/authorize/oidc/revoke",
		EndSessionEndpoint:               uc.baseURL + "/auth/login/v1/authorize/oidc/logout",
		ScopesSupported:                  []string{dto.ScopeOpenID, dto.ScopeProfile, dto.ScopeEmail, dto.ScopePhone, dto.ScopeAddress},
		ResponseTypesSupported:           []string{dto.ResponseTypeCode},
		GrantTypesSupported:              []string{dto.GrantTypeAuthCode, dto.GrantTypeRefresh},
		SubjectTypesSupported:            []string{"public"},
		IDTokenSigningAlgValuesSupported: []string{"HS256"},
		CodeChallengeMethodsSupported:    []string{dto.CodeChallengeMethod},
	}
}

// GetJWKS 获取JWKS
func (uc *OIDCUsecase) GetJWKS() *dto.JWKSet {
	// TODO: 实现RSA密钥对生成和JWKS返回
	// 目前返回空的JWKS，因为我们使用HMAC签名
	return &dto.JWKSet{
		Keys: []dto.JWK{},
	}
}

// 辅助方法

func (uc *OIDCUsecase) generateAccessToken(ctx context.Context, clientID, userID, scope string) (*dto.OIDCAccessToken, error) {
	tokenID := uuid.New().String()

	// 生成JWT访问令牌
	claims := jwt.MapClaims{
		"sub":      userID,
		"aud":      clientID,
		"iss":      uc.issuer,
		"exp":      time.Now().Add(1 * time.Hour).Unix(),
		"iat":      time.Now().Unix(),
		"token_id": tokenID,
		"scope":    scope,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(uc.jwtSecret)
	if err != nil {
		return nil, err
	}

	return &dto.OIDCAccessToken{
		TokenID:   tokenID,
		ClientID:  clientID,
		UserID:    userID,
		Token:     tokenString,
		TokenType: dto.TokenTypeBearer,
		Scope:     scope,
		ExpiresIn: 3600, // 1小时
		ExpiresAt: time.Now().Add(1 * time.Hour),
	}, nil
}

func (uc *OIDCUsecase) generateRefreshToken(ctx context.Context, clientID, userID, accessTokenID string) (*dto.OIDCRefreshToken, error) {
	tokenID := uuid.New().String()

	// 生成随机刷新令牌
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return nil, err
	}
	tokenString := base64.URLEncoding.EncodeToString(tokenBytes)

	return &dto.OIDCRefreshToken{
		TokenID:       tokenID,
		ClientID:      clientID,
		UserID:        userID,
		Token:         tokenString,
		AccessTokenID: accessTokenID,
		ExpiresIn:     2592000, // 30天
		ExpiresAt:     time.Now().Add(30 * 24 * time.Hour),
	}, nil
}

func (uc *OIDCUsecase) generateIDToken(ctx context.Context, clientID, userID, nonce string, user *dto.User) (string, error) {
	now := time.Now()

	claims := &dto.OIDCClaims{
		Sub:                 userID,
		Aud:                 clientID,
		Iss:                 uc.issuer,
		Exp:                 now.Add(1 * time.Hour).Unix(),
		Iat:                 now.Unix(),
		AuthTime:            now.Unix(),
		Nonce:               nonce,
		Name:                user.Name,
		Nickname:            user.Name,
		PreferredUsername:   user.ID, // 使用用户ID作为用户名
		Email:               user.Email,
		EmailVerified:       user.Email != "",
		PhoneNumber:         user.Phone,
		PhoneNumberVerified: user.Phone != "",
	}

	// 构建address信息
	claims.Address = map[string]interface{}{
		"group_id":    user.GroupID,
		"group_name":  "", // TODO: 从group表获取
		"corp_id":     user.CorpID,
		"source_type": "", // TODO: 从source表获取
		"source_name": "", // TODO: 从source表获取
		"identifier":  user.Identify,
		"auth_type":   user.AuthType,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"sub":                   claims.Sub,
		"aud":                   claims.Aud,
		"iss":                   claims.Iss,
		"exp":                   claims.Exp,
		"iat":                   claims.Iat,
		"auth_time":             claims.AuthTime,
		"nonce":                 claims.Nonce,
		"name":                  claims.Name,
		"nickname":              claims.Nickname,
		"preferred_username":    claims.PreferredUsername,
		"email":                 claims.Email,
		"email_verified":        claims.EmailVerified,
		"phone_number":          claims.PhoneNumber,
		"phone_number_verified": claims.PhoneNumberVerified,
		"address":               claims.Address,
	})

	return token.SignedString(uc.jwtSecret)
}

func (uc *OIDCUsecase) verifyPKCE(codeChallenge, codeVerifier, method string) bool {
	if method == "" || method == dto.CodeChallengeMethod {
		// S256方法
		hash := sha256.Sum256([]byte(codeVerifier))
		encoded := base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(hash[:])
		return encoded == codeChallenge
	}
	return false
}

// 请求响应结构体
type AuthorizeRequest struct {
	ResponseType        string
	ClientID            string
	RedirectURI         string
	Scope               string
	State               string
	CodeChallenge       string
	CodeChallengeMethod string
	Nonce               string
}

type TokenRequest struct {
	GrantType    string
	ClientID     string
	ClientSecret string
	Code         string
	RedirectURI  string
	CodeVerifier string
}

type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int64  `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
	Scope        string `json:"scope"`
}

type UserInfoResponse struct {
	Sub                 string                 `json:"sub"`
	Name                string                 `json:"name,omitempty"`
	Nickname            string                 `json:"nickname,omitempty"`
	PreferredUsername   string                 `json:"preferred_username,omitempty"`
	Picture             string                 `json:"picture,omitempty"`
	Email               string                 `json:"email,omitempty"`
	EmailVerified       bool                   `json:"email_verified,omitempty"`
	PhoneNumber         string                 `json:"phone_number,omitempty"`
	PhoneNumberVerified bool                   `json:"phone_number_verified,omitempty"`
	Address             map[string]interface{} `json:"address,omitempty"`
	UpdatedAt           int64                  `json:"updated_at,omitempty"`
}

type RevokeRequest struct {
	ClientID      string
	ClientSecret  string
	Token         string
	TokenTypeHint string
}

// CleanExpiredTokens 清理过期的令牌
func (uc *OIDCUsecase) CleanExpiredTokens(ctx context.Context) error {
	// uc.log.Debugf("开始清理过期的OIDC令牌")

	// 清理过期的授权码
	if err := uc.repo.CleanExpiredAuthorizationCodes(ctx); err != nil {
		uc.log.Errorf("清理过期授权码失败: %v", err)
		return err
	}

	// 清理过期的访问令牌
	if err := uc.repo.CleanExpiredAccessTokens(ctx); err != nil {
		uc.log.Errorf("清理过期访问令牌失败: %v", err)
		return err
	}

	// 清理过期的刷新令牌
	if err := uc.repo.CleanExpiredRefreshTokens(ctx); err != nil {
		uc.log.Errorf("清理过期刷新令牌失败: %v", err)
		return err
	}

	// uc.log.Debugf("OIDC令牌清理完成")
	return nil
}
