package data

import (
	"asdsec.com/asec/platform/app/auth/internal/common"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"context"
	"errors"
	"github.com/google/uuid"
	"github.com/jackc/pgtype"
	"github.com/jinzhu/copier"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/data/query"
	"asdsec.com/asec/platform/app/auth/internal/dto"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"gorm.io/gorm"

	"asdsec.com/asec/platform/app/auth/internal/data/model"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"github.com/go-kratos/kratos/v2/log"
)

type authPolicyRepo struct {
	data *Data
	log  *log.Helper
}

func (a authPolicyRepo) UpdatePolicy(ctx context.Context, param dto.UpdateAuthPolicyParam) error {
	return a.data.db.Transaction(func(tx *gorm.DB) error {
		//修改策略信息
		err := a.UpdateAuthPolicy(ctx, param, tx)
		if err != nil {
			a.log.Errorf("UpdateAuthPolicy Failed. err=%v", err)
			return err
		}

		//删除策略与认证源关系
		err = a.DeleteAuthPolicyIdpMapper(ctx, param, tx)
		if err != nil {
			a.log.Errorf("DeleteAuthPolicyIdpMapper Failed. err=%v", err)
			return err
		}

		//重新绑定认证策略和认证源关系
		var mappers []*model.TbAuthPolicyIdpMapper
		for _, idpId := range param.IdpList {
			mappers = append(mappers, &model.TbAuthPolicyIdpMapper{
				PolicyID: param.PolicyId,
				IdpID:    idpId,
				CorpID:   param.CorpId,
			})
		}
		if err := tx.Model(model.TbAuthPolicyIdpMapper{}).Create(mappers).Error; err != nil {
			return err
		}
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         param.CorpId,
				ResourceType:   common.AuthPolicyType,
				OperationType:  common.OperateUpdate,
				Representation: param.Name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			a.data.db.Create(&oplog)
		}()
		return nil
	})
}

func (a authPolicyRepo) UpdateAuthPolicy(ctx context.Context, param dto.UpdateAuthPolicyParam, tx *gorm.DB) error {
	if param.GroupIds == nil {
		param.GroupIds = []string{}
	}
	return tx.Transaction(func(tx *gorm.DB) error {
		authEnhancement := pgtype.JSONB{
			Bytes: []byte("{}"), Status: pgtype.Present,
		}

		if len(param.AuthEnhancement) > 0 {
			authEnhancement.Bytes = param.AuthEnhancement
		}
		if err := tx.WithContext(ctx).Model(model.TbAuthPolicy{}).
			Where("corp_id = ? and id = ?", param.CorpId, param.PolicyId).
			Updates(model.TbAuthPolicy{
				Name:            param.Name,
				Description:     param.Description,
				GroupIds:        param.GroupIds,
				UserIds:         param.UserIds,
				EnableAllUser:   param.EnableAllUser,
				Enable:          param.Enable,
				AuthEnhancement: authEnhancement,
				TimeIds:         param.TimeIds,
			}).Error; err != nil {
			return err
		}

		err := tx.WithContext(ctx).Model(model.TbAuthPolicy{}).
			Where("corp_id = ? and id = ?", param.CorpId, param.PolicyId).
			UpdateColumn("enable_all_user", param.EnableAllUser).Error
		if err != nil {
			a.log.Errorf("UpdateStatus Failed. err=%v", err)
			return err
		}
		err = tx.WithContext(ctx).Model(model.TbAuthPolicy{}).
			Where("corp_id = ? and id = ?", param.CorpId, param.PolicyId).
			UpdateColumn("enable", param.Enable).Error
		if err != nil {
			a.log.Errorf("UpdateStatus Failed. err=%v", err)
			return err
		}
		return nil
	})
}

func (a authPolicyRepo) DeleteAuthPolicyIdpMapper(ctx context.Context, param dto.UpdateAuthPolicyParam, tx *gorm.DB) error {
	return tx.Transaction(func(tx *gorm.DB) error {
		if err := tx.WithContext(ctx).Model(model.TbAuthPolicyIdpMapper{}).
			Where("corp_id = ? and policy_id = ?", param.CorpId, param.PolicyId).
			Delete(&model.TbAuthPolicyIdpMapper{}).Error; err != nil {
			return err
		}
		return nil
	})
}

func (a authPolicyRepo) DeleteAuthPolicy(ctx context.Context, corpId, policyId, name string) error {
	// 删除idp映射
	// 删除策略
	return a.data.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.WithContext(ctx).Model(model.TbAuthPolicyIdpMapper{}).
			Where("corp_id = ? and policy_id = ?", corpId, policyId).
			Delete(&model.TbAuthPolicyIdpMapper{}).Error; err != nil {
			return err
		}
		err := tx.WithContext(ctx).Model(model.TbAuthPolicy{}).
			Where("corp_id = ? and id = ?", corpId, policyId).
			Delete(&model.TbAuthPolicy{}).Error
		if err != nil {
			return err
		}
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         corpId,
				ResourceType:   common.AuthPolicyType,
				OperationType:  common.OperateDelete,
				Representation: name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			a.data.db.Create(&oplog)
		}()
		return nil
	})
}

func (a authPolicyRepo) ListIdpInfo(ctx context.Context, corpId, policyId string) ([]dto.BindIDPInfo, error) {
	q := query.Use(a.data.db)
	mapper := q.TbAuthPolicyIdpMapper
	idp := q.TbIdentityProvider
	var result []dto.BindIDPInfo
	err := mapper.WithContext(ctx).LeftJoin(idp, idp.ID.EqCol(mapper.IdpID)).
		Where(mapper.CorpID.Eq(corpId), mapper.PolicyID.Eq(policyId)).
		Select(mapper.IdpID.As("Id"), idp.Name, idp.Type).Scan(&result)
	return result, err
}

func (a authPolicyRepo) QueryPolicyByName(ctx context.Context, corpId, groupId, name string, id string) (*model.TbAuthPolicy, error) {
	var result model.TbAuthPolicy
	var err error
	if id != "" {
		err = a.data.db.WithContext(ctx).
			Model(model.TbAuthPolicy{}).
			Where("corp_id = ? and root_group_id = ? and name= ? and id != ?", corpId, groupId, name, id).
			First(&result).Error
	} else {
		err = a.data.db.WithContext(ctx).Model(model.TbAuthPolicy{}).Where("corp_id = ? and root_group_id = ? and name= ?", corpId, groupId, name).
			First(&result).Error
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbAuthPolicy{}, pb.ErrorRecordNotFound("auth policy not found, corp_id=%v, groupId=%v, name=%v", corpId, groupId, name)
		}
		return &model.TbAuthPolicy{}, err
	}
	return &result, nil
}

func (a authPolicyRepo) QueryPolicyById(ctx context.Context, corpId, id string) (*model.TbAuthPolicy, error) {
	var result model.TbAuthPolicy
	err := a.data.db.WithContext(ctx).Model(model.TbAuthPolicy{}).Where("corp_id = ? and id = ?", corpId, id).
		First(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbAuthPolicy{}, pb.ErrorRecordNotFound("auth policy not found, corp_id=%v, id=%v", corpId, id)
		}
		return &model.TbAuthPolicy{}, err
	}
	return &result, nil
}

func (a authPolicyRepo) ListPolicyInRootGroup(ctx context.Context, corpId, groupId string, limit, offset int) ([]*model.TbAuthPolicy, error) {
	var result []*model.TbAuthPolicy
	err := a.data.db.WithContext(ctx).Model(model.TbAuthPolicy{}).Where("corp_id = ? AND root_group_id = ?", corpId, groupId).Order("created_at desc").
		Offset(offset).Limit(limit).Find(&result).Error
	return result, err
}

func (a authPolicyRepo) GetDefaultPolicyInRootGroup(ctx context.Context, corpId, groupId string) (*model.TbAuthPolicy, error) {
	var result model.TbAuthPolicy
	err := a.data.db.WithContext(ctx).Model(model.TbAuthPolicy{}).Where("corp_id = ? and root_group_id = ? and is_default = true", corpId, groupId).
		First(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbAuthPolicy{}, nil
		}
		return &model.TbAuthPolicy{}, err
	}
	return &result, nil
}

func (a authPolicyRepo) CountPolicyInRootGroup(ctx context.Context, corpId, groupId string) (int64, error) {
	var result int64
	err := a.data.db.WithContext(ctx).Model(model.TbAuthPolicy{}).
		Where("corp_id = ? AND root_group_id = ?", corpId, groupId).Count(&result).Error
	return result, err
}

func (a authPolicyRepo) CreatePolicy(ctx context.Context, param dto.CreateAuthPolicyDaoParam) error {
	return a.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var authPolicy model.TbAuthPolicy
		if err := copier.Copy(&authPolicy, &param); err != nil {
			return err
		}
		authPolicy.ID = param.Id
		authPolicy.CorpID = param.CorpId
		authPolicy.TimeIds = param.TimeIds
		if len(param.AuthEnhancement) > 0 {
			authPolicy.AuthEnhancement = pgtype.JSONB{Bytes: param.AuthEnhancement, Status: pgtype.Present}
		} else {
			authPolicy.AuthEnhancement = pgtype.JSONB{Bytes: []byte("{}"), Status: pgtype.Present}
		}
		if err := tx.Model(model.TbAuthPolicy{}).Create(&authPolicy).Error; err != nil {
			return err
		}
		var mappers []*model.TbAuthPolicyIdpMapper
		for _, idpId := range param.IdpList {
			mappers = append(mappers, &model.TbAuthPolicyIdpMapper{
				PolicyID: param.Id,
				IdpID:    idpId,
				CorpID:   param.CorpId,
			})
		}
		err := tx.Model(model.TbAuthPolicyIdpMapper{}).Create(mappers).Error
		if err != nil {
			return err
		}
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         param.CorpId,
				ResourceType:   common.AuthPolicyType,
				OperationType:  common.OperateCreate,
				Representation: param.Name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			a.data.db.Create(&oplog)
		}()
		return nil
	})
}

func (a authPolicyRepo) AddAuthPolicyIdpMapper(ctx context.Context, param model.TbAuthPolicyIdpMapper) error {
	credT := query.Use(a.data.db).TbAuthPolicyIdpMapper
	return credT.WithContext(ctx).Create(&model.TbAuthPolicyIdpMapper{
		PolicyID:  param.PolicyID,
		IdpID:     param.IdpID,
		CorpID:    param.CorpID,
		CreatedAt: time.Time{},
		UpdatedAt: time.Time{},
	})
}

func NewAuthPolicyRepo(data *Data, logger log.Logger) biz.AuthPolicyRepo {
	return &authPolicyRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
