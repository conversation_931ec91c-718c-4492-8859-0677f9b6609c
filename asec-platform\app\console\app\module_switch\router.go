package module_switch

import (
	"asdsec.com/asec/platform/app/console/app/module_switch/api"
	"github.com/gin-gonic/gin"
)

func ModuleSwitchApi(r *gin.RouterGroup) {
	v := r.Group("/v1/module_switch")
	{
		v.PUT("/agent", api.UpsetAgentModuleSwitch)
		v.PUT("", api.UpdateModuleSwitch)
		v.GET("", api.GetMainModuleSwitch)
		v.GET("/agents", api.GetAllModuleSwitchAgents)
		v.GET("/agentsSwitch", api.GetAllModuleSwitch)
		v.GET("/events", api.GetDataSurveySwitch)
		v.POST("/nf_settings", api.UpdateNetworkFilterSettings)
		v.GET("/feature/:module_key", api.GetFeatureSwitches)
		v.PUT("/feature", api.UpdateFeatureSwitches)
	}
}
