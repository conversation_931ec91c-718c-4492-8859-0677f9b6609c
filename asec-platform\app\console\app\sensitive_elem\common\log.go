package common

import (
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func RecordLog(ctx *gin.Context, resourceType string, opt_type string, repr string, errstr string) {
	oplog := model.Oprlog{
		ResourceType:   resourceType,
		OperationType:  opt_type,
		Representation: repr,
		Error:          errstr,
	}
	_, loc_err := oprService.GetOprlogService().Create(ctx, oplog)
	if loc_err != nil {
		global.SysLog.Error("record operate log failed", zap.Error(loc_err))
	}
}
