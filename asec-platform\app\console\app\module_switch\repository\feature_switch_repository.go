package repository

import (
	"asdsec.com/asec/platform/app/console/app/module_switch/dto"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type FeatureSwitchRepository interface {
	GetFeatureSwitches(ctx context.Context, moduleKey string, flag int) ([]dto.FeatureSwitchDto, error)
	UpdateFeatureSwitch(ctx context.Context, featureSwitch model.FeatureSwitch) error
	DeleteByFeatureKey(ctx context.Context, moduleKey string, flag int) error
}

type featureSwitchRepository struct {
}

func NewFeatureSwitchRepository() FeatureSwitchRepository {
	return &featureSwitchRepository{}
}

const GroupType = "GROUP"
const AgentType = "angent"
const USERTYPE = "USER"
const ROLE = "ROLE"

func (r *featureSwitchRepository) GetFeatureSwitches(ctx context.Context, moduleKey string, flag int) ([]dto.FeatureSwitchDto, error) {
	var groupFeatureSwitches []dto.FeatureSwitchDto
	var agentFeatureSwitches []dto.FeatureSwitchDto
	var roleFeatureSwitches []dto.FeatureSwitchDto
	var userFeatureSwitches []dto.FeatureSwitchDto
	var featureSwitches []dto.FeatureSwitchDto
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return groupFeatureSwitches, err
	}
	//查询分组类型的模块开关,join group表获取分组path
	err = db.Model(&model.FeatureSwitch{}).Select(fmt.Sprintf("%s.*, "+
		"CASE "+
		"WHEN tug.path = '/' THEN tug.path "+
		"WHEN tug.path LIKE '/%%' THEN tug.path "+
		"ELSE concat('/', tug.path) END as path , "+
		"tug.name as name", model.FeatureSwitch{}.TableName())).
		Joins(fmt.Sprintf("left join tb_user_group tug on %s.entity_id = tug.id ", model.FeatureSwitch{}.TableName())).
		Where("module_key = ? and entity_type='GROUP' and flag=?", moduleKey, flag).
		Find(&groupFeatureSwitches).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get FeatureSwitches err", zap.Error(err))
		return groupFeatureSwitches, err
	}
	//查询终端类型的模块开关
	err = db.Model(&model.FeatureSwitch{}).Select(fmt.Sprintf("%s.*,'' as path,ta.app_plat as agent_type, ta.app_name as name", model.FeatureSwitch{}.TableName())).
		Joins(fmt.Sprintf("left join tb_agent ta on %s.entity_id = ta.appliance_id::TEXT ", model.FeatureSwitch{}.TableName())).
		Where("module_key = ? and entity_type='angent' and flag=?", moduleKey, flag).Find(&agentFeatureSwitches).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get FeatureSwitches err", zap.Error(err))
		return groupFeatureSwitches, err
	}
	//查询角色类型的模块开关
	err = db.Model(&model.FeatureSwitch{}).Select(fmt.Sprintf("%s.*,'' as path,tr.name as name", model.FeatureSwitch{}.TableName())).
		Joins(fmt.Sprintf("left join tb_role tr on %s.entity_id = tr.id::TEXT ", model.FeatureSwitch{}.TableName())).
		Where("module_key = ? and entity_type='ROLE' and flag=?", moduleKey, flag).Find(&roleFeatureSwitches).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get FeatureSwitches err", zap.Error(err))
		return groupFeatureSwitches, err
	}

	//CASE
	//WHEN ug.PATH = '/' THEN concat(ug.PATH, ug."name")
	//ELSE concat(ug.PATH, '/', ug."name")
	//END
	//查询用户类型的模块开关
	err = db.Model(&model.FeatureSwitch{}).Select(fmt.Sprintf("%s.*,CASE "+
		"WHEN tug.PATH='/' THEN concat(tug.PATH, tug.name) "+
		"WHEN tug.PATH LIKE '/%%' THEN concat(tug.PATH, '/', tug.name) "+
		"ELSE concat('/', tug.PATH, '/', tug.name) END as path, "+
		"tue.name as name, "+
		"CASE WHEN (tue.display_name IS NULL OR tue.display_name = '') THEN tue.name ELSE tue.display_name END as display_name", model.FeatureSwitch{}.TableName())).
		Joins(fmt.Sprintf("left join tb_user_entity tue on %s.entity_id = tue.id ", model.FeatureSwitch{}.TableName())).
		Joins(fmt.Sprintf("left join tb_user_group tug on tue.group_id = tug.id ")).
		Where("module_key = ? and entity_type='USER' and flag=?", moduleKey, flag).Find(&userFeatureSwitches).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get FeatureSwitches err", zap.Error(err))
		return groupFeatureSwitches, err
	}
	featureSwitches = append(featureSwitches, groupFeatureSwitches...)
	featureSwitches = append(featureSwitches, agentFeatureSwitches...)
	featureSwitches = append(featureSwitches, roleFeatureSwitches...)
	featureSwitches = append(featureSwitches, userFeatureSwitches...)
	return featureSwitches, nil
}

func (r *featureSwitchRepository) UpdateFeatureSwitch(ctx context.Context, featureSwitch model.FeatureSwitch) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return err
	}
	return db.Save(&featureSwitch).Error
}

func (r *featureSwitchRepository) DeleteByFeatureKey(ctx context.Context, moduleKey string, flag int) error {
	if moduleKey == "" {
		return nil
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return err
	}
	return db.Where("module_key = ? and flag=?", moduleKey, flag).Delete(&model.FeatureSwitch{}).Error
}
