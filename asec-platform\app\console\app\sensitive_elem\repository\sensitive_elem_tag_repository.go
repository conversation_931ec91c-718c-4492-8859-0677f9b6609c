package repository

import (
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	global "asdsec.com/asec/platform/app/console/global"
	"context"
	"errors"
	"time"
)

// public
type SensitiveElemTagReposImpl interface {
	All(ctx context.Context) ([]model.SensitiveElemTagDB, error)
	Query(ctx context.Context, limit int, offset int32) ([]model.SensitiveElemTagDB, error)
	Find(ctx context.Context, id int) (model.SensitiveElemTagDB, error)
	Add(ctx context.Context, name string) error
	Delete(ctx context.Context, id int) error
}

func NewSensitiveElemTabRepository() SensitiveElemTagReposImpl {
	return &sensitiveElemTagReposPriv{}
}

// private
type sensitiveElemTagReposPriv struct {
}

func (self *sensitiveElemTagReposPriv) All(ctx context.Context) ([]model.SensitiveElemTagDB, error) {
	var result []model.SensitiveElemTagDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(model.SensitiveElemTagDB{}).Find(&result).Error
	if err != nil {
		global.SysLog.Error(err.Error())
		return nil, err
	}
	return result, nil
}

func (self *sensitiveElemTagReposPriv) Query(ctx context.Context, limit int, offset int32) ([]model.SensitiveElemTagDB, error) {
	var result []model.SensitiveElemTagDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(&model.SensitiveElemTagDB{}).Where("id>=?", offset).Limit(limit).Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (self *sensitiveElemTagReposPriv) Find(ctx context.Context, id int) (model.SensitiveElemTagDB, error) {
	var result model.SensitiveElemTagDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&model.SensitiveElemTagDB{}).Where("id = ?", id).Find(&result).Error
	if err != nil {
		return result, err
	}
	return result, nil
}

func (self *sensitiveElemTagReposPriv) Add(ctx context.Context, name string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	var data model.SensitiveElemTagDB
	err = db.Model(model.SensitiveElemTagDB{}).Where("name = ?", name).Find(&data).Error
	if err != nil {
		return err
	}
	if data.Id > 0 {
		return errors.New("duplicate name")
	}
	elemTagItem := model.SensitiveElemTagDB{Name: name, CreateTime: time.Now()}
	return db.Create(&elemTagItem).Error
}

func (self *sensitiveElemTagReposPriv) Delete(ctx context.Context, id int) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Delete(&model.SensitiveElemTagDB{}, id).Error
}
