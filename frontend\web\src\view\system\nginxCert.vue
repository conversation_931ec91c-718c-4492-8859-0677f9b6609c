<template>
  <div class="nginx-cert admin-box-m">
    <div class="frame" style="background-color: #FFFFFF;padding: 15px 20px 72px 20px;border-radius: 4px;">
      <div class="header">
        <t-button theme="primary" class="asdc-primary-but" @click="showUploadDialog">
          <template #icon><add-icon /></template>
          更新证书
        </t-button>
        <t-button variant="outline" class="asdc-default-but" @click="fetchCertificateInfo">
          <template #icon>
            <i class="iconfont icon-shuaxin" />
          </template>
          刷新
        </t-button>
      </div>

      <!-- 证书信息表格 -->
      <div v-if="certificateInfo.issuer || certificateInfo.serialNumber" class="cert-info-container">
        <t-table :data="[certificateInfo]" :columns="columns" bordered stripe>
          <template #subject="{ row }">
            {{ row.subject || (row.dnsNames && row.dnsNames.length ? row.dnsNames.join(', ') : '未知') }}
          </template>
          <template #issuer="{ row }">{{ row.issuer }}</template>
          <template #notBefore="{ row }">{{ formatDate(row.notBefore) }}</template>
          <template #notAfter="{ row }">{{ formatDate(row.notAfter) }}</template>
          <template #status="{ row }">
            <t-tag :theme="isCertValid ? 'success' : 'danger'" :variant="isCertValid ? 'light' : 'light'">
              {{ isCertValid ? '有效' : '已过期' }}
            </t-tag>
          </template>
        </t-table>

        <div class="cert-detail-section">
          <h3 style="margin-bottom: 15px;">证书详细信息</h3>
          <div class="cert-detail-grid">
            <div class="detail-item">
              <span class="detail-label">序列号：</span>
              <span class="detail-value">{{ certificateInfo.serialNumber }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">签名算法：</span>
              <span class="detail-value">{{ certificateInfo.signatureAlgo }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">公钥算法：</span>
              <span class="detail-value">{{ certificateInfo.publicKeyAlgo }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">证书版本：</span>
              <span class="detail-value">{{ certificateInfo.version }}</span>
            </div>
            <div class="detail-item" style="grid-column: span 2;">
              <span class="detail-label">SHA1指纹：</span>
              <span class="detail-value">{{ certificateInfo.sha1 }}</span>
            </div>
            <div class="detail-item" style="grid-column: span 2;">
              <span class="detail-label">SHA256指纹：</span>
              <span class="detail-value">{{ certificateInfo.sha256 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 无证书提示 -->
      <t-empty v-else description="暂无证书信息" class="empty-cert">
        <template #extra>
          <t-button theme="primary" @click="showUploadDialog">上传证书</t-button>
        </template>
      </t-empty>
    </div>

    <!-- 上传证书抽屉 -->
    <el-drawer
      v-model="dialogVisible"
      title="上传SSL证书"
      direction="rtl"
      :show-close="false"
      size="624px"
      :before-close="cancelUpload"
    >
      <template #header="{ close, titleId }">
        <div style="width: 15px;max-width: 20px;float: left">
          <el-button link @click="close">
            <el-icon>
              <Close />
            </el-icon>
          </el-button>
        </div>
        <span :id="titleId" class="titleClass">上传SSL证书</span>
      </template>
      <div style="padding: 20px;">
        <el-alert
          type="warning"
          :closable="false"
          show-icon
          title="更新证书提示"
          description="更新证书将重启服务，网站可能会短暂无法访问。"
          style="margin-bottom: 20px;"
        />
        <CertificateUpload 
          ref="certificateUploadRef"
          v-model="certForm"
          :rules="rules"
          :show-tips="false"
          :show-name="false"
        />
      </div>
      <template #footer>
        <t-button theme="primary" class="asdc-primary-but" @click="uploadCertificate" :loading="uploading">
          确定更新
        </t-button>
        <t-button variant="outline" class="asdc-default-but" style="float: left;margin-left: 5px" @click="cancelUpload">
          取消
        </t-button>
      </template>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'NginxCert',
}
</script>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { ElMessageBox } from 'element-plus'
import { getNginxSSL, uploadNginxSSL } from '@/api/nginxCert'
import { AddIcon } from 'tdesign-icons-vue-next'
import { Close } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import CertificateUpload from '@/components/certificate/CertificateUpload.vue'

// 数据定义
const certificateInfo = ref({})
const dialogVisible = ref(false)
const uploading = ref(false)
const certificateUploadRef = ref(null)
const certForm = ref({
  certificate: '',
  private_key: ''
})

// 表格列定义
const columns = [
  { colKey: 'subject', title: '域名',  },
  { colKey: 'issuer', title: '颁发者',  },
  { colKey: 'notBefore', title: '生效时间', },
  { colKey: 'notAfter', title: '过期时间', },
  { colKey: 'status', title: '状态',  }
]

// 表单验证规则
const rules = {
  certificate: [
    { required: true, message: '证书内容不能为空', trigger: 'blur' },
  ],
  private_key: [
    { required: true, message: '私钥内容不能为空', trigger: 'blur' },
  ]
}

// 计算证书是否有效
const isCertValid = computed(() => {
  if (!certificateInfo.value.notAfter) return false
  return new Date(certificateInfo.value.notAfter) > new Date()
})

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 显示上传对话框
const showUploadDialog = () => {
  certForm.value = {
    certificate: '',
    private_key: ''
  }
  dialogVisible.value = true
}

// 取消上传
const cancelUpload = () => {
  dialogVisible.value = false
  if (certificateUploadRef.value) {
    certificateUploadRef.value.resetForm()
  }
}

// 获取证书信息
const fetchCertificateInfo = async () => {
  try {
    console.log('获取证书信息')
    const response = await getNginxSSL()
    console.log('获取证书信息', response)
    certificateInfo.value = response.data
  } catch (error) {
    console.error('获取证书信息失败:', error)
    MessagePlugin.error('获取证书信息失败')
  }
}

// 上传证书
const uploadCertificate = async () => {
  if (!certForm.value.certificate || !certForm.value.private_key) {
    MessagePlugin.warning('请填写证书和私钥内容')
    return
  }

  // 验证证书表单
  const isValid = await certificateUploadRef.value?.validate()
  if (!isValid) {
    return
  }

  // 添加确认对话框
  try {
    await ElMessageBox.confirm(
      '更新证书将会重启服务，可能导致网站短暂无法访问。确定要继续吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 用户确认后继续上传流程
    uploading.value = true
    // 转换数据格式以适配 nginx API
    const uploadData = {
      certificate: certForm.value.certificate,
      privateKey: certForm.value.private_key  // 注意这里改回 privateKey
    }
    await uploadNginxSSL(uploadData)
    MessagePlugin.success('网站安全证书更新中，服务将自动重启，页面可能短暂无法访问，请稍后刷新重试。')
    dialogVisible.value = false
    // 更新证书信息，增加延迟以等待Nginx完成重启
    setTimeout(fetchCertificateInfo, 3000)
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    console.error('上传证书失败:', error)
    MessagePlugin.error('上传证书失败: ' + (error.response?.data?.message || error.message))
  } finally {
    uploading.value = false
  }
}

// 页面加载时获取证书信息
onMounted(() => {
  fetchCertificateInfo()
})
</script>

<style scoped>
.admin-box-m {
  padding: 0;
  height: 100%;
}

.header {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.asdc-primary-but {
  width: 110px;
  margin-right: 12px;
}

.asdc-default-but {
  width: 90px;
  margin-right: 12px;
}

.cert-info-container {
  width: 100%;
}

.cert-detail-section {
  margin-top: 20px;
  background-color: #FCFCFC;
  border: 1px solid #EBEBEB;
  border-radius: 5px;
  padding: 15px 20px;
}

.cert-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-label {
  color: #252631;
  opacity: 0.5;
  min-width: 80px;
}

.detail-value {
  color: #252631;
  word-break: break-all;
}

.empty-cert {
  margin: 60px auto;
  width: 100%;
  text-align: center;
}

.titleClass {
  font-size: 14px;
  font-weight: 500;
  color: #72767b;
  margin-left: 15px;
}

.frame {
  min-height: calc(100vh - 280px);
}

/* .nginx-cert-frame {
  border-left: 4px solid var(--main-color-theme) !important;
} */
</style>

<style lang="scss">
.el-drawer__header {
  margin-bottom: 5px !important;
  border-bottom: 1px solid #EBEBEB;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
</style>