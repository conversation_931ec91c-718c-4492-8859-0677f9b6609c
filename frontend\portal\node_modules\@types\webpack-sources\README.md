# Installation
> `npm install --save @types/webpack-sources`

# Summary
This package contains type definitions for webpack-sources (https://github.com/webpack/webpack-sources).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-sources.

### Additional Details
 * Last updated: Tu<PERSON>, 07 Nov 2023 20:08:00 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node), [@types/source-list-map](https://npmjs.com/package/@types/source-list-map), [source-map](https://npmjs.com/package/source-map)

# Credits
These definitions were written by [e-cloud](https://github.com/e-cloud), [<PERSON>](https://github.com/chriseppstein), and [<PERSON><PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON><PERSON>).
