package sensitive_elem

import (
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/api"
	"github.com/gin-gonic/gin"
)

func SensitiveElementApi(r *gin.RouterGroup) {

	grp := r.Group("/v1/sensitive_element/tags")
	{
		grp.GET("", api.QuerySensitiveElemTag)
		grp.POST("", api.AddSensitiveElemTag)
		grp.DELETE("", api.DeleteSensitiveElemTag)
	}

	grp = r.Group("/v1/sensitive_elem")
	{
		grp.GET("", api.QuerySensitiveElem)
		grp.POST("", api.AddSensitiveElem)
		grp.DELETE("", api.DeleteSensitiveElem)
		grp.PUT("", api.ChangeSensitiveElem)
		grp.GET("/summary", api.ElemSummary)
		grp.GET("/total", api.SensitiveElemTotal)
		grp.GET("/algorithm", api.QuerySensitiveAlgorithmElem)
	}

	grp = r.Group("/v1/sensitive_strategy")
	{
		grp.POST("/query", api.QuerySensitiveStrategy)
		grp.POST("", api.AddSensitiveStrategy)
		grp.DELETE("", api.DeleteSensitiveStrategy)
		grp.PUT("", api.ChangeSensitiveStrategy)
		grp.DELETE("/batch", api.DelSensitiveStgList)
		grp.PUT("/batch", api.UpdateStgList)
	}
	grp = r.Group("/v1/sensitive_strategy")
	{
		grp.GET("/summary", api.DefaultStrategySummary)
	}
	grp = r.Group("/v1/sensitive_category")
	{
		grp.POST("", api.AddSensitiveCategory)
		grp.GET("/list", api.GetSensitiveCategoryList)
		grp.PUT("", api.UpdateSensitiveCategory)
		grp.DELETE("", api.DelSensitiveCategory)
		grp.GET("/condition", api.GetSensitiveCategoryCondition)
		grp.POST("/temp", api.CreateStgByTemp)
	}
}
