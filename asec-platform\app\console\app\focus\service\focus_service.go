package service

import (
	"asdsec.com/asec/platform/app/console/app/focus/model"
	"asdsec.com/asec/platform/app/console/app/focus/repository"
	tagService "asdsec.com/asec/platform/app/console/app/user_tag/service"
	"asdsec.com/asec/platform/pkg/aerrors"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"context"
	"sort"
	"strings"
	"sync"
)

var FocusServiceImpl FocusService

var FocusServiceInit sync.Once

type focusService struct {
	db repository.FocusRepository
}

func (f focusService) GetFocusUserList(ctx context.Context, req model.GetFocusUserListReq) (model.GetFocusUserListRsp, error) {
	rsp, err := f.db.GetFocusUserList(ctx, req)
	if err != nil {
		return model.GetFocusUserListRsp{}, err
	}
	tagMap := make(map[string]bool)
	for _, v := range rsp.Data {
		if !tagMap[v.TagId] {
			tagMap[v.TagId] = true
		}
	}
	tagList, err := tagService.GetUserTagService().GetUserTagList(ctx)
	resData := rsp.Data
	for _, v := range tagList {
		if !tagMap[v.Id] {
			if req.Search != "" && !strings.Contains(v.Name, req.Search) {
				continue
			}
			resData = append(resData, model.FocusUserItem{
				TagInfo: model.TagInfo{TagId: v.Id, TagName: v.Name}, Users: make([]model.FocusUser, 0),
			})
			tagMap[v.Id] = true
		}
	}
	// 分页
	res := PageItems(resData, req.Offset, req.Limit)
	return model.GetFocusUserListRsp{
		CommonPage: modelTable.CommonPage{TotalNum: len(res), PageSize: req.Limit, CurrentPage: req.Offset},
		Data:       res}, nil
}

func PageItems(items []model.FocusUserItem, page, pageSize int) []model.FocusUserItem {
	if pageSize == -1 || pageSize == 0 || len(items) == 0 {
		return items
	}

	// 根据page和pageSize计算offset
	offset := page

	// 对items进行排序
	sort.Slice(items, func(i, j int) bool {
		return items[i].Count > items[j].Count
	})

	maxLen := offset + pageSize
	if len(items) < maxLen {
		maxLen = len(items)
	}
	// 返回分页后数据
	return items[offset:maxLen]
}
func (f focusService) GetFocusList(ctx context.Context, req model.FocusOnReq) (model.GetFocusListRsp, error) {
	return f.db.GetFocusList(ctx, req)
}

func (f focusService) FocusOn(ctx context.Context, req model.FocusOnCommonReq) aerrors.AError {
	return f.db.FocusOn(ctx, req)
}

type FocusService interface {
	GetFocusList(ctx context.Context, req model.FocusOnReq) (model.GetFocusListRsp, error)
	FocusOn(ctx context.Context, req model.FocusOnCommonReq) aerrors.AError
	GetFocusUserList(ctx context.Context, req model.GetFocusUserListReq) (model.GetFocusUserListRsp, error)
}

func GetFocusService() FocusService {
	FocusServiceInit.Do(func() {
		FocusServiceImpl = &focusService{db: repository.NewFocusRepository()}
	})
	return FocusServiceImpl
}
