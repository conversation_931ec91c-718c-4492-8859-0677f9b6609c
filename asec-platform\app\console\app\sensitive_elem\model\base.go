package model

// TODO cl 把这些表结构定义放到 pkg下面
import (
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"time"
)

type SensitiveElemDB struct {
	Id           int           `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	Category     int8          `gorm:"column:category;type:int2;comment:类型(1关键词匹配;2正则)" json:"category"`
	Description  string        `gorm:"column:description;type:varchar(255);comment:规则描述" json:"description"`
	ElementName  string        `gorm:"column:sensitive_element_name;type:varchar(255);comment:敏感元素名" json:"sensitive_element_name"`
	RelatedCount int           `gorm:"column:related_count;type:int;comment:匹配条数" json:"related_count"`
	BuiltIn      int8          `gorm:"column:built_in;type:int2;comment:类型(1关键词匹配;2正则)" json:"built_in"`
	Value        pgtype.JSONB  `gorm:"column:value;type:varchar[];comment:关键词/正则表达式" json:"value"`
	Tags         pq.Int32Array `gorm:"column:tags;type:int[];comment:标签(数组)" json:"tags"`
	CorpId       string        `gorm:"column:corp_id;type:varchar(255);comment:租户id" json:"corp_id"`
	CreateTime   time.Time     `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"created_at"`
	UpdateTime   time.Time     `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"updated_at"`
}

func (SensitiveElemDB) TableName() string {
	return "tb_sensitive_element"
}

type SensitiveElemTagDB struct {
	Id         uint64    `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	Name       string    `gorm:"column:name;type:varchar;comment:标签名称" json:"name"`
	CreateTime time.Time `gorm:"column:create_time;type:timestamptz;comment:创建时间" json:"create_time"`
}

func (SensitiveElemTagDB) TableName() string {
	return "tb_sensitive_elem_label"
}

type SensitiveStrategyDB struct {
	Id                 string               `gorm:"column:id;primaryKey;type:varchar(64);comment:主键" json:"id"`
	RuleName           string               `gorm:"column:rule_name;type:varchar(255);comment:规则名称" json:"rule_name"`
	SensitiveLevel     int16                `gorm:"column:sensitive_level;type:smallint;comment:敏感等级" json:"sensitive_level"`
	RuleDescription    string               `gorm:"column:rule_description;type:varchar(255);comment:规则描述" json:"rule_description"`
	Enable             int16                `gorm:"column:enable;type:smallint;comment:是否开启此规则" json:"enable"`
	CheckFileSuffix    int16                `gorm:"column:check_file_suffix;type:smallint;comment:是否检查文件后缀" json:"check_file_suffix"`
	MinFileSize        int32                `gorm:"column:min_file_size;type:integer;comment:最小文件大小" json:"min_file_size"`
	MinFileSizeUnit    string               `gorm:"column:min_file_size_unit;type:varchar;comment:最小文件大小类型(KB/MB)" json:"min_file_size_unit"`
	MaxFileSize        int32                `gorm:"column:max_file_size;type:integer;comment:最大文件大小" json:"max_file_size"`
	MaxFileSizeUnit    string               `gorm:"column:max_file_size_unit;type:varchar;comment:最大文件大小类型(KB/MB)" json:"max_file_size_unit"`
	CorpId             string               `gorm:"column:corp_id;type:varchar(64);comment:租户ID " json:"corp_id"`
	FileTypeCode       pq.Int64Array        `gorm:"column:file_type_code;type:bigint[];comment:要扫描的文件类型" json:"file_type_code"`
	CheckFileEncrypted int16                `gorm:"column:check_file_encrypted;type:smallint;comment:检查文件是否加密 " json:"check_file_encrypted"`
	RuleOperator       string               `gorm:"column:rule_operator;type:varchar(255);comment:规则操作符 " json:"rule_operator"`
	FileNameOperator   string               `gorm:"column:filename_operator;type:varchar(255);comment:文件名操作符 " json:"filename_operator"`
	ContentOperator    string               `gorm:"column:content_operator;type:varchar(255);comment:??操作符 " json:"content_operator"`
	CreateAt           time.Time            `gorm:"column:create_at;type:timestamptz;comment:创建时间" json:"create_at"`
	UpdateAt           time.Time            `gorm:"column:update_at;type:timestamptz;comment:更新时间" json:"update_at"`
	CategoryId         string               `gorm:"column:category_id;type:varchar;comment:种类ID" json:"category_id"`
	Category           string               `gorm:"column:category;type:varchar(255);comment:种类描述" json:"category"`
	FileNameRule       []byte               `gorm:"column:filename_rule;type:[]byte;comment:文件名匹配规则" json:"filename_rule"`
	ContentRule        []byte               `gorm:"column:content_rule;type:[]byte;comment:文件内容匹配规则" json:"content_rule"`
	BuiltIn            int8                 `gorm:"column:built_in;type:int2;comment:内置(1内置;2自定义)" json:"built_in"`
	AssocRule          []AlertRulePartialDB `gorm:"-" json:"assoc_rule"`
	SourceId           pq.StringArray       `gorm:"column:source_id;type:string" json:"source_id"`
	IdentifyWay        pq.Int32Array        `gorm:"column:identify_way;type:int4" json:"identify_way"`
}

func (SensitiveStrategyDB) TableName() string {
	return "tb_sensitive_strategy"
}

type SensitiveRuleTypeDB struct {
	Id       uint64 `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	TypeName string `gorm:"column:name;type_name:varchar(255);comment:规则名称" json:"type_name"` //产品项目资料
}

func (SensitiveRuleTypeDB) TableName() string {
	return "tb_sensitive_rule_type"
}

type AlertRulePartialDB struct {
	ID           string         `gorm:"column:id;primaryKey;type:string;comment:主键" json:"id"`
	Name         string         `gorm:"column:name;type:string;comment:规则名称" json:"name"`
	SensitiveIDS pq.StringArray `gorm:"column:sensitive_ids;type:varchar[];comment:关联的敏感数据策略" json:"sensitive_ids"`
}

func (AlertRulePartialDB) TableName() string {
	return "tb_alert_rule"
}

type FileTypeDB struct {
	Id   uint64 `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	Code int64  `gorm:"column:code;type:int64;comment:文件类型Code" json:"code"`
	Name string `gorm:"column:name;type:string;comment:文件类型名称" json:"name"`
}

type DdrScore struct {
	Id             string
	CorpId         string
	SensitiveLevel int
}

func (FileTypeDB) TableName() string {
	return "tb_file_type"
}
