package service

import (
	v1 "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/console/app/module_switch/repository"
	"context"
	"sync"
)

var NFSettingsServiceImpl NFSettingsService

// NFSettingsServiceInit 单例对象
var NFSettingsServiceInit sync.Once

type NFSettingsService interface {
	UpdateNFSettings(ctx context.Context, req *v1.NetworkFilterSettings) error
}

type nfSettingsService struct {
	db repository.NFSettingsRepository
}

func (n nfSettingsService) UpdateNFSettings(ctx context.Context, req *v1.NetworkFilterSettings) error {
	return n.db.UpdateNFSettings(ctx, req)
}

func GetNFSettingsService() NFSettingsService {
	NFSettingsServiceInit.Do(func() {
		NFSettingsServiceImpl = &nfSettingsService{db: repository.NewNFSettingsRepository()}
	})
	return NFSettingsServiceImpl
}
