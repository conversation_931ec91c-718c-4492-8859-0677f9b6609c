package service

import (
	"asdsec.com/asec/platform/app/console/app/scan_stg/dto"
	"asdsec.com/asec/platform/app/console/app/scan_stg/repository"
	"context"
	"sync"
	"time"
)

var ScanTaskServiceImpl ScanTaskService

// ScanTaskServiceInit 单例对象
var ScanTaskServiceInit sync.Once

type ScanTaskService interface {
	AddTaskByStg(ctx context.Context, stgId string) error
	CheckTaskByStg(ctx context.Context, stgId string) (bool, error)
	ScanNowByStg(ctx context.Context, stgId string) error
	StopScanByStg(ctx context.Context, stgId, date, taskId string) error

	GetScanTaskList(ctx context.Context, req dto.GetScanTaskListReq) (dto.GetScanTaskListRsp, error)
	GetTaskResultDetail(ctx context.Context, id string) (dto.GetScanTaskDetailRsp, error)
	GetTaskResultUser(ctx context.Context, req dto.GetScanTaskUserReq) (dto.GetScanTaskUserRsp, error)
	GetCleanTaskList(ctx context.Context) ([]dto.CleanTask, error)
}

type scanTaskService struct {
	db repository.ScanTaskRepository
}

func (s scanTaskService) GetCleanTaskList(ctx context.Context) ([]dto.CleanTask, error) {
	return s.db.GetCleanTaskList(ctx)
}

func (s scanTaskService) GetScanTaskList(ctx context.Context, req dto.GetScanTaskListReq) (dto.GetScanTaskListRsp, error) {
	return s.db.GetScanTaskList(ctx, req)
}

func (s scanTaskService) GetTaskResultDetail(ctx context.Context, id string) (dto.GetScanTaskDetailRsp, error) {
	return s.db.GetTaskResultDetail(ctx, id)
}

func (s scanTaskService) GetTaskResultUser(ctx context.Context, req dto.GetScanTaskUserReq) (dto.GetScanTaskUserRsp, error) {
	return s.db.GetTaskResultUser(ctx, req)
}

func (s scanTaskService) AddTaskByStg(ctx context.Context, stgId string) error {
	date := time.Now().Format("2006-01-02")
	return s.db.AddTaskByStgAndDate(ctx, stgId, date)
}

func (s scanTaskService) CheckTaskByStg(ctx context.Context, stgId string) (bool, error) {
	date := time.Now().Format("2006-01-02")
	return s.db.CheckTaskByStgAndDate(ctx, stgId, date)
}

func (s scanTaskService) ScanNowByStg(ctx context.Context, stgId string) error {
	date := time.Now().Format("2006-01-02")
	return s.db.AddTaskByStgAndDate(ctx, stgId, date)
}

func (s scanTaskService) StopScanByStg(ctx context.Context, stgId, date, taskId string) error {
	return s.db.StopTaskByStgAndDate(ctx, stgId, date, taskId)
}

func GetScanTaskService() ScanTaskService {
	ScanTaskServiceInit.Do(func() {
		ScanTaskServiceImpl = &scanTaskService{db: repository.NewScanTaskRepository()}
	})
	return ScanTaskServiceImpl
}
