package model

import "time"

type SensitiveCategory struct {
	Id        string    `gorm:"id" json:"id"`
	Name      string    `gorm:"name" json:"name"`
	CorpId    string    `gorm:"corp_id" json:"corp_id"`
	IsDefault bool      `gorm:"is_default" json:"is_default"`
	IconCode  string    `gorm:"icon_code" json:"icon_code"`
	BuiltIn   int       `gorm:"built_in" json:"built_in"` // 1-自定义， 2-内置
	CreatedAt time.Time `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"updated_at"`
}

func (SensitiveCategory) TableName() string {
	return "tb_sensitive_category"
}
