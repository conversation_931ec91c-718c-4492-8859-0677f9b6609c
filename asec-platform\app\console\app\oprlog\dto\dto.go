package dto

import (
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/pkg/model"
)

type ListOperateLogReq struct {
	model.Pagination
	// 生效时间起
	StartTime      string `json:"start_time"`
	StartTimestamp int64  `json:"-"`
	// 生效时间止
	EndTime      string `json:"end_time"`
	EndTimestamp int64  `json:"-"`
	ResourceType string `json:"resource_type"`
}

var DisResourceTypes = []string{common.ApplianceResourceType,
	common.AppResourceType,
	common.AppGroupResourceType,
	common.UserResourceType,
	common.AccessStrategyResourceType,
	common.GroupResourceType,
	common.AuthStrategyResourceType,
	common.RoleResourceType,
	//common.AuthPolicy,
	common.AdminRoleResourceType,
	common.AdminResourceType,
	common.AlertRuleResourceType,
	common.Notification,
	common.ProcessNameConfig,
	common.SensitiveCategory,
	common.EvidenceConfigType,
	common.UebaStrategyType,
	common.DdrRiskScoreType,
	common.UserRiskConfigType,
	common.SensitiveStrategyType,
	common.SensitiveStrategyElemType,
	common.SystemType,
	common.DynamicStrategyWatermarkKType,
	common.AlertWatermarkType,
	common.FactorIpTYpe,
	common.FactorNetLocationTYpe,
	common.FactorProcessTYpe,
	common.FactorTimeTYpe,
	common.FactorFileTYpe,
	common.FactorNotificationTYpe,
	common.ChannelDefineTYpe,
	common.ChannelTYpe,
	common.UnknownChannelAnalysisTYpe,
	common.DataSourceTYpe,
	common.ScanStrategyTYpe,
	common.ScanStrategyStopTYpe,
	common.SslCertificate,
	common.ModuleSwitchTYpe,
	common.AgentsSysTYpe,
	common.PlatformUpgradeTYpe,
	common.TerminalManagementTYpe,
	common.UserGroupType,
	common.RoleMangerType,
	common.AuthPolicyType,
	common.IdpType,
	common.VirtualIPResourceType,
	common.VirtualIPPoolResourceType,
	common.VirtualIPSettingsResourceType,
	common.SPAConfigType,
}

type OptResourceType struct {
	Id           string `gorm:"column:id" json:"id"`
	Name         string `gorm:"column:name" json:"name"`
	ResourceType string `gorm:"column:resource_type" json:"resource_type"`
	OptType      int    `gorm:"column:opt_type" json:"opt_type"`
}

type OptResourceTypeList struct {
	ApplicationTypes []OptResourceType `json:"application_types"`
	OptTypes         []OptResourceType `json:"opt_types"`
}

func (OptResourceType) TableName() string {
	return "tb_opt_resource"
}
