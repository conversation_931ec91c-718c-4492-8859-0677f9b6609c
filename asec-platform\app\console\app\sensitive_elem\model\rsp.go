package model

import (
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"time"
)

// 敏感分类
type GetSensitiveCategoryListRsp struct {
	Id        string `gorm:"id" json:"id"`
	Name      string `gorm:"name" json:"name"`
	Count     int    `gorm:"count" json:"count"`
	IsDefault bool   `gorm:"is_default" json:"is_default"`
	IconCode  string `gorm:"icon_code" json:"icon_code"`
}

type GetSensitiveCategoryConditionRsp struct {
	Id                string                  `gorm:"id" json:"id"`
	Name              string                  `gorm:"name" json:"name"`
	Count             int                     `gorm:"count" json:"count"`
	SensitiveStrategy []SensitiveStrategyItem `json:"sensitive_strategy"`
}
type SensitiveStrategyItem struct {
	Id    string `gorm:"id" json:"id"`
	Name  string `gorm:"name" json:"name"`
	Level int    `gorm:"level" json:"level"`
	Count int    `gorm:"count" json:"count"`
}

// 敏感数据
type GetSensitiveStrategyListRsp struct {
	CategoryId            string              `gorm:"category_id" json:"category_id"`
	Category              string              `gorm:"category" json:"category"`
	CategoryIcon          string              `gorm:"column:category_icon" json:"category_icon"`
	CCreatedAt            time.Time           `gorm:"column:c_created_at" json:"c_created_at"`
	SensitiveStrategy     pgtype.JSONBArray   `gorm:"sensitive_strategy;type:jsonb" json:"-"`
	SensitiveStrategyList []SensitiveStrategy `gorm:"-" json:"sensitive_strategy_list"`
}

// 关联告警规则
type AssocRule struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type SensitiveStrategyList struct {
	ID               string            `gorm:"id" json:"id"`
	Enable           int               `gorm:"enable" json:"enable"`
	RuleName         string            `gorm:"rule_name" json:"rule_name"`
	SourceID         pq.StringArray    `gorm:"source_id;type:string" json:"source_id"`
	AssocRule        pgtype.JSONBArray `gorm:"assoc_rule;type:[]jsonb" json:"assoc_rule"`
	CategoryID       string            `gorm:"category_id" json:"category_id"`
	IdentifyWay      pq.Int32Array     `gorm:"identify_way;type:int" json:"identify_way"`
	FileNameRule     []byte            `gorm:"column:filename_rule;type:[]byte;comment:文件名匹配规则" json:"filename_rule"`
	ContentRule      []byte            `gorm:"column:content_rule;type:[]byte;comment:文件内容匹配规则" json:"content_rule"`
	CategoryIcon     string            `gorm:"column:category_icon" json:"category_icon"`
	MaxFileSize      int               `gorm:"max_file_size" json:"max_file_size"`
	MinFileSize      int               `gorm:"min_file_size" json:"min_file_size"`
	FileEncrypted    int               `gorm:"file_encrypted" json:"file_encrypted"`
	FileTypeCode     pq.Int32Array     `gorm:"file_type_code;type:int" json:"file_type_code"`
	RuleDescription  string            `gorm:"rule_description" json:"rule_description"`
	CheckFileSuffix  int               `gorm:"check_file_suffix" json:"check_file_suffix"`
	FilenameOperator string            `gorm:"filename_operator" json:"filename_operator"`
	ContentOperator  string            `gorm:"content_operator" json:"content_operator"`
	MaxFileSizeUnit  string            `gorm:"max_file_size_unit" json:"max_file_size_unit"`
	MinFileSizeUnit  string            `gorm:"min_file_size_unit" json:"min_file_size_unit"`
	SensitiveLevel   int16             `gorm:"sensitive_level" json:"sensitive_level"`
	CreateAt         time.Time         `gorm:"column:create_at;type:timestamptz;comment:更新时间" json:"create_at"`
	CategoryName     string            `gorm:"category_name" json:"category_name"`
	CCreatedAt       time.Time         `gorm:"column:c_created_at" json:"c_created_at"`
}

type SensitiveStrategy struct {
	ID               string         `gorm:"column:id" json:"id"`
	Enable           int            `gorm:"column:enable" json:"enable"`
	RuleName         string         `gorm:"column:rule_name" json:"rule_name"`
	SourceID         []string       `gorm:"column:source_id" json:"source_id"`
	AssocRule        []AssocRule    `gorm:"column:assoc_rule" json:"assoc_rule"`
	CategoryID       string         `gorm:"column:category_id" json:"category_id"`
	CategoryName     string         `gorm:"column:category_name" json:"category_name"`
	CategoryIcon     string         `gorm:"column:category_icon" json:"category_icon"`
	IdentifyWay      []int32        `gorm:"column:identify_way" json:"identify_way"`
	FilenameRule     []FilenameRule `gorm:"column:filename_rule" json:"filename_rule"`
	ContentRule      []FilenameRule `gorm:"column:content_rule" json:"content_rule"`
	MaxFileSize      int            `gorm:"column:max_file_size" json:"max_file_size"`
	MinFileSize      int            `gorm:"column:min_file_size" json:"min_file_size"`
	FileEncrypted    int            `gorm:"column:file_encrypted" json:"file_encrypted"`
	FileTypeCode     []int32        `gorm:"column:file_type_code" json:"file_type_code"`
	RuleDescription  string         `gorm:"column:rule_description" json:"rule_description"`
	CheckFileSuffix  int            `gorm:"column:check_file_suffix" json:"check_file_suffix"`
	FilenameOperator string         `gorm:"column:filename_operator" json:"filename_operator"`
	ContentOperator  string         `gorm:"column:content_operator" json:"content_operator"`
	MaxFileSizeUnit  string         `gorm:"column:max_file_size_unit" json:"max_file_size_unit"`
	MinFileSizeUnit  string         `gorm:"column:min_file_size_unit" json:"min_file_size_unit"`
	SensitiveLevel   int16          `gorm:"column:sensitive_level" json:"sensitive_level"`
	CreateAt         time.Time      `gorm:"column:create_at;type:timestamptz;comment:更新时间" json:"create_at"`
	CCreatedAt       time.Time      `gorm:"column:c_created_at" json:"c_created_at"`
}

type FilenameRule struct {
	Count                 int    `json:"count"`
	Operator              string `json:"operator"`
	SensitiveElementCodes []int  `json:"sensitive_element_codes"`
}

type AlertEventCountRsp struct {
	SensitiveRuleId string `gorm:"sensitive_rule_id" json:"sensitive_rule_id"`
	Count           int    `gorm:"count" json:"count"`
}

type SgCategoryRelation struct {
	SensitiveRuleId string `gorm:"sensitive_rule_id" json:"sensitive_rule_id"`
	RuleName        string `gorm:"rule_name" json:"rule_name"`
	SensitiveLevel  int    `gorm:"sensitive_level" json:"sensitive_level"`
	CategoryId      string `gorm:"category_id" json:"category_id"`
	CategoryName    string `gorm:"category_name" json:"category_name"`
}
