package constants

const (
	ApisixGlobalRuleRoute       = "/apisix/admin/global_rules/1"
	ApisixKafkaPluginName       = "asec-kafka-logger"
	SdpInValidError             = "SdpInValidError"
	RequestHeaderField          = "request_header"
	ResponseHeaderField         = "response_header"
	KafkaSevenAccessLogTopic    = "seven_access_log"
	KafkaSevenAccessLogPort     = 39092
	DefaultStaticResourceEnable = false
	ReqBody                     = "req_body"
	RspBody                     = "rsp_body"
)

var DefaultStaticResource = []string{
	"js",
	"css",
	"png",
	"jpg",
	"gif",
	"bmp",
	"svg",
	"ico",
	"woff",
	"ttf",
	"otf",
	"eot",
	"mp4",
	"avi",
	"flv",
	"mpeg",
	"mpg",
	"mp3",
	"wav",
	"ogg",
	"mov",
}
var DefaultLogFormatMap = map[string]string{
	"request_id":  "$http_X-Request-Id",
	"access_time": "$http_Asec_Access_Time",
	"user_id":     "$http_Asec-User-Id",
	"user_name":   "$http_Asec-User-Name",

	"app_id":          "$http_app_id",
	"app_name":        "$http_app_name",
	"url":             "$http_Asec_Full_Url",
	"strategy_id":     "$http_strategy_id",
	"strategy_name":   "$http_strategy_name",
	"strategy_action": "$http_action",

	"client_ip":   "$remote_addr",
	"host":        "$host",
	"uri":         "$uri",
	"pid":         "$pid",
	"etag":        "$sent_http_ETag",
	"server_addr": "$upstream_addr",
	"schema":      "$scheme",
}
