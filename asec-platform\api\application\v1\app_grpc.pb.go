// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: application/v1/app.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	App_SeGetApp_FullMethodName                   = "/asdsec.core.api.app.App/SeGetApp"
	App_SERoute_FullMethodName                    = "/asdsec.core.api.app.App/SERoute"
	App_SeGetStrategy_FullMethodName              = "/asdsec.core.api.app.App/SeGetStrategy"
	App_WebAppInfo_FullMethodName                 = "/asdsec.core.api.app.App/WebAppInfo"
	App_WebAccessInfo_FullMethodName              = "/asdsec.core.api.app.App/WebAccessInfo"
	App_UciUserInfo_FullMethodName                = "/asdsec.core.api.app.App/UciUserInfo"
	App_WebGatewayRsApp_FullMethodName            = "/asdsec.core.api.app.App/WebGatewayRsApp"
	App_WebGatewayRsCrt_FullMethodName            = "/asdsec.core.api.app.App/WebGatewayRsCrt"
	App_WebGatewayWatermark_FullMethodName        = "/asdsec.core.api.app.App/WebGatewayWatermark"
	App_WebGatewayHosts_FullMethodName            = "/asdsec.core.api.app.App/WebGatewayHosts"
	App_WebGatewayVirtualIPPools_FullMethodName   = "/asdsec.core.api.app.App/WebGatewayVirtualIPPools"
	App_ReportVirtualIPAllocations_FullMethodName = "/asdsec.core.api.app.App/ReportVirtualIPAllocations"
	App_ReportTrafficStats_FullMethodName         = "/asdsec.core.api.app.App/ReportTrafficStats"
	App_GatewayCommand_FullMethodName             = "/asdsec.core.api.app.App/GatewayCommand"
	App_ReportGatewayCommandResult_FullMethodName = "/asdsec.core.api.app.App/ReportGatewayCommandResult"
	App_WebGatewayRsSSOIDP_FullMethodName         = "/asdsec.core.api.app.App/WebGatewayRsSSOIDP"
	App_WebGatewayPlatformDomain_FullMethodName   = "/asdsec.core.api.app.App/WebGatewayPlatformDomain"
)

// AppClient is the client API for App service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppClient interface {
	// 网关获取应用信息
	SeGetApp(ctx context.Context, in *SeGetAppReq, opts ...grpc.CallOption) (*GetAppResp, error)
	// connector模式下网关拉取路由信息
	SERoute(ctx context.Context, in *SERouteReq, opts ...grpc.CallOption) (*SERoutes, error)
	// 网关获取策略信息
	SeGetStrategy(ctx context.Context, in *SeGetAppReq, opts ...grpc.CallOption) (*SeGetStrategyResp, error)
	// web网关获取应用
	WebAppInfo(ctx context.Context, in *WebAppInfoReq, opts ...grpc.CallOption) (*WebAppInfoResp, error)
	// web网关获取策略
	WebAccessInfo(ctx context.Context, in *WebAccessInfoReq, opts ...grpc.CallOption) (*WebAccessInfoResp, error)
	// 网关获取用户评分
	UciUserInfo(ctx context.Context, in *UciUserInfoReq, opts ...grpc.CallOption) (*UciUserInfoResp, error)
	// 网关拉取关联应用
	WebGatewayRsApp(ctx context.Context, in *WebGatewayRsAppReq, opts ...grpc.CallOption) (*WebGatewayRsAppResp, error)
	// 网关拉取关联证书
	WebGatewayRsCrt(ctx context.Context, in *WebGatewayRsCrtReq, opts ...grpc.CallOption) (*WebGatewayRsCrtResp, error)
	// 网关拉取web水印
	WebGatewayWatermark(ctx context.Context, in *WebGatewayWatermarkReq, opts ...grpc.CallOption) (*WebGatewayWatermarkResp, error)
	// 网关拉取Hosts
	WebGatewayHosts(ctx context.Context, in *WebGatewayHostsReq, opts ...grpc.CallOption) (*WebGatewayHostsResp, error)
	// 网关拉取虚拟IP池配置
	WebGatewayVirtualIPPools(ctx context.Context, in *WebGatewayVirtualIPPoolsReq, opts ...grpc.CallOption) (*WebGatewayVirtualIPPoolsResp, error)
	// 网关上报IP分配状态
	ReportVirtualIPAllocations(ctx context.Context, in *ReportVirtualIPAllocationsReq, opts ...grpc.CallOption) (*ReportVirtualIPAllocationsResp, error)
	// 网关上报流量统计
	ReportTrafficStats(ctx context.Context, in *TrafficStatsReq, opts ...grpc.CallOption) (*TrafficStatsResp, error)
	// 网关命令拉取
	GatewayCommand(ctx context.Context, in *GatewayCommandReq, opts ...grpc.CallOption) (*GatewayCommandResp, error)
	// 网关命令结果报告
	ReportGatewayCommandResult(ctx context.Context, in *GatewayCommandResultReq, opts ...grpc.CallOption) (*GatewayCommandResultResp, error)
	// 网关拉取SSO配置
	WebGatewayRsSSOIDP(ctx context.Context, in *WebGatewayRsSSOIDPReq, opts ...grpc.CallOption) (*WebGatewayRsSSOIDPResp, error)
	// 网关获取连接的平台域名
	WebGatewayPlatformDomain(ctx context.Context, in *WebGatewayPlatformDomainReq, opts ...grpc.CallOption) (*WebGatewayPlatformDomainResp, error)
}

type appClient struct {
	cc grpc.ClientConnInterface
}

func NewAppClient(cc grpc.ClientConnInterface) AppClient {
	return &appClient{cc}
}

func (c *appClient) SeGetApp(ctx context.Context, in *SeGetAppReq, opts ...grpc.CallOption) (*GetAppResp, error) {
	out := new(GetAppResp)
	err := c.cc.Invoke(ctx, App_SeGetApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) SERoute(ctx context.Context, in *SERouteReq, opts ...grpc.CallOption) (*SERoutes, error) {
	out := new(SERoutes)
	err := c.cc.Invoke(ctx, App_SERoute_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) SeGetStrategy(ctx context.Context, in *SeGetAppReq, opts ...grpc.CallOption) (*SeGetStrategyResp, error) {
	out := new(SeGetStrategyResp)
	err := c.cc.Invoke(ctx, App_SeGetStrategy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebAppInfo(ctx context.Context, in *WebAppInfoReq, opts ...grpc.CallOption) (*WebAppInfoResp, error) {
	out := new(WebAppInfoResp)
	err := c.cc.Invoke(ctx, App_WebAppInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebAccessInfo(ctx context.Context, in *WebAccessInfoReq, opts ...grpc.CallOption) (*WebAccessInfoResp, error) {
	out := new(WebAccessInfoResp)
	err := c.cc.Invoke(ctx, App_WebAccessInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) UciUserInfo(ctx context.Context, in *UciUserInfoReq, opts ...grpc.CallOption) (*UciUserInfoResp, error) {
	out := new(UciUserInfoResp)
	err := c.cc.Invoke(ctx, App_UciUserInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebGatewayRsApp(ctx context.Context, in *WebGatewayRsAppReq, opts ...grpc.CallOption) (*WebGatewayRsAppResp, error) {
	out := new(WebGatewayRsAppResp)
	err := c.cc.Invoke(ctx, App_WebGatewayRsApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebGatewayRsCrt(ctx context.Context, in *WebGatewayRsCrtReq, opts ...grpc.CallOption) (*WebGatewayRsCrtResp, error) {
	out := new(WebGatewayRsCrtResp)
	err := c.cc.Invoke(ctx, App_WebGatewayRsCrt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebGatewayWatermark(ctx context.Context, in *WebGatewayWatermarkReq, opts ...grpc.CallOption) (*WebGatewayWatermarkResp, error) {
	out := new(WebGatewayWatermarkResp)
	err := c.cc.Invoke(ctx, App_WebGatewayWatermark_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebGatewayHosts(ctx context.Context, in *WebGatewayHostsReq, opts ...grpc.CallOption) (*WebGatewayHostsResp, error) {
	out := new(WebGatewayHostsResp)
	err := c.cc.Invoke(ctx, App_WebGatewayHosts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebGatewayVirtualIPPools(ctx context.Context, in *WebGatewayVirtualIPPoolsReq, opts ...grpc.CallOption) (*WebGatewayVirtualIPPoolsResp, error) {
	out := new(WebGatewayVirtualIPPoolsResp)
	err := c.cc.Invoke(ctx, App_WebGatewayVirtualIPPools_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) ReportVirtualIPAllocations(ctx context.Context, in *ReportVirtualIPAllocationsReq, opts ...grpc.CallOption) (*ReportVirtualIPAllocationsResp, error) {
	out := new(ReportVirtualIPAllocationsResp)
	err := c.cc.Invoke(ctx, App_ReportVirtualIPAllocations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) ReportTrafficStats(ctx context.Context, in *TrafficStatsReq, opts ...grpc.CallOption) (*TrafficStatsResp, error) {
	out := new(TrafficStatsResp)
	err := c.cc.Invoke(ctx, App_ReportTrafficStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GatewayCommand(ctx context.Context, in *GatewayCommandReq, opts ...grpc.CallOption) (*GatewayCommandResp, error) {
	out := new(GatewayCommandResp)
	err := c.cc.Invoke(ctx, App_GatewayCommand_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) ReportGatewayCommandResult(ctx context.Context, in *GatewayCommandResultReq, opts ...grpc.CallOption) (*GatewayCommandResultResp, error) {
	out := new(GatewayCommandResultResp)
	err := c.cc.Invoke(ctx, App_ReportGatewayCommandResult_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebGatewayRsSSOIDP(ctx context.Context, in *WebGatewayRsSSOIDPReq, opts ...grpc.CallOption) (*WebGatewayRsSSOIDPResp, error) {
	out := new(WebGatewayRsSSOIDPResp)
	err := c.cc.Invoke(ctx, App_WebGatewayRsSSOIDP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) WebGatewayPlatformDomain(ctx context.Context, in *WebGatewayPlatformDomainReq, opts ...grpc.CallOption) (*WebGatewayPlatformDomainResp, error) {
	out := new(WebGatewayPlatformDomainResp)
	err := c.cc.Invoke(ctx, App_WebGatewayPlatformDomain_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppServer is the server API for App service.
// All implementations must embed UnimplementedAppServer
// for forward compatibility
type AppServer interface {
	// 网关获取应用信息
	SeGetApp(context.Context, *SeGetAppReq) (*GetAppResp, error)
	// connector模式下网关拉取路由信息
	SERoute(context.Context, *SERouteReq) (*SERoutes, error)
	// 网关获取策略信息
	SeGetStrategy(context.Context, *SeGetAppReq) (*SeGetStrategyResp, error)
	// web网关获取应用
	WebAppInfo(context.Context, *WebAppInfoReq) (*WebAppInfoResp, error)
	// web网关获取策略
	WebAccessInfo(context.Context, *WebAccessInfoReq) (*WebAccessInfoResp, error)
	// 网关获取用户评分
	UciUserInfo(context.Context, *UciUserInfoReq) (*UciUserInfoResp, error)
	// 网关拉取关联应用
	WebGatewayRsApp(context.Context, *WebGatewayRsAppReq) (*WebGatewayRsAppResp, error)
	// 网关拉取关联证书
	WebGatewayRsCrt(context.Context, *WebGatewayRsCrtReq) (*WebGatewayRsCrtResp, error)
	// 网关拉取web水印
	WebGatewayWatermark(context.Context, *WebGatewayWatermarkReq) (*WebGatewayWatermarkResp, error)
	// 网关拉取Hosts
	WebGatewayHosts(context.Context, *WebGatewayHostsReq) (*WebGatewayHostsResp, error)
	// 网关拉取虚拟IP池配置
	WebGatewayVirtualIPPools(context.Context, *WebGatewayVirtualIPPoolsReq) (*WebGatewayVirtualIPPoolsResp, error)
	// 网关上报IP分配状态
	ReportVirtualIPAllocations(context.Context, *ReportVirtualIPAllocationsReq) (*ReportVirtualIPAllocationsResp, error)
	// 网关上报流量统计
	ReportTrafficStats(context.Context, *TrafficStatsReq) (*TrafficStatsResp, error)
	// 网关命令拉取
	GatewayCommand(context.Context, *GatewayCommandReq) (*GatewayCommandResp, error)
	// 网关命令结果报告
	ReportGatewayCommandResult(context.Context, *GatewayCommandResultReq) (*GatewayCommandResultResp, error)
	// 网关拉取SSO配置
	WebGatewayRsSSOIDP(context.Context, *WebGatewayRsSSOIDPReq) (*WebGatewayRsSSOIDPResp, error)
	// 网关获取连接的平台域名
	WebGatewayPlatformDomain(context.Context, *WebGatewayPlatformDomainReq) (*WebGatewayPlatformDomainResp, error)
	mustEmbedUnimplementedAppServer()
}

// UnimplementedAppServer must be embedded to have forward compatible implementations.
type UnimplementedAppServer struct {
}

func (UnimplementedAppServer) SeGetApp(context.Context, *SeGetAppReq) (*GetAppResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeGetApp not implemented")
}
func (UnimplementedAppServer) SERoute(context.Context, *SERouteReq) (*SERoutes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SERoute not implemented")
}
func (UnimplementedAppServer) SeGetStrategy(context.Context, *SeGetAppReq) (*SeGetStrategyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeGetStrategy not implemented")
}
func (UnimplementedAppServer) WebAppInfo(context.Context, *WebAppInfoReq) (*WebAppInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebAppInfo not implemented")
}
func (UnimplementedAppServer) WebAccessInfo(context.Context, *WebAccessInfoReq) (*WebAccessInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebAccessInfo not implemented")
}
func (UnimplementedAppServer) UciUserInfo(context.Context, *UciUserInfoReq) (*UciUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UciUserInfo not implemented")
}
func (UnimplementedAppServer) WebGatewayRsApp(context.Context, *WebGatewayRsAppReq) (*WebGatewayRsAppResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebGatewayRsApp not implemented")
}
func (UnimplementedAppServer) WebGatewayRsCrt(context.Context, *WebGatewayRsCrtReq) (*WebGatewayRsCrtResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebGatewayRsCrt not implemented")
}
func (UnimplementedAppServer) WebGatewayWatermark(context.Context, *WebGatewayWatermarkReq) (*WebGatewayWatermarkResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebGatewayWatermark not implemented")
}
func (UnimplementedAppServer) WebGatewayHosts(context.Context, *WebGatewayHostsReq) (*WebGatewayHostsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebGatewayHosts not implemented")
}
func (UnimplementedAppServer) WebGatewayVirtualIPPools(context.Context, *WebGatewayVirtualIPPoolsReq) (*WebGatewayVirtualIPPoolsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebGatewayVirtualIPPools not implemented")
}
func (UnimplementedAppServer) ReportVirtualIPAllocations(context.Context, *ReportVirtualIPAllocationsReq) (*ReportVirtualIPAllocationsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportVirtualIPAllocations not implemented")
}
func (UnimplementedAppServer) ReportTrafficStats(context.Context, *TrafficStatsReq) (*TrafficStatsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportTrafficStats not implemented")
}
func (UnimplementedAppServer) GatewayCommand(context.Context, *GatewayCommandReq) (*GatewayCommandResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GatewayCommand not implemented")
}
func (UnimplementedAppServer) ReportGatewayCommandResult(context.Context, *GatewayCommandResultReq) (*GatewayCommandResultResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportGatewayCommandResult not implemented")
}
func (UnimplementedAppServer) WebGatewayRsSSOIDP(context.Context, *WebGatewayRsSSOIDPReq) (*WebGatewayRsSSOIDPResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebGatewayRsSSOIDP not implemented")
}
func (UnimplementedAppServer) WebGatewayPlatformDomain(context.Context, *WebGatewayPlatformDomainReq) (*WebGatewayPlatformDomainResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebGatewayPlatformDomain not implemented")
}
func (UnimplementedAppServer) mustEmbedUnimplementedAppServer() {}

// UnsafeAppServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppServer will
// result in compilation errors.
type UnsafeAppServer interface {
	mustEmbedUnimplementedAppServer()
}

func RegisterAppServer(s grpc.ServiceRegistrar, srv AppServer) {
	s.RegisterService(&App_ServiceDesc, srv)
}

func _App_SeGetApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeGetAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).SeGetApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_SeGetApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).SeGetApp(ctx, req.(*SeGetAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_SERoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SERouteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).SERoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_SERoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).SERoute(ctx, req.(*SERouteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_SeGetStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeGetAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).SeGetStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_SeGetStrategy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).SeGetStrategy(ctx, req.(*SeGetAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebAppInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebAppInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebAppInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebAppInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebAppInfo(ctx, req.(*WebAppInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebAccessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebAccessInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebAccessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebAccessInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebAccessInfo(ctx, req.(*WebAccessInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_UciUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UciUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).UciUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_UciUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).UciUserInfo(ctx, req.(*UciUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebGatewayRsApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebGatewayRsAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebGatewayRsApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebGatewayRsApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebGatewayRsApp(ctx, req.(*WebGatewayRsAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebGatewayRsCrt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebGatewayRsCrtReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebGatewayRsCrt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebGatewayRsCrt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebGatewayRsCrt(ctx, req.(*WebGatewayRsCrtReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebGatewayWatermark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebGatewayWatermarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebGatewayWatermark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebGatewayWatermark_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebGatewayWatermark(ctx, req.(*WebGatewayWatermarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebGatewayHosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebGatewayHostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebGatewayHosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebGatewayHosts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebGatewayHosts(ctx, req.(*WebGatewayHostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebGatewayVirtualIPPools_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebGatewayVirtualIPPoolsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebGatewayVirtualIPPools(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebGatewayVirtualIPPools_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebGatewayVirtualIPPools(ctx, req.(*WebGatewayVirtualIPPoolsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_ReportVirtualIPAllocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportVirtualIPAllocationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).ReportVirtualIPAllocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_ReportVirtualIPAllocations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).ReportVirtualIPAllocations(ctx, req.(*ReportVirtualIPAllocationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_ReportTrafficStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrafficStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).ReportTrafficStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_ReportTrafficStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).ReportTrafficStats(ctx, req.(*TrafficStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GatewayCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GatewayCommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GatewayCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GatewayCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GatewayCommand(ctx, req.(*GatewayCommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_ReportGatewayCommandResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GatewayCommandResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).ReportGatewayCommandResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_ReportGatewayCommandResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).ReportGatewayCommandResult(ctx, req.(*GatewayCommandResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebGatewayRsSSOIDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebGatewayRsSSOIDPReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebGatewayRsSSOIDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebGatewayRsSSOIDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebGatewayRsSSOIDP(ctx, req.(*WebGatewayRsSSOIDPReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_WebGatewayPlatformDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebGatewayPlatformDomainReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).WebGatewayPlatformDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_WebGatewayPlatformDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).WebGatewayPlatformDomain(ctx, req.(*WebGatewayPlatformDomainReq))
	}
	return interceptor(ctx, in, info, handler)
}

// App_ServiceDesc is the grpc.ServiceDesc for App service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var App_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "asdsec.core.api.app.App",
	HandlerType: (*AppServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SeGetApp",
			Handler:    _App_SeGetApp_Handler,
		},
		{
			MethodName: "SERoute",
			Handler:    _App_SERoute_Handler,
		},
		{
			MethodName: "SeGetStrategy",
			Handler:    _App_SeGetStrategy_Handler,
		},
		{
			MethodName: "WebAppInfo",
			Handler:    _App_WebAppInfo_Handler,
		},
		{
			MethodName: "WebAccessInfo",
			Handler:    _App_WebAccessInfo_Handler,
		},
		{
			MethodName: "UciUserInfo",
			Handler:    _App_UciUserInfo_Handler,
		},
		{
			MethodName: "WebGatewayRsApp",
			Handler:    _App_WebGatewayRsApp_Handler,
		},
		{
			MethodName: "WebGatewayRsCrt",
			Handler:    _App_WebGatewayRsCrt_Handler,
		},
		{
			MethodName: "WebGatewayWatermark",
			Handler:    _App_WebGatewayWatermark_Handler,
		},
		{
			MethodName: "WebGatewayHosts",
			Handler:    _App_WebGatewayHosts_Handler,
		},
		{
			MethodName: "WebGatewayVirtualIPPools",
			Handler:    _App_WebGatewayVirtualIPPools_Handler,
		},
		{
			MethodName: "ReportVirtualIPAllocations",
			Handler:    _App_ReportVirtualIPAllocations_Handler,
		},
		{
			MethodName: "ReportTrafficStats",
			Handler:    _App_ReportTrafficStats_Handler,
		},
		{
			MethodName: "GatewayCommand",
			Handler:    _App_GatewayCommand_Handler,
		},
		{
			MethodName: "ReportGatewayCommandResult",
			Handler:    _App_ReportGatewayCommandResult_Handler,
		},
		{
			MethodName: "WebGatewayRsSSOIDP",
			Handler:    _App_WebGatewayRsSSOIDP_Handler,
		},
		{
			MethodName: "WebGatewayPlatformDomain",
			Handler:    _App_WebGatewayPlatformDomain_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "application/v1/app.proto",
}
