<!-- //应用图标上传组件-->
<template>
  <div class="appC">

    <t-form-item name="account">
      <t-checkbox v-model="appCompatible.CrossDomain">自动处理跨域
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            开启跨域请求后，可以使应用不受浏览器同源策略的限制，在域名、协议 、端口不相同时也能请求成功
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming" />
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>

    <t-form-item name="account">
      <t-checkbox v-model="appCompatible.smartRedirect">智能加密跳转
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            将访问HTTP协议（默认端口80）的域名请求强制转换成HTTPS协议访问，<br/>
            提升应用安全性，确保数据传输加密
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming" />
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>

    <!-- 请求/响应头改写  start--->
    <t-form-item name="account">
      <t-checkbox v-model="appCompatible.requestResponseRewrite">请求/响应头改写
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            当应用请求/响应中包含自定义头时需要在此进行配置，否则无法对自定义头进行透传
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming" />
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>

    <div id="headers-config" v-if="appCompatible.requestResponseRewrite" style="background:#fafbfc;border-radius:8px;padding:16px 20px;margin-bottom:18px;box-shadow:0 1px 4px rgba(0,0,0,0.03);">
      <div style="background:#fff;border:1px solid #e5e7eb;border-radius:6px;overflow:hidden;margin-bottom:12px;">
        <div id="headers-rewrite-list">
          <div class="header-rewrite-row" v-for=" (item,index) in appCompatible.header" :key="index" >
            <select v-model="item.field" style="width:70px;font-size:12px;padding:4px 6px;border:1px solid #d1d5db;border-radius:4px;" >
              <option value="request_header">请求头</option>
              <option value="response_header">响应头</option>
            </select>
            <select v-model="item.operation" style="width:60px;font-size:12px;padding:4px 6px;border:1px solid #d1d5db;border-radius:4px;">
              <option value="add">添加</option>
              <option value="set">修改</option>
              <template v-if="item.field === 'response_header'">
                <option value="update">更新</option>
                <option value="remove">删除</option>
              </template>
            </select>
            <input type="text" v-model="item.key" placeholder="如：Authorization"   style="flex:1;padding:4px 8px;font-size:12px;border:1px solid #d1d5db;border-radius:4px;">
            <input v-if="item.operation !== 'remove'" type="text" v-model="item.value" placeholder="如：Bearer token123"  style="flex:1;padding:4px 8px;font-size:12px;border:1px solid #d1d5db;border-radius:4px;">
            <div v-else style="flex:1;padding:4px 8px;font-size:12px;color:#9ca3af;"></div>
            <button type="button" class="row-delete-btn" @click="removeHeaderRewriteRow(index)" title="删除此规则">×</button>
          </div>
        </div>
      </div>
      <button type="button" class="add-url-rewrite-btn" @click="addHeaderRewriteRow">+ 添加规则</button>
    </div>
    <!-- 请求/响应头改写  end--->


    <t-form-item name="account">
      <t-checkbox v-model="appCompatible.customHost">自定义HOST
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            允许用户自定义Host端口。这在需要连接高端口到非标准端口的服务时非常有用，<br/>
            例如在开发环境中使用非80或443端口的服务。
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming" />
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>

    <div id="custom-host-config" v-if="appCompatible.customHost" style="background:#fafbfc;border-radius:8px;padding:18px 24px 8px 24px;margin-bottom:18px;box-shadow:0 1px 4px rgba(0,0,0,0.03);">
      <div class="form-row">
        <label>自定义HOST地址</label>
        <input type="text" v-model="appCompatible.customHostValue" style="width:100%;" placeholder="如：api.example.com:8080 或 *************:3000">
      </div>
    </div>


    <t-form-item name="account">
      <t-checkbox v-model="appCompatible.ipExtract">源IP提取方式
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
        <template #content>
          允许用户自定义如何从请求中提取源IP地中提取方式址。<br/>
          在有多个网络接口或复杂网络环境下，可以通过这个配置来确保正确获取源IP。
        </template>
        <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
          <use xlink:href="#icon-shuoming" />
        </svg>
      </el-tooltip>
      </t-checkbox>
    </t-form-item>
    <div id="ip-extract-config" v-if="appCompatible.ipExtract" style="background:#fafbfc;border-radius:8px;padding:18px 24px 8px 24px;margin-bottom:18px;box-shadow:0 1px 4px rgba(0,0,0,0.03);">
      <div class="form-row">
        <label>IP提取方式
        </label>
        <select v-model="appCompatible.ipExtractValue" style="width:100%;">
          <option value="X-Forwarded-For">X-Forwarded-For</option>
          <option value="X-Real-IP">X-Real-IP</option>
          <option value="Remote-Addr">Remote-Addr</option>
        </select>
      </div>
    </div>


    <t-form-item name="account">
      <t-checkbox v-model="appCompatible.ipInsert">源IP插入方式
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            在请求头部中插入源IP，可以配置在哪个请求头部中插入源IP，<br/>
            如果有多个字段时，可以配置字段分隔符以及插入的位置
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming" />
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>
    <div id="ip-insert-config" v-if="appCompatible.ipInsert" style="background:#fafbfc;border-radius:8px;padding:18px 24px 8px 24px;margin-bottom:18px;box-shadow:0 1px 4px rgba(0,0,0,0.03);">
      <div class="form-row" style="margin-bottom:12px;">
        <div style="display:flex;justify-content:space-between;align-items:center;">
          <label>插入位置</label>
          <el-tooltip effect="light" placement="left" popper-class="popper-shadow">
            <template #content>
              <div style="font-size:12px;">
                <div style="font-weight:500;margin-bottom:6px;">配置示例：</div>
                <div style="margin-bottom:4px;">• X-Real-IP 从左到右 分隔符, 1</div>
                <div style="margin-bottom:2px;">插入前：X-Real-IP:*******,*******</div>
                <div style="margin-bottom:6px;">插入后：X-Real-IP:客户端源IP,*******,*******</div>
                <div style="margin-bottom:4px;">• X-Real-IP 从右到左 分隔符, 1</div>
                <div style="margin-bottom:2px;">插入前：X-Real-IP:*******,*******</div>
                <div>插入后：X-Real-IP:*******,*******,客户端源IP</div>
              </div>
            </template>
            <span style="color:#347ACB;font-size:12px;cursor:pointer;text-decoration:underline;">配置示例</span>
          </el-tooltip>
        </div>
        <select v-model="appCompatible.ipInsertConfig.header" style="width:100%;">
          <option value="X-Forwarded-For">X-Forwarded-For</option>
          <option value="X-Real-IP">X-Real-IP</option>
          <option value="CF-Connecting-IP">CF-Connecting-IP</option>
          <option value="True-Client-IP">True-Client-IP</option>
        </select>
      </div>
      <div class="form-row" style="margin-bottom:12px;">
        <label>插入方向</label>
        <select v-model="appCompatible.ipInsertConfig.direction" style="width:100%;">
          <option value="left">从左到右</option>
          <option value="right">从右到左</option>
        </select>
      </div>
      <div style="display:flex;gap:12px;margin-bottom:12px;">
        <div style="flex:1;">
          <label>分隔符</label>
          <input type="text" v-model="appCompatible.ipInsertConfig.separator" style="width:100%;" placeholder="默认为逗号">
        </div>
        <div style="flex:1;">
          <label>插入位置 (第几个)</label>
          <input type="number" v-model.number="appCompatible.ipInsertConfig.position" style="width:100%;" min="1" max="100" placeholder="1-100的整数">
        </div>
      </div>
    </div>

    <t-form-item name="account">
      <t-checkbox v-model="appCompatible.async">异步请求响应
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            开启异步请求响应影响范围<br/>
            1.用户cookie过期自动调整到登录页面<br/>
            2.应用在【动态管控策略】下触发禁止下载文件弹出禁止下载拦截页面
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming" />
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>

    <t-form-item name="account">
      <t-checkbox v-model="appCompatible.routing">前端路由增强控制
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            开启资源增强控制后，可以对前后端分离模式应用进行前端资源细粒度控制，<br/>针对URL中#标识资源的权限细粒度控制。
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming" />
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>


  </div>
</template>

<script>
export default {
  name: 'AppCompatible'
}
</script>
<script setup>
import {ref, watch, onMounted, reactive} from 'vue'
import {MessagePlugin} from "tdesign-vue-next";
const props = defineProps({
  apiData:{
    type: Object,
    required: false,
    default: {}
  }
})
const appCompatible = ref(
    {
      header:[ //请求、响应头改写规则
        {field: 'request_header', operation: 'add', key: '', value: ''}
      ],
      CrossDomain:false, //自动处理跨域
      smartRedirect:false, //智能加密跳转
      requestResponseRewrite:false,//请求/响应头改写
      customHost:false,//自定义HOST
      customHostValue:'', //自定义HOST地址值
      ipExtract:false,//IP提取方式
      ipExtractValue:'X-Forwarded-For',//IP提取方式值
      ipInsert:false,//源IP插入方式
      ipInsertConfig:{ //源IP插入配置
        header:'X-Forwarded-For',//插入的请求头
        direction:'left',//插入方向：left从左到右，right从右到左
        separator:',',//分隔符
        position:1//插入位置（第几个）
      },
      async:false,//异步请求响应
      routing:false //前端路由增强控制
    }
)
defineExpose({ appCompatible })
watch(
    () => props.apiData,
    (newVal) => {
      if (newVal && newVal.id !== undefined &&
          newVal.app_type !== 'portal' &&
          newVal.id !== null && newVal.id !== '') {
        appCompatible.value.CrossDomain = newVal.web_compatible_config.default_rule.includes('cross_domain')
        appCompatible.value.smartRedirect = newVal.web_compatible_config.default_rule.includes('http2https')
        appCompatible.value.requestResponseRewrite = newVal.web_compatible_config.default_rule.includes('header_config')
        if (appCompatible.value.requestResponseRewrite ){
          appCompatible.value.header = newVal.web_compatible_config.header_config
        }
        appCompatible.value.customHost =newVal.web_compatible_config.default_rule.includes('hosts')
        appCompatible.value.customHostValue =newVal.web_compatible_config.hosts
        appCompatible.value.ipExtract =newVal.web_compatible_config.default_rule.includes('source_ip_get')
        if (appCompatible.value.ipExtract){
          appCompatible.value.ipExtractValue =newVal.web_compatible_config.source_ip_get.source
        }
        appCompatible.value.ipInsert =newVal.web_compatible_config.default_rule.includes('source_ip_insert')
        if (appCompatible.value.ipInsert && newVal.web_compatible_config.source_ip_insert){
          appCompatible.value.ipInsertConfig = {
            header: newVal.web_compatible_config.source_ip_insert.header || 'X-Forwarded-For',
            direction: newVal.web_compatible_config.source_ip_insert.direction || 'left',
            separator: newVal.web_compatible_config.source_ip_insert.separator || ',',
            position: newVal.web_compatible_config.source_ip_insert.position || 1
          }
        }
        appCompatible.value.async =newVal.web_compatible_config.default_rule.includes('error_response')
        appCompatible.value.routing =newVal.web_compatible_config.default_rule.includes('front_url_control')
      }
    },
    { immediate: true, deep: true }
);

const addHeaderRewriteRow = async () => {
  let validateR = true;
  let msg = '';

  for (const item of appCompatible.value.header) {
    if (!item.key || (item.operation !== 'remove' && !item.value)) {
      validateR = false;
      msg = "上一行数据填写不完整";
      break; // 可以直接 break
    }
  }

  if (!validateR) {
    await MessagePlugin.error(msg);
    return;
  }

  appCompatible.value.header.push({field: 'request_header', operation: 'add', key: '', value: ''})
}

const removeHeaderRewriteRow = (index)=>{
  appCompatible.value.header.splice(index,1)
}

</script>
<style scoped>
/* 标签样式 */
.appC label {
  display: block;
  font-size: 12px;
  color: #374151;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 添加URL重写按钮样式 */
.appC .add-url-rewrite-btn {
  background: none;
  border: none;
  color: #2563eb;
  font-size: 12px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-top: 4px;
}

.appC .add-url-rewrite-btn:hover {
  background: #eff6ff;
  color: #1d4ed8;
}

/* URL重写行样式 */
.appC .url-rewrite-row {
  display: flex;
  gap: 8px;
  margin-bottom: 6px;
  align-items: center;
  padding: 6px 10px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 12px;
}

.appC .url-rewrite-row input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  transition: all 0.2s ease;
}

.appC .url-rewrite-row input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #eff6ff;
  outline: none;
}

.appC .url-rewrite-row .arrow {
  color: #9ca3af;
  font-weight: bold;
  font-size: 14px;
  margin: 0 4px;
  min-width: 16px;
  text-align: center;
}

/* 删除按钮样式 */
.appC .row-delete-btn {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 14px;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  transition: all 0.2s ease;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

.appC .row-delete-btn:hover {
  background: #fef2f2;
  color: #b91c1c;
}

/* 表单行样式 */
.appC .form-row {
  margin-bottom: 12px;
}

.appC .form-row:last-child {
  margin-bottom: 0;
}

/* 表单控件通用样式 */
.appC input[type="text"],
.appC input[type="url"],
.appC input[type="number"],
.appC select,
.appC textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.2s ease;
  background: white;
  color: #1f2937;
}

.appC input[type="text"]:focus,
.appC input[type="url"]:focus,
.appC input[type="number"]:focus,
.appC select:focus,
.appC textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #eff6ff;
  transform: translateY(-1px);
}

.appC input[type="text"]:hover:not(:focus),
.appC input[type="url"]:hover:not(:focus),
.appC input[type="number"]:hover:not(:focus),
.appC select:hover:not(:focus),
.appC textarea:hover:not(:focus) {
  border-color: #9ca3af;
}

.appC input[type="text"]::placeholder,
.appC input[type="url"]::placeholder,
.appC input[type="number"]::placeholder,
.appC textarea::placeholder {
  color: #9ca3af;
}

/* 输入框带单位样式 */
.appC .input-with-unit {
  display: flex;
  align-items: center;
  gap: 8px;
}

.appC .input-with-unit input {
  width: 60px;
}

.appC .input-with-unit span {
  color: #6b7280;
  font-size: 12px;
  white-space: nowrap;
}

/* 头部改写规则样式 */
.appC .header-rewrite-row {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 10px 12px;
  font-size: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.appC .header-rewrite-row:last-child {
  border-bottom: none;
}

.appC .header-rewrite-row:hover {
  background: #f9fafb;
}

.appC .header-rewrite-row select:focus,
.appC .header-rewrite-row input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.appC .header-rewrite-row button:hover {
  background: #fef2f2 !important;
  color: #dc2626 !important;
}

</style>
