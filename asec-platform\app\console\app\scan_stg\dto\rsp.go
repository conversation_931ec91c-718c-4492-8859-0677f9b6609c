package dto

import (
	"asdsec.com/asec/platform/app/console/common/utils"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/scan_stg"
	"github.com/lib/pq"
)

type ScanStgCommon struct {
	StrategyName   string         `json:"strategy_name" gorm:"column:strategy_name"`
	StrategyDetail string         `json:"strategy_detail" gorm:"column:strategy_detail"`
	ScanPosition   pq.StringArray `json:"scan_position" gorm:"column:scan_position"`
	EnableAllUser  int            `json:"enable_all_user" gorm:"column:enable_all_user"`
	StrategyStatus int            `json:"strategy_status" gorm:"column:strategy_status"`
	Id             string         `json:"id" gorm:"column:id"`
}
type ScanStgListItem struct {
	ScanStgCommon
	FileType  pq.StringArray `json:"file_type" gorm:"column:file_type;type:[]varchar"`
	UserInfo  pq.StringArray `json:"user_info" gorm:"column:user_info;type:[]varchar"`
	GroupInfo pq.StringArray `json:"group_info" gorm:"column:group_info;type:[]varchar"`
}

type GetScanStgListRsp struct {
	model.CommonPage
	ScanStrategy []ScanStgListItem `json:"scan_strategy"`
}

type GetScanStgDetailRsp struct {
	scan_stg.ScanStrategy
	UserInfo  []model.UserEcho  `json:"user_info" gorm:"-"`
	GroupInfo []model.GroupEcho `json:"group_info" gorm:"-"`
}

type GetScanTaskListRsp struct {
	model.CommonPage
	ScanTask []ScanTask `json:"scan_task"`
}
type ScanTask struct {
	StartTime          utils.FrontTime `gorm:"column:start_time" json:"start_time"`
	EndTime            utils.FrontTime `gorm:"column:end_time" json:"end_time"`
	StrategyName       string          `gorm:"column:strategy_name" json:"strategy_name"`
	StrategyId         string          `json:"strategy_id" gorm:"column:strategy_id"`
	TaskId             string          `gorm:"column:task_id" json:"task_id"`
	TaskDate           string          `json:"task_date" gorm:"column:task_date"`
	SensitiveFileCount int             `gorm:"column:sensitive_file_count" json:"sensitive_file_count"`
	L1FileCount        int             `gorm:"column:l1_file_count" json:"l1_file_count"`
	L2FileCount        int             `gorm:"column:l2_file_count" json:"l2_file_count"`
	L3FileCount        int             `gorm:"column:l3_file_count" json:"l3_file_count"`
	L4FileCount        int             `gorm:"column:l4_file_count" json:"l4_file_count"`
	ProcessTask        int             `gorm:"column:process_task" json:"process_task"`
	FinishTask         int             `gorm:"column:finish_task" json:"finish_task"`
	AllTask            int             `gorm:"column:all_task" json:"all_task"`
}

type GetScanTaskDetailRsp struct {
	TaskId       string          `gorm:"column:task_id" json:"task_id"`
	StrategyId   string          `json:"strategy_id" gorm:"column:strategy_id"`
	TaskDate     string          `json:"task_date" gorm:"column:task_date"`
	StartTime    utils.FrontTime `gorm:"column:start_time" json:"start_time"`
	EndTime      utils.FrontTime `gorm:"column:end_time" json:"end_time"`
	UserInfoByte []byte          `gorm:"column:user_info_byte;type:[]jsonb" json:"-"`
	UserInfo     []UserInfo      `gorm:"-" json:"user_info"`
	L1FileCount  int             `gorm:"column:l1_file_count" json:"l1_file_count"`
	L2FileCount  int             `gorm:"column:l2_file_count" json:"l2_file_count"`
	L3FileCount  int             `gorm:"column:l3_file_count" json:"l3_file_count"`
	L4FileCount  int             `gorm:"column:l4_file_count" json:"l4_file_count"`
	FinishTask   int             `gorm:"column:finish_task" json:"finish_task"`
	AllTask      int             `gorm:"column:all_task" json:"all_task"`
	ScanConf     string          `gorm:"column:scan_conf" json:"-"`
	TaskStgConf
}

type TaskStgConf struct {
	ScanPosition        []string `json:"scan_position"`
	FileType            []string `json:"file_type"`
	ScanExcludePosition []string `json:"scan_exclude_position"`
	FileSizeRange       string   `json:"file_size_range"`
	EnableAllUser       int      `json:"enable_all_user"`
}

type UserInfo struct {
	UserId   string `json:"user_id"`
	UserName string `json:"user_name"`
}

type GetScanTaskUserRsp struct {
	model.CommonPage
	UserTask []UserTask `json:"user_task"`
}

type UserTask struct {
	StartTime          utils.FrontTime `gorm:"column:start_time" json:"start_time"`
	AgentName          string          `gorm:"column:agent_name" json:"agent_name"`
	UserName           string          `gorm:"column:user_name" json:"user_name"`
	L1FileCount        int             `gorm:"column:l1_file_count" json:"l1_file_count"`
	L2FileCount        int             `gorm:"column:l2_file_count" json:"l2_file_count"`
	L3FileCount        int             `gorm:"column:l3_file_count" json:"l3_file_count"`
	L4FileCount        int             `gorm:"column:l4_file_count" json:"l4_file_count"`
	SensitiveFileCount int             `gorm:"column:sensitive_file_count" json:"sensitive_file_count"`
	TaskStatus         string          `gorm:"column:task_status" json:"task_status"`
	ScanCount          int             `gorm:"column:scan_count" json:"scan_count"`
}

type CleanTask struct {
	TaskId        string         `gorm:"column:task_id" json:"task_id"`
	StgId         string         `gorm:"column:stg_id" json:"stg_id"`
	TaskDate      string         `gorm:"column:task_date" json:"task_date"`
	EnableAllUser int            `gorm:"column:enable_all_user" json:"enable_all_user"`
	UserIds       pq.StringArray `gorm:"column:user_ids" json:"user_ids"`
	UserGroupIds  pq.StringArray `gorm:"column:user_group_ids" json:"user_group_ids"`
}

type TaskStgRsp struct {
	scan_stg.ScanStrategy
	FileType pq.StringArray `gorm:"column:file_type;type:[]varchar"`
}
