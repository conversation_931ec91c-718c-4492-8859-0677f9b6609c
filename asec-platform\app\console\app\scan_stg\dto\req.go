package dto

import "time"

type CreateScanStgReq struct {
	StrategyName        string   `json:"strategy_name" binding:"required"`
	StrategyStatus      int      `json:"strategy_status"`
	StrategyDetail      string   `json:"strategy_detail"`
	EnableAllUser       int      `json:"enable_all_user"`
	UserIds             []string `json:"user_ids"`
	UserGroupIds        []string `json:"user_group_ids"`
	FileTypeCode        []int64  `json:"file_type_code"`
	TimeIds             []string `json:"time_ids"`
	MinFileSize         int64    `json:"min_file_size"`
	MinFileUnit         string   `json:"min_file_unit"`
	MaxFileSize         int64    `json:"max_file_size"`
	MaxFileUnit         string   `json:"max_file_unit"`
	ScanPosition        []string `json:"scan_position"`
	ScanExcludePosition []string `json:"scan_exclude_position"`
	ScanCondition       []string `json:"scan_condition"`
	ScanFileCount       int      `json:"scan_file_count"`
}

type UpdateScanStgReq struct {
	Id string `json:"id" binding:"required"`
	CreateScanStgReq
}

type GetCommonListReq struct {
	Limit  int    `form:"limit" json:"limit"`
	Offset int    `form:"offset" json:"offset"`
	Search string `form:"search" json:"search"`
}

type GetScanTaskListReq struct {
	GetCommonListReq
	StartTime string    `json:"start_time"`
	StartT    time.Time `json:"-"`
	EndT      time.Time `json:"-"`
	EndTime   string    `json:"end_time"`
}

type GetScanTaskUserReq struct {
	GetCommonListReq
	TaskStatus string `json:"task_status"`
	TaskId     string `json:"task_id" binding:"required"`
}

type StopScanTaskReq struct {
	StrategyId string `json:"strategy_id"`
	TaskDate   string `json:"task_date"`
	Name       string `json:"name"`
	TaskId     string `json:"task_id" binding:"required"`
}
