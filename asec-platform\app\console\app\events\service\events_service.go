package service

import (
	"asdsec.com/asec/platform/app/console/app/events/constants"
	"asdsec.com/asec/platform/app/console/app/events/dto"
	"asdsec.com/asec/platform/app/console/app/events/repository"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"sync"
)

var AnalysisImpl AnalysisService

// AnalysisInit 单例对象
var AnalysisInit sync.Once

type AnalysisService interface {
	DeleteHistory(ctx context.Context, ids []string) error
	FilterCondition(ctx context.Context) (dto.ConditionResp, error)
	GetEventsList(ctx context.Context, req dto.GetEventListReq) (dto.GetEventListRsp, error)
	GetEventsDetail(ctx context.Context, id string) (model.FileEvents, error)
	EventsExcel(ctx context.Context, req dto.GetEventListReq) (string, map[string]string, []model.FileEvents, error)
	GetFileType(ctx context.Context) ([]model.FileType, error)
	GetActivityType(ctx context.Context, builtIn string) ([]model.ChannelTypeDB, error)
	GetFileTypes(ctx context.Context) ([]model.FileType, error)
	GetActivityList(ctx context.Context) ([]dto.GetActivityResp, error)
	GetEventCondition(ctx context.Context, req dto.GetEventConditionReq) (dto.Condition, error)
	GetHistoryList(ctx context.Context, req dto.GetHistoryListReq) ([]dto.GetHistoryListRsp, error)
	GetActivityCategoryList(ctx context.Context) (dto.GetActivityCategoryRsp, error)
	GetEventsUserList(ctx context.Context, req dto.GetEventListReq) (dto.GetUsersRsp, error)
	DeleteEvents(c context.Context, req dto.Ids) error
}

type analysisService struct {
	db repository.AnalysisRepository
}

func (a *analysisService) DeleteEvents(c context.Context, req dto.Ids) error {
	return a.db.DeleteEvents(c, req)
}

func (a *analysisService) GetEventsUserList(ctx context.Context, req dto.GetEventListReq) (dto.GetUsersRsp, error) {
	return a.db.GetEventsUserList(ctx, req)
}

func (a *analysisService) GetActivityCategoryList(ctx context.Context) (dto.GetActivityCategoryRsp, error) {
	getActivityList := constants.GetActivityList
	getActivityList = append(getActivityList, constants.GitCloneActivity)
	useActivityList := constants.UseActivityList
	useActivityList = append(useActivityList, constants.GitPushActivity)
	return dto.GetActivityCategoryRsp{
		UseActivityList:     useActivityList,
		GetActivityList:     getActivityList,
		ChannelActivityList: []string{constants.SendActivity},
	}, nil
}

func (a *analysisService) GetHistoryList(ctx context.Context, req dto.GetHistoryListReq) ([]dto.GetHistoryListRsp, error) {
	return a.db.GetHistoryList(ctx, req)
}

func (a *analysisService) GetEventCondition(ctx context.Context, req dto.GetEventConditionReq) (dto.Condition, error) {
	return a.db.GetEventCondition(ctx, req)
}

func (a *analysisService) GetActivityList(ctx context.Context) ([]dto.GetActivityResp, error) {
	modelConfigs, err := a.db.GetActivityList(ctx)
	var resList []dto.GetActivityResp
	if err != nil {
		return resList, err
	}
	if len(modelConfigs) <= 0 {
		return resList, nil
	}
	for _, config := range modelConfigs {
		resList = append(resList, dto.GetActivityResp{Id: config.ID,
			Activity:     config.Key,
			ActivityDesc: config.Value})
	}
	return resList, nil
}

func (a *analysisService) GetFileTypes(ctx context.Context) ([]model.FileType, error) {
	return a.db.GetFileTypes(ctx)
}

func (a *analysisService) GetActivityType(ctx context.Context, builtIn string) ([]model.ChannelTypeDB, error) {
	return a.db.GetActivityType(ctx, builtIn)
}

func (a *analysisService) GetFileType(ctx context.Context) ([]model.FileType, error) {
	return a.db.GetFileType(ctx)
}

func (a *analysisService) EventsExcel(ctx context.Context, req dto.GetEventListReq) (string, map[string]string, []model.FileEvents, error) {
	// 标题
	title := constants.EventsExcel
	// 查询条件
	searchCondition := make(map[string]string, 0)
	// 查询结果
	req.Limit = constants.MaxExportNum
	data, err := a.db.GetEventsList(ctx, req, true)
	if err != nil {
		return title, searchCondition, nil, err
	}
	var events []model.FileEvents
	events = data.EvenList
	return title, searchCondition, events, nil
}

func (a *analysisService) GetEventsDetail(ctx context.Context, id string) (model.FileEvents, error) {
	return a.db.GetEventsDetail(ctx, id)
}

func (a *analysisService) GetEventsList(ctx context.Context, req dto.GetEventListReq) (dto.GetEventListRsp, error) {
	return a.db.GetEventsList(ctx, req, false)
}

func (a *analysisService) FilterCondition(ctx context.Context) (dto.ConditionResp, error) {
	return a.db.FilterCondition(ctx)
}

func (a *analysisService) DeleteHistory(ctx context.Context, ids []string) error {
	return a.db.DeleteHistory(ctx, ids)
}

func GetAnalysisService() AnalysisService {
	AnalysisInit.Do(func() {
		AnalysisImpl = &analysisService{db: repository.NewAnalysisRepository()}
	})
	return AnalysisImpl
}
