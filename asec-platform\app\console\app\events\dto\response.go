package dto

import (
	"asdsec.com/asec/platform/pkg/model"
	"time"
)

type HistoryItemResp struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`
}

type EventsHistoryData struct {
	Id           uint64    `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	Name         string    `gorm:"column:name;type:varchar;comment:名称" json:"name" binding:"required"`
	StartTime    time.Time `gorm:"column:start_time;type:timestamptz;comment:开始时间" json:"start_time"`
	EndTime      time.Time `gorm:"column:end_time;type:timestamptz;comment:结束时间" json:"end_time"`
	Condition    string    `gorm:"column:condition;type:varchar;comment:条件" json:"condition"`
	CreateTime   time.Time `gorm:"column:create_time;type:timestamptz;comment:创建时间" json:"create_time"`
	SensitiveHit int       `gorm:"column:sensitive_hit" json:"sensitive_hit" binding:"required"`
}

type EventsHistoryResp struct {
	Id         uint64               `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	Name       string               `gorm:"column:name;type:varchar;comment:名称" json:"name" binding:"required"`
	StartTime  time.Time            `gorm:"column:start_time;type:timestamptz;comment:开始时间" json:"start_time"`
	EndTime    time.Time            `gorm:"column:end_time;type:timestamptz;comment:结束时间" json:"end_time"`
	Condition  []model.SubCondition `gorm:"column:condition;type:varchar;comment:条件" json:"condition"`
	CreateTime time.Time            `gorm:"column:create_time;type:timestamptz;comment:创建时间" json:"create_time"`
}

type ConditionResp struct {
	Condition []SubLabelCondition `json:"condition"`
}

type Label struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type SubLabelCondition struct {
	Label
	Children []Label `json:"children"`
}
