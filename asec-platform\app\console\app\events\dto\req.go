package dto

import "time"

type Ids struct {
	Ids []string
}

type PageReq struct {
	Limit  int `form:"limit" json:"limit" binding:"required,min=1,max=200"`
	Offset int `form:"offset" json:"offset"`
}

type ListEventsReq struct {
	PageReq
	StartTime    string         `gorm:"column:start_time;type:varchar;comment:开始时间" json:"start_time" binding:"required"`
	EndTime      string         `gorm:"column:end_time;type:varchar;comment:结束时间" json:"end_time" binding:"required"`
	Condition    []SubCondition `gorm:"column:condition;type:varchar;comment:条件" json:"condition"`
	SensitiveHit int            `json:"sensitive_hit" binding:"required"`
}

type SubCondition struct {
	Type     string        `gorm:"column:type;type:varchar;comment:键" json:"type"`
	Relation string        `gorm:"column:relation;type:varchar;comment:关系" json:"relation"`
	Value    []interface{} `gorm:"column:value;type:[]varchar;comment:值" json:"value"`
}

type ExportReq struct {
	StartTime    string         `gorm:"column:start_time;type:varchar;comment:开始时间" json:"start_time" binding:"required"`
	EndTime      string         `gorm:"column:end_time;type:varchar;comment:结束时间" json:"end_time" binding:"required"`
	Condition    []SubCondition `json:"condition"`
	SensitiveHit int            `json:"sensitive_hit" binding:"required"`
}

type GetActivityResp struct {
	// Activity id
	Id string `json:"id"`
	// Activity Code
	Activity string `json:"activity"`
	// Activity 描述
	ActivityDesc string `json:"activity_desc"`
}

type GetEventListReq struct {
	Limit             int               `json:"limit" binding:"required"` //页大小
	Offset            int               `json:"offset"`                   //起始条数
	StartTime         string            `json:"start_time,omitempty"`     // 开始时间
	EndTime           string            `json:"end_time,omitempty"`       // 结束时间
	StartT            time.Time         `json:"-"`
	EndT              time.Time         `json:"-"`
	UserTags          []string          `json:"user_tags,omitempty"`          // 用户标签（因为是历史数据，传的是中文）
	DataCondition     DataCondition     `json:"data_condition,omitempty"`     // 数据条件
	BehaviorCondition BehaviorCondition `json:"behavior_condition,omitempty"` // 行为条件
	GroupIds          []string          `json:"group_ids,omitempty"`          // 所属组织
	Severity          []string          `json:"severity,omitempty"`           // 风险等级
	FileKeyword       string            `json:"file_keyword,omitempty"`       // 查数据输入
	NameKeyword       string            `json:"name_keyword,omitempty"`       // 查人输入
	UserIds           []string          `json:"user_ids,omitempty"`           // 关注用户ids
	EventIds          []string          `json:"event_ids,omitempty"`          // 关注事件ids
	Order             string            `json:"order" binding:"required"`     // 排序
	UserId            string            `json:"user_id"`                      // 用户视角user_id
	SourceIds         []string          `json:"source_ids"`                   // 来源ids
}

type GetEventConditionReq struct {
	StartTime         string            `form:"start_time" json:"start_time"` // 开始时间
	EndTime           string            `form:"end_time" json:"end_time"`     // 结束时间
	StartT            time.Time         `json:"-"`
	EndT              time.Time         `json:"-"`
	UserTags          []string          `json:"user_tags,omitempty"`              // 用户标签（因为是历史数据，传的是中文）
	DataCondition     DataCondition     `json:"data_condition,omitempty"`         // 数据条件
	BehaviorCondition BehaviorCondition `json:"behavior_condition,omitempty"`     // 行为条件
	GroupIds          []string          `json:"group_ids,omitempty"`              // 所属组织
	Severity          []string          `json:"severity,omitempty"`               // 风险等级
	UserIds           []string          `json:"user_ids,omitempty"`               // 关注用户ids
	EventIds          []string          `json:"event_ids,omitempty"`              // 关注事件ids
	FileKeyword       string            `form:"file_keyword" json:"file_keyword"` // 查数据输入
	NameKeyword       string            `form:"name_keyword" json:"name_keyword"` // 查人输入
	SourceIds         []string          `json:"source_ids"`                       // 来源ids
}

type DataCondition struct {
	SensitiveLevel  []string `json:"sensitive_level,omitempty"`   // 敏感等级
	FileCategoryIds []string `json:"file_category_ids,omitempty"` // 文件类型
	SensitiveIds    []string `json:"sensitive_ids,omitempty"`     // 敏感数据id
}

type BehaviorCondition struct {
	Activity    []string `json:"activity,omitempty"`     // 事件类型
	ChannelList []string `json:"channel_list,omitempty"` // 通道
}

type GetHistoryListReq struct {
	Type   string `form:"type" json:"type"`
	Search string `form:"search" json:"search"`
}
