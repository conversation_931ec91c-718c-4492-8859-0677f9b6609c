package main

import (
	"log"

	"gorm.io/gorm/logger"

	"gorm.io/driver/postgres"
	"gorm.io/gen"
	"gorm.io/gorm"
)

func main() {
	d := postgres.Open("host=************* port=5432 user=asec dbname=asec_platform password=pg@asd@1234! sslmode=disable")
	var err error
	db, err := gorm.Open(d, &gorm.Config{Logger: logger.Default.LogMode(logger.Error)})
	if err != nil {
		log.Fatal("err")
	}

	g := gen.NewGenerator(gen.Config{
		OutPath: "../../data/query",
	})

	g.UseDB(db)

	// generate all table from database
	g.ApplyBasic(
		g.GenerateModel("tb_corp"),
		g.GenerateModel("tb_user_source"),
		g.GenerateModel("tb_identity_provider"),
		g.GenerateModel("tb_idp_group_mapper"),
		g.<PERSON>rateModel("tb_identity_provider_attribute"),
		g.GenerateModel("tb_identity_provider_template"),
		g.GenerateModel("tb_idp_template_attribute"),
		g.GenerateModel("tb_user_group"),
		g.GenerateModel("tb_user_group_sync"),
		g.GenerateModel("tb_user_group_sync_config"),
		g.GenerateModel("tb_role"),
		g.GenerateModel("tb_user_role"),
		g.GenerateModel("tb_credential"),
		g.GenerateModel("tb_user_login_log"),
		g.GenerateModel("tb_user_group_sync_log"),
		g.GenerateModel("tb_special_config"),
	)

	type UserEntityQuerier interface {
		// INSERT INTO @@table
		// {{if phone != "" && email != ""}}
		//	(id, name, group_id, corp_id, source_id, phone, email) VALUES (@id, @name, @groupId, @corpId, @sourceId, @phone, @email)
		// {{else if phone != ""}}
		//  (id, name, group_id, corp_id, source_id, phone) VALUES (@id, @name, @groupId, @corpId, @sourceId, @phone)
		// {{else if email != ""}}
		//  (id, name, group_id, corp_id, source_id, email) VALUES (@id, @name, @groupId, @corpId, @sourceId, @email)
		// {{else}}
		//  (id, name, group_id, corp_id, source_id) VALUES (@id, @name, @groupId, @corpId, @sourceId)
		// {{end}}
		CreateUser(id, name, groupId, corpId, sourceId, phone, email string) error
	}
	g.ApplyInterface(func(UserEntityQuerier) {}, g.GenerateModel("tb_user_entity"))

	g.ApplyBasic(g.GenerateModel("tb_auth_policy_idp_mapper"))
	g.ApplyBasic(g.GenerateModel("tb_component"), g.GenerateModel("tb_component_config"))
	g.ApplyBasic(g.GenerateModel("tb_access_config"))

	// wx
	g.ApplyBasic(
		g.GenerateModel("tb_wx_department"),
		g.GenerateModel("tb_wx_user"),
	)

	g.ApplyBasic(
		g.GenerateModel("tb_external_department"),
		g.GenerateModel("tb_external_user"),
	)

	g.Execute()
}
