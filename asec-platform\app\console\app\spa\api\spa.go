package api

import (
	"fmt"
	"strings"
	"time"

	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/app/spa/model"
	"asdsec.com/asec/platform/app/console/app/spa/repository"
	"asdsec.com/asec/platform/app/console/app/spa/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/sms"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 获取用户安全配置
func GetUserSecurity(c *gin.Context) {
	userSecurityMap, err := service.GetSpaService().GetUserSecurity()
	if err != nil {
		global.SysLog.Error("GetUserSecurity error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, userSecurityMap)
}

// 获取配置
func GetConfig(c *gin.Context) {
	config, err := service.GetSpaService().GetConfig()
	if err != nil {
		global.SysLog.Error("GetConfig error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, config)
}

// 更新配置
func UpdateConfig(c *gin.Context) {
	var req model.UpdateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("UpdateConfig param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	// 获取原配置用于对比
	oldConfig, err := service.GetSpaService().GetConfig()
	if err != nil {
		global.SysLog.Error("GetConfig for comparison failed", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	// 操作日志记录
	var errorLog = ""
	defer func() {
		// 构建配置变更描述
		var hasChanges bool

		// 检查SPA功能开关变化
		if oldConfig.EnableSPA != req.EnableSPA {
			hasChanges = true
		} else {
			// 检查其他配置项是否有变化
			var otherChanges bool

			// 对比IP白名单
			oldIPWhiteListsStr := fmt.Sprintf("%v", oldConfig.IpWhiteLists)
			newIPWhiteListsStr := fmt.Sprintf("%v", req.IpWhiteLists)
			if oldIPWhiteListsStr != newIPWhiteListsStr {
				otherChanges = true
			}

			if fmt.Sprintf("%v", oldConfig.AllowPorts) != fmt.Sprintf("%v", req.AllowPorts) {
				otherChanges = true
			}

			if oldConfig.EnableTCPInvisible != req.EnableTCPInvisible {
				otherChanges = true
			}

			if oldConfig.KnockDoorInterval != req.KnockDoorInterval {
				otherChanges = true
			}

			if oldConfig.ServerAllowAccessTime != req.ServerAllowAccessTime {
				otherChanges = true
			}

			if oldConfig.ActivationCodeValidity != req.ActivationCodeValidity {
				otherChanges = true
			}

			if otherChanges {
				hasChanges = true
			}
		}

		// 只有有变更时才记录日志
		if hasChanges {
			var operationType, representation string
			if oldConfig.EnableSPA != req.EnableSPA {
				if req.EnableSPA {
					operationType = common.OperateEnable
					representation = "SPA功能"
				} else {
					operationType = common.OperateDisable
					representation = "SPA功能"
				}
			} else {
				operationType = common.OperateUpdate
				representation = "SPA配置参数"
			}

			oplog := modelTable.Oprlog{
				ResourceType:   common.SPAConfigType,
				OperationType:  operationType,
				Representation: representation,
				Error:          errorLog,
			}
			_, err := oprService.GetOprlogService().Create(c, oplog)
			if err != nil {
				global.SysLog.Error("record operate log failed", zap.Error(err))
			}
		}
	}()

	var config model.SpaConfig
	config.EnableSPA = req.EnableSPA
	ipWhiteLists, err := model.StringToIPWhiteLists(req.IpWhiteLists)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("UpdateConfig param err", zap.Error(err))
		common.FailWithMessage(c, common.OperateFailCode, err.Error())
		return
	}
	config.IpWhiteLists = ipWhiteLists
	config.AllowPorts = req.AllowPorts
	config.EnableTCPInvisible = req.EnableTCPInvisible
	config.KnockDoorInterval = req.KnockDoorInterval
	config.ServerAllowAccessTime = req.ServerAllowAccessTime
	config.KnockDoorTimeError = req.KnockDoorTimeError
	config.ActivationCodeValidity = req.ActivationCodeValidity
	if err := service.GetSpaService().UpdateConfig(&config); err != nil {
		errorLog = err.Error()
		global.SysLog.Error("UpdateConfig err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// 激活结果验证
func VerifyActivation(c *gin.Context) {
	var req model.VerifyActivationRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		global.SysLog.Error("VerifyActivation param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if len(req.ActivationCode) != 6 {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	valid, userID, err := service.GetSpaService().VerifyActivationCode(req)
	if err != nil {
		global.SysLog.Error("VerifyActivation error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, model.VerifyActivationResponse{Valid: valid, UserID: userID})
}

// 批量设置激活码
func SendActivation(c *gin.Context) {
	var userIds []string
	if err := c.ShouldBindJSON(&userIds); err != nil {
		global.SysLog.Error("BatchSetActivationCode param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	smsInstance, err := sms.GetClient()
	if err != nil {
		global.SysLog.Error("SendActivation error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	config, err := service.GetSpaService().GetConfig()
	if err != nil {
		global.SysLog.Error("GetConfig error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	// 检查SPA功能是否开启
	if !config.EnableSPA {
		global.SysLog.Warn("SendActivation failed: SPA功能未开启")
		common.FailWithMessage(c, common.OperateFailCode, "SPA功能未开启，无法发送激活码")
		return
	}

	// 获取用户手机号信息，检查手机号是否存在
	userPhoneInfoList, err := repository.GetSpaRepository().GetUserPhoneInfo(c, userIds)
	if err != nil {
		global.SysLog.Error("SendActivation error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	// 检查哪些用户没有手机号
	var usersWithoutPhone []string
	var phoneList []string
	for _, userInfo := range userPhoneInfoList {
		if userInfo.Phone == "" {
			// 优先使用显示名称，如果为空则使用用户名
			userName := userInfo.DisplayName
			if userName == "" {
				userName = userInfo.Name
			}
			if userName == "" {
				userName = userInfo.ID // 如果都为空，使用用户ID
			}
			usersWithoutPhone = append(usersWithoutPhone, userName)
		} else {
			phoneList = append(phoneList, userInfo.Phone)
		}
	}

	// 如果有用户没有手机号，返回错误
	if len(usersWithoutPhone) > 0 {
		var errorMsg string
		if len(usersWithoutPhone) <= 3 {
			// 用户数量较少时，显示所有用户名
			errorMsg = "用户 " + strings.Join(usersWithoutPhone, "、") + " 的手机号不存在"
		} else {
			// 用户数量较多时，只显示前3个用户名和总数
			displayUsers := strings.Join(usersWithoutPhone[:3], "、")
			errorMsg = fmt.Sprintf("用户 %s 等 %d 个用户的手机号不存在", displayUsers, len(usersWithoutPhone))
		}
		global.SysLog.Error("SendActivation error: users without phone", zap.Strings("users", usersWithoutPhone))
		common.FailWithMessage(c, common.OperateFailCode, errorMsg)
		return
	}

	validity := time.Duration(config.ActivationCodeValidity) * time.Minute
	result, err := smsInstance.SendBatch(c, userIds, phoneList, validity)
	if err != nil {
		global.SysLog.Error("SendActivation error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, result)
}

// 获取组织ID
func GetLicenseID(c *gin.Context) {
	licenseID, err := service.GetSpaService().GetLicenseID()
	if err != nil {
		global.SysLog.Error("GetLicenseID error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, gin.H{"license_id": licenseID})
}
