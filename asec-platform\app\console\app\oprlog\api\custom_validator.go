package api

import (
	"net"
	"strings"

	validator "github.com/go-playground/validator/v10"
)

/*
IpAndipSegfunc 操作日志Ip查询的自定义验证规则
可以是ip,也可以是ip段，也可以是ip和ip段组合如下合法：
*******
*******-*********
*******,*******-*********
*/
var IpAndipSegfunc validator.Func = func(fl validator.FieldLevel) bool {
	if ip, ok := fl.Field().Interface().(string); ok {
		if ip == "" {
			return true
		}
		ipList := strings.Split(ip, ",")
		for _, v := range ipList {
			oneList := strings.Split(v, "-")
			if len(oneList) == 2 {
				for _, o := range oneList {
					if net.ParseIP(o) == nil {
						return false
					}
				}
			} else if len(oneList) == 1 {
				if net.ParseIP(oneList[0]) == nil {
					return false
				}
			} else {
				return false
			}
		}
	}
	return true
}
