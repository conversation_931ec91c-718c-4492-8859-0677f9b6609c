package model

type UpdateSensitiveCategoryReq struct {
	Id   string `json:"id" binding:"required"`
	Name string `json:"name" binding:"required"`
}

type DelSensitiveCategoryReq struct {
	Id   string `json:"id" binding:"required"`
	Name string `json:"name"`
	Type int    `json:"type" binding:"oneof=1 2"` // 1-删除仅选分类 2-删除分类及其分类下的所有敏感数据
}

type AddSensitiveCategoryReq struct {
	Name     string `json:"name" binding:"required"`
	IconCode string `json:"iconCode"`
}

type CreateStgByTempReq struct {
	Items []CreateStgTempItem `json:"items" binding:"required"`
	Names string              `json:"names"`
}

type CreateStgTempItem struct {
	CategoryName string   `json:"category_name"`
	CategoryIcon string   `json:"category_icon"`
	StrategyIds  []string `json:"strategy_ids"`
}
