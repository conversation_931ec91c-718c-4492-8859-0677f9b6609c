package constants

const (
	EventsExcel      = "Events.ExcelName.EventsExcel"
	MaxExportNum     = 100000
	TimeFormat       = "2006-01-02 15:04:05"
	SensitiveHitType = 2

	GitPushActivity     = "push"
	GitPushActivityName = "git push"

	GitCloneActivity      = "clone"
	DownloadActivityName  = "web下载"
	SendActivity          = "send"
	DownloadActivity      = "download"
	SvnCommitActivity     = "commit"
	SvnCommitNameActivity = "svn commit"
	OtherCondition        = "0"
	OtherConditionName    = "其它"
	ActivityType          = "activity"
	HistoryUserType       = "USER"
	HistoryDataType       = "DATA"
	UdefChannel           = "udef_"
)

var GetActivityList = []string{DownloadActivity, "monitor"}
var UseActivityList = []string{"compress", "rename", "copy", "move", "push", "commit"}
var GitActivityList = []string{"clone", "checkout"}
