package service

import (
	"asdsec.com/asec/platform/app/console/app/module_switch/dto"
	"asdsec.com/asec/platform/app/console/app/module_switch/repository"
	"asdsec.com/asec/platform/app/console/common/utils"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"errors"
	"go.uber.org/zap"
	"time"
)

type FeatureSwitchService interface {
	GetFeatureSwitches(ctx context.Context, moduleKey string, flag int) ([]dto.FeatureSwitchDto, error)
	UpdateFeatureSwitches(ctx context.Context, req dto.UpdateFeatureSwitchReq) error
}

type featureSwitchService struct {
	repo repository.FeatureSwitchRepository
}

func NewFeatureSwitchService() FeatureSwitchService {
	return &featureSwitchService{repo: repository.NewFeatureSwitchRepository()}
}

func (s *featureSwitchService) GetFeatureSwitches(ctx context.Context, moduleKey string, flag int) ([]dto.FeatureSwitchDto, error) {
	return s.repo.GetFeatureSwitches(ctx, moduleKey, flag)
}

const switchCachePrefix = "module_switch_"

func (s *featureSwitchService) UpdateFeatureSwitches(ctx context.Context, req dto.UpdateFeatureSwitchReq) error {
	if err := s.repo.DeleteByFeatureKey(ctx, req.ModuleKey, req.Flag); err != nil {
		return err
	}

	for _, fs := range req.FeatureSwitches {
		featureSwitch := model.FeatureSwitch{
			EntityType: fs.EntityType,
			EntityID:   fs.EntityID,
			ModuleKey:  req.ModuleKey,
			Flag:       req.Flag,
			CreatedAt:  time.Now(),
		}
		if err := s.repo.UpdateFeatureSwitch(ctx, featureSwitch); err != nil {
			return err
		}
	}
	redisDb, err := global.GetRedisClient(ctx)
	if err != nil {
		global.SysLog.Error("get redis cli err", zap.Error(err))
		return err
	}
	err = utils.DeleteKeysByPrefixWithPipeline(ctx, redisDb, switchCachePrefix)
	if err != nil {
		global.SysLog.Error("get redis cli err", zap.Error(err))
		return errors.New("delete cache failed")
	}
	return nil
}
