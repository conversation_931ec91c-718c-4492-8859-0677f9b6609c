package dto

import (
	"asdsec.com/asec/platform/pkg/model"
	"github.com/lib/pq"
)

type AddApplicationByTypeReq struct {
	AppName             string             `json:"app_name" binding:"required"`
	AppType             string             `json:"app_type"`
	SdpList             []string           `json:"sdp_list" binding:"required"`
	GroupIds            []string           `gorm:"column:group_ids;type:varchar;comment:应用标签" json:"group_ids"`
	WebUrl              string             `json:"web_url"`
	IconURL             string             `json:"icon_url"`
	AppStatus           int                `json:"app_status"`
	ServerAddress       string             `json:"server_address"`
	ServerSchema        string             `json:"server_schema"`
	PublishAddress      string             `json:"publish_address"`
	PublishSchema       string             `json:"publish_schema"`
	Uri                 string             `json:"uri"`
	WebCompatibleConfig CompatibleConfig   `json:"web_compatible_config"`
	AppSites            []model.AppAddress `json:"app_sites"`
	IdpId               string             `json:"idp_id"`
	HealthConfig        HealthConfig       `json:"health_config"`
	PortalDesc          string             `json:"portal_desc"`
	PortalShowName      string             `json:"portal_show_name"`
	ShowStatus          int                `json:"show_status"`
	FormFillEnabled     int                `json:"form_fill_enabled"`
}

type ImportAppReq struct {
	AppData [][]string `json:"app_data"`
}

type HealthConfig struct {
	Enable string  `json:"enable"`
	Config HConfig `json:"config"`
}

type HConfig struct {
	FailNums          int    `json:"fail_nums"`
	HealthCode        []int  `json:"health_code"`
	IntervalTime      int    `json:"interval_time"`
	Path              string `json:"path"`
	Protocol          string `json:"protocol"`
	Timeout           int    `json:"timeout"`
	HealthIntervals   int    `json:"health_intervals"`
	UnHealthIntervals int    `json:"un_health_intervals"`
	SuccessNum        int    `json:"success_num"`
}

type DeleteApplicationByTypeReq struct {
	Ids  []string `json:"ids" binding:"required"`
	Name string   `json:"name"`
}

type UpdateApplicationByTypeReq struct {
	ID uint64 `json:"id,string" binding:"required"`
	AddApplicationByTypeReq
}

type CompatibleConfig struct {
	DefaultRule      []string           `json:"default_rule"`
	HeaderConfig     []HeaderConfig     `json:"header_config"`
	DependSite       DependSite         `json:"depend_site"`
	Hosts            string             `json:"hosts"`
	SingleSignOn     SingleSignOn       `json:"single_sign_on"`
	SourceIpGet      SourceIpGet        `json:"source_ip_get"`
	SourceIpInsert   *SourceIpInsert    `json:"source_ip_insert"`
	UrlControl       UrlControl         `json:"url_control"`
	UrlManualRewrite []UrlManualRewrite `json:"url_manual_rewrite"`
}

type SourceIpGet struct {
	TrustIps  string `json:"trust_ips"`
	Source    string `json:"source"`
	Recursive string `json:"recursive"`
}

type SourceIpInsert struct {
	Header    string `json:"header"`
	Direction string `json:"direction"`
	Separator string `json:"separator"`
	Position  int    `json:"position"`
}

type UrlControl struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type UrlManualRewrite struct {
	After  string `json:"after"`
	Before string `json:"before"`
}

type SingleSignOn struct {
	Type   string           `json:"type"`
	Config SingleSignOnConf `json:"config"`
}

type SingleSignOnConf struct {
	IdpType             string `json:"idp_type"`
	IdpId               string `json:"idp_id"`
	Appid               string `json:"app_id"`
	AppSecret           string `json:"app_secret"`
	Callback            string `json:"callback"`
	RecognizePatterns   string `json:"recognize_patterns"`
	LoginUrl            string `json:"login_url"`
	LoginType           string `json:"login_type"`
	UsernameInput       string `json:"username_input"`
	UsernameValue       string `json:"username_value"`
	CustomUsernameValue string `json:"custom_username_value"`
	PasswordInput       string `json:"password_input"`
	PasswordValue       string `json:"password_value"`
	CustomPasswordValue string `json:"custom_password_value"`
	LoginButton         string `json:"login_button"`
}

type DependSite struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type HeaderConfig struct {
	Field     string `json:"field"`
	Operation string `json:"operation"`
	Key       string `json:"key"`
	Value     string `json:"value"`
}

type AddWebAppReq struct {
	AppName             string           `json:"app_name"`
	SeApp               []SeApps         `json:"se_app" binding:"omitempty"`
	ServerAddress       string           `json:"server_address"`
	ServerSchema        string           `json:"server_schema"`
	PublishAddress      string           `json:"publish_address"`
	PublishSchema       string           `json:"publish_schema"`
	Uri                 string           `json:"uri"`
	WebCompatibleConfig CompatibleConfig `json:"web_compatible_config"`
	ServerHost          string           `json:"server_host"`
	SdpList             []string         `json:"sdp_list"`
	GroupIds            pq.Int64Array    `gorm:"column:group_ids;type:varchar;comment:应用标签" json:"group_ids"`
	WebUrl              string           `gorm:"web_url" json:"web_url"`
	IconURL             string           `json:"icon_url"`
	AppStatus           int              `json:"app_status"`
	IdpId               string           `json:"idp_id"`
	HealthConfig        HealthConfig     `json:"health_config"`
	PortalDesc          string           `json:"portal_desc"`
	PortalShowName      string           `json:"portal_show_name"`
	ShowStatus          int              `json:"show_status"`
	FormFillEnabled     int              `json:"form_fill_enabled"`
}

type GetApplicationListReq struct {
	model.Pagination
	GroupId       int      `form:"group_id" json:"group_id"`
	AppType       string   `form:"app_type" json:"app_type"`
	SearchColumns []string `form:"search_columns" json:"search_columns"`
}

type UserAppReq struct {
	UserId string `form:"user_id" json:"user_id"`
}

type GetApplicationGroupListReq struct {
	NeedDefault bool   `form:"need_default" json:"need_default"`
	Name        string `form:"name" json:"name"`
}
