package service

import (
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/constants"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/repository"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/snowflake"
	"context"
	"strconv"
	"sync"
	"time"
)

var SensitiveCategoryServiceImpl SensitiveCategoryService

// SensitiveCategoryServiceInit 单例对象
var SensitiveCategoryServiceInit sync.Once

type SensitiveCategoryService interface {
	GetSensitiveCategoryList(ctx context.Context, stgName string) ([]model.GetSensitiveCategoryListRsp, error)
	UpdateSensitiveCategory(ctx context.Context, req model.UpdateSensitiveCategoryReq) error
	AddSensitiveCategory(ctx context.Context, req model.AddSensitiveCategoryReq) error
	DeleteSensitiveCategory(ctx context.Context, req model.DelSensitiveCategoryReq) error
	GetSensitiveCategoryByName(ctx context.Context, name string) (model.SensitiveCategory, error)
	CheckDelCondition(ctx context.Context, id string, delType int) (bool, error)
	GetCategoryCondition(ctx context.Context, startTime time.Time, endTime time.Time) ([]model.GetSensitiveCategoryConditionRsp, error)
	CreateStgByTemp(ctx context.Context, req model.CreateStgByTempReq) error
}

type sensitiveCategoryService struct {
	db repository.SensitiveCategoryRepository
}

func (s sensitiveCategoryService) CreateStgByTemp(ctx context.Context, req model.CreateStgByTempReq) error {
	categoryList := make([]model.SensitiveCategory, 0)
	stgCMap := make(map[string][]model.SensitiveStrategyDB)
	for _, v := range req.Items {
		id, err := snowflake.Sf.GetId()
		if err != nil {
			global.SysLog.Sugar().Errorf("generate snow flake id failed. err=%v", err)
			return err
		}
		createTime, err := time.ParseInLocation("2006-01-02 15:04:05", constants.TimeOrder[v.CategoryName], time.Local)
		if err != nil {
			global.SysLog.Sugar().Errorf("parse time failed. err=%v", err)
			return err
		}
		categoryList = append(categoryList, model.SensitiveCategory{
			Id:       strconv.FormatUint(id, 10),
			Name:     v.CategoryName,
			BuiltIn:  constants.CustomBuiltInType,
			IconCode: v.CategoryIcon, CreatedAt: createTime})
		strategyListItem, err := s.db.GetStgByIds(ctx, v.StrategyIds)
		if err != nil {
			global.SysLog.Sugar().Errorf("generate snow flake id failed. err=%v", err)
			return err
		}
		stgCMap[v.CategoryName] = strategyListItem
	}
	return s.db.CreateCtgAndStgBatch(ctx, categoryList, stgCMap)
}

func (s sensitiveCategoryService) GetCategoryCondition(ctx context.Context, startTime time.Time, endTime time.Time) ([]model.GetSensitiveCategoryConditionRsp, error) {
	return s.db.GetCategoryCondition(ctx, startTime, endTime)
}

func (s sensitiveCategoryService) CheckDelCondition(ctx context.Context, id string, delType int) (bool, error) {
	return s.db.CheckDelCondition(ctx, id, delType)
}

func (s sensitiveCategoryService) GetSensitiveCategoryByName(ctx context.Context, name string) (model.SensitiveCategory, error) {
	return s.db.GetSensitiveCategoryByName(ctx, name)
}

func (s sensitiveCategoryService) GetSensitiveCategoryList(ctx context.Context, stgName string) ([]model.GetSensitiveCategoryListRsp, error) {
	return s.db.GetSensitiveCategoryList(ctx, stgName)
}

func (s sensitiveCategoryService) UpdateSensitiveCategory(ctx context.Context, req model.UpdateSensitiveCategoryReq) error {
	return s.db.UpdateSensitiveCategory(ctx, req)
}

func (s sensitiveCategoryService) AddSensitiveCategory(ctx context.Context, req model.AddSensitiveCategoryReq) error {
	return s.db.AddSensitiveCategory(ctx, req)
}

func (s sensitiveCategoryService) DeleteSensitiveCategory(ctx context.Context, req model.DelSensitiveCategoryReq) error {
	return s.db.DeleteSensitiveCategory(ctx, req)
}

func GetSensitiveCategoryService() SensitiveCategoryService {
	SensitiveCategoryServiceInit.Do(func() {
		SensitiveCategoryServiceImpl = &sensitiveCategoryService{db: repository.NewScRepository()}
	})
	return SensitiveCategoryServiceImpl
}
