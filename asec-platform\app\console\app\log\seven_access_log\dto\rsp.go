package dto

import (
	"asdsec.com/asec/platform/pkg/model"
)

type GetSevenAccessLogListRsp struct {
	model.CommonPage
	Data []SevenAccessLogListItem `json:"data"`
}

type SevenAccessLogListItem struct {
	Uuid           string `json:"uuid" gorm:"column:uuid"`
	AccessTime     string `json:"access_time" gorm:"-"`
	UserName       string `json:"user_name" gorm:"column:user_name"`
	Browser        string `json:"browser" gorm:"column:browser"`
	AppName        string `json:"app_name" gorm:"column:app_name"`
	Url            string `json:"url" gorm:"column:url"`
	StrategyName   string `json:"strategy_name" gorm:"column:strategy_name"`
	StrategyAction string `json:"strategy_action" gorm:"column:strategy_action"`
	AppType        string `json:"app_type" gorm:"app_type"`
}
type CommonDetailRsp struct {
	UserName       string            `json:"user_name" gorm:"column:user_name"`
	AccessTime     string            `json:"access_time" gorm:"-"`
	ClientIp       string            `json:"client_ip" gorm:"column:client_ip"`
	AppName        string            `json:"app_name" gorm:"column:app_name"`
	ServerAddr     string            `json:"server_addr" gorm:"column:server_addr"`
	Url            string            `json:"url" gorm:"column:url"`
	ReqBody        string            `json:"req_body" gorm:"column:req_body"`
	RspBody        string            `json:"rsp_body" gorm:"column:rsp_body"`
	AppType        string            `json:"app_type" gorm:"app_type"`
	RequestHeader  map[string]string `json:"request_header" gorm:"column:request_header"`
	ResponseHeader map[string]string `json:"response_header" gorm:"column:response_header"`
}

type SevenAccessLogDetail struct {
	Browser string `json:"browser" gorm:"column:browser"`
	CommonDetailRsp
}

type ApisixRsp struct {
	key   string         `json:"key"`
	Value ApisixRspValue `json:"value"`
}
type ApisixRspValue struct {
	Id      string         `json:"id"`
	Plugins map[string]any `json:"plugins"`
}

type GetSensitiveLogListRsp struct {
	model.CommonPage
	Data []SensitiveLogListItem `json:"data"`
}

type SensitiveLogListItem struct {
	Uuid           string          `json:"uuid" gorm:"column:uuid"`
	AccessTime     string          `json:"access_time" gorm:"-"`
	UserName       string          `json:"user_name" gorm:"column:user_name"`
	AppName        string          `json:"app_name" gorm:"column:app_name"`
	Activity       string          `json:"activity" gorm:"column:activity"`
	Url            string          `json:"url" gorm:"column:url"`
	FileName       string          `json:"file_name" gorm:"column:file_name"`
	SensitiveInfo  []SensitiveItem `json:"sensitive_info" gorm:"-"`
	AppType        string          `json:"app_type" gorm:"app_type"`
	StrategyAction string          `json:"strategy_action" gorm:"column:strategy_action"`
}

type SensitiveItem struct {
	SensitiveLevel      int    `json:"sensitive_level"`
	SensitiveId         string `json:"sensitive_id"`
	SensitiveRuleName   string `json:"sensitive_rule_name"`
	SensitiveCategoryId string `json:"sensitive_category_id"`
}

type SensitiveLogDetail struct {
	CommonDetailRsp
	SensitiveInfo  []SensitiveItem `json:"sensitive_info" gorm:"-"`
	FileName       string          `json:"file_name" gorm:"column:file_name"`
	Activity       string          `json:"activity" gorm:"column:activity"`
	StrategyAction string          `json:"strategy_action" gorm:"column:strategy_action"`
}
