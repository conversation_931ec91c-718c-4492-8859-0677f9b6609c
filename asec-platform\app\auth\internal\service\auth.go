package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"asdsec.com/asec/platform/pkg/utils/encrypt"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/transport"
	"gorm.io/gorm"

	"github.com/google/uuid"

	v1 "asdsec.com/asec/platform/api/auth/v1"
	pb "asdsec.com/asec/platform/api/auth/v1/auth"
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp/cas"
	"asdsec.com/asec/platform/app/auth/internal/idp/oauth2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/protobuf/jsonpb"
	"github.com/jinzhu/copier"
	"google.golang.org/protobuf/types/known/structpb"
)

type AuthService struct {
	pb.UnimplementedAuthServer
	auth *biz.AuthUsecase
	idp  *biz.IdpUsecase
	totp TotpService
	log  *log.Helper
}

// SsoAuthProvider 定义sso单点登录身份提供商接口
type SsoAuthProvider interface {
	GetAuthorizationURL(ctx context.Context, state, redirectUri string, offline bool) (string, error)
	HandleCallback(ctx context.Context, code, state string) (*dto.ExternalUser, error)
}

func NewAuthService(auth *biz.AuthUsecase, idp *biz.IdpUsecase, logger log.Logger) *AuthService {
	return &AuthService{
		auth: auth,
		idp:  idp,
		totp: TotpService{},
		log:  log.NewHelper(logger),
	}
}

const (
	TokenCookieName = "asec_token"
	SmsCookieName   = "asec_sms"
	RealmValue      = "default"
)

func (s *AuthService) TokenVerify(ctx context.Context, req *pb.TokenVerifyRequest) (*pb.TokenVerifyReply, error) {
	header, ok := transport.FromServerContext(ctx)
	if ok {
		cookies := header.RequestHeader().Get("Cookie")
		cookieList := strings.Split(cookies, ";")
		tokenVerify := false
		var userId string
		var userName string
		var err error
		for _, value := range cookieList {
			valueStrList := strings.Split(value, "=")
			if len(valueStrList) < 2 {
				continue
			}
			if strings.TrimSpace(valueStrList[0]) == TokenCookieName {
				claims, err := s.auth.ParseJwtToken(ctx, dto.AccessTokenTyp, valueStrList[1])
				if err != nil {
					//s.log.Debugf("Get userInfo by userToken failed. userToken=%v, err=%v", valueStrList[1], err)
					continue
				}
				userId = claims.Subject
				userName = claims.PreferredUsername
				header.ReplyHeader().Set("Asec-User-Id", userId)
				prefixEmail, lastPhoneNumber := getPrefixAndLast4(claims.Email, claims.Phone)
				//水印所需响应头
				header.ReplyHeader().Set("Asec_Group_Id", claims.GroupId)
				header.ReplyHeader().Set("Asec_last_phone_number", lastPhoneNumber)
				header.ReplyHeader().Set("Asec_full_phone_number", claims.Phone)
				header.ReplyHeader().Set("Asec_full_email", claims.Email)
				header.ReplyHeader().Set("Asec_prefix_email", prefixEmail)
				header.ReplyHeader().Set("Asec_User_Identifier", claims.Identifier)
				tokenVerify = true
			}
		}
		if tokenVerify {
			smsIdpList, err := s.auth.GetSecondarySmsIdp(ctx, userId)
			if err != nil {
				s.log.Errorf("get secondary idp by userId failed. userToken=%v, err=%v,", userId, err)
			}
			return &pb.TokenVerifyReply{UserId: userId, UserName: userName, SmsIdp: smsIdpList}, nil
		}
		redirectUrl, err := url.Parse(req.RedirectUrl)
		if err != nil {
			s.log.Errorf("Parse redirect url failed. err=%v", err)
			return &pb.TokenVerifyReply{}, v1.ErrorCodeVerifyError(req.RedirectUrl)
		}
		hostUrl, err := url.Parse(req.HostUrl)
		if err != nil {
			s.log.Errorf("Parse host url failed. err=%v", err)
			return &pb.TokenVerifyReply{}, v1.ErrorCodeVerifyError(req.RedirectUrl)
		}
		queryParams := redirectUrl.Query()
		code := queryParams.Get(AsecCodeName)
		if code == "" {
			return &pb.TokenVerifyReply{}, v1.ErrorCodeVerifyError(fmt.Sprintf("%s://%s/#/verify?redirect_url=%s", hostUrl.Scheme, hostUrl.Host, req.RedirectUrl))
		}
		token, err := s.auth.GetTokeByCode(ctx, code)
		if err != nil {
			s.log.Errorf("Get token by code failed. code=%v, err=%v,", code, err)
			return &pb.TokenVerifyReply{}, v1.ErrorCodeVerifyError(fmt.Sprintf("%s://%s/#/verify?redirect_url=%s", hostUrl.Scheme, hostUrl.Host, req.RedirectUrl))
		}
		header.ReplyHeader().Set("Set-Cookie", fmt.Sprintf("%s=%s", TokenCookieName, token))
		//TODO duplicate code to be refactor
		claims, err := s.auth.ParseJwtToken(ctx, dto.AccessTokenTyp, token)
		if err != nil {
			s.log.Errorf("Get userId by token failed. code=%v, err=%v,", token, err)
			return &pb.TokenVerifyReply{}, v1.ErrorCodeVerifyError(fmt.Sprintf("%s://%s/#/verify?redirect_url=%s", hostUrl.Scheme, hostUrl.Host, req.RedirectUrl))
		}
		prefixEmail, lastPhoneNumber := getPrefixAndLast4(claims.Email, claims.Phone)
		userId = claims.Subject
		header.ReplyHeader().Set("Asec-User-Id", userId)
		header.ReplyHeader().Set("Asec_Group_Id", claims.GroupId)
		header.ReplyHeader().Set("Asec_last_phone_number", lastPhoneNumber)
		header.ReplyHeader().Set("Asec_full_phone_number", claims.Phone)
		header.ReplyHeader().Set("Asec_full_email", claims.Email)
		header.ReplyHeader().Set("Asec_prefix_email", prefixEmail)
		header.ReplyHeader().Set("Asec_User_Identifier", claims.Identifier)
		smsIdpList, err := s.auth.GetSecondarySmsIdp(ctx, userId)
		if err != nil {
			s.log.Errorf("get secondary idp by userId failed. userToken=%v, err=%v,", userId, err)
		}
		return &pb.TokenVerifyReply{UserId: userId, UserName: userName, SmsIdp: smsIdpList}, nil
	}
	return &pb.TokenVerifyReply{}, fmt.Errorf("get header failed")
}

func getPrefixAndLast4(email, phone string) (prefix, last4 string) {
	prefix = ""
	last4 = ""

	if email != "" {
		// 提取邮箱前缀
		idx := strings.Index(email, "@")
		if idx != -1 {
			prefix = email[:idx]
		}
	}

	if phone != "" {
		// 取手机号后四位
		if len(phone) >= 4 {
			last4 = phone[len(phone)-4:]
		}
	}

	return
}

func (s *AuthService) GetToken(ctx context.Context, req *pb.GetTokenRequest) (*pb.GetTokenReply, error) {
	token, err := s.auth.GetToken(ctx, req.GrantType, req.Code, req.ClientId)
	if err != nil {
		return &pb.GetTokenReply{}, err
	}
	var resp pb.GetTokenReply
	if err := copier.Copy(&resp, &token); err != nil {
		return &pb.GetTokenReply{}, v1.ErrorParamError("param err")
	}
	return &resp, nil
}

// ListMainIDP 登录前获取主认证idp列表
func (s *AuthService) ListMainIDP(ctx context.Context, req *pb.ListMainIDPRequest) (*pb.ListMainIDPReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListMainIDPReply{}, err
	}

	idpList, err := s.idp.ListMainIdpWithAttrs(ctx, corpId)
	if err != nil {
		return &pb.ListMainIDPReply{}, err
	}
	var result []*pb.ListMainIDPReply_IDP
	for _, idp := range idpList {
		jsonStr, err := json.Marshal(idp.Attrs)
		if err != nil {
			return &pb.ListMainIDPReply{}, err
		}
		var pbStruct structpb.Struct
		err = jsonpb.UnmarshalString(string(jsonStr), &pbStruct)
		if err != nil {
			return &pb.ListMainIDPReply{}, err
		}
		result = append(result, &pb.ListMainIDPReply_IDP{
			Id:           idp.ID,
			Name:         idp.Name,
			Avatar:       idp.Avatar,
			Type:         idp.Type,
			TemplateType: idp.TemplateType,
			Attrs:        &pbStruct,
		})
	}

	return &pb.ListMainIDPReply{IdpList: result}, nil
}

func (s *AuthService) RefreshToken(ctx context.Context, req *pb.RefreshTokenRequest) (*pb.GetTokenReply, error) {
	token, err := s.auth.RefreshToken(ctx)
	if err != nil {
		return &pb.GetTokenReply{}, err
	}
	var resp pb.GetTokenReply
	if err := copier.Copy(&resp, &token); err != nil {
		return &pb.GetTokenReply{}, v1.ErrorParamError("param err")
	}
	return &resp, nil
}

// buildSecondaryCacheValue 构建用于Redis缓存的map数据
func (s *AuthService) buildSecondaryCacheValue(authInfo dto.LoginAuthInfo) (map[string]interface{}, int) {
	period := 30 // 默认周期为30秒
	value := map[string]interface{}{
		"userId": authInfo.UserId,
		"period": period,
	}
	if authInfo.SecondaryType == "totp" {
		deviationStr := authInfo.IdpAttr.TokenDeviation
		if deviationStr != "" {
			parsedPeriod, err := strconv.ParseUint(deviationStr, 10, 32)
			if err != nil {
				s.log.Errorf("invalid token deviation: %v", err)
				return value, period
			}
			period = int(parsedPeriod)
		}
		value["period"] = period
	}
	return value, period
}
func (s *AuthService) handleSecondaryAuth(ctx context.Context, authInfo dto.LoginAuthInfo) (map[string]interface{}, error) {
	value, period := s.buildSecondaryCacheValue(authInfo)
	jsonByte, err := json.Marshal(value)
	if err != nil {
		s.log.Errorf("redis Cache err=%v", err)
		return nil, v1.ErrorParamError("param err")
	}
	expires := 15 * time.Minute //二次认证有效时间 15分钟
	uniqKey, err := s.auth.Cache(ctx, dto.CacheType(dto.UserSourceSMS), string(jsonByte), int(expires))
	if err != nil {
		s.log.Errorf("redis Cache err=%v", err)
		return nil, err
	}
	// 增强认证信息
	err = s.auth.CacheAuthEnhancement(ctx, uniqKey, dto.CacheType(dto.UserSourceSMS), authInfo.AuthEnhancementInfo, int(expires))
	if err != nil {
		s.log.Errorf("redis Cache err=%v", err)
		return nil, err
	}

	// 3. 构造返回数据
	ret := map[string]interface{}{
		"secondary":      true,
		"uniqKey":        &uniqKey,
		"secondaryType":  authInfo.SecondaryType,
		"hasContactInfo": false,
		"userID":         authInfo.UserId,
		"userName":       authInfo.Name,
	}

	switch authInfo.SecondaryType {
	case "sms":
		ret["contactType"] = "phone"
		if authInfo.Phone != "" {
			ret["hasContactInfo"] = true
			ret["contactValue"] = common.MaskPhoneNumber(authInfo.Phone)
		} else {
			ret["contactValue"] = ""
		}
	case "email":
		ret["contactType"] = "email"
		if authInfo.Email != "" {
			ret["hasContactInfo"] = true
			ret["contactValue"] = common.MaskEmailAddress(authInfo.Email)
		} else {
			ret["contactValue"] = ""
		}
	case "totp":
		ret["contactType"] = "totp"
		if authInfo.CurrentSecret != "" { //已经扫过了的  就不再需要返回二维码了 直接给输入框输入口令就行了
			ret["CurrentSecret"] = true
		} else {
			key, qrCode, err := s.totp.generateTOTPKey(authInfo.Name, uint(period))
			if err != nil {
				return nil, err
			}
			s.auth.UpdateTotpKey(ctx, authInfo.UserId, key.Secret())
			ret["qrcode"] = qrCode
		}
	}
	return ret, nil
}

func (s *AuthService) Login(ctx context.Context, req *pb.LoginRequest) (*pb.LoginReply, error) {
	var param dto.LoginForm
	if err := copier.Copy(&param, req); err != nil {
		s.log.Errorf("Copy failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		s.log.Errorf("GetCorpId failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	param.CorpId = corpId
	if req.Encryption == "rsa" {
		param.UserName, err = encrypt.RsaDecrypt(param.UserName)
	}
	if err != nil {
		s.log.Errorf("Decrypt UserName failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	idp, err := s.idp.GetIDPById(ctx, param.CorpId, param.IdpId)
	if err != nil {
		s.log.Errorf("GetIDPById failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}

	var isEnhancement bool

	ret := make(map[string]any)
	result, authInfo, err := s.auth.Login(ctx, param)
	// 登录日志
	defer func() {
		errMsg := ""
		loginLogType := dto.LoginLogType
		desc := ""
		if err != nil {
			errMsg = err.Error()
			loginLogType = dto.LoginErrorLogType
			desc = dto.ErrReasonChineseMap[errors.FromError(err).Reason]
		}
		loginLogParam := dto.CreateLoginLogParam{
			Id:          uuid.New().String(),
			CorpId:      corpId,
			Error:       errMsg,
			IpAddress:   common.GetClientHost(ctx),
			Type:        loginLogType,
			ClientId:    dto.AuthLoginLog,
			EventTime:   time.Now().UnixMilli(),
			UserName:    authInfo.Account,
			SourceId:    idp.SourceID,
			Description: desc,
		}
		// 增强认证触发，不收集日志，在增强认证的地方收集日志
		if !isEnhancement {
			err = s.auth.CreateUserLoginLog(ctx, loginLogParam)
			if err != nil {
				s.log.Errorf("CreateLoginLogFailed err=%v", err)
				return
			}
		}
	}()
	if err != nil {
		s.log.Errorf("Login err=%v", err)
		return &pb.LoginReply{}, err
	}
	jsonByte, err := json.Marshal(result)
	if err != nil {
		s.log.Errorf("param err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	if authInfo.NeedSecondary {
		secondaryData, err := s.handleSecondaryAuth(ctx, authInfo)
		if err != nil {
			return &pb.LoginReply{}, v1.ErrorParamError("param err")
		}
		for k, v := range secondaryData {
			ret[k] = v
		}
		ret["secondary"] = result
		isEnhancement = authInfo.AuthEnhancementInfo.IsRegionEnhancement && authInfo.AuthEnhancementInfo.IsTimeEnhancement
		jsonByte, err = json.Marshal(ret)
		if err != nil {
			s.log.Errorf("param err=%v", err)
			return &pb.LoginReply{}, v1.ErrorParamError("param err")
		}
	}

	var pbStruct structpb.Struct
	err = jsonpb.UnmarshalString(string(jsonByte), &pbStruct)
	if err != nil {
		s.log.Errorf("param err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}

	return &pb.LoginReply{Data: &pbStruct}, nil
}

func (s *AuthService) ThirdLogin(ctx context.Context, req *pb.ThirdLoginRequest) (*pb.LoginReply, error) {
	var param dto.ThirdLoginForm
	if err := copier.Copy(&param, req); err != nil {
		s.log.Errorf("Copy failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		s.log.Errorf("GetCorpId failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	param.CorpId = corpId

	ret := make(map[string]any)
	result, authInfo, err := s.auth.ThirdLogin(ctx, param)
	if err != nil {
		return &pb.LoginReply{}, err
	}
	jsonByte, err := json.Marshal(result)
	if authInfo.NeedSecondary {
		secondaryData, err := s.handleSecondaryAuth(ctx, authInfo)
		if err != nil {
			return &pb.LoginReply{}, v1.ErrorParamError("param err")
		}
		for k, v := range secondaryData {
			ret[k] = v
		}
		ret["secondary"] = result
		jsonByte, err = json.Marshal(ret)
		if err != nil {
			return &pb.LoginReply{}, v1.ErrorParamError("param err")
		}
	}

	if err != nil {
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	var pbStruct structpb.Struct
	err = jsonpb.UnmarshalString(string(jsonByte), &pbStruct)
	if err != nil {
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	return &pb.LoginReply{Data: &pbStruct}, nil
}

func (s *AuthService) Cache(ctx context.Context, req *pb.CacheRequest) (*pb.CacheReply, error) {
	// todo 这个接口要考虑做接口签名校验，不能被滥用，滥用可能会导致redis撑爆
	jsonByte, err := json.Marshal(req.Data)
	if err != nil {
		return &pb.CacheReply{}, err
	}

	uniqKey, err := s.auth.Cache(ctx, dto.CacheType(req.Type), string(jsonByte), 0)
	if err != nil {
		return &pb.CacheReply{}, err
	}
	return &pb.CacheReply{UniqKey: uniqKey}, nil
}

func (s *AuthService) SendSms(ctx context.Context, param *pb.SendSmsRequest) (*pb.SendSmsReply, error) {
	return s.auth.SendSms(ctx, param)
}

func (s *AuthService) SmsVerify(ctx context.Context, req *pb.SmsVerifyRequest) (*pb.LoginReply, error) {
	// 如果是同样的idp则
	var param dto.LoginForm
	if err := copier.Copy(&param, req); err != nil {
		s.log.Errorf("Copy failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		s.log.Errorf("GetCorpId failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	param.CorpId = corpId
	param.UniqKey = req.UniqKey
	param.AuthCode = req.AuthCode
	param.IdpId = req.IdpId
	param.RedirectUri = req.RedirectUri
	param.GrantType = dto.GrantType(req.GrantType)
	param.ClientId = req.ClientId
	param.Scope = req.Scope
	param.TotpKey = req.TotpKey

	// 获取IDP类型信息
	idpInfo, err := s.idp.GetIDPById(ctx, corpId, req.IdpId)
	if err != nil {
		s.log.Errorf("GetIDPById failed. err=%v", err)
		return &pb.LoginReply{}, v1.ErrorParamError("辅助认证信息获取异常")
	}

	enhance := dto.AuthEnhancementInfo{}
	//判断是否为应用访问需要认证的场景，而不是登录场景
	if param.RedirectUri != "app_redirect" {
		enhance, err = s.auth.GetRedisAuthEnhancementInfo(ctx, param)
		if err != nil {
			s.log.Errorf("GetCorpId failed. err=%v", err)
			return &pb.LoginReply{}, v1.ErrorParamError("param err")
		}
	}

	// 登录日志
	defer func() {
		errMsg := ""
		desc := ""
		loginLogType := dto.LoginLogType
		if err != nil {
			errMsg = err.Error()
			loginLogType = dto.LoginErrorLogType
			desc = dto.ErrReasonChineseMap[errors.FromError(err).Reason]
		}
		if param.RedirectUri != "app_redirect" {
			if enhance.IsRegionEnhancement && enhance.IsTimeEnhancement {
				var description string
				if enhance.Time != "" {
					description = fmt.Sprintf(dto.TimeEnhancement, enhance.Time) + "," + description
				}
				if enhance.Region != "" {
					description = fmt.Sprintf(dto.RegionEnhancement, enhance.Region) + "," + description
				}
				desc = description + dto.Enhancement
			}
		}
		// 根据验证类型设置不同的sourceId
		sourceId := idpInfo.SourceID
		loginLogParam := dto.CreateLoginLogParam{
			Id:          uuid.New().String(),
			CorpId:      corpId,
			Error:       errMsg,
			IpAddress:   common.GetClientHost(ctx),
			Type:        loginLogType,
			ClientId:    dto.AuthLoginLog,
			EventTime:   time.Now().UnixMilli(),
			UserName:    param.UserName,
			SourceId:    sourceId,
			Description: desc,
		}
		err = s.auth.CreateUserLoginLog(ctx, loginLogParam)
		if err != nil {
			s.log.Errorf("CreateLoginLogFailed err=%v", err)
		}
	}()
	var result any
	if dto.IDPType(idpInfo.Type) == dto.IDPTypeTotp {
		result, err = s.auth.TotpVerify(ctx, param)
	} else {
		result, err = s.auth.VerifyCode(ctx, param)
	}
	if err != nil {
		return &pb.LoginReply{}, err
	}
	jsonByte, err := json.Marshal(result)
	if err != nil {
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	var pbStruct structpb.Struct
	err = jsonpb.UnmarshalString(string(jsonByte), &pbStruct)
	if err != nil {
		return &pb.LoginReply{}, v1.ErrorParamError("param err")
	}
	//todo 当存在多个增强认证时，这里需要判断增强认证是否需要

	return &pb.LoginReply{Data: &pbStruct}, nil
}

func (s *AuthService) GetSendSmsKey(ctx context.Context, req *pb.SendSmsKeyRequest) (*pb.SendSmsKeyReply, error) {
	user, err := s.auth.GetUserInfoById(ctx, req.UserId)
	if err != nil {
		return &pb.SendSmsKeyReply{}, v1.ErrorParamError("param err")
	}
	userName := user.Name
	value := map[string]interface{}{
		"userId": req.UserId,
	}
	jsonByte, err := json.Marshal(value)
	if err != nil {
		return &pb.SendSmsKeyReply{}, v1.ErrorParamError("param err")
	}
	expires := 15 * time.Minute
	uniqKey, err := s.auth.Cache(ctx, dto.CacheType(dto.UserSourceSMS), string(jsonByte), int(expires))
	if err != nil {
		return &pb.SendSmsKeyReply{}, err
	}
	noPhone := false
	if user.Phone == "" {
		noPhone = true
	}
	if userName == "" {
		userName = user.DisplayName
	}
	return &pb.SendSmsKeyReply{Data: &pb.SmsInfo{UniqKey: uniqKey, NotPhone: noPhone, UserName: userName}}, nil
}

func (s *AuthService) AdmsLogin(ctx context.Context, req *pb.AdmsLoginRequest) (*pb.AdmsLoginReply, error) {
	token, err := s.auth.LoginAdms(ctx, req.ServerAddr, req.Entry, req.UserName)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &pb.AdmsLoginReply{AdmsCheck: false}, nil
		}
		return &pb.AdmsLoginReply{AdmsCheck: false}, err
	}
	if token.AccessToken != "" {
		return &pb.AdmsLoginReply{
			TokenInfo: &pb.TokenInfo{
				Token:        token.AccessToken,
				RefreshToken: token.RefreshToken,
				Realm:        RealmValue,
			},
			AdmsCheck: true,
		}, nil
	}
	return &pb.AdmsLoginReply{AdmsCheck: false}, err
}

func (s *AuthService) UserBind(ctx context.Context, req *pb.UserBindRequest) (*pb.UserBindReply, error) {
	result, err := s.auth.UserBind(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	return &pb.UserBindReply{
		Success:       result.Success,
		LocalUserId:   result.LocalUserId,
		LocalUserName: result.LocalUserName,
		Message:       result.Message,
	}, nil
}

// OAuth2Callback 处理OAuth2身份认证回调
func (s *AuthService) OAuth2Callback(ctx context.Context, req *pb.OAuth2CallbackRequest) (*pb.LoginReply, error) {
	// 0. 收集所有回调参数
	allParams := s.collectOAuth2Params(ctx, req)

	// 1. 获取必要参数
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 2. 获取IDP配置和属性
	idp, err := s.idp.GetIDPById(ctx, corpId, req.IdpId)
	if err != nil {
		s.log.Errorf("获取IDP配置失败: %v", err)
		return nil, v1.ErrorParamError("身份提供商配置未找到")
	}
	attrs, err := s.idp.GetIDPAttrs(ctx, corpId, req.IdpId)
	if err != nil {
		s.log.Errorf("获取IDP属性失败: %v", err)
		return nil, v1.ErrorParamError("身份提供商属性获取失败")
	}

	// 3. 从配置中查找授权码和状态参数的映射
	idpType := dto.GetIdpType(idp.Type, idp.TemplateType)
	code, state, err := s.findOAuth2CodeAndState(ctx, allParams, idpType, attrs)
	if err != nil {
		s.log.Errorf("解析OAuth2回调参数失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("OAuth2回调参数错误: %v", err))
	}

	s.log.Infof("接收到回调请求,IDP ID: %s, Code: %s, State: %s, 总参数数量: %d",
		req.IdpId, code, state, len(allParams))

	redirectUri := req.RedirectUri
	if redirectUri == "" {
		scheme := common.GetClientScheme(ctx)
		hostPort := common.GetClientHostPortWithConfig(ctx)
		redirectUri = fmt.Sprintf("%s://%s/auth/login/v1/callback/%s", scheme, hostPort, req.IdpId)
		s.log.Infof("动态生成重定向URI: %s", redirectUri)
	}

	SsoState := dto.SsoState{}
	if req.State != "" {
		decodedState, err := base64.StdEncoding.DecodeString(req.State)
		if err == nil {
			decodedStateStr := string(decodedState)
			if err := json.Unmarshal([]byte(decodedStateStr), &SsoState); err != nil {
				s.log.Warnf("State不是有效的JSON格式: %v,当作普通状态参数处理", err)
				// 不返回错误，继续处理
			} else {
				s.log.Infof("检测到base64编码的测试模式state: %s, 测试ID: %s", decodedStateStr, SsoState.TestId)
			}
		} else {
			s.log.Infof("State参数非Base64编码: %v,当作普通状态参数处理", err)
		}
	}
	// 如果是测试模式，处理测试回调
	if SsoState.IsTest == "true" && SsoState.TestId != "" {
		return s.handleOAuth2Test(ctx, req, SsoState.TestId)
	}

	s.log.Infof("非测试模式，执行标准%s登录流程", idp.TypeName)

	// 2. 如果没有授权码，需要重定向到授权页面
	if code == "" {
		s.log.Debugf("授权码为空,需要生成%s授权页面", idp.TypeName)
		var authProvider SsoAuthProvider
		var authProviderErr error
		switch dto.IDPType(idpType) {
		case dto.IDPTypeOAuth2:
			// 2.1创建OAuth2Provider并获取授权URL
			logger := log.With(log.GetLogger())
			authProvider, authProviderErr = oauth2.NewOAuth2Provider(
				idp.ID,
				idp.Name,
				attrs["oauth2_global_data"],
				attrs["oauth2_code_data"],
				attrs["oauth2_user_data"],
				attrs["oauth2_logout_data"],
				attrs["oauth2_logout_open"] == "1",
				redirectUri,
				logger,
			)
		case dto.IDPTypeCas:
			// 2.1创建OAuth2Provider并获取授权URL
			logger := log.With(log.GetLogger())
			authProvider, authProviderErr = cas.NewCasProvider(
				idp.ID,
				idp.Name,
				attrs["cas_auth_data"],
				[]dto.KV{},
				redirectUri,
				logger,
			)
		default:
			s.log.Errorf("不支持的IDP类型: %s", idp.Type)
			return nil, v1.ErrorParamError("系统配置错误")
		}
		if authProviderErr != nil {
			s.log.Errorf("创建SsoAuthProvider失败: %v", authProviderErr)
			return nil, v1.ErrorParamError("系统配置错误")
		}

		// 2.2 生成授权URL
		frontendCallback := fmt.Sprintf("%s://%s/auth/login/v1/callback/%s",
			common.GetClientScheme(ctx),
			common.GetClientHostPortWithConfig(ctx),
			req.IdpId)
		// s.log.Infof("前端回调URL: %s", frontendCallback)
		state := dto.SsoState{
			State:       req.State,
			CallBackUrl: frontendCallback,
			IdpId:       req.IdpId,
			Redirect:    req.Redirect,
			Time:        fmt.Sprintf("%d", time.Now().Unix()),
		}
		jsonData, err := json.Marshal(state)
		if err != nil {
			s.log.Errorf("构建state失败: %v", err)
			return nil, fmt.Errorf("构建state失败: %v", err)
		}
		encodedState := base64.StdEncoding.EncodeToString([]byte(jsonData))
		authURL, err := authProvider.GetAuthorizationURL(ctx, encodedState, redirectUri, false)
		if err != nil {
			s.log.Errorf("生成授权URL失败: %v", err)
			return nil, v1.ErrorParamError("无法生成授权URL")
		}
		var pbStruct structpb.Struct
		jerr := errors.New(302, v1.ErrorReason_OAUTH2_AUTHORIZE_ERROR.String(), authURL)
		return &pb.LoginReply{Data: &pbStruct}, jerr
	}

	// 3. 构造第三方登录表单并处理
	param := dto.ThirdLoginForm{
		AuthBasicForm: dto.AuthBasicForm{
			CorpId:      corpId,
			IdpId:       req.IdpId,
			GrantType:   dto.GrantTypeImplicit, // 修改为正确的授权类型常量
			ClientId:    attrs["client_id"],    // 从配置属性获取
			Scope:       attrs["scope"],
			RedirectUri: redirectUri, //
		},
		RedirectUri: redirectUri,
		AuthWeb: dto.AuthWebLoginForm{
			AuthWebCode:  code,
			AuthWebState: req.State,
			AuthParams:   allParams,
		},
	}

	// 4. 使用通用第三方登录流程
	s.log.Infof("开始%s登录流程,参数: %+v", idp.TypeName, param)
	result, authInfo, err := s.auth.ThirdLogin(ctx, param)
	if err != nil {
		s.log.Errorf("%s登录失败: %v", idp.TypeName, err)
		errorURL := fmt.Sprintf("%s://%s/#/login?oauth_callbak=1&idp_id=%s&auth_error=%s&login_type=%s",
			common.GetClientScheme(ctx), common.GetClientHostPortWithConfig(ctx), req.IdpId, err.Error(), idpType)
		var pbStruct structpb.Struct
		jerr := errors.New(302, v1.ErrorReason_OAUTH2_CALLBACK_ERROR.String(), errorURL)
		return &pb.LoginReply{Data: &pbStruct}, jerr
	}
	// 暂存给前端调用ThirdLogin的结果
	ret := make(map[string]interface{})
	ret["result"] = result
	ret["authInfo"] = authInfo
	// 序列化为返回格式
	resultJSON, err := json.Marshal(ret)
	if err != nil {
		s.log.Errorf("序列化登录结果失败: %v", err)
		return nil, v1.ErrorParamError("序列化结果失败")
	}
	// resultJSON存储到redis
	authCode, err := s.auth.Cache(ctx, dto.CacheType(idpType), string(resultJSON), 300)
	if err != nil {
		s.log.Errorf("缓存登录结果失败: %v", err)
		return nil, v1.ErrorParamError("系统处理错误")
	}
	// 设置header头重定向
	successURL := fmt.Sprintf("%s://%s/#/login?oauth_callbak=1&idp_id=%s&auth_token=%s&login_type=%s&redirect_url=%s&state=%s",
		common.GetClientScheme(ctx), common.GetClientHostPortWithConfig(ctx), req.IdpId, authCode, idpType, url.QueryEscape(SsoState.Redirect), SsoState.State)
	var pbStruct structpb.Struct
	jerr := errors.New(302, v1.ErrorReason_OAUTH2_CALLBACK_ERROR.String(), successURL)
	return &pb.LoginReply{Data: &pbStruct}, jerr
}

// 在AuthService结构体中添加一个新方法
func (s *AuthService) handleOAuth2Test(ctx context.Context, req *pb.OAuth2CallbackRequest, testId string) (*pb.LoginReply, error) {
	s.log.Infof("进入OAuth2测试专用处理流程，测试ID: %s", testId)
	// 0. 收集所有回调参数，与正式流程一致
	allParams := s.collectOAuth2Params(ctx, req)

	// 1. 获取必要参数
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 2. 获取IDP配置和属性
	idp, err := s.idp.GetIDPById(ctx, corpId, req.IdpId)
	if err != nil {
		s.log.Errorf("获取IDP配置失败: %v", err)
		return nil, v1.ErrorParamError("身份提供商配置未找到")
	}

	attrs, err := s.idp.GetIDPAttrs(ctx, corpId, req.IdpId)
	if err != nil {
		s.log.Errorf("获取IDP属性失败: %v", err)
		return nil, v1.ErrorParamError("身份提供商属性获取失败")
	}

	// 3. 从配置中查找授权码和状态参数的映射，与正式流程一致
	idpType := dto.GetIdpType(idp.Type, idp.TemplateType)
	code, state, err := s.findOAuth2CodeAndState(ctx, allParams, idpType, attrs)
	if err != nil {
		s.log.Errorf("解析OAuth2回调参数失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("OAuth2回调参数错误: %v", err))
	}

	s.log.Infof("测试模式，根据配置解析授权码: %s, 状态: %s", code, state)

	// 4. 设置重定向URI
	redirectUri := req.RedirectUri
	if redirectUri == "" {
		scheme := common.GetClientScheme(ctx)
		hostPort := common.GetClientHostPortWithConfig(ctx)
		redirectUri = fmt.Sprintf("%s://%s/auth/login/v1/callback/%s", scheme, hostPort, req.IdpId)
		s.log.Infof("测试模式动态生成重定向URI: %s", redirectUri)
	}

	// 5. 获取当前测试结果缓存
	cacheResult, err := s.auth.GetTestCache(ctx, testId)
	var testResult map[string]interface{}
	if err != nil {
		s.log.Warnf("获取测试缓存失败: %v，将使用空缓存", err)
		testResult = make(map[string]interface{})
	} else if cacheResult == "" {
		s.log.Infof("测试缓存为空，创建新的测试结果")
		testResult = make(map[string]interface{})
	} else {
		if err := json.Unmarshal([]byte(cacheResult), &testResult); err != nil {
			s.log.Errorf("解析测试结果失败: %v", err)
			testResult = make(map[string]interface{})
		} else {
			s.log.Infof("成功加载测试缓存，内容大小: %d字节", len(cacheResult))
		}
	}

	// 6. 更新测试结果基本信息
	testResult["auth_code"] = code
	testResult["test_id"] = testId

	// 记录所有接收到的参数，便于调试
	paramDetails := make(map[string]string)
	for k, v := range allParams {
		if len(v) > 20 {
			// 对长字符串进行掩码处理
			paramDetails[k] = v[:5] + "..." + v[len(v)-5:]
		} else {
			paramDetails[k] = v
		}
	}
	testResult["received_params"] = paramDetails

	// 7. 准备日志数组
	var logs []string
	if testResult["logs"] == nil {
		logs = []string{
			"┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
			"┃ 测试已开始,已生成授权URL,等待用户授权...",
			"┃ 收到授权码回调",
			fmt.Sprintf("┃ 授权码: %s", code),
			"┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"}
	} else {
		// 安全获取现有日志
		existingLogs, ok := testResult["logs"].([]interface{})
		if ok {
			logs = make([]string, len(existingLogs))
			for i, v := range existingLogs {
				if strVal, ok := v.(string); ok {
					logs[i] = strVal
				} else {
					logs[i] = fmt.Sprintf("%v", v)
				}
			}
		} else {
			logs = []string{}
		}

		logs = append(logs, "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
		logs = append(logs, "┃ 收到授权码回调")
	}

	// 添加参数解析信息
	logs = append(logs, fmt.Sprintf("┃ 收到参数总数: %d", len(allParams)))
	// if len(paramDetails) > 0 {
	// 	logs = append(logs, "┃ 接收到的主要参数:")
	// 	for k, v := range paramDetails {
	// 		logs = append(logs, fmt.Sprintf("  %s = %s", k, v))
	// 	}
	// }
	logs = append(logs, fmt.Sprintf("┃ 根据配置解析得到授权码: %s", code))

	// 8. 创建OAuth2提供商，用于直接获取用户信息
	logger := log.With(log.GetLogger())
	oauth2Provider, err := oauth2.NewOAuth2Provider(
		idp.ID,
		idp.Name,
		attrs["oauth2_global_data"],
		attrs["oauth2_code_data"],
		attrs["oauth2_user_data"],
		attrs["oauth2_logout_data"],
		attrs["oauth2_logout_open"] == "1",
		redirectUri,
		logger,
	)

	if err != nil {
		s.log.Errorf("创建OAuth2Provider失败: %v", err)
		logs = append(logs, fmt.Sprintf("创建OAuth2Provider失败: %v", err))
		testResult["success"] = false
		testResult["error"] = err.Error()
	} else {
		// 8. 直接调用HandleCallback获取用户信息，捕获所有执行日志
		s.log.Infof("开始执行OAuth2回调处理，获取用户信息")
		logs = append(logs, "┃ 开始执行OAuth2回调处理流程，获取用户信息")
		logs = append(logs, "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

		// 设置webauth执行器的日志捕获钩子
		executor := oauth2Provider.GetExecutor()
		if executor != nil {
			// 重置日志
			executor.ClearLogs()
		}

		// 执行回调处理
		oauth2UserInfo, err := oauth2Provider.HandleCallback(ctx, code, req.State)

		// 获取执行器中的详细HTTP请求日志
		if executor != nil {
			httpLogs := executor.GetLogs()
			if len(httpLogs) > 0 {
				// logs = append(logs, "---- OAuth2请求执行日志 ----")
				logs = append(logs, httpLogs...)
				// logs = append(logs, "------------------------")
			}
		}

		if err != nil {
			s.log.Errorf("OAuth2获取用户信息失败: %v", err)
			logs = append(logs, fmt.Sprintf("OAuth2获取用户信息失败: %v", err))
			testResult["success"] = false
			testResult["error"] = err.Error()
		} else {
			s.log.Infof("成功获取OAuth2用户信息: %+v", oauth2UserInfo)

			// 记录用户信息
			userInfo := map[string]interface{}{
				"user_id":          oauth2UserInfo.Userid,
				"user_name":        oauth2UserInfo.Name,
				"email":            oauth2UserInfo.Email,
				"phone":            oauth2UserInfo.Mobile,
				"uniq_key":         oauth2UserInfo.UniqKey,
				"user_displayname": oauth2UserInfo.DisplayName,
				"user_truename":    oauth2UserInfo.TrueName,
				"user_nickname":    oauth2UserInfo.NickName,
			}
			testResult["user_info"] = userInfo

			// 添加用户信息到日志
			logs = append(logs, "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
			logs = append(logs, "┃ OAuth2认证成功，获取到用户信息:")
			if oauth2UserInfo.Userid != "" {
				logs = append(logs, fmt.Sprintf("┃ 用户ID: %s", oauth2UserInfo.Userid))
			}
			if oauth2UserInfo.Name != "" {
				logs = append(logs, fmt.Sprintf("┃ 用户姓名: %s", oauth2UserInfo.Name))
			}
			if oauth2UserInfo.DisplayName != "" {
				logs = append(logs, fmt.Sprintf("┃ 显示名称: %s", oauth2UserInfo.DisplayName))
			}
			if oauth2UserInfo.TrueName != "" {
				logs = append(logs, fmt.Sprintf("┃ 真实姓名: %s", oauth2UserInfo.TrueName))
			}
			if oauth2UserInfo.NickName != "" {
				logs = append(logs, fmt.Sprintf("┃ 用户昵称: %s", oauth2UserInfo.NickName))
			}
			if oauth2UserInfo.Email != "" {
				logs = append(logs, fmt.Sprintf("┃ 用户邮箱: %s", oauth2UserInfo.Email))
			}
			if oauth2UserInfo.Mobile != "" {
				logs = append(logs, fmt.Sprintf("┃ 用户电话: %s", oauth2UserInfo.Mobile))
			}

			logs = append(logs, "┃ 测试完成，授权流程验证成功")
			logs = append(logs, "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
			testResult["success"] = true
		}
	}

	// 9. 构造第三方登录表单(可选，为了完整测试)
	if code != "" && testResult["success"] != true {
		logs = append(logs, "尝试使用标准登录流程验证...")
		param := dto.ThirdLoginForm{
			AuthBasicForm: dto.AuthBasicForm{
				CorpId:      corpId,
				IdpId:       req.IdpId,
				GrantType:   dto.GrantTypeImplicit,
				ClientId:    attrs["client_id"],
				Scope:       attrs["scope"],
				RedirectUri: redirectUri,
			},
			RedirectUri: redirectUri,
			AuthWeb: dto.AuthWebLoginForm{
				AuthWebCode:  code,
				AuthWebState: req.State,
			},
		}

		// 使用标准第三方登录流程验证
		_, authInfo, err := s.auth.ThirdLogin(ctx, param)
		if err != nil {
			s.log.Errorf("标准OAuth2登录流程失败: %v", err)
			logs = append(logs, fmt.Sprintf("标准登录流程失败: %v", err))
		} else {
			s.log.Infof("标准OAuth2登录流程成功，用户: %s", authInfo.UserId)
			logs = append(logs, "标准登录流程成功")
			logs = append(logs, fmt.Sprintf("标准流程用户ID: %s", authInfo.UserId))
			logs = append(logs, fmt.Sprintf("标准流程用户姓名: %s", authInfo.Name))
			testResult["success"] = true
		}
	}

	// 10. 更新测试结果
	testResult["logs"] = logs

	// 11. 更新测试结果缓存
	resultJSON, err := json.Marshal(testResult)
	if err != nil {
		s.log.Errorf("序列化测试结果失败: %v", err)
		// 即使序列化失败，我们仍然返回测试页面，所以这里不返回错误
		// 但我们不会尝试更新缓存
	} else {
		s.log.Infof("准备更新测试结果缓存，testId=%s，内容摘要: %d字节", testId, len(resultJSON))

		if err := s.auth.CacheTest(ctx, testId, string(resultJSON), 10*60); err != nil {
			s.log.Errorf("缓存OAuth2测试结果失败: %v", err)
		} else {
			s.log.Infof("成功缓存OAuth2测试结果，testId=%s", testId)
		}
	}

	// 12. 返回测试回调页面
	ret := map[string]interface{}{
		"testSuccess": testResult["success"] == true,
		"message":     "OAuth2认证测试完成，请返回管理平台查看测试结果",
	}

	// 13. 序列化并返回
	responseJSON, err := json.Marshal(ret)
	if err != nil {
		s.log.Errorf("测试结果响应序列化失败: %v", err)
		return nil, v1.ErrorParamError("测试结果响应序列化失败")
	}

	var pbStruct structpb.Struct
	err = jsonpb.UnmarshalString(string(responseJSON), &pbStruct)
	if err != nil {
		s.log.Errorf("测试结果序列化失败: %v", err)
		return nil, v1.ErrorParamError("测试结果序列化失败")
	}

	resultStatus := "失败"
	if testResult["success"] == true {
		resultStatus = "成功"
	}
	s.log.Infof("成功返回OAuth2测试结果，测试%s", resultStatus)
	successURL := fmt.Sprintf("%s://%s/#/login?oauth_callbak=1&idp_id=%s&login_type=is_test",
		common.GetClientScheme(ctx), common.GetClientHostPortWithConfig(ctx), req.IdpId)
	jerr := errors.New(302, v1.ErrorReason_OAUTH2_CALLBACK_ERROR.String(), successURL)
	return &pb.LoginReply{Data: &pbStruct}, jerr
}

// collectOAuth2Params 收集OAuth2回调参数
func (s *AuthService) collectOAuth2Params(ctx context.Context, req *pb.OAuth2CallbackRequest) map[string]string {
	allParams := make(map[string]string)

	// 1. 从请求对象获取已解析的参数
	if req.Code != "" {
		allParams["code"] = req.Code
	}
	if req.Ticket != "" {
		allParams["ticket"] = req.Ticket
	}
	if req.State != "" {
		allParams["state"] = req.State
	}

	// 2. 从原始请求URL获取更多参数
	if tr, ok := transport.FromServerContext(ctx); ok {
		urlPath := tr.RequestHeader().Get("X-Original-URI")
		if urlPath != "" {
			if parsedURL, err := url.Parse(urlPath); err == nil {
				query := parsedURL.Query()
				for k, v := range query {
					if len(v) > 0 {
						allParams[k] = v[0]
						s.log.Debugf("从URL获取参数: %s = %s", k, v[0])
					}
				}
			}
		}
	}

	s.log.Infof("收集到OAuth2回调参数: %v", allParams)
	return allParams
}

// findOAuth2CodeAndState 从配置中查找授权码和状态参数的映射
func (s *AuthService) findOAuth2CodeAndState(ctx context.Context, allParams map[string]string, idpType string, attrs map[string]string) (code string, state string, err error) {
	// 如果不是OAuth2类型，快速返回
	if dto.IDPType(idpType) != dto.IDPTypeOAuth2 {
		// 对CAS和其他类型直接使用标准参数
		if v, exists := allParams["code"]; exists && v != "" {
			return v, allParams["state"], nil
		} else if v, exists := allParams["ticket"]; exists && v != "" {
			return v, allParams["state"], nil
		}
		return "", allParams["state"], nil
	}

	// OAuth2类型但没有配置，使用标准参数
	if attrs["oauth2_code_data"] == "" {
		s.log.Warnf("OAuth2配置为空，使用标准参数")
		if v, exists := allParams["code"]; exists && v != "" {
			return v, allParams["state"], nil
		} else if v, exists := allParams["ticket"]; exists && v != "" {
			return v, allParams["state"], nil
		}
		return "", allParams["state"], nil
	}

	// 解析OAuth2配置
	var codeConfig struct {
		Output struct {
			Params []struct {
				Key    string `json:"key"`
				Jx     string `json:"jx"`
				Select string `json:"select"`
			} `json:"params"`
		} `json:"output"`
	}

	if err := json.Unmarshal([]byte(attrs["oauth2_code_data"]), &codeConfig); err != nil {
		s.log.Errorf("解析OAuth2参数映射配置失败: %v", err)
		// 配置解析失败时仍尝试使用标准参数
		if v, exists := allParams["code"]; exists && v != "" {
			return v, allParams["state"], nil
		} else if v, exists := allParams["ticket"]; exists && v != "" {
			return v, allParams["state"], nil
		}
		return "", allParams["state"], nil
	}

	s.log.Infof("成功解析OAuth2参数映射配置，参数数量: %d", len(codeConfig.Output.Params))

	var foundCodeParam, foundStateParam bool
	var codeParamName, stateParamName string

	// 查找映射到AuthCode和State的参数定义
	for _, param := range codeConfig.Output.Params {
		if strings.Contains(param.Select, "User.AuthCode") {
			foundCodeParam = true
			codeParamName = param.Key
			if v, exists := allParams[param.Key]; exists && v != "" {
				code = v
				s.log.Infof("根据配置从参数[%s]获取授权码: %s", param.Key, code)
			}
		}

		if strings.Contains(param.Select, "User.State") {
			foundStateParam = true
			stateParamName = param.Key
			if v, exists := allParams[param.Key]; exists && v != "" {
				state = v
				s.log.Infof("根据配置从参数[%s]获取状态: %s", param.Key, state)
			}
		}
	}

	// 记录参数情况，但不要因为缺少参数而报错
	// 这样才能让上层函数决定是重定向还是处理授权
	if foundCodeParam && code == "" {
		s.log.Infof("未找到配置的授权码参数[%s]，可能需要重定向到授权页面", codeParamName)
	}

	if foundStateParam && state == "" {
		s.log.Warnf("未找到配置的状态参数[%s]", stateParamName)
	}

	return code, state, nil
}

// AuthCallback 处理认证回调（统一入口）
func (s *AuthService) AuthCallback(ctx context.Context, req *pb.AuthCallbackRequest) (*pb.LoginReply, error) {
	// s.log.Infof("处理AuthCallback认证回调: type=%s, provider=%s, code=%s, corp_id=%s, idp_id=%s",
	// 	req.AuthType, req.AuthProvider, req.AuthCode, req.CorpId, req.IdpId)

	// 1. 参数验证
	if req.AuthCode == "" {
		return nil, v1.ErrorParamError("认证码不能为空")
	}
	if req.AuthType == "" {
		return nil, v1.ErrorParamError("认证类型不能为空")
	}
	if req.CorpId == "" {
		return nil, v1.ErrorParamError("企业ID不能为空")
	}

	// 2. 根据认证类型分发处理
	switch req.AuthType {
	case "micro_app":
		return s.handleMicroAppAuthCallback(ctx, req)
	default:
		return nil, v1.ErrorParamError(fmt.Sprintf("不支持的认证类型: %s", req.AuthType))
	}
}

// handleMicroAppAuthCallback 处理微应用认证回调
func (s *AuthService) handleMicroAppAuthCallback(ctx context.Context, req *pb.AuthCallbackRequest) (*pb.LoginReply, error) {
	// s.log.Infof("处理微应用认证回调: provider=%s, code=%s", req.AuthProvider, req.AuthCode)
	if req.AuthProvider == "" {
		return nil, v1.ErrorParamError("认证提供商不能为空")
	}

	// s.log.Infof("收到微应用认证参数: auth_code=%s, auth_provider=%s, corp_id=%s, idp_id=%s, redirect_uri=%s",
	// 	req.AuthCode, req.AuthProvider, req.CorpId, req.IdpId, req.RedirectUri)

	// 构造微应用认证参数，保持前端传递的第三方企业ID
	param := dto.MicroAppAuthParam{
		AuthProvider: req.AuthProvider,
		AuthCode:     req.AuthCode,
		CorpId:       req.CorpId, // 使用前端传递的第三方企业ID（如钉钉的corp_id）
		IdpId:        req.IdpId,  // 传递 IdpId
		RedirectUri:  req.RedirectUri,
	}
	// 调用业务层处理微应用认证
	result, err := s.auth.HandleMicroAppAuth(ctx, param)
	if err != nil {
		s.log.Errorf("微应用认证失败: %v", err)
		return nil, v1.ErrorParamError("认证失败")
	}
	// 微应用认证成功，通过Set-Cookie响应头设置token - 这是唯一的token设置方式
	// s.log.Infof("微应用认证成功，通过Set-Cookie设置token: user_id=%s, user_name=%s, provider=%s", result.UserId, result.UserName, result.AuthProvider)
	if header, ok := transport.FromServerContext(ctx); ok {
		// 构建Cookie设置，优化兼容性（去掉HttpOnly以确保跨域兼容）
		cookieValue := fmt.Sprintf("%s=%s; Path=/; SameSite=Lax", TokenCookieName, result.AccessToken) // 尝试从redirect_url提取域名信息设置跨域Cookie
		if req.RedirectUri != "" {
			// s.log.Infof("原始重定向URL: %s", req.RedirectUri)
			if redirectUrl, err := url.Parse(req.RedirectUri); err == nil {
				// 如果是HTTPS，添加Secure属性
				if redirectUrl.Scheme == "https" {
					cookieValue += "; Secure"
					// s.log.Infof("添加Secure属性（HTTPS环境）")
				} // 跨域Cookie设置：动态提取泛域名
				// hostname := redirectUrl.Hostname()
				// s.log.Infof("目标域名: %s", hostname) // 跨域Cookie设置：去掉第一级域名，设置剩余部分为泛域名
				targetHostname := redirectUrl.Hostname()
				// s.log.Infof("目标域名: %s", targetHostname)

				if targetHostname != "" && targetHostname != "localhost" {
					domain := s.getCookieDomain(targetHostname)
					if domain != "" {
						cookieValue += fmt.Sprintf("; Domain=%s", domain)
						s.log.Debugf("设置跨域Cookie Domain为: %s (原域名: %s)", domain, targetHostname)
					} else {
						s.log.Warnf("域名结构不足，跳过Domain设置: %s", targetHostname)
					}
				} else {
					s.log.Warnf("无效的hostname，跳过Domain设置: %s", targetHostname)
				}
			} else {
				s.log.Errorf("解析重定向URL失败: %v", err)
			}
		} else {
			s.log.Warnf("没有提供重定向URL")
		}

		// 设置Cookie过期时间（24小时）
		expires := time.Now().Add(24 * time.Hour).Format("Mon, 02 Jan 2006 15:04:05 GMT")
		cookieValue += fmt.Sprintf("; Expires=%s", expires)

		header.ReplyHeader().Set("Set-Cookie", cookieValue)
		// s.log.Infof("通过Set-Cookie响应头设置token: %s", cookieValue)
	}
	// 构造登录响应 - 包含fallback token用于URL参数传递
	responseData := map[string]interface{}{
		"success":       true,
		"user_id":       result.UserId,
		"user_name":     result.UserName,
		"auth_type":     result.AuthType,
		"auth_provider": result.AuthProvider,
	}

	jsonBytes, err := json.Marshal(responseData)
	if err != nil {
		return nil, v1.ErrorParamError("响应数据序列化失败")
	}

	var pbStruct structpb.Struct
	err = json.Unmarshal(jsonBytes, &pbStruct)
	if err != nil {
		return nil, v1.ErrorParamError("响应数据转换失败")
	}

	return &pb.LoginReply{Data: &pbStruct}, nil
}

// getCookieDomain 智能计算Cookie域名共享范围
func (s *AuthService) getCookieDomain(targetHostname string) string {
	if targetHostname == "" || targetHostname == "localhost" {
		return ""
	}

	if colonIndex := strings.Index(targetHostname, ":"); colonIndex != -1 {
		targetHostname = targetHostname[:colonIndex]
	}

	parts := strings.Split(targetHostname, ".")
	if len(parts) < 2 {
		return ""
	}

	// 检查是否是公共后缀
	isPublicSuffix := s.isPublicSuffix(parts)

	var domain string

	if len(parts) >= 4 && isPublicSuffix {
		// 如 htdfs.infogo.net.cn -> .infogo.net.cn (保留3级域名)
		domain = "." + strings.Join(parts[len(parts)-3:], ".")
		s.log.Debugf("检测到%d级域名且含公共后缀，设置三级域名共享: %s -> %s", len(parts), targetHostname, domain)
	} else if len(parts) >= 4 {
		// 如 a.app.asdsec.com -> .asdsec.com (保留2级域名)
		domain = "." + strings.Join(parts[len(parts)-2:], ".")
		s.log.Debugf("检测到%d级域名，设置主域名共享: %s -> %s", len(parts), targetHostname, domain)
	} else if len(parts) == 3 && isPublicSuffix {
		// 如 infogo.net.cn -> .infogo.net.cn (保留当前域名)
		domain = "." + strings.Join(parts, ".")
		s.log.Debugf("检测到3级域名且含公共后缀，设置当前域名共享: %s -> %s", targetHostname, domain)
	} else if len(parts) == 3 {
		// 如 app.asdsec.com -> .asdsec.com (保留2级域名)
		domain = "." + strings.Join(parts[1:], ".")
		s.log.Debugf("检测到3级域名，设置主域名共享: %s -> %s", targetHostname, domain)
	} else {
		// 如 asdsec.com -> .asdsec.com (保留当前域名)
		domain = "." + strings.Join(parts, ".")
		s.log.Debugf("检测到2级域名，设置当前域名共享: %s -> %s", targetHostname, domain)
	}

	return domain
}

// isPublicSuffix 检查是否是公共后缀
func (s *AuthService) isPublicSuffix(parts []string) bool {
	if len(parts) < 2 {
		return false
	}

	// 检查最后两个部分
	lastTwo := strings.Join(parts[len(parts)-2:], ".")

	// 常见的双级公共后缀
	commonDoubleSuffixes := []string{
		"com.cn", "net.cn", "org.cn", "gov.cn", "edu.cn", "ac.cn",
		"co.uk", "org.uk", "me.uk", "ltd.uk", "plc.uk",
		"co.jp", "or.jp", "ne.jp", "ac.jp", "ad.jp", "ed.jp", "go.jp",
		"com.au", "net.au", "org.au", "edu.au", "gov.au",
		"com.tw", "net.tw", "org.tw", "edu.tw", "gov.tw",
		"com.hk", "net.hk", "org.hk", "edu.hk", "gov.hk",
		"com.sg", "net.sg", "org.sg", "edu.sg", "gov.sg",
		"co.in", "net.in", "org.in", "edu.in", "gov.in",
		"com.my", "net.my", "org.my", "edu.my", "gov.my",
		"co.th", "net.th", "org.th", "edu.th", "gov.th",
		"co.kr", "net.kr", "org.kr", "edu.kr", "gov.kr",
		"com.br", "net.br", "org.br", "edu.br", "gov.br",
		"co.za", "net.za", "org.za", "edu.za", "gov.za",
	}

	for _, suffix := range commonDoubleSuffixes {
		if lastTwo == suffix {
			return true
		}
	}

	// 检查是否是已知的单级公共后缀但有特殊规则的情况
	if len(parts) >= 3 {
		lastThree := strings.Join(parts[len(parts)-3:], ".")
		// 一些特殊的三级公共后缀
		specialTripleSuffixes := []string{
			"blogspot.com", "wordpress.com", "github.io",
		}

		for _, suffix := range specialTripleSuffixes {
			if lastThree == suffix {
				return true
			}
		}
	}

	return false
}
