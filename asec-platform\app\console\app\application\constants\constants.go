package constants

const (
	SwitchSpecialType        = "SWITCH"
	WebApplicationSpecialKey = "web_application"
	FalseSpecialValue        = "false"
	GatewayAppType           = 3
	AppExcel                 = "App.ExcelName.AppExcel"
	WEBExcelTemplate         = "App.ExcelName.WEBExcelTemplate"
	TUNExcelTemplate         = "App.ExcelName.TUNExcelTemplate"
	SdpInValidError          = "SdpInValidError"

	DefaultGroupId   = 1
	DefaultGroupName = "全部标签"

	DefaultUri   = "/*"
	AppGroupType = "GROUP"
	AppType      = "APP"
	WebAppType   = "web"
	TunAppType   = "tun"

	MaxDisplayLength = 20
	MoreThanName     = "等"
)

const (
	AppGroupNameDuplicateErr      = "AppGroupNameDuplicateErr"
	AppNotExistErr                = "AppNotExistErr"
	AppDomainInvalidErr           = "AppDomainInvalidErr"
	AppPublishAddressDuplicateErr = "AppPublishAddressDuplicateErr"
	MissingPort                   = "missing port in address"
)

const (
	AppName        = "APP_NAME"
	ServerAddress  = "SERVER_ADDRESS"
	PublishAddress = "PUBLISH_ADDRESS"
	Sdp            = "SDP"
)
