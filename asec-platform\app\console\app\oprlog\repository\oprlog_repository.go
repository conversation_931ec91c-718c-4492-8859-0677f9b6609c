package repository

import (
	"asdsec.com/asec/platform/app/console/app/oprlog/dto"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"context"
	"fmt"
	"go.uber.org/zap"
	"strconv"
	"time"
)

// OprlogRepository 接口定义
type OprlogRepository interface {
	GetById(ctx context.Context, id string) model.Oprlog
	DeleteById(ctx context.Context, id string)
	Create(ctx context.Context, oprlog model.Oprlog) (string, error)
	// ReadOprLogList 操作日志搜索接口
	OprLogList(ctx context.Context, req dto.ListOperateLogReq) (model.Pagination, error)
	// ClearLog 删除六个月之前的数据
	ClearLog(ctx context.Context) error
	CreateOptResource(ctx context.Context, req dto.OptResourceTypeReq) error
	GetOptResourceList(ctx context.Context) (dto.OptResourceTypeList, error)
}

// NewOprlogRepository 创建接口实现接口实现
func NewOprlogRepository() OprlogRepository {
	return &oprlogRepository{}
}

type oprlogRepository struct {
}

const (
	ApplicationType = 2
	optType         = 1
)

func (u *oprlogRepository) CreateOptResource(ctx context.Context, req dto.OptResourceTypeReq) error {
	id, err := snowflake.NewSnowFlake().GetId()
	if err != nil {
		return err
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	optResource := dto.OptResourceType{
		Id:           strconv.FormatUint(id, 10),
		Name:         req.Name,
		ResourceType: req.ResourceType,
		OptType:      req.OptType,
	}
	err = db.Create(optResource).Error
	return err
}

func (u *oprlogRepository) GetOptResourceList(ctx context.Context) (dto.OptResourceTypeList, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.OptResourceTypeList{}, err
	}
	var optTypes []dto.OptResourceType
	err = db.Model(dto.OptResourceType{}).Where("opt_type = ?", optType).Find(&optTypes).Error
	if err != nil {
		return dto.OptResourceTypeList{}, err
	}

	var appTypes []dto.OptResourceType
	err = db.Model(dto.OptResourceType{}).Where("opt_type = ?", ApplicationType).Find(&appTypes).Error
	return dto.OptResourceTypeList{OptTypes: optTypes, ApplicationTypes: appTypes}, err
}

func (u *oprlogRepository) DeleteById(ctx context.Context, id string) {
	oprlog := model.Oprlog{
		Id: id,
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return
	}
	db.Delete(&oprlog)
}

func (u *oprlogRepository) GetById(ctx context.Context, id string) model.Oprlog {
	var oprlog model.Oprlog
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return oprlog
	}
	db.Find(&oprlog, id)
	return oprlog
}

func (u *oprlogRepository) Create(ctx context.Context, oprlog model.Oprlog) (string, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return "", err
	}
	result := db.Create(&oprlog)
	return oprlog.Id, result.Error
}

func (u *oprlogRepository) OprLogList(ctx context.Context, req dto.ListOperateLogReq) (model.Pagination, error) {

	var res = req.Pagination
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return res, err
	}
	var logs []model.Oprlog
	oplog := model.Oprlog{}
	db = db.Model(&oplog).Where("ue.name is not null")
	if req.StartTimestamp > 0 {
		db = db.Where("admin_event_time >= ? ", req.StartTimestamp)
	}
	if req.EndTimestamp > 0 {
		db = db.Where("admin_event_time <= ? ", req.EndTimestamp)
	}
	if req.Search != "" {
		res.SearchColumns = []string{"resource_type", "operation_type", "representation", "user", "ip_address"}
	}
	if req.ResourceType != "" {
		db = db.Where("resource_type = ? ", req.ResourceType)
	} else {
		db = db.Where("resource_type in ? ", dto.DisResourceTypes)
	}
	res.Sort = "admin_event_time DESC"
	db = db.Select(fmt.Sprintf("%s.*,ue.name as username,to_timestamp(admin_event_time/1000) as operate_time", oplog.TableName())).
		Joins(fmt.Sprintf("left join tb_admin_entity ue on ue.id = %s.auth_user_id", oplog.TableName()))
	return model.Paginate(&logs, &res, db)
}

func (u *oprlogRepository) ClearLog(ctx context.Context) error {
	timeNow := time.Now()
	sixMouthAgo := timeNow.AddDate(0, -6, 0)
	global.SysLog.Info("清除日志，清除日志")
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	//做物理删除
	if err := db.Unscoped().Where("opr_time <= ?", sixMouthAgo.Unix()).Delete(model.Oprlog{}).Error; err != nil {
		global.SysLog.Error("删除操作失败失败", zap.Error(err))
		return err
	}
	return nil
}
