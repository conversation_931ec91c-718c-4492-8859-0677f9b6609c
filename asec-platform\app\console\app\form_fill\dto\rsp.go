package dto

// GetFormFillAccountResp 获取表单代填账户响应
type GetFormFillAccountResp struct {
	ErrCode     string `json:"errcode"`     // 错误码
	ErrMsg      string `json:"errmsg"`      // 错误消息
	Credentials string `json:"credentials"` // Base64编码的凭证(username:password)
}

// UpdateFormFillAccountResp 更新表单代填账户响应
type UpdateFormFillAccountResp struct {
	ErrCode string `json:"errcode"` // 错误码
	ErrMsg  string `json:"errmsg"`  // 错误消息
}
