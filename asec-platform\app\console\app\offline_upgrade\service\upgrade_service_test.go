package service

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"fmt"
	"io"
	"os"
	"testing"
)

func decryptFile(inputFile, outputFile, key string) error {
	// 打开输入文件（加密文件）
	inFile, err := os.Open(inputFile)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inFile.Close()

	// 创建输出文件（解密后内容的文件）
	outFile, err := os.Create(outputFile)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outFile.Close()

	// 转换密钥为字节数组，AES-256 要求密钥长度为 32 字节
	keyBytes := []byte(key)
	if len(keyBytes) != 32 {
		return fmt.Errorf("invalid key length: AES-256 requires 32-byte key")
	}

	// 创建 AES 块加密器
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return fmt.Errorf("failed to create AES cipher: %v", err)
	}

	// 初始化 GCM (Galois Counter Mode)，它是 AES 的一种加密模式
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %v", err)
	}

	// 读取 nonce (前面加密时写入文件的)
	nonce := make([]byte, aesGCM.NonceSize())
	if _, err = io.ReadFull(inFile, nonce); err != nil {
		return fmt.Errorf("failed to read nonce: %v", err)
	}

	// 读取加密数据
	ciphertext, err := io.ReadAll(inFile)
	if err != nil {
		return fmt.Errorf("failed to read ciphertext: %v", err)
	}

	// 解密数据
	plaintext, err := aesGCM.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt data: %v", err)
	}

	// 将解密后的内容写入输出文件
	if _, err = outFile.Write(plaintext); err != nil {
		return fmt.Errorf("failed to write plaintext: %v", err)
	}

	fmt.Println("File decryption successful")
	return nil
}
func encryptFile(inputFile, outputFile, key string) error {
	// 打开输入文件
	inFile, err := os.Open(inputFile)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inFile.Close()

	// 创建输出文件
	outFile, err := os.Create(outputFile)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outFile.Close()

	// 转换密钥为字节数组，AES-256 要求密钥长度为 32 字节
	keyBytes := []byte(key)
	if len(keyBytes) != 32 {
		return fmt.Errorf("invalid key length: AES-256 requires 32-byte key")
	}

	// 创建 AES 块加密器
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return fmt.Errorf("failed to create AES cipher: %v", err)
	}

	// 初始化 GCM (Galois Counter Mode)，它是 AES 的一种加密模式
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %v", err)
	}

	// 创建随机的 nonce (number used once)，长度必须为 aesGCM.NonceSize()
	nonce := make([]byte, aesGCM.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return fmt.Errorf("failed to generate nonce: %v", err)
	}

	// 将 nonce 写入输出文件，解密时需要使用相同的 nonce
	if _, err = outFile.Write(nonce); err != nil {
		return fmt.Errorf("failed to write nonce: %v", err)
	}

	// 读取输入文件内容并加密
	inputData, err := io.ReadAll(inFile)
	if err != nil {
		return fmt.Errorf("failed to read input file: %v", err)
	}

	// 加密文件内容
	ciphertext := aesGCM.Seal(nil, nonce, inputData, nil)

	// 将加密数据写入输出文件
	if _, err = outFile.Write(ciphertext); err != nil {
		return fmt.Errorf("failed to write ciphertext: %v", err)
	}

	fmt.Println("File encryption successful")
	return nil
}

func TestEncryptFile(t *testing.T) {
	inputFile := "D:\\Downloads\\ASec_Client_Setup_1.0.16.0 Build100.exe"
	outputFile := "encrypted.bin"
	key := "70769497eb30c61c29f9afc4fadd436d" // 32 字节长的密钥

	err := encryptFile(inputFile, outputFile, key)
	if err != nil {
		fmt.Printf("Error encrypting file: %v\n", err)
	}
}
func TestDecryptFile(t *testing.T) {
	inputFile := "encrypted.bin"
	outputFile := "decrypted.exe"
	key := "70769497eb30c61c29f9afc4fadd436d" // 32 字节长的密钥

	// 调用解密函数
	err := decryptFile(inputFile, outputFile, key)
	if err != nil {
		fmt.Printf("Error decrypting file: %v\n", err)
	}
}
