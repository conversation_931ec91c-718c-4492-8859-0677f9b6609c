package repository

import (
	"asdsec.com/asec/platform/app/console/app/focus/constants"
	"asdsec.com/asec/platform/app/console/app/focus/model"
	"asdsec.com/asec/platform/app/console/common"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"asdsec.com/asec/platform/pkg/aerrors"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/focus_model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"sort"
	"strconv"
	"time"
)

type focusRepository struct {
}

const (
	FocusEventType = "EVENT"
)

func (f focusRepository) GetFocusUserList(ctx context.Context, req model.GetFocusUserListReq) (model.GetFocusUserListRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return model.GetFocusUserListRsp{}, err
	}
	var selectSql = `tf."key" as user_id,uep.user_name,ust.score,ust.risk_level,uep.user_path as path,
	array_agg(distinct CAST(tut.tag_id AS text)) filter(where tut.tag_id is not null) as tag_ids,
	(array_agg(distinct jsonb_build_object('tag_id',CAST(tut.tag_id AS text),'tag_name',tutc."name",'created_at',tutc.created_at)) filter(where tut.tag_id is not null)) as tag_info
	`
	if req.Search != "" {
		searchStr := "%" + dbutil.EscapeForLike(req.Search) + "%"
		db = db.Where("(tutc.name like ? OR uep.user_name like ?)", searchStr, searchStr)
	}
	focusUsers := make([]model.FocusUserModel, 0)
	err = db.Table("tb_focus tf").Select(selectSql).
		Joins("left join tb_user_tag tut on tf.\"key\" = tut.user_id ").
		Joins("left join tb_user_tag_config tutc on tut.tag_id  = tutc.id ").
		Joins(fmt.Sprintf("left join (%s) ust on ust.user_id  = tf.\"key\" ", commonApi.UserRiskSql)).
		Joins(fmt.Sprintf("left join (%s) uep on uep.user_id = tf.\"key\"  ", commonApi.AllUserEchoSql)).
		Group("tf.key,tf.id ,ust.score,ust.risk_level,uep.user_path,uep.user_name").
		Where("tf.type = ? AND tf.status = ?", constants.FocusUserType, constants.FocusStatus).
		Find(&focusUsers).Error
	if err != nil {
		global.SysLog.Error("get focus user err", zap.Error(err))
		return model.GetFocusUserListRsp{}, err
	}

	data, err := structFocusUser(ctx, focusUsers)
	if err != nil {
		global.SysLog.Error("struct focus user err", zap.Error(err))
		return model.GetFocusUserListRsp{}, err
	}

	return model.GetFocusUserListRsp{
		Data: data,
	}, nil
}

func structFocusUser(ctx context.Context, data []model.FocusUserModel) ([]model.FocusUserItem, error) {
	var res []model.FocusUserItem
	tagMap := make(map[string][]model.FocusUser)
	tagNameMap := make(map[string]model.TagInfo)
	for _, v := range data {
		var user model.FocusUser
		err := copier.Copy(&user, &v)
		if err != nil {
			return nil, err
		}
		if user.Path == "" {
			user.Path = "-"
		}
		if user.UserName == "" {
			user.UserName, err = commonApi.GetHistoryUserName(ctx, user.UserId)
			// 历史用户未产生事件直接跳过
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					continue
				}
				return nil, err
			}
		}
		if len(v.TagInfo.Elements) == 0 {
			tagMap[constants.NoTagId] = append(tagMap[constants.NoTagId], user)
			continue
		}
		for _, tagInfo := range v.TagInfo.Elements {
			var tagItem model.TagInfo
			err = json.Unmarshal(tagInfo.Bytes, &tagItem)
			if err != nil {
				return nil, err
			}
			var users []model.FocusUser
			if items, ok := tagMap[tagItem.TagId]; ok {
				users = append(users, items...)
			}
			users = append(users, user)
			tagMap[tagItem.TagId] = users
			tagNameMap[tagItem.TagId] = tagItem
		}
	}
	for k, v := range tagMap {
		sort.Slice(v, func(i, j int) bool {
			return v[i].Score > v[j].Score
		})
		res = append(res, model.FocusUserItem{
			Users:   v,
			TagInfo: tagNameMap[k],
			Count:   len(v),
		})
	}
	return res, nil
}

func (f focusRepository) GetFocusList(ctx context.Context, req model.FocusOnReq) (model.GetFocusListRsp, error) {
	res := make([]focus_model.Focus, 0)
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return model.GetFocusListRsp{}, err
	}
	db = db.Model(focus_model.Focus{})
	if req.Type != "" {
		db = db.Where("type = ? AND status = ?", req.Type, constants.FocusStatus)
	}

	pageReq := modelTable.Pagination{Limit: req.Limit, Offset: req.Offset}
	page, err := modelTable.Paginate(&res, &pageReq, db)
	if err != nil {
		global.SysLog.Error("get res err", zap.Error(err))
		return model.GetFocusListRsp{}, err
	}
	return model.GetFocusListRsp{
		Data:       res,
		CommonPage: modelTable.CommonPage{PageSize: page.Limit, TotalNum: int(page.TotalRows), CurrentPage: page.Page}}, nil
}

func (f focusRepository) FocusOn(ctx context.Context, req model.FocusOnCommonReq) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return aerrors.NewWithError(err, common.OperateError)
	}
	if req.Status == constants.FocusStatus && req.Type != FocusEventType && len(req.UserTags) == 0 {
		return aerrors.New("no tags", constants.FocusOnNoTagsErr)
	}
	var focus focus_model.Focus
	err = copier.Copy(&focus, &req)
	if err != nil {
		global.SysLog.Error("copier err", zap.Error(err))
		return aerrors.NewWithError(err, common.OperateError)
	}
	focus.FocusTime = time.Now()
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	focus.Id = strconv.FormatUint(id, 10)
	err = db.Transaction(func(tx *gorm.DB) error {
		err = tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "key"}},
			DoUpdates: clause.AssignmentColumns([]string{"status", "focus_time"}),
		}).Create(&focus).Error
		if err != nil {
			return err
		}
		if req.Type == constants.FocusUserType {
			err = tx.Where("user_id = ?", req.Key).Delete(modelTable.UserTag{}).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			for _, tagId := range req.UserTags {
				userTagId, err := snowflake.Sf.GetId()
				if err != nil {
					return err
				}
				tagIdStr, err := strconv.ParseUint(tagId, 10, 64)
				if err != nil {
					return err
				}
				err = tx.Create(&modelTable.UserTag{Id: userTagId, UserId: req.Key, TagId: tagIdStr}).Error
				if err != nil {
					return err
				}
			}

		}
		return nil
	})
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

type FocusRepository interface {
	GetFocusList(ctx context.Context, req model.FocusOnReq) (model.GetFocusListRsp, error)
	FocusOn(ctx context.Context, req model.FocusOnCommonReq) aerrors.AError
	GetFocusUserList(ctx context.Context, req model.GetFocusUserListReq) (model.GetFocusUserListRsp, error)
}

func NewFocusRepository() FocusRepository {
	return focusRepository{}
}
