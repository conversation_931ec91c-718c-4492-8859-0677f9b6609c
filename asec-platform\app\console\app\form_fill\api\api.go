package api

import (
	"net/http"

	"asdsec.com/asec/platform/app/console/app/form_fill/dto"
	"asdsec.com/asec/platform/app/console/app/form_fill/service"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/app/console/utils/web"
	"github.com/gin-gonic/gin"
)

// GetFormFillAccount godoc
// @Summary 获取表单代填账户
// @Schemes
// @Description 获取表单代填账户凭证
// @Tags FormFill
// @Accept json
// @Produce json
// @Param app_id query string true "应用ID"
// @Success 200 {object} dto.GetFormFillAccountResp "success"
// @Router /v1/form-fill/account [get]
func GetFormFillAccount(c *gin.Context) {
	// 获取当前用户ID
	curUserId := web.GetCurrentUserId(c)
	if curUserId == "" {
		common.Fail(c, common.AuthFailedError)
		return
	}

	var req dto.GetFormFillAccountReq
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"errcode": "400", "errmsg": "请求参数错误"})
		return
	}
	// 调用服务层
	formFillService := service.GetFormFillService()
	resp, err := formFillService.GetFormFillAccount(c, req.AppID, curUserId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"errcode": "500", "errmsg": err.Error()})
		return
	}

	// 返回响应
	c.JSON(http.StatusOK, resp)
}

// UpdateFormFillAccount godoc
// @Summary 更新表单代填账户
// @Schemes
// @Description 更新表单代填账户（需要JWT认证）
// @Tags FormFill
// @Accept json
// @Produce json
// @Param req body dto.UpdateFormFillAccountReq true "更新参数"
// @Success 200 {object} common.Response{} "success"
// @Security ApiKeyAuth
// @Router /v1/form-fill/account [put]
func UpdateFormFillAccount(c *gin.Context) {
	// 获取当前用户ID和企业ID
	curUserId := web.GetCurrentUserId(c)
	if curUserId == "" {
		common.Fail(c, common.AuthFailedError)
		return
	}

	// 获取企业ID - 暂时使用固定值进行测试
	corpID := int64(1) // 使用固定的企业ID进行测试

	var req dto.UpdateFormFillAccountReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"errcode": "400", "errmsg": "请求参数错误: " + err.Error()})
		return
	}

	// 调用服务层
	formFillService := service.GetFormFillService()
	err := formFillService.UpdateFormFillAccount(c, req, curUserId, corpID)
	if err != nil {
		common.FailWithMessage(c, common.OperateFailCode, err.Error())
		return
	}

	common.Ok(c)
}
