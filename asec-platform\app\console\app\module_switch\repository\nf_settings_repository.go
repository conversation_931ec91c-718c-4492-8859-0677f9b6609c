package repository

import (
	v1 "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/console/app/system_control/process_rename/constants"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"github.com/golang/protobuf/proto"
	"gorm.io/gorm"
)

type NFSettingsRepository interface {
	UpdateNFSettings(ctx context.Context, req *v1.NetworkFilterSettings) error
}

// NewNFSettingsRepository 创建接口实现接口实现
func NewNFSettingsRepository() NFSettingsRepository {
	return &nfSettingsRepository{}
}

type nfSettingsRepository struct {
}

func (n nfSettingsRepository) UpdateNFSettings(ctx context.Context, req *v1.NetworkFilterSettings) error {
	confData, err := proto.Marshal(req)
	if err != nil {
		return err
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {

		changeReq := conf_center.ConfChangeReq{
			ConfBizId:       "1",
			ConfType:        constants.NFSettingsKey,
			ConfData:        confData,
			ConfGranularity: conf_center.GlobalConf,
			Tx:              tx,
			RedisCli:        global.SysRedisClient,
			ChangeType:      conf_center.UpdateConf,
		}

		existConf, err := conf_center.GetConfById(changeReq)
		if err != nil || existConf.ConfBizId == "" {
			changeReq.ChangeType = conf_center.AddConf
			err = conf_center.ConfChange(changeReq)
		} else {
			err = conf_center.ConfChange(changeReq)
		}

		if err != nil {
			return err
		}
		return nil
	})
}
