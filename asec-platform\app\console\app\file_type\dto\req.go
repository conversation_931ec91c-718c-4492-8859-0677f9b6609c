package dto

import "github.com/lib/pq"

type CreateFileTypeReq struct {
	Name        string         `json:"name"  binding:"required"`
	ParentId    int64          `json:"parent_id"`
	ParentCode  int64          `json:"parent_code"`
	Description string         `json:"description"`
	Suffix      pq.StringArray `json:"suffix"`
}

type UpdateFileTypeReq struct {
	Name        string         `json:"name" binding:"required"`
	Description string         `json:"description"`
	Suffix      pq.StringArray `json:"suffix"`
	Id          int64          `json:"id"`
	Code        int64          `json:"code"`
}

type DeleteFileTypeReq struct {
	Code int64  `json:"code"  binding:"required"`
	Name string `json:"name"`
}
