syntax = "proto3";

package api.auth.v1;

import "errors/errors.proto";

option go_package = "asdsec.com/asec/platform/api/auth/v1;v1";

enum ErrorReason {
  option (errors.default_code) = 200;

  // 为某个枚举单独设置错误码
  RECORD_NOT_FOUND = 0;
  NAME_CONFLICT = 1;
  SOURCE_TYPE_CONFLICT = 2;
  IDP_SOURCE_NUM_LIMIT = 3;
  ONLY_ROOT_GROUP_CAN_BIND_IDP = 4;
  MAIN_IDP_ONLY_BIND_ONE_SOURCE = 5;
  USER_ENTITY_UNIQUE_CONFLICT = 6;
  SOURCE_TYPE_NOT_SUPPORT = 7;
  IDP_SOURCE_TYPE_ERROR = 8;
  ONLY_MAIN_IDP_CAN_BIND = 9;
  USER_SOURCE_CONFLICT = 10;
  USER_GROUP_DUPLICATE = 11;
  USER_GROUP_CIRCLE = 12;
  GROUP_NOT_FOUND = 13;
  USER_GROUP_CONFLICT = 14;
  GROUP_NOT_SUPPORT_IDP = 15;
  AUTH_OBJECT_EMPTY = 16;
  CRED_ERROR = 17;
  LOGIN_ERROR = 18;
  AUTH_POLICY_ERROR = 19;
  TYPE_ERROR = 20;
  PARAM_ERROR = 21;

  TOKEN_EXPIRE = 22 [(errors.code) = 401];
  TOKEN_INVALID = 23 [(errors.code) = 401];
  TOKEN_PARSE_FAILED = 24 [(errors.code) = 401];

  PASS_WORD_NOT_CORRECT = 25;
  ROOT_SOURCE_ERROR = 26;
  DISABLE_ERROR = 27;
  DEFAULT_DATA_CONFLICT = 28;
  QIYEWX_TRUSTED_IP_ERROR = 29;
  QIYEWX_CONFIG_ERROR = 30;
  ACCOUNT_OR_PASSWORD_ERROR = 31;
  CACHE_ERROR = 32;
  EXPIRE_ERROR = 33;
  FIELD_MAP_CONFIG_ERROR = 34;
  SMS_CODE_INVALID_ERROR = 35;
  SMS_CODE_ERROR = 36;
  AUTH_CHAIN_FAILURE = 37;
  NOT_MAIN_AUTH = 38;
  NOT_PHONE = 39;
  FEISHU_SYNC_ERROR = 40;
  SEND_SMS_ERROR = 41;
  TOKEN_VERIFY = 42 [(errors.code) = 302];
  CODE_VERIFY_ERROR = 43 [(errors.code) = 302];
  PARAM_VERIFY_ERROR = 44 [(errors.code) = 400];
  OAUTH2_AUTHORIZE_ERROR = 45 [(errors.code) = 302];
  OAUTH2_CALLBACK_ERROR = 46 [(errors.code) = 302];
  USER_NOT_FOUND = 47 [(errors.code) = 200];

  EMAIL_NOT_EXISTS = 48;
  EMAIL_FORMAT_INVALID = 49;
  EMAIL_CODE_INVALID_ERROR = 50;
  EMAIL_CODE_ERROR = 51;
  EMAIL_SEND_ERROR = 52;
  EMAIL_AUTH_FAILURE = 53;

  AUTH_SERVER_CONFIG_ERROR = 55;    // 认证服务器配置错误
  AUTH_SERVER_CONNECT_ERROR = 56;   // 认证服务器连接错误
  NETWORK_CONNECTION_ERROR = 57;    // 网络连接错误
  AUTH_SEARCH_NOT_UNIQUE = 58;
  AUTH_USER_LOCK = 59;
  SECURITY_CODE_ERROR = 60;
}