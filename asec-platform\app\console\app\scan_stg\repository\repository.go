package repository

import (
	"asdsec.com/asec/platform/app/console/app/scan_stg/dto"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/scan_stg"
	"asdsec.com/asec/platform/pkg/snowflake"
	"context"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type ScanStgRepository interface {
	GetScanStgDetail(ctx context.Context, id string) (dto.GetScanStgDetailRsp, error)
	GetScanStgList(ctx context.Context, req dto.GetCommonListReq) (dto.GetScanStgListRsp, error)
	CreateScanStg(ctx context.Context, req dto.CreateScanStgReq) aerrors.AError
	UpdateScanStg(ctx context.Context, req dto.UpdateScanStgReq) aerrors.AError
	DeleteScanStg(ctx context.Context, id string) error
	FindScanStgByName(ctx context.Context, name string) (scan_stg.ScanStrategy, error)
}

// NewScanStgRepository 创建接口实现接口实现
func NewScanStgRepository() ScanStgRepository {
	return &scanStgRepository{}
}

type scanStgRepository struct {
}

func (s scanStgRepository) FindScanStgByName(ctx context.Context, name string) (scan_stg.ScanStrategy, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return scan_stg.ScanStrategy{}, err
	}
	var res scan_stg.ScanStrategy
	err = db.Model(scan_stg.ScanStrategy{}).Where("strategy_name = ?", name).Find(&res).Error
	if err != nil {
		return scan_stg.ScanStrategy{}, err
	}
	return res, nil
}

func (s scanStgRepository) GetScanStgDetail(ctx context.Context, id string) (dto.GetScanStgDetailRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetScanStgDetailRsp{}, err
	}
	var res dto.GetScanStgDetailRsp
	err = db.Model(scan_stg.ScanStrategy{}).
		Select("tb_scan_strategy.*,"+
			" COALESCE (array_agg(distinct tb_file_type.name)filter(where tb_file_type.name is not null)) AS file_type").
		Joins("LEFT JOIN tb_user_entity ON tb_user_entity.id = any(tb_scan_strategy.user_ids)").
		Joins("LEFT JOIN tb_user_group ON tb_user_group.id = any(tb_scan_strategy.user_group_ids)").
		Joins("LEFT JOIN tb_file_type ON tb_file_type.code = any(tb_scan_strategy.file_type_code)").
		Where("tb_scan_strategy.id = ?", id).Group("tb_scan_strategy.id").Find(&res).Error
	if err != nil {
		return dto.GetScanStgDetailRsp{}, err
	}
	return res, nil
}

func (s scanStgRepository) GetScanStgList(ctx context.Context, req dto.GetCommonListReq) (dto.GetScanStgListRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetScanStgListRsp{}, err
	}
	pageReq := model.Pagination{
		Limit:  req.Limit,
		Offset: req.Offset,
		Search: req.Search,
	}
	if req.Search != "" {
		pageReq.SearchColumns = []string{"strategy_name"}
	}
	db = db.Model(scan_stg.ScanStrategy{}).Select("tb_scan_strategy.id,strategy_name,strategy_detail,strategy_status,scan_position,enable_all_user," +
		" COALESCE (array_agg(distinct tb_file_type.name)filter(where tb_file_type.name is not null)) AS file_type," +
		" COALESCE (array_agg(distinct tb_user_entity.name)filter(where tb_user_entity.name is not null)) AS user_info," +
		" COALESCE (array_agg(distinct tb_user_group.name)filter(where tb_user_group.name is not null)) AS group_info").
		Joins("LEFT JOIN tb_user_entity ON tb_user_entity.id = any(tb_scan_strategy.user_ids)").
		Joins("LEFT JOIN tb_user_group ON tb_user_group.id = any(tb_scan_strategy.user_group_ids)").
		Joins("LEFT JOIN tb_file_type ON tb_file_type.code = any(tb_scan_strategy.file_type_code)").
		Group("tb_scan_strategy.id").Order("tb_scan_strategy.created_at desc")
	var stgList []dto.ScanStgListItem
	pageReq, err = model.Paginate(&stgList, &pageReq, db)
	if err != nil {
		return dto.GetScanStgListRsp{}, err
	}
	return dto.GetScanStgListRsp{
		CommonPage:   model.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page},
		ScanStrategy: stgList,
	}, nil
}

func (s scanStgRepository) CreateScanStg(ctx context.Context, req dto.CreateScanStgReq) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	id, _ := snowflake.Sf.GetId()
	var stg scan_stg.ScanStrategy
	err = copier.Copy(&stg, &req)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	stg.Id = strconv.FormatUint(id, 10)
	err = db.Transaction(func(tx *gorm.DB) error {
		return tx.Model(scan_stg.ScanStrategy{}).Create(&stg).Error
	})
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	return nil
}

func (s scanStgRepository) UpdateScanStg(ctx context.Context, req dto.UpdateScanStgReq) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	var stg scan_stg.ScanStrategy
	err = copier.Copy(&stg, &req)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(scan_stg.ScanStrategy{}).Where("id = ?", req.Id).
			Select("strategy_name", "strategy_status", "strategy_detail", "user_ids", "user_group_ids", "enable_all_user", "file_type_code",
				"min_file_size", "min_file_unit", "max_file_size", "max_file_unit", "scan_position", "scan_exclude_position", "scan_condition", "scan_file_count").
			Updates(&stg).Error
		if err != nil {
			return err
		}
		err = NewScanTaskRepository().DoDelTaskTx(tx, req.Id, time.Now().Format("2006-01-02"))
		if err != nil {
			return err
		}
		// 不同步新增任务，因为会导致固定超时
		//if req.StrategyStatus == constants.DefaultStatus {
		//	return NewScanTaskRepository().DoTaskByStgAndDate(ctx, tx, req.Id, time.Now().Format("2006-01-02"))
		//}
		return nil
	})
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	return nil
}

func (s scanStgRepository) DeleteScanStg(ctx context.Context, id string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {

		err = tx.Where("id = ?", id).Delete(scan_stg.ScanStrategy{}).Error
		if err != nil {
			return err
		}
		return NewScanTaskRepository().DoDelTaskTx(tx, id, "")
	})
}
