package api

import (
	"asdsec.com/asec/platform/app/console/app/file_type/dto"
	"asdsec.com/asec/platform/app/console/app/file_type/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateFileType godoc
// @Summary 新增文件类型
// @Schemes
// @Description 新增文件类型
// @Tags        FileType
// @Produce     application/json
// @Success     200
// @Router      /v1/file_type [POST]
// @success     200 {object} common.Response{} "response"
func CreateFileType(c *gin.Context) {
	var req dto.CreateFileTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetFileTypeService().CreateFileType(c, req)
	if err != nil {
		global.SysLog.Error("create file type err", zap.Error(err))
		common.FailAError(c, err)
		return
	}
	common.Ok(c)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorFileTYpe,
			OperationType:  common.OperateCreate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
}

// UpdateFileType godoc
// @Summary 修改文件类型
// @Schemes
// @Description 修改文件类型
// @Tags        FileType
// @Produce     application/json
// @Success     200
// @Router      /v1/file_type [PUT]
// @success     200 {object} common.Response{} "response"
func UpdateFileType(c *gin.Context) {
	var req dto.UpdateFileTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetFileTypeService().UpdateFileType(c, req)
	if err != nil {
		global.SysLog.Error("update file type err", zap.Error(err))
		common.FailAError(c, err)
		return
	}
	common.Ok(c)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorFileTYpe,
			OperationType:  common.OperateUpdate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
}

// DeleteFileType godoc
// @Summary 删除文件类型
// @Schemes
// @Description 删除文件类型
// @Tags        FileType
// @Produce     application/json
// @Success     200
// @Router      /v1/file_type [DELETE]
// @success     200 {object} common.Response{} "response"
func DeleteFileType(c *gin.Context) {
	var req dto.DeleteFileTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetFileTypeService().DeleteFileType(c, req)
	if err != nil {
		global.SysLog.Error("delete file type err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorFileTYpe,
			OperationType:  common.OperateDelete,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
}

// GetFileTypeList godoc
// @Summary 获取文件类型树结构
// @Schemes
// @Description 获取文件类型树结构
// @Tags        FileType
// @Produce     application/json
// @Success     200
// @Router      /v1/file_type/list
// @success     200 {object} common.Response{} "response"
func GetFileTypeList(c *gin.Context) {
	keyword := c.Query("keyword")
	tree, err := service.GetFileTypeService().GetFileTypeTree(c, keyword)
	if err != nil {
		global.SysLog.Error("get file type tree err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, tree)
}
