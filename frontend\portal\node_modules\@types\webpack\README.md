# Installation
> `npm install --save @types/webpack`

# Summary
This package contains type definitions for webpack (https://github.com/webpack/webpack).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack/v4.

### Additional Details
 * Last updated: Wed, 23 Oct 2024 03:36:41 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node), [@types/tapable](https://npmjs.com/package/@types/tapable), [@types/uglify-js](https://npmjs.com/package/@types/uglify-js), [@types/webpack-sources](https://npmjs.com/package/@types/webpack-sources), [anymatch](https://npmjs.com/package/anymatch), [source-map](https://npmjs.com/package/source-map)

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/tkqubo), [<PERSON>](https://github.com/bumbleblym), [<PERSON>](https://github.com/bcherny), [<PERSON>](https://github.com/tommytroylin), [Mohsen Azimi](https://github.com/mohsen1), [Jonathan Creamer](https://github.com/jcreamer898), [Alan Agius](https://github.com/alan-agius4), [Dennis George](https://github.com/dennispg), [Christophe Hurpeau](https://github.com/christophehurpeau), [ZSkycat](https://github.com/ZSkycat), [John Reilly](https://github.com/johnnyreilly), [Ryan Waskiewicz](https://github.com/rwaskiewicz), [Kyle Uehlein](https://github.com/kuehlein), [Grgur Grisogono](https://github.com/grgur), [Rubens Pinheiro Gonçalves Cavalcante](https://github.com/rubenspgcavalcante), [Anders Kaseorg](https://github.com/andersk), [Felix Haus](https://github.com/ofhouse), [Daniel Chin](https://github.com/danielthank), [Daiki Ihara](https://github.com/sasurau4), [Dion Shi](https://github.com/dionshihk), [Piotr Błażejewicz](https://github.com/peterblazejewicz), and [Michał Grzegorzewski](https://github.com/spamshaker).
