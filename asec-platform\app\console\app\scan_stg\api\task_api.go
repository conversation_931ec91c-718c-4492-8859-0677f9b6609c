package api

import (
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/app/scan_stg/dto"
	"asdsec.com/asec/platform/app/console/app/scan_stg/service"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/app/console/common/utils"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetTaskResult godoc
// @Summary 获取扫描任务列表
// @Schemes
// @Description 获取扫描任务列表
// @Tags        scan_strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/scan_strategy/result [POST]
// @success     200 {object} common.Response{} "ok"
func GetTaskResult(c *gin.Context) {
	var req dto.GetScanTaskListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.StartTime != "" && req.EndTime != "" {
		checkP := true
		req.StartT, req.EndT, checkP = utils.CheckParam(req.StartTime, req.EndTime)
		if !checkP {
			common.Fail(c, common.ParamInvalidError)
			return
		}
	}
	data, err := service.GetScanTaskService().GetScanTaskList(c, req)
	if err != nil {
		global.SysLog.Error("GetTaskResult err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetTaskResultDetail godoc
// @Summary 获取扫描任务详情
// @Schemes
// @Description 获取扫描任务详情
// @Tags        scan_strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/scan_strategy/result_detail [GET]
// @success     200 {object} common.Response{} "ok"
func GetTaskResultDetail(c *gin.Context) {
	id := c.Query("task_id")
	data, err := service.GetScanTaskService().GetTaskResultDetail(c, id)
	if err != nil {
		global.SysLog.Error("GetTaskResultDetail err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetTaskResultUser godoc
// @Summary 获取扫描任务详情列表
// @Schemes
// @Description 获取扫描任务详情列表
// @Tags        scan_strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/scan_strategy/result_user [POST]
// @success     200 {object} common.Response{} "ok"
func GetTaskResultUser(c *gin.Context) {
	var req dto.GetScanTaskUserReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetScanTaskService().GetTaskResultUser(c, req)
	if err != nil {
		global.SysLog.Error("GetTaskResultUser err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// StopScan godoc
// @Summary 停止扫描
// @Schemes
// @Description 停止扫描
// @Tags        scan_strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/scan_strategy/result_stop [PUT]
// @success     200 {object} common.Response{} "ok"
func StopScan(c *gin.Context) {
	var req dto.StopScanTaskReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetScanTaskService().StopScanByStg(c, req.StrategyId, req.TaskDate, req.TaskId)
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.ScanStrategyStopTYpe,
			OperationType:  common.OperateStop,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	if err != nil {
		global.SysLog.Error("StopScan err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}
