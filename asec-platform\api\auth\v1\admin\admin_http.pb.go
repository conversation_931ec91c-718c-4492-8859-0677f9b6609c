// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.1
// source: auth/v1/admin/admin.proto

package admin

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAdminCreateAuthPolicy = "/api.auth.v1.admin.Admin/CreateAuthPolicy"
const OperationAdminCreateCorp = "/api.auth.v1.admin.Admin/CreateCorp"
const OperationAdminCreateIDP = "/api.auth.v1.admin.Admin/CreateIDP"
const OperationAdminCreateRole = "/api.auth.v1.admin.Admin/CreateRole"
const OperationAdminCreateRootGroup = "/api.auth.v1.admin.Admin/CreateRootGroup"
const OperationAdminCreateUser = "/api.auth.v1.admin.Admin/CreateUser"
const OperationAdminCreateUserCustom = "/api.auth.v1.admin.Admin/CreateUserCustom"
const OperationAdminCreateUserGroup = "/api.auth.v1.admin.Admin/CreateUserGroup"
const OperationAdminCreateUserGroupCustom = "/api.auth.v1.admin.Admin/CreateUserGroupCustom"
const OperationAdminCreateUserSource = "/api.auth.v1.admin.Admin/CreateUserSource"
const OperationAdminDeleteAuthPolicy = "/api.auth.v1.admin.Admin/DeleteAuthPolicy"
const OperationAdminDeleteCorp = "/api.auth.v1.admin.Admin/DeleteCorp"
const OperationAdminDeleteIDP = "/api.auth.v1.admin.Admin/DeleteIDP"
const OperationAdminDeleteRole = "/api.auth.v1.admin.Admin/DeleteRole"
const OperationAdminDeleteRootGroup = "/api.auth.v1.admin.Admin/DeleteRootGroup"
const OperationAdminDeleteRootGroupCustom = "/api.auth.v1.admin.Admin/DeleteRootGroupCustom"
const OperationAdminDeleteUser = "/api.auth.v1.admin.Admin/DeleteUser"
const OperationAdminDeleteUserCustom = "/api.auth.v1.admin.Admin/DeleteUserCustom"
const OperationAdminDeleteUserSource = "/api.auth.v1.admin.Admin/DeleteUserSource"
const OperationAdminGetCorp = "/api.auth.v1.admin.Admin/GetCorp"
const OperationAdminGetFieldMap = "/api.auth.v1.admin.Admin/GetFieldMap"
const OperationAdminGetFieldOptions = "/api.auth.v1.admin.Admin/GetFieldOptions"
const OperationAdminGetIDPDetail = "/api.auth.v1.admin.Admin/GetIDPDetail"
const OperationAdminGetRootGroupDetail = "/api.auth.v1.admin.Admin/GetRootGroupDetail"
const OperationAdminGetRootGroupIdpList = "/api.auth.v1.admin.Admin/GetRootGroupIdpList"
const OperationAdminGetUserCount = "/api.auth.v1.admin.Admin/GetUserCount"
const OperationAdminGetUserSource = "/api.auth.v1.admin.Admin/GetUserSource"
const OperationAdminIdleAccountList = "/api.auth.v1.admin.Admin/IdleAccountList"
const OperationAdminListAuthPolicy = "/api.auth.v1.admin.Admin/ListAuthPolicy"
const OperationAdminListCorp = "/api.auth.v1.admin.Admin/ListCorp"
const OperationAdminListIDP = "/api.auth.v1.admin.Admin/ListIDP"
const OperationAdminListIDPType = "/api.auth.v1.admin.Admin/ListIDPType"
const OperationAdminListRole = "/api.auth.v1.admin.Admin/ListRole"
const OperationAdminListRootGroup = "/api.auth.v1.admin.Admin/ListRootGroup"
const OperationAdminListSyncLog = "/api.auth.v1.admin.Admin/ListSyncLog"
const OperationAdminListUser = "/api.auth.v1.admin.Admin/ListUser"
const OperationAdminListUserGroup = "/api.auth.v1.admin.Admin/ListUserGroup"
const OperationAdminListUserSource = "/api.auth.v1.admin.Admin/ListUserSource"
const OperationAdminListUserSourceType = "/api.auth.v1.admin.Admin/ListUserSourceType"
const OperationAdminOAuth2Test = "/api.auth.v1.admin.Admin/OAuth2Test"
const OperationAdminSwitchAutoSync = "/api.auth.v1.admin.Admin/SwitchAutoSync"
const OperationAdminSyncTrigger = "/api.auth.v1.admin.Admin/SyncTrigger"
const OperationAdminTestResult = "/api.auth.v1.admin.Admin/TestResult"
const OperationAdminTotpUnbind = "/api.auth.v1.admin.Admin/TotpUnbind"
const OperationAdminUpdateAuthPolicy = "/api.auth.v1.admin.Admin/UpdateAuthPolicy"
const OperationAdminUpdateCorp = "/api.auth.v1.admin.Admin/UpdateCorp"
const OperationAdminUpdateIDP = "/api.auth.v1.admin.Admin/UpdateIDP"
const OperationAdminUpdateIdleTime = "/api.auth.v1.admin.Admin/UpdateIdleTime"
const OperationAdminUpdateLockStatus = "/api.auth.v1.admin.Admin/UpdateLockStatus"
const OperationAdminUpdateRole = "/api.auth.v1.admin.Admin/UpdateRole"
const OperationAdminUpdateRootGroup = "/api.auth.v1.admin.Admin/UpdateRootGroup"
const OperationAdminUpdateRootGroupCustom = "/api.auth.v1.admin.Admin/UpdateRootGroupCustom"
const OperationAdminUpdateUser = "/api.auth.v1.admin.Admin/UpdateUser"
const OperationAdminUpdateUserCustom = "/api.auth.v1.admin.Admin/UpdateUserCustom"
const OperationAdminUpdateUserGroup = "/api.auth.v1.admin.Admin/UpdateUserGroup"
const OperationAdminUpdateUserSource = "/api.auth.v1.admin.Admin/UpdateUserSource"
const OperationAdminValidateWebAuthScript = "/api.auth.v1.admin.Admin/ValidateWebAuthScript"

type AdminHTTPServer interface {
	CreateAuthPolicy(context.Context, *CreateAuthPolicyRequest) (*CreateAuthPolicyReply, error)
	CreateCorp(context.Context, *CreateCorpRequest) (*CreateCorpReply, error)
	CreateIDP(context.Context, *CreateIDPRequest) (*CreateIDPReply, error)
	CreateRole(context.Context, *CreateRoleRequest) (*CreateRoleReply, error)
	CreateRootGroup(context.Context, *CreateRootGroupRequest) (*CreateRootGroupReply, error)
	CreateUser(context.Context, *CreateUserRequest) (*CreateUserReply, error)
	CreateUserCustom(context.Context, *CreateUserRequest) (*CreateUserReply, error)
	CreateUserGroup(context.Context, *CreateUserGroupRequest) (*CreateUserGroupReply, error)
	CreateUserGroupCustom(context.Context, *CreateUserGroupRequestCustom) (*CreateUserGroupReply, error)
	CreateUserSource(context.Context, *CreateUserSourceRequest) (*CreateUserSourceReply, error)
	DeleteAuthPolicy(context.Context, *DeleteAuthPolicyRequest) (*DeleteAuthPolicyReply, error)
	DeleteCorp(context.Context, *DeleteCorpRequest) (*DeleteCorpReply, error)
	DeleteIDP(context.Context, *DeleteIDPRequest) (*DeleteIDPReply, error)
	DeleteRole(context.Context, *DeleteRoleRequest) (*DeleteRoleReply, error)
	DeleteRootGroup(context.Context, *DeleteRootGroupRequest) (*DeleteRootGroupReply, error)
	DeleteRootGroupCustom(context.Context, *DeleteRootGroupRequest) (*DeleteRootGroupReply, error)
	DeleteUser(context.Context, *DeleteUserRequest) (*DeleteUserReply, error)
	DeleteUserCustom(context.Context, *CustomDeleteUserRequest) (*DeleteUserReply, error)
	DeleteUserSource(context.Context, *DeleteUserSourceRequest) (*DeleteUserSourceReply, error)
	GetCorp(context.Context, *GetCorpRequest) (*GetCorpReply, error)
	GetFieldMap(context.Context, *GetFieldMapRequest) (*GetFieldMapReply, error)
	GetFieldOptions(context.Context, *GetFieldOptionsRequest) (*GetFieldOptionsReply, error)
	GetIDPDetail(context.Context, *GetIDPDetailRequest) (*GetIDPDetailReply, error)
	GetRootGroupDetail(context.Context, *GetRootGroupDetailRequest) (*GetRootGroupDetailReply, error)
	GetRootGroupIdpList(context.Context, *GetRootGroupIdpListRequest) (*GetRootGroupIdpListReply, error)
	GetUserCount(context.Context, *GetUserCountRequest) (*GetUserCountReply, error)
	GetUserSource(context.Context, *GetUserSourceRequest) (*GetUserSourceReply, error)
	// IdleAccountList 闲置账号列表
	IdleAccountList(context.Context, *IdleAccountRequest) (*IdleAccountReply, error)
	ListAuthPolicy(context.Context, *ListAuthPolicyRequest) (*ListAuthPolicyReply, error)
	ListCorp(context.Context, *ListCorpRequest) (*ListCorpReply, error)
	ListIDP(context.Context, *ListIDPRequest) (*ListIDPReply, error)
	ListIDPType(context.Context, *ListIDPTypeRequest) (*ListIDPTypeReply, error)
	ListRole(context.Context, *ListRoleRequest) (*ListRoleReply, error)
	ListRootGroup(context.Context, *ListRootGroupRequest) (*ListRootGroupReply, error)
	ListSyncLog(context.Context, *ListSyncLogRequest) (*ListSyncLogReply, error)
	ListUser(context.Context, *ListUserRequest) (*ListUserReply, error)
	ListUserGroup(context.Context, *ListUserGroupRequest) (*ListUserGroupReply, error)
	ListUserSource(context.Context, *ListUserSourceRequest) (*ListUserSourceReply, error)
	ListUserSourceType(context.Context, *ListUserSourceTypeRequest) (*ListUserSourceTypeReply, error)
	OAuth2Test(context.Context, *OAuth2TestRequest) (*OAuth2TestReply, error)
	SwitchAutoSync(context.Context, *SwitchAutoSyncRequest) (*SwitchAutoSyncReply, error)
	SyncTrigger(context.Context, *SyncTriggerRequest) (*SyncTriggerReply, error)
	TestResult(context.Context, *TestResultRequest) (*TestResultReply, error)
	TotpUnbind(context.Context, *TotpUnbindRequest) (*TotpUnbindTimeReply, error)
	UpdateAuthPolicy(context.Context, *UpdateAuthPolicyRequest) (*UpdateAuthPolicyReply, error)
	UpdateCorp(context.Context, *UpdateCorpRequest) (*UpdateCorpReply, error)
	UpdateIDP(context.Context, *UpdateIDPRequest) (*UpdateIDPReply, error)
	UpdateIdleTime(context.Context, *UpdateIdleTimeRequest) (*UpdateIdleTimeReply, error)
	UpdateLockStatus(context.Context, *UpdateLockStatusRequest) (*UpdateLockStatusReply, error)
	UpdateRole(context.Context, *UpdateRoleRequest) (*UpdateRoleReply, error)
	UpdateRootGroup(context.Context, *UpdateRootGroupRequest) (*UpdateRootGroupReply, error)
	UpdateRootGroupCustom(context.Context, *UpdateRootGroupRequest) (*UpdateRootGroupReply, error)
	UpdateUser(context.Context, *UpdateUserRequest) (*UpdateUserReply, error)
	UpdateUserCustom(context.Context, *CustomUpdateUserRequest) (*UpdateUserReply, error)
	UpdateUserGroup(context.Context, *UpdateUserGroupRequest) (*UpdateUserGroupReply, error)
	UpdateUserSource(context.Context, *UpdateUserSourceRequest) (*UpdateUserSourceReply, error)
	ValidateWebAuthScript(context.Context, *ValidateWebAuthScriptRequest) (*ValidateWebAuthScriptReply, error)
}

func RegisterAdminHTTPServer(s *http.Server, srv AdminHTTPServer) {
	r := s.Route("/")
	r.POST("/auth/admin/v1/corp", _Admin_CreateCorp0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/corp", _Admin_UpdateCorp0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/lock_status", _Admin_UpdateLockStatus0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/idle_time", _Admin_UpdateIdleTime0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/totp_unbind", _Admin_TotpUnbind0_HTTP_Handler(srv))
	r.DELETE("/auth/admin/v1/corp", _Admin_DeleteCorp0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/corp", _Admin_GetCorp0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/corp/list", _Admin_ListCorp0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/user_source", _Admin_CreateUserSource0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/user_source", _Admin_UpdateUserSource0_HTTP_Handler(srv))
	r.DELETE("/auth/admin/v1/user_source", _Admin_DeleteUserSource0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/user_source", _Admin_GetUserSource0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/user_source/list", _Admin_ListUserSource0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/user_source/type/list", _Admin_ListUserSourceType0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/idp_type/list", _Admin_ListIDPType0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/idp/oauth2_test", _Admin_OAuth2Test0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/idp/test_result/{test_id}", _Admin_TestResult0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/idp", _Admin_CreateIDP0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/idp", _Admin_UpdateIDP0_HTTP_Handler(srv))
	r.DELETE("/auth/admin/v1/idp", _Admin_DeleteIDP0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/idp/list", _Admin_ListIDP0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/idp/detail", _Admin_GetIDPDetail0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/root_group/list", _Admin_ListRootGroup0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/root_group/detail", _Admin_GetRootGroupDetail0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/root_group", _Admin_CreateRootGroup0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/root_group/idp_list", _Admin_GetRootGroupIdpList0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/field_map", _Admin_GetFieldMap0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/field_option", _Admin_GetFieldOptions0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/root_group", _Admin_UpdateRootGroup0_HTTP_Handler(srv))
	r.PUT("/auth/openapi/v1/group", _Admin_UpdateRootGroupCustom0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/root_group/auto_sync", _Admin_SwitchAutoSync0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/root_group/sync_trigger", _Admin_SyncTrigger0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/root_group/sync_log", _Admin_ListSyncLog0_HTTP_Handler(srv))
	r.DELETE("/auth/admin/v1/root_group", _Admin_DeleteRootGroup0_HTTP_Handler(srv))
	r.DELETE("/auth/openapi/v1/group", _Admin_DeleteRootGroupCustom0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/user_group/list", _Admin_ListUserGroup0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/user_group", _Admin_CreateUserGroup0_HTTP_Handler(srv))
	r.POST("/auth/openapi/v1/group", _Admin_CreateUserGroupCustom0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/user_group", _Admin_UpdateUserGroup0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/user", _Admin_CreateUser0_HTTP_Handler(srv))
	r.POST("/auth/openapi/v1/user", _Admin_CreateUserCustom0_HTTP_Handler(srv))
	r.DELETE("/auth/openapi/v1/user", _Admin_DeleteUserCustom0_HTTP_Handler(srv))
	r.PUT("/auth/openapi/v1/user", _Admin_UpdateUserCustom0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/user/list", _Admin_ListUser0_HTTP_Handler(srv))
	r.DELETE("/auth/admin/v1/user", _Admin_DeleteUser0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/user", _Admin_UpdateUser0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/role", _Admin_CreateRole0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/role/list", _Admin_ListRole0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/idle_account/list", _Admin_IdleAccountList0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/role", _Admin_UpdateRole0_HTTP_Handler(srv))
	r.DELETE("/auth/admin/v1/role", _Admin_DeleteRole0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/auth_policy", _Admin_CreateAuthPolicy0_HTTP_Handler(srv))
	r.PUT("/auth/admin/v1/auth_policy", _Admin_UpdateAuthPolicy0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/auth_policy/list", _Admin_ListAuthPolicy0_HTTP_Handler(srv))
	r.DELETE("/auth/admin/v1/auth_policy", _Admin_DeleteAuthPolicy0_HTTP_Handler(srv))
	r.POST("/auth/admin/v1/idp/validate_script", _Admin_ValidateWebAuthScript0_HTTP_Handler(srv))
	r.GET("/auth/admin/v1/user_count", _Admin_GetUserCount0_HTTP_Handler(srv))
}

func _Admin_CreateCorp0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCorpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateCorp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCorp(ctx, req.(*CreateCorpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateCorpReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateCorp0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateCorpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateCorp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateCorp(ctx, req.(*UpdateCorpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateCorpReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateLockStatus0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateLockStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateLockStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateLockStatus(ctx, req.(*UpdateLockStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateLockStatusReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateIdleTime0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateIdleTimeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateIdleTime)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateIdleTime(ctx, req.(*UpdateIdleTimeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateIdleTimeReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_TotpUnbind0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TotpUnbindRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminTotpUnbind)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TotpUnbind(ctx, req.(*TotpUnbindRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TotpUnbindTimeReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteCorp0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteCorpRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteCorp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteCorp(ctx, req.(*DeleteCorpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteCorpReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_GetCorp0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCorpRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminGetCorp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCorp(ctx, req.(*GetCorpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCorpReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListCorp0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCorpRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListCorp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCorp(ctx, req.(*ListCorpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCorpReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateUserSource0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserSourceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateUserSource)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserSource(ctx, req.(*CreateUserSourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserSourceReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateUserSource0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserSourceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateUserSource)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserSource(ctx, req.(*UpdateUserSourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateUserSourceReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteUserSource0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserSourceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteUserSource)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserSource(ctx, req.(*DeleteUserSourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteUserSourceReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_GetUserSource0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserSourceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminGetUserSource)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserSource(ctx, req.(*GetUserSourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserSourceReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListUserSource0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserSourceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListUserSource)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserSource(ctx, req.(*ListUserSourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserSourceReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListUserSourceType0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserSourceTypeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListUserSourceType)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserSourceType(ctx, req.(*ListUserSourceTypeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserSourceTypeReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListIDPType0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListIDPTypeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListIDPType)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListIDPType(ctx, req.(*ListIDPTypeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListIDPTypeReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_OAuth2Test0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OAuth2TestRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminOAuth2Test)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OAuth2Test(ctx, req.(*OAuth2TestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OAuth2TestReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_TestResult0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TestResultRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminTestResult)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TestResult(ctx, req.(*TestResultRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TestResultReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateIDP0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateIDPRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateIDP)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateIDP(ctx, req.(*CreateIDPRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateIDPReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateIDP0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateIDPRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateIDP)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateIDP(ctx, req.(*UpdateIDPRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateIDPReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteIDP0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteIDPRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteIDP)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteIDP(ctx, req.(*DeleteIDPRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteIDPReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListIDP0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListIDPRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListIDP)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListIDP(ctx, req.(*ListIDPRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListIDPReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_GetIDPDetail0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetIDPDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminGetIDPDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetIDPDetail(ctx, req.(*GetIDPDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetIDPDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListRootGroup0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRootGroupRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListRootGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListRootGroup(ctx, req.(*ListRootGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListRootGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_GetRootGroupDetail0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRootGroupDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminGetRootGroupDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRootGroupDetail(ctx, req.(*GetRootGroupDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRootGroupDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateRootGroup0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateRootGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateRootGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateRootGroup(ctx, req.(*CreateRootGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateRootGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_GetRootGroupIdpList0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRootGroupIdpListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminGetRootGroupIdpList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRootGroupIdpList(ctx, req.(*GetRootGroupIdpListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRootGroupIdpListReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_GetFieldMap0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFieldMapRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminGetFieldMap)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFieldMap(ctx, req.(*GetFieldMapRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetFieldMapReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_GetFieldOptions0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFieldOptionsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminGetFieldOptions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFieldOptions(ctx, req.(*GetFieldOptionsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetFieldOptionsReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateRootGroup0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRootGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateRootGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateRootGroup(ctx, req.(*UpdateRootGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateRootGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateRootGroupCustom0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRootGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateRootGroupCustom)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateRootGroupCustom(ctx, req.(*UpdateRootGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateRootGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_SwitchAutoSync0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SwitchAutoSyncRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminSwitchAutoSync)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SwitchAutoSync(ctx, req.(*SwitchAutoSyncRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SwitchAutoSyncReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_SyncTrigger0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncTriggerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminSyncTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SyncTrigger(ctx, req.(*SyncTriggerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncTriggerReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListSyncLog0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSyncLogRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListSyncLog)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSyncLog(ctx, req.(*ListSyncLogRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSyncLogReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteRootGroup0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteRootGroupRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteRootGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteRootGroup(ctx, req.(*DeleteRootGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteRootGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteRootGroupCustom0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteRootGroupRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteRootGroupCustom)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteRootGroupCustom(ctx, req.(*DeleteRootGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteRootGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListUserGroup0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserGroupRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListUserGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserGroup(ctx, req.(*ListUserGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateUserGroup0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateUserGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserGroup(ctx, req.(*CreateUserGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateUserGroupCustom0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserGroupRequestCustom
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateUserGroupCustom)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserGroupCustom(ctx, req.(*CreateUserGroupRequestCustom))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateUserGroup0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateUserGroup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserGroup(ctx, req.(*UpdateUserGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateUserGroupReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateUser0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUser(ctx, req.(*CreateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateUserCustom0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateUserCustom)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserCustom(ctx, req.(*CreateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteUserCustom0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CustomDeleteUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteUserCustom)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserCustom(ctx, req.(*CustomDeleteUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteUserReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateUserCustom0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CustomUpdateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateUserCustom)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserCustom(ctx, req.(*CustomUpdateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateUserReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListUser0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUser(ctx, req.(*ListUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteUser0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUser(ctx, req.(*DeleteUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteUserReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateUser0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUser(ctx, req.(*UpdateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateUserReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateRole0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateRole(ctx, req.(*CreateRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListRole0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListRole(ctx, req.(*ListRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_IdleAccountList0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IdleAccountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminIdleAccountList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IdleAccountList(ctx, req.(*IdleAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IdleAccountReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateRole0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateRole(ctx, req.(*UpdateRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteRole0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteRole(ctx, req.(*DeleteRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_CreateAuthPolicy0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAuthPolicyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminCreateAuthPolicy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAuthPolicy(ctx, req.(*CreateAuthPolicyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateAuthPolicyReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_UpdateAuthPolicy0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAuthPolicyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminUpdateAuthPolicy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAuthPolicy(ctx, req.(*UpdateAuthPolicyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateAuthPolicyReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ListAuthPolicy0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListAuthPolicyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminListAuthPolicy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAuthPolicy(ctx, req.(*ListAuthPolicyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAuthPolicyReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteAuthPolicy0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAuthPolicyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDeleteAuthPolicy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAuthPolicy(ctx, req.(*DeleteAuthPolicyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteAuthPolicyReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ValidateWebAuthScript0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ValidateWebAuthScriptRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminValidateWebAuthScript)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ValidateWebAuthScript(ctx, req.(*ValidateWebAuthScriptRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ValidateWebAuthScriptReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_GetUserCount0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminGetUserCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserCount(ctx, req.(*GetUserCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserCountReply)
		return ctx.Result(200, reply)
	}
}

type AdminHTTPClient interface {
	CreateAuthPolicy(ctx context.Context, req *CreateAuthPolicyRequest, opts ...http.CallOption) (rsp *CreateAuthPolicyReply, err error)
	CreateCorp(ctx context.Context, req *CreateCorpRequest, opts ...http.CallOption) (rsp *CreateCorpReply, err error)
	CreateIDP(ctx context.Context, req *CreateIDPRequest, opts ...http.CallOption) (rsp *CreateIDPReply, err error)
	CreateRole(ctx context.Context, req *CreateRoleRequest, opts ...http.CallOption) (rsp *CreateRoleReply, err error)
	CreateRootGroup(ctx context.Context, req *CreateRootGroupRequest, opts ...http.CallOption) (rsp *CreateRootGroupReply, err error)
	CreateUser(ctx context.Context, req *CreateUserRequest, opts ...http.CallOption) (rsp *CreateUserReply, err error)
	CreateUserCustom(ctx context.Context, req *CreateUserRequest, opts ...http.CallOption) (rsp *CreateUserReply, err error)
	CreateUserGroup(ctx context.Context, req *CreateUserGroupRequest, opts ...http.CallOption) (rsp *CreateUserGroupReply, err error)
	CreateUserGroupCustom(ctx context.Context, req *CreateUserGroupRequestCustom, opts ...http.CallOption) (rsp *CreateUserGroupReply, err error)
	CreateUserSource(ctx context.Context, req *CreateUserSourceRequest, opts ...http.CallOption) (rsp *CreateUserSourceReply, err error)
	DeleteAuthPolicy(ctx context.Context, req *DeleteAuthPolicyRequest, opts ...http.CallOption) (rsp *DeleteAuthPolicyReply, err error)
	DeleteCorp(ctx context.Context, req *DeleteCorpRequest, opts ...http.CallOption) (rsp *DeleteCorpReply, err error)
	DeleteIDP(ctx context.Context, req *DeleteIDPRequest, opts ...http.CallOption) (rsp *DeleteIDPReply, err error)
	DeleteRole(ctx context.Context, req *DeleteRoleRequest, opts ...http.CallOption) (rsp *DeleteRoleReply, err error)
	DeleteRootGroup(ctx context.Context, req *DeleteRootGroupRequest, opts ...http.CallOption) (rsp *DeleteRootGroupReply, err error)
	DeleteRootGroupCustom(ctx context.Context, req *DeleteRootGroupRequest, opts ...http.CallOption) (rsp *DeleteRootGroupReply, err error)
	DeleteUser(ctx context.Context, req *DeleteUserRequest, opts ...http.CallOption) (rsp *DeleteUserReply, err error)
	DeleteUserCustom(ctx context.Context, req *CustomDeleteUserRequest, opts ...http.CallOption) (rsp *DeleteUserReply, err error)
	DeleteUserSource(ctx context.Context, req *DeleteUserSourceRequest, opts ...http.CallOption) (rsp *DeleteUserSourceReply, err error)
	GetCorp(ctx context.Context, req *GetCorpRequest, opts ...http.CallOption) (rsp *GetCorpReply, err error)
	GetFieldMap(ctx context.Context, req *GetFieldMapRequest, opts ...http.CallOption) (rsp *GetFieldMapReply, err error)
	GetFieldOptions(ctx context.Context, req *GetFieldOptionsRequest, opts ...http.CallOption) (rsp *GetFieldOptionsReply, err error)
	GetIDPDetail(ctx context.Context, req *GetIDPDetailRequest, opts ...http.CallOption) (rsp *GetIDPDetailReply, err error)
	GetRootGroupDetail(ctx context.Context, req *GetRootGroupDetailRequest, opts ...http.CallOption) (rsp *GetRootGroupDetailReply, err error)
	GetRootGroupIdpList(ctx context.Context, req *GetRootGroupIdpListRequest, opts ...http.CallOption) (rsp *GetRootGroupIdpListReply, err error)
	GetUserCount(ctx context.Context, req *GetUserCountRequest, opts ...http.CallOption) (rsp *GetUserCountReply, err error)
	GetUserSource(ctx context.Context, req *GetUserSourceRequest, opts ...http.CallOption) (rsp *GetUserSourceReply, err error)
	IdleAccountList(ctx context.Context, req *IdleAccountRequest, opts ...http.CallOption) (rsp *IdleAccountReply, err error)
	ListAuthPolicy(ctx context.Context, req *ListAuthPolicyRequest, opts ...http.CallOption) (rsp *ListAuthPolicyReply, err error)
	ListCorp(ctx context.Context, req *ListCorpRequest, opts ...http.CallOption) (rsp *ListCorpReply, err error)
	ListIDP(ctx context.Context, req *ListIDPRequest, opts ...http.CallOption) (rsp *ListIDPReply, err error)
	ListIDPType(ctx context.Context, req *ListIDPTypeRequest, opts ...http.CallOption) (rsp *ListIDPTypeReply, err error)
	ListRole(ctx context.Context, req *ListRoleRequest, opts ...http.CallOption) (rsp *ListRoleReply, err error)
	ListRootGroup(ctx context.Context, req *ListRootGroupRequest, opts ...http.CallOption) (rsp *ListRootGroupReply, err error)
	ListSyncLog(ctx context.Context, req *ListSyncLogRequest, opts ...http.CallOption) (rsp *ListSyncLogReply, err error)
	ListUser(ctx context.Context, req *ListUserRequest, opts ...http.CallOption) (rsp *ListUserReply, err error)
	ListUserGroup(ctx context.Context, req *ListUserGroupRequest, opts ...http.CallOption) (rsp *ListUserGroupReply, err error)
	ListUserSource(ctx context.Context, req *ListUserSourceRequest, opts ...http.CallOption) (rsp *ListUserSourceReply, err error)
	ListUserSourceType(ctx context.Context, req *ListUserSourceTypeRequest, opts ...http.CallOption) (rsp *ListUserSourceTypeReply, err error)
	OAuth2Test(ctx context.Context, req *OAuth2TestRequest, opts ...http.CallOption) (rsp *OAuth2TestReply, err error)
	SwitchAutoSync(ctx context.Context, req *SwitchAutoSyncRequest, opts ...http.CallOption) (rsp *SwitchAutoSyncReply, err error)
	SyncTrigger(ctx context.Context, req *SyncTriggerRequest, opts ...http.CallOption) (rsp *SyncTriggerReply, err error)
	TestResult(ctx context.Context, req *TestResultRequest, opts ...http.CallOption) (rsp *TestResultReply, err error)
	TotpUnbind(ctx context.Context, req *TotpUnbindRequest, opts ...http.CallOption) (rsp *TotpUnbindTimeReply, err error)
	UpdateAuthPolicy(ctx context.Context, req *UpdateAuthPolicyRequest, opts ...http.CallOption) (rsp *UpdateAuthPolicyReply, err error)
	UpdateCorp(ctx context.Context, req *UpdateCorpRequest, opts ...http.CallOption) (rsp *UpdateCorpReply, err error)
	UpdateIDP(ctx context.Context, req *UpdateIDPRequest, opts ...http.CallOption) (rsp *UpdateIDPReply, err error)
	UpdateIdleTime(ctx context.Context, req *UpdateIdleTimeRequest, opts ...http.CallOption) (rsp *UpdateIdleTimeReply, err error)
	UpdateLockStatus(ctx context.Context, req *UpdateLockStatusRequest, opts ...http.CallOption) (rsp *UpdateLockStatusReply, err error)
	UpdateRole(ctx context.Context, req *UpdateRoleRequest, opts ...http.CallOption) (rsp *UpdateRoleReply, err error)
	UpdateRootGroup(ctx context.Context, req *UpdateRootGroupRequest, opts ...http.CallOption) (rsp *UpdateRootGroupReply, err error)
	UpdateRootGroupCustom(ctx context.Context, req *UpdateRootGroupRequest, opts ...http.CallOption) (rsp *UpdateRootGroupReply, err error)
	UpdateUser(ctx context.Context, req *UpdateUserRequest, opts ...http.CallOption) (rsp *UpdateUserReply, err error)
	UpdateUserCustom(ctx context.Context, req *CustomUpdateUserRequest, opts ...http.CallOption) (rsp *UpdateUserReply, err error)
	UpdateUserGroup(ctx context.Context, req *UpdateUserGroupRequest, opts ...http.CallOption) (rsp *UpdateUserGroupReply, err error)
	UpdateUserSource(ctx context.Context, req *UpdateUserSourceRequest, opts ...http.CallOption) (rsp *UpdateUserSourceReply, err error)
	ValidateWebAuthScript(ctx context.Context, req *ValidateWebAuthScriptRequest, opts ...http.CallOption) (rsp *ValidateWebAuthScriptReply, err error)
}

type AdminHTTPClientImpl struct {
	cc *http.Client
}

func NewAdminHTTPClient(client *http.Client) AdminHTTPClient {
	return &AdminHTTPClientImpl{client}
}

func (c *AdminHTTPClientImpl) CreateAuthPolicy(ctx context.Context, in *CreateAuthPolicyRequest, opts ...http.CallOption) (*CreateAuthPolicyReply, error) {
	var out CreateAuthPolicyReply
	pattern := "/auth/admin/v1/auth_policy"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateAuthPolicy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateCorp(ctx context.Context, in *CreateCorpRequest, opts ...http.CallOption) (*CreateCorpReply, error) {
	var out CreateCorpReply
	pattern := "/auth/admin/v1/corp"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateCorp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateIDP(ctx context.Context, in *CreateIDPRequest, opts ...http.CallOption) (*CreateIDPReply, error) {
	var out CreateIDPReply
	pattern := "/auth/admin/v1/idp"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateIDP))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateRole(ctx context.Context, in *CreateRoleRequest, opts ...http.CallOption) (*CreateRoleReply, error) {
	var out CreateRoleReply
	pattern := "/auth/admin/v1/role"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateRootGroup(ctx context.Context, in *CreateRootGroupRequest, opts ...http.CallOption) (*CreateRootGroupReply, error) {
	var out CreateRootGroupReply
	pattern := "/auth/admin/v1/root_group"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateRootGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...http.CallOption) (*CreateUserReply, error) {
	var out CreateUserReply
	pattern := "/auth/admin/v1/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateUserCustom(ctx context.Context, in *CreateUserRequest, opts ...http.CallOption) (*CreateUserReply, error) {
	var out CreateUserReply
	pattern := "/auth/openapi/v1/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateUserCustom))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateUserGroup(ctx context.Context, in *CreateUserGroupRequest, opts ...http.CallOption) (*CreateUserGroupReply, error) {
	var out CreateUserGroupReply
	pattern := "/auth/admin/v1/user_group"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateUserGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateUserGroupCustom(ctx context.Context, in *CreateUserGroupRequestCustom, opts ...http.CallOption) (*CreateUserGroupReply, error) {
	var out CreateUserGroupReply
	pattern := "/auth/openapi/v1/group"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateUserGroupCustom))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) CreateUserSource(ctx context.Context, in *CreateUserSourceRequest, opts ...http.CallOption) (*CreateUserSourceReply, error) {
	var out CreateUserSourceReply
	pattern := "/auth/admin/v1/user_source"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminCreateUserSource))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteAuthPolicy(ctx context.Context, in *DeleteAuthPolicyRequest, opts ...http.CallOption) (*DeleteAuthPolicyReply, error) {
	var out DeleteAuthPolicyReply
	pattern := "/auth/admin/v1/auth_policy"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteAuthPolicy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteCorp(ctx context.Context, in *DeleteCorpRequest, opts ...http.CallOption) (*DeleteCorpReply, error) {
	var out DeleteCorpReply
	pattern := "/auth/admin/v1/corp"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteCorp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteIDP(ctx context.Context, in *DeleteIDPRequest, opts ...http.CallOption) (*DeleteIDPReply, error) {
	var out DeleteIDPReply
	pattern := "/auth/admin/v1/idp"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteIDP))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteRole(ctx context.Context, in *DeleteRoleRequest, opts ...http.CallOption) (*DeleteRoleReply, error) {
	var out DeleteRoleReply
	pattern := "/auth/admin/v1/role"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteRootGroup(ctx context.Context, in *DeleteRootGroupRequest, opts ...http.CallOption) (*DeleteRootGroupReply, error) {
	var out DeleteRootGroupReply
	pattern := "/auth/admin/v1/root_group"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteRootGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteRootGroupCustom(ctx context.Context, in *DeleteRootGroupRequest, opts ...http.CallOption) (*DeleteRootGroupReply, error) {
	var out DeleteRootGroupReply
	pattern := "/auth/openapi/v1/group"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteRootGroupCustom))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...http.CallOption) (*DeleteUserReply, error) {
	var out DeleteUserReply
	pattern := "/auth/admin/v1/user"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteUserCustom(ctx context.Context, in *CustomDeleteUserRequest, opts ...http.CallOption) (*DeleteUserReply, error) {
	var out DeleteUserReply
	pattern := "/auth/openapi/v1/user"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteUserCustom))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) DeleteUserSource(ctx context.Context, in *DeleteUserSourceRequest, opts ...http.CallOption) (*DeleteUserSourceReply, error) {
	var out DeleteUserSourceReply
	pattern := "/auth/admin/v1/user_source"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDeleteUserSource))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) GetCorp(ctx context.Context, in *GetCorpRequest, opts ...http.CallOption) (*GetCorpReply, error) {
	var out GetCorpReply
	pattern := "/auth/admin/v1/corp"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminGetCorp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) GetFieldMap(ctx context.Context, in *GetFieldMapRequest, opts ...http.CallOption) (*GetFieldMapReply, error) {
	var out GetFieldMapReply
	pattern := "/auth/admin/v1/field_map"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminGetFieldMap))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) GetFieldOptions(ctx context.Context, in *GetFieldOptionsRequest, opts ...http.CallOption) (*GetFieldOptionsReply, error) {
	var out GetFieldOptionsReply
	pattern := "/auth/admin/v1/field_option"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminGetFieldOptions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) GetIDPDetail(ctx context.Context, in *GetIDPDetailRequest, opts ...http.CallOption) (*GetIDPDetailReply, error) {
	var out GetIDPDetailReply
	pattern := "/auth/admin/v1/idp/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminGetIDPDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) GetRootGroupDetail(ctx context.Context, in *GetRootGroupDetailRequest, opts ...http.CallOption) (*GetRootGroupDetailReply, error) {
	var out GetRootGroupDetailReply
	pattern := "/auth/admin/v1/root_group/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminGetRootGroupDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) GetRootGroupIdpList(ctx context.Context, in *GetRootGroupIdpListRequest, opts ...http.CallOption) (*GetRootGroupIdpListReply, error) {
	var out GetRootGroupIdpListReply
	pattern := "/auth/admin/v1/root_group/idp_list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminGetRootGroupIdpList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) GetUserCount(ctx context.Context, in *GetUserCountRequest, opts ...http.CallOption) (*GetUserCountReply, error) {
	var out GetUserCountReply
	pattern := "/auth/admin/v1/user_count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminGetUserCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) GetUserSource(ctx context.Context, in *GetUserSourceRequest, opts ...http.CallOption) (*GetUserSourceReply, error) {
	var out GetUserSourceReply
	pattern := "/auth/admin/v1/user_source"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminGetUserSource))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) IdleAccountList(ctx context.Context, in *IdleAccountRequest, opts ...http.CallOption) (*IdleAccountReply, error) {
	var out IdleAccountReply
	pattern := "/auth/admin/v1/idle_account/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminIdleAccountList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListAuthPolicy(ctx context.Context, in *ListAuthPolicyRequest, opts ...http.CallOption) (*ListAuthPolicyReply, error) {
	var out ListAuthPolicyReply
	pattern := "/auth/admin/v1/auth_policy/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListAuthPolicy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListCorp(ctx context.Context, in *ListCorpRequest, opts ...http.CallOption) (*ListCorpReply, error) {
	var out ListCorpReply
	pattern := "/auth/admin/v1/corp/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListCorp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListIDP(ctx context.Context, in *ListIDPRequest, opts ...http.CallOption) (*ListIDPReply, error) {
	var out ListIDPReply
	pattern := "/auth/admin/v1/idp/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListIDP))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListIDPType(ctx context.Context, in *ListIDPTypeRequest, opts ...http.CallOption) (*ListIDPTypeReply, error) {
	var out ListIDPTypeReply
	pattern := "/auth/admin/v1/idp_type/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListIDPType))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListRole(ctx context.Context, in *ListRoleRequest, opts ...http.CallOption) (*ListRoleReply, error) {
	var out ListRoleReply
	pattern := "/auth/admin/v1/role/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListRootGroup(ctx context.Context, in *ListRootGroupRequest, opts ...http.CallOption) (*ListRootGroupReply, error) {
	var out ListRootGroupReply
	pattern := "/auth/admin/v1/root_group/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListRootGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListSyncLog(ctx context.Context, in *ListSyncLogRequest, opts ...http.CallOption) (*ListSyncLogReply, error) {
	var out ListSyncLogReply
	pattern := "/auth/admin/v1/root_group/sync_log"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListSyncLog))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListUser(ctx context.Context, in *ListUserRequest, opts ...http.CallOption) (*ListUserReply, error) {
	var out ListUserReply
	pattern := "/auth/admin/v1/user/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListUserGroup(ctx context.Context, in *ListUserGroupRequest, opts ...http.CallOption) (*ListUserGroupReply, error) {
	var out ListUserGroupReply
	pattern := "/auth/admin/v1/user_group/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListUserGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListUserSource(ctx context.Context, in *ListUserSourceRequest, opts ...http.CallOption) (*ListUserSourceReply, error) {
	var out ListUserSourceReply
	pattern := "/auth/admin/v1/user_source/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListUserSource))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ListUserSourceType(ctx context.Context, in *ListUserSourceTypeRequest, opts ...http.CallOption) (*ListUserSourceTypeReply, error) {
	var out ListUserSourceTypeReply
	pattern := "/auth/admin/v1/user_source/type/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminListUserSourceType))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) OAuth2Test(ctx context.Context, in *OAuth2TestRequest, opts ...http.CallOption) (*OAuth2TestReply, error) {
	var out OAuth2TestReply
	pattern := "/auth/admin/v1/idp/oauth2_test"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminOAuth2Test))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) SwitchAutoSync(ctx context.Context, in *SwitchAutoSyncRequest, opts ...http.CallOption) (*SwitchAutoSyncReply, error) {
	var out SwitchAutoSyncReply
	pattern := "/auth/admin/v1/root_group/auto_sync"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminSwitchAutoSync))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) SyncTrigger(ctx context.Context, in *SyncTriggerRequest, opts ...http.CallOption) (*SyncTriggerReply, error) {
	var out SyncTriggerReply
	pattern := "/auth/admin/v1/root_group/sync_trigger"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminSyncTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) TestResult(ctx context.Context, in *TestResultRequest, opts ...http.CallOption) (*TestResultReply, error) {
	var out TestResultReply
	pattern := "/auth/admin/v1/idp/test_result/{test_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminTestResult))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) TotpUnbind(ctx context.Context, in *TotpUnbindRequest, opts ...http.CallOption) (*TotpUnbindTimeReply, error) {
	var out TotpUnbindTimeReply
	pattern := "/auth/admin/v1/totp_unbind"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminTotpUnbind))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateAuthPolicy(ctx context.Context, in *UpdateAuthPolicyRequest, opts ...http.CallOption) (*UpdateAuthPolicyReply, error) {
	var out UpdateAuthPolicyReply
	pattern := "/auth/admin/v1/auth_policy"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateAuthPolicy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateCorp(ctx context.Context, in *UpdateCorpRequest, opts ...http.CallOption) (*UpdateCorpReply, error) {
	var out UpdateCorpReply
	pattern := "/auth/admin/v1/corp"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateCorp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateIDP(ctx context.Context, in *UpdateIDPRequest, opts ...http.CallOption) (*UpdateIDPReply, error) {
	var out UpdateIDPReply
	pattern := "/auth/admin/v1/idp"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateIDP))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateIdleTime(ctx context.Context, in *UpdateIdleTimeRequest, opts ...http.CallOption) (*UpdateIdleTimeReply, error) {
	var out UpdateIdleTimeReply
	pattern := "/auth/admin/v1/idle_time"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateIdleTime))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateLockStatus(ctx context.Context, in *UpdateLockStatusRequest, opts ...http.CallOption) (*UpdateLockStatusReply, error) {
	var out UpdateLockStatusReply
	pattern := "/auth/admin/v1/lock_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateLockStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateRole(ctx context.Context, in *UpdateRoleRequest, opts ...http.CallOption) (*UpdateRoleReply, error) {
	var out UpdateRoleReply
	pattern := "/auth/admin/v1/role"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateRootGroup(ctx context.Context, in *UpdateRootGroupRequest, opts ...http.CallOption) (*UpdateRootGroupReply, error) {
	var out UpdateRootGroupReply
	pattern := "/auth/admin/v1/root_group"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateRootGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateRootGroupCustom(ctx context.Context, in *UpdateRootGroupRequest, opts ...http.CallOption) (*UpdateRootGroupReply, error) {
	var out UpdateRootGroupReply
	pattern := "/auth/openapi/v1/group"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateRootGroupCustom))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...http.CallOption) (*UpdateUserReply, error) {
	var out UpdateUserReply
	pattern := "/auth/admin/v1/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateUserCustom(ctx context.Context, in *CustomUpdateUserRequest, opts ...http.CallOption) (*UpdateUserReply, error) {
	var out UpdateUserReply
	pattern := "/auth/openapi/v1/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateUserCustom))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateUserGroup(ctx context.Context, in *UpdateUserGroupRequest, opts ...http.CallOption) (*UpdateUserGroupReply, error) {
	var out UpdateUserGroupReply
	pattern := "/auth/admin/v1/user_group"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateUserGroup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) UpdateUserSource(ctx context.Context, in *UpdateUserSourceRequest, opts ...http.CallOption) (*UpdateUserSourceReply, error) {
	var out UpdateUserSourceReply
	pattern := "/auth/admin/v1/user_source"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminUpdateUserSource))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminHTTPClientImpl) ValidateWebAuthScript(ctx context.Context, in *ValidateWebAuthScriptRequest, opts ...http.CallOption) (*ValidateWebAuthScriptReply, error) {
	var out ValidateWebAuthScriptReply
	pattern := "/auth/admin/v1/idp/validate_script"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminValidateWebAuthScript))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
