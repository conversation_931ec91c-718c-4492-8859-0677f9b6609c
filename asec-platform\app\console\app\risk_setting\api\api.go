package api

import (
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/app/risk_setting/model"
	"asdsec.com/asec/platform/app/console/app/risk_setting/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

// GetRiskSettingTotal godoc
// @Summary 风险设置评分配置
// @Schemes
// @Description 获取风险设置评分配置
// @Tags        risk_setting
// @Produce     application/json
// @Success     200
// @Router      /v1/risk/setting/total [GET]
// @success     200 {object} common.Response{data=model.GetRiskSettingTotalRsp} "ok"
func GetRiskSettingTotal(c *gin.Context) {
	riskSetting, err := service.GetRiskSettingService().GetRiskSettingTotal(c)
	if err != nil {
		global.SysLog.Error("GetRiskSetting err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, riskSetting)
}

// GetRiskSettingList godoc
// @Summary 风险设置配置列表
// @Schemes
// @Description 获取风险设置配置列表
// @Tags        risk_setting_list
// @Produce     application/json
// @Success     200
// @Router      /v1/risk/setting/list [GET]
// @success     200 {object} common.Response{data=[]model.GetRiskSettingListResp} "ok"
func GetRiskSettingList(c *gin.Context) {
	riskSettingList, err := service.GetRiskSettingService().GetRiskSettingList(c)
	if err != nil {
		global.SysLog.Error("GetRiskSettingList err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, riskSettingList)
}

// UpdateRiskSetting godoc
// @Summary 更新风险设置
// @Schemes
// @Description 更新风险设置
// @Tags        Application
// @Produce     application/json
// @Param       req body model.UpdateRiskScoreSettingReq true "更新风险设置"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/risk/setting/score [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateRiskSetting(c *gin.Context) {
	var req model.UpdateRiskScoreSettingReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetRiskSettingService().UpdateRiskSetting(c, &req)
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.DdrRiskScoreType,
			OperationType:  common.OperateUpdate,
			Representation: "Update Ddr Risk Score Id:" + strconv.Itoa(req.Id),
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	if err != nil {
		common.FailWithMessage(c, -1, "更新评分失败")
		return
	}
	common.Ok(c)
}
