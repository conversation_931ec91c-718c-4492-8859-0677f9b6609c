package repository

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"asdsec.com/asec/platform/app/console/app/module_switch/constants"
	"asdsec.com/asec/platform/app/console/app/module_switch/dto"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	"asdsec.com/asec/platform/app/console/common/utils"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/auth_model"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// SwitchRepository 接口定义
type SwitchRepository interface {
	UpdateSwitch(ctx context.Context, req model.AgentModuleSwitch) error
	UpdateModuleSwitch(ctx context.Context, req model.ModuleSwitch) error
	GetUnbindAgent(ctx context.Context, search string, platform string) ([]dto.ModuleSwitchAgents, error)
	GetGroupAndAgents(ctx context.Context, req dto.EffectUserReq) ([]dto.GroupAndAgents, error)
	GetGroupAndAllAgents(ctx context.Context, req dto.EffectUserReq) ([]dto.GroupAndAgents, error)
	GetModuleSwitch(ctx context.Context) ([]model.ModuleSwitch, error)
	GetDirSwitch(ctx context.Context, groupId string, moduleCode int) bool
	GetAgentSwitch(ctx context.Context, agentId string, moduleCode int) bool
	GetChildUserGroup(ctx context.Context, groupId string) []auth_model.TbUserGroup
	GetSwitchedAgent(ctx context.Context, moduleCode int, isUnbind bool) []string
	GetDataSurveySwitch(ctx context.Context) (model.ModuleSwitch, error)
}

// NewSwitchRepository 创建接口实现接口实现
func NewSwitchRepository() SwitchRepository {
	return &switchRepository{}
}

type switchRepository struct {
}

func (u *switchRepository) GetDataSurveySwitch(ctx context.Context) (model.ModuleSwitch, error) {
	value, err := commonApi.GetSpecialConfig(ctx, constants.SwitchSpecialType, constants.EventSpecialKey)
	if err != nil {
		global.SysLog.Sugar().Errorf("get special config faile. err=%v", err)
		return model.ModuleSwitch{}, err
	}
	var switchValue bool
	if value == constants.TrueSpecialValue {
		switchValue = true
	}
	return model.ModuleSwitch{Switch: switchValue}, err
}

func (u *switchRepository) GetModuleSwitch(ctx context.Context) ([]model.ModuleSwitch, error) {
	var result []model.ModuleSwitch
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return result, err
	}
	err = db.Model(&model.ModuleSwitch{}).Find(&result).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get ModuleSwitch err", zap.Error(err))
		return result, err
	}
	return result, nil
}

const FakeRootUserGroupId = "0" //根分组

func (u *switchRepository) GetGroupAndAgents(ctx context.Context, req dto.EffectUserReq) ([]dto.GroupAndAgents, error) {
	var result []dto.GroupAndAgents
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	userName := req.UserName
	agentName := req.AgentName
	groupName := req.GroupName
	var groupIds []string
	if groupName != "" {
		userGroups := u.GetSearchGroup(ctx, groupName)
		for _, v := range userGroups {
			groupIds = append(groupIds, v.ID)
		}
	}
	// 根分组
	var rootGroup []dto.UserGroups
	groupTable := auth_model.TableNameTbUserGroup
	sourceTb := auth_model.TableNameTbUserSource
	err = db.Table(groupTable).Select(fmt.Sprintf("%s.*,%s.source_type",
		groupTable, sourceTb)).Where("parent_group_id = ?", FakeRootUserGroupId).Joins(
		fmt.Sprintf("left join %s on %s.source_id = %s.id", sourceTb, groupTable, sourceTb)).Find(&rootGroup).Error
	if err != nil {
		return result, err
	}
	var isAllFlag = false
	if userName != "" || agentName != "" || groupName != "" {
		isAllFlag = true
	}
	// 根分组下的目录和用户
	selectDir := u.GetAllSwitchDir(ctx, req.ModuleCode)
	selectDirMap := make(map[string]bool, 0)
	for _, v := range selectDir {
		selectDirMap[v] = true
	}
	for _, v := range rootGroup {
		isSelect := false
		if _, ok := selectDirMap[v.ID]; ok {
			isSelect = true
		}
		child := u.GetChildAgents(ctx, req.ModuleCode, v.ID, userName, agentName, groupName, req.Platform)
		tmp := dto.GroupAndAgents{
			Id:         v.ID,
			Name:       v.Name,
			IsDir:      true,
			Children:   child,
			SourceType: v.SourceType,
			Switch:     isSelect,
		}
		if len(child) == 0 && isAllFlag && !utils.InArray(v.ID, groupIds) {
			continue
		}
		//单独查分组且没有命中的情况下，根分组的终端不显示
		if groupName != "" && agentName == "" && !utils.InArray(v.ID, groupIds) {
			var tmpChild []dto.GroupAndAgents
			if len(tmp.Children) > 0 {
				for _, c := range tmp.Children {
					if c.IsDir {
						tmpChild = append(tmpChild, c)
					}
				}
			}
			tmp.Children = tmpChild
		}
		result = append(result, tmp)
	}
	return result, err
}

func (u *switchRepository) GetGroupAndAllAgents(ctx context.Context, req dto.EffectUserReq) ([]dto.GroupAndAgents, error) {
	var result []dto.GroupAndAgents
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	userName := req.UserName
	agentName := req.AgentName
	groupName := req.GroupName
	var groupIds []string
	if groupName != "" {
		userGroups := u.GetSearchGroup(ctx, groupName)
		for _, v := range userGroups {
			groupIds = append(groupIds, v.ID)
		}
	}
	// 根分组
	var rootGroup []dto.UserGroups
	groupTable := auth_model.TableNameTbUserGroup
	sourceTb := auth_model.TableNameTbUserSource
	err = db.Table(groupTable).Select(fmt.Sprintf("%s.*,%s.source_type",
		groupTable, sourceTb)).Where("parent_group_id = ?", FakeRootUserGroupId).Joins(
		fmt.Sprintf("left join %s on %s.source_id = %s.id", sourceTb, groupTable, sourceTb)).Find(&rootGroup).Error
	if err != nil {
		return result, err
	}
	var isAllFlag = false
	if userName != "" || agentName != "" || groupName != "" {
		isAllFlag = true
	}
	// 根分组下的目录和用户
	selectDir := u.GetAllSwitchDir(ctx, req.ModuleCode)
	selectDirMap := make(map[string]bool, 0)
	for _, v := range selectDir {
		selectDirMap[v] = true
	}
	for _, v := range rootGroup {
		isSelect := false
		if _, ok := selectDirMap[v.ID]; ok {
			isSelect = true
		}
		child := u.GetChildAgents(ctx, req.ModuleCode, v.ID, userName, agentName, groupName, req.Platform)
		tmp := dto.GroupAndAgents{
			Id:    v.ID,
			Name:  v.Name,
			IsDir: true,
			//Children:   child,
			SourceType: v.SourceType,
			Switch:     isSelect,
		}
		for _, val := range child {
			val.SourceType = "origin"
			if isSelect {
				val.Switch = true
			}
			if _, ok := selectDirMap[val.Id]; ok {
				val.Switch = true
			}
			//if _, ok := selectDirMap[val.ID]; ok {
			//	isSelect = true
			//}
			result = append(result, val)
		}
		//fmt.Println("tmpvv 啊是的是的 :", tmp)
		if len(child) == 0 && isAllFlag && !utils.InArray(v.ID, groupIds) {
			continue
		}
		//单独查分组且没有命中的情况下，根分组的终端不显示
		if groupName != "" && agentName == "" && !utils.InArray(v.ID, groupIds) {
			var tmpChild []dto.GroupAndAgents
			if len(tmp.Children) > 0 {
				for _, c := range tmp.Children {
					if c.IsDir {
						tmpChild = append(tmpChild, c)
					}
				}
			}
			tmp.Children = tmpChild
		}
		result = append(result, tmp)
	}
	return result, err
}

func (u *switchRepository) GetSearchGroup(ctx context.Context, groupName string) []auth_model.TbUserGroup {
	var result []auth_model.TbUserGroup
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return result
	}
	err = db.Table(auth_model.TableNameTbUserGroup).Where("name ilike ?",
		fmt.Sprintf("%%%s%%", dbutil.EscapeForLike(groupName))).Find(&result).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return result
	}
	return result
}

func (u *switchRepository) GetSelfAgents(ctx context.Context, groupId, userName, agentName string, platform string) []dto.ModuleSwitchAgents {
	var result []dto.ModuleSwitchAgents
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return result
	}
	db = db.Model(&model.Agent{})
	agentTable := model.Agent{}.TableName()
	hbTable := model.AgentHeartbeat{}.TableName()
	userTable := auth_model.TableNameTbUserEntity
	if userName != "" && agentName != "" {
		db = db.Where(fmt.Sprintf("%s.name ilike ? or %s.app_name ilike ?", userTable, agentTable),
			fmt.Sprintf("%%%s%%", dbutil.EscapeForLike(userName)),
			fmt.Sprintf("%%%s%%", dbutil.EscapeForLike(agentName)),
		)
	} else if userName != "" {
		db = db.Where(fmt.Sprintf("%s.name ilike ? OR %s.display_name like ?", userTable, userTable), fmt.Sprintf("%%%s%%", dbutil.EscapeForLike(userName)), fmt.Sprintf("%%%s%%", dbutil.EscapeForLike(userName)))
	} else if agentName != "" {
		db = db.Where(fmt.Sprintf("%s.app_name ilike ?", agentTable), fmt.Sprintf("%%%s%%", dbutil.EscapeForLike(agentName)))
	}
	sourceTb := auth_model.TableNameTbUserSource
	if platform != constants.AllPlatform {
		db = db.Where("app_plat = ?", platform)
	}
	err = db.Select(fmt.Sprintf("appliance_id, app_name, app_plat,CASE WHEN (%s.display_name is null or %s.display_name ='') THEN %s.name ELSE %s.display_name END as user_name, %s.source_type", userTable, userTable, userTable, userTable, sourceTb)).Joins(
		fmt.Sprintf("left join %s on %s.appliance_id = %s.agent_id", hbTable, agentTable, hbTable)).Joins(
		fmt.Sprintf("left join %s on %s.user_id = %s.id", userTable, hbTable, userTable)).Joins(
		fmt.Sprintf("left join %s on %s.source_id = %s.id", sourceTb, userTable, sourceTb)).Where(
		fmt.Sprintf("%s.user_id is not null", hbTable)).Where(
		fmt.Sprintf("%s.group_id=?", userTable), groupId).Find(&result).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get agent err", zap.Error(err))
		return result
	}
	return result
}

func (u *switchRepository) GetChildAgents(ctx context.Context, moduleCode int, groupId, userName, agentName, groupName string, platform string) []dto.GroupAndAgents {
	var result []dto.GroupAndAgents
	var group []dto.UserGroups
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return result
	}
	groupTable := auth_model.TableNameTbUserGroup
	sourceTb := auth_model.TableNameTbUserSource
	err = db.Table(groupTable).Select(fmt.Sprintf("%s.*,%s.source_type",
		groupTable, sourceTb)).Where("parent_group_id = ?", groupId).Joins(
		fmt.Sprintf("left join %s on %s.source_id = %s.id", sourceTb, groupTable, sourceTb)).Find(&group).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get parent_group_id err", zap.Error(err))
		return result
	}
	var groupIds []string
	if groupName != "" {
		userGroups := u.GetSearchGroup(ctx, groupName)
		for _, v := range userGroups {
			groupIds = append(groupIds, v.ID)
		}
	}
	agents := u.GetSelfAgents(ctx, groupId, userName, agentName, platform)
	selectAgent := u.GetSwitchedAgent(ctx, moduleCode, false)
	selectMap := make(map[string]bool, 0)
	for _, v := range selectAgent {
		selectMap[v] = true
	}
	for _, v := range agents {
		if groupName != "" && agentName == "" && !utils.InArray(groupId, groupIds) {
			break
		}
		isSelect := false
		agentId := strconv.FormatUint(v.ApplianceId, 10)
		if _, ok := selectMap[agentId]; ok {
			isSelect = true
		}
		tmp := dto.GroupAndAgents{
			Id:         agentId,
			Name:       v.AppName,
			AppPlat:    v.AppPlat,
			UserName:   v.UserName,
			SourceType: v.SourceType,
			Switch:     isSelect,
			IsDir:      false,
		}
		result = append(result, tmp)
	}
	var isAllFlag = false
	if userName != "" || agentName != "" || groupName != "" {
		isAllFlag = true
	}
	selectDir := u.GetAllSwitchDir(ctx, moduleCode)
	selectDirMap := make(map[string]bool, 0)
	for _, v := range selectDir {
		selectDirMap[v] = true
	}
	for _, v := range group {
		isDirSelect := false
		if _, ok := selectDirMap[v.ID]; ok {
			isDirSelect = true
		}
		child := u.GetChildAgents(ctx, moduleCode, v.ID, userName, agentName, groupName, platform)
		tmp := dto.GroupAndAgents{
			Id:         v.ID,
			Name:       v.Name,
			IsDir:      true,
			Children:   child,
			SourceType: v.SourceType,
			Switch:     isDirSelect,
		}
		if len(child) == 0 && isAllFlag && !utils.InArray(v.ID, groupIds) {
			continue
		}
		result = append(result, tmp)
	}
	return result
}

func (u *switchRepository) GetSwitchedAgent(ctx context.Context, moduleCode int, isUnbind bool) []string {
	var switchedAgents []string
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return switchedAgents
	}
	var res model.AgentModuleSwitch
	err = db.Model(model.AgentModuleSwitch{}).Where("module_code=?", moduleCode).First(&res).Error
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			global.SysLog.Error("get agent err", zap.Error(err))
		}
		return switchedAgents
	}
	if len(res.AgentIds) > 0 {
		for _, v := range res.AgentIds {
			switchedAgents = append(switchedAgents, v)
		}
	}
	if isUnbind {
		return switchedAgents
	}
	var userId []string
	if len(res.UserIds) > 0 {
		userId = append(userId, res.UserIds...)
	}
	userTable := auth_model.TableNameTbUserEntity
	if len(res.GroupIds) > 0 { //父分组保存得情况下，子分组也会保存，所以只需要查询当前分组下得终端即可
		var groupIds []string
		for _, v := range res.GroupIds {
			groupIds = append(groupIds, v)
		}
		var groupUserId []string
		err = db.Table(userTable).Select("id").Where("group_id in (?)", groupIds).Find(&groupUserId).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			global.SysLog.Error("get userId err", zap.Error(err))
			return switchedAgents
		}
		if len(groupUserId) > 0 {
			userId = append(userId, groupUserId...)
		}
	}
	if len(res.RoleIds) > 0 {
		var roleUser []string
		var roleIds []string
		for _, v := range res.RoleIds {
			roleIds = append(roleIds, v)
		}
		roleMappingTable := auth_model.TableNameTbUserRole
		err = db.Table(roleMappingTable).Select("user_id").Where("role_id in (?)", roleIds).Find(&roleUser).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			global.SysLog.Error("get userId err", zap.Error(err))
			return switchedAgents
		}
		if len(roleUser) > 0 {
			userId = append(userId, roleUser...)
		}
	}
	if len(userId) > 0 {
		var agentIds []int64
		hbTable := model.AgentHeartbeat{}.TableName()
		err = db.Table(hbTable).Select("agent_id").Where("user_id in (?)", userId).Find(&agentIds).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			global.SysLog.Error("get agent_id err", zap.Error(err))
			return switchedAgents
		}
		for _, v := range agentIds {
			switchedAgents = append(switchedAgents, strconv.FormatInt(v, 10))
		}
	}
	return switchedAgents
}

func (u *switchRepository) GetAgentSwitch(ctx context.Context, agentId string, moduleCode int) bool {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return false
	}
	var res []model.AgentModuleSwitch
	err = db.Model(model.AgentModuleSwitch{}).Where("?=any(agent_ids) and module_code=?", agentId, moduleCode).Find(&res).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get agent err", zap.Error(err))
		return false
	}
	if len(res) > 0 {
		return true
	}
	// 判断终端是否勾选，还需要判断其所在的组是否勾选
	var userId string
	hbTable := model.AgentHeartbeat{}.TableName()
	userTable := auth_model.TableNameTbUserEntity
	err = db.Table(hbTable).Select("user_id").Where("agent_id = ? and user_id is not null", agentId).Take(&userId).Error
	if err != nil {
		return false
	}
	if userId != "" {
		var groupId string
		err = db.Table(userTable).Select("group_id").Where("id = ?", userId).Take(&groupId).Error
		if err != nil {
			return false
		}
		if groupId != "" {
			var switchRes []model.AgentModuleSwitch
			err = db.Model(model.AgentModuleSwitch{}).Where("?=any(user_groups) and module_code=?", groupId, moduleCode).Find(&switchRes).Error
			if err != nil {
				return false
			}
			if len(switchRes) > 0 {
				return true
			}
		}
	}
	return false
}

func (u *switchRepository) GetAllSwitchDir(ctx context.Context, moduleCode int) []string {
	var selectDir []string
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return selectDir
	}
	var res model.AgentModuleSwitch
	err = db.Model(model.AgentModuleSwitch{}).Where("module_code=?", moduleCode).Find(&res).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("get db err", zap.Error(err))
		return selectDir
	}
	if len(res.GroupIds) > 0 {
		for _, v := range res.GroupIds {
			selectDir = append(selectDir, v)
		}
	}
	return selectDir
}

func (u *switchRepository) GetDirSwitch(ctx context.Context, groupId string, moduleCode int) bool {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return false
	}
	var res []model.AgentModuleSwitch
	err = db.Model(model.AgentModuleSwitch{}).Where("?=any(user_groups) and module_code=?", groupId, moduleCode).Find(&res).Error
	if err != nil {
		return false
	}
	if len(res) > 0 {
		return true
	}
	return false
}

func (u *switchRepository) GetUnbindAgent(ctx context.Context, search string, platform string) ([]dto.ModuleSwitchAgents, error) {
	var result []dto.ModuleSwitchAgents
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	db = db.Model(&model.Agent{})
	agentTable := model.Agent{}.TableName()
	hbTable := model.AgentHeartbeat{}.TableName()
	userTable := auth_model.TableNameTbUserEntity
	if search != "" {
		db.Where("app_name ilike ?", fmt.Sprintf("%%%s%%", dbutil.EscapeForLike(search)))
	}
	if platform != constants.AllPlatform {
		db = db.Where("app_plat = ?", platform)
	}
	err = db.Select(fmt.Sprintf("appliance_id, app_name, app_plat, %s.name as user_name", userTable)).Joins(
		fmt.Sprintf("left join %s on %s.appliance_id = %s.agent_id", hbTable, agentTable, hbTable)).Joins(
		fmt.Sprintf("left join %s on %s.user_id = %s.id", userTable, hbTable, userTable)).Where(
		fmt.Sprintf("%s.id is null or %s.id = ''", userTable, userTable)).Find(&result).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("err:", zap.Error(err))
		return result, err
	}
	return result, nil
}

func (u *switchRepository) UpdateModuleSwitch(ctx context.Context, req model.ModuleSwitch) error {
	redisDb, err := global.GetRedisClient(ctx)
	if err != nil {
		global.SysLog.Error("get redis cli err", zap.Error(err))
		return err
	}
	err = utils.DeleteKeysByPrefixWithPipeline(ctx, redisDb, "module_switch_")
	if err != nil {
		global.SysLog.Error("get redis cli err", zap.Error(err))
		return errors.New("delete cache failed")
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return err
	}
	return db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "module_code"}},
		UpdateAll: true,
	}).Create(&req).Error
}

func (u *switchRepository) UpdateSwitch(ctx context.Context, req model.AgentModuleSwitch) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return err
	}
	return db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "module_code"}},
		UpdateAll: true,
	}).Create(&req).Error
}

func (u *switchRepository) GetAllUserAgent(ctx context.Context) ([]uint64, error) {
	var agents []uint64
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return agents, err
	}
	err = db.Model(model.AgentHeartbeat{}).Select("agent_id").Where("user_id is not null").Find(&agents).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("err:", zap.Error(err))
		return nil, err
	}
	return agents, nil
}

func (u *switchRepository) GetAllAgentIds(ctx context.Context) ([]uint64, error) {
	var agents []uint64
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return agents, err
	}
	err = db.Model(model.Agent{}).Select("appliance_id").Find(&agents).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("err:", zap.Error(err))
		return nil, err
	}
	return agents, nil
}

func (u *switchRepository) GetChildUserGroup(ctx context.Context, groupId string) []auth_model.TbUserGroup {
	db, err := global.GetDBClient(ctx)
	var tmp []auth_model.TbUserGroup
	var result []auth_model.TbUserGroup
	err = db.Table(auth_model.TableNameTbUserGroup).Where("parent_group_id = ?", groupId).Find(&tmp).Error
	if err != nil {
		return result
	}
	if len(tmp) > 0 {
		for _, v := range tmp {
			tt := u.GetChildUserGroup(ctx, v.ID)
			result = append(result, tt...)
		}
	}
	return result
}
