package model

import (
	modelTable "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/focus_model"
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"time"
)

type GetFocusListRsp struct {
	modelTable.CommonPage
	Data []focus_model.Focus `json:"data"`
}

type GetFocusUserListRsp struct {
	modelTable.CommonPage
	Data []FocusUserItem `json:"data"`
}

type FocusUserItem struct {
	TagInfo
	Count int         `json:"count"`
	Users []FocusUser `json:"users"`
}

type FocusUser struct {
	UserId    string         `gorm:"column:user_id" json:"user_id"`
	UserName  string         `gorm:"column:user_name" json:"user_name"`
	Score     int            `gorm:"column:score" json:"score"`
	RiskLevel int            `gorm:"column:risk_level" json:"risk_level"`
	Path      string         `gorm:"column:path" json:"path"`
	TagIds    pq.StringArray `json:"tag_ids,omitempty" gorm:"column:tag_ids"`
}

type FocusUserModel struct {
	FocusUser
	TagInfo pgtype.JSONBArray `gorm:"column:tag_info;type:[]jsonb" json:"-"`
}

type TagInfo struct {
	TagId     string    `json:"tag_id"`
	TagName   string    `json:"tag_name"`
	CreatedAt time.Time `json:"created_at"`
}
