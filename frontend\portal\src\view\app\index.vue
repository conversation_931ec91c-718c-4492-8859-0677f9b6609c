<template>
  <div class="app-page-root">
    <div class="person">
      <base-header class="app-header" height="44px">
        <div class="header-left">
          <h1 class="page-title">我的应用</h1>
        </div>
        <div class="header-right">
          <div class="search-controls">
            <base-icon class="search-icon" name="search" />
            <base-input
              v-model="searchQuery"
              class="search-input"
              clearable
              placeholder="搜索应用"
              prefix-icon="Search"
              @input="handleSearch"
            />
            <base-button
              class="refresh-btn"
              icon="Refresh"
              size="small"
              @click="handleRefreshClick"
            >
              <svg aria-hidden="true" class="icon refresh-btn-icon">
                <use xlink:href="#icon-search" />
              </svg>
            </base-button>
            <base-select
              v-model="viewType"
              class="view-select"
              size="small"
            >
              <base-option
                v-for="item in viewOptions"
                :key="item.key"
                :label="item.label"
                :value="item.key"
              />
            </base-select>
          </div>
        </div>
      </base-header>

      <base-header v-if="actualIsConnected" class="app-header" height="36px" padding="12px 16px 8px 16px">
        <div class="el-row">
          <span class="el-recent-access">最近访问</span>
          <span class="el-recent-data">
            <span
              v-for="(item, index) in recentApps"
              :key="item.id"
              class="el-recent-item"
              @click="handleAppClick(item)"
            >
              {{ item.app_name }}
              <base-icon
                class="el-recent-icon"
                name="close"
                size="8px"
                @click.stop="removeFromRecent(index)"
              />
            </span>
            <span v-if="recentApps.length === 0" class="el-recent-empty">
              暂无最近访问记录
            </span>
            <svg
              v-if="recentApps.length > 0"
              aria-hidden="true"
              class="icon el-recent-clear"
              title="清空最近访问"
              @click="clearRecentApps"
            >
              <use xlink:href="#icon-qingkong" />
            </svg>
          </span>
        </div>
      </base-header>

      <!-- 主体内容区域：根据连接状态显示不同内容 -->
      <div v-if="actualIsConnected" class="connected-content">
        <!-- 已连接：显示完整的应用列表 -->
        <base-container class="flex-container flex-row">
          <!-- 左侧分类导航 -->
          <base-aside class="category-aside" width="104px">
            <base-menu
              :default-active="defaultActiveIndex"
              background-color="#ffffff"
              class="category-menu"
              mode="vertical"
              @select="handleCategoryChange"
            >
              <base-menu-item class="category-menu-item" index="-1" @click="handleCategoryChange(-1)">
                收藏
              </base-menu-item>
              <base-menu-item class="category-menu-item" index="0" @click="handleCategoryChange(null)">
                全部
              </base-menu-item>
              <base-menu-item
                v-for="category in categoryList"
                :key="category.id"
                :index="category.id.toString()"
              >
                <base-tooltip
                  :content="category.name"
                  :disabled="category.name.length <= 5"
                  effect="light"
                  placement="right"
                >
                  <span class="category-menu-text">{{ showCategoryName(category.name) }}</span>
                </base-tooltip>
              </base-menu-item>
            </base-menu>
          </base-aside>

          <!-- 右侧应用列表 -->
          <base-main class="app-main custom-scrollbar">
            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-wrapper" />

            <!-- 有应用时显示应用列表 -->
            <div v-else-if="hasApps">
              <div
                v-for="category in filteredCategories"
                :key="category.id"
                class="category-section"
              >
                <!-- 分类标题已隐藏，由左侧 category-aside 控制 -->

                <!-- 应用列表 -->
                <div
                  :class="[`view-${viewType}`, getGridClass(category), { 'favorite-category': isFavoriteCategory }]"
                  class="apps-grid"
                >
                  <div
                    v-for="item in category.apps"
                    :key="item.id"
                    :class="{ 'disabled': ((!item.WebUrl && item.app_type !== 'portal' ) || ( !item.WebUrl && item.app_type === 'portal' && item.open_config.enabled === false )) || item.maint }"
                    class="app-item transition transform-hover"
                    @click="handleAppClick(item)"
                  >
                    <div
                      v-if="item.maint && viewType === 'compact'"
                      class="status-badge status-badge-compact"
                    >
                      维护中
                    </div>

                    <div class="app-content">
                      <div class="app-icon">
                        <base-tooltip effect="light" placement="bottom">
                          <template #content>
                            <div class="tooltip-content">
                              <span v-if="item.WebUrl">{{ item.WebUrl }}</span>
                              <span v-else-if="item.app_type === 'portal' && item.open_config.enabled">打开方式：{{
                                item.open_config.open_type === 'browser' ? '浏览器' : (item.open_config.open_type === 'client' ? '指定程序' : '系统应用')
                              }}</span>
                              <span v-else>暂无访问地址</span>
                            </div>
                          </template>
                          <base-avatar
                            :size="viewType === 'compact' ? 20 : 28"
                            :src="item.iconError ? '' : getFullIconUrl(item.icon)"
                            :style="(!item.icon || item.iconError) ? `background-color: ${getRandomColor(item.app_name)} !important` : 'background-color: #f7f7fa !important'"
                            shape="square"
                            @error="() => handleIconError(item)"
                          >
                            {{ (!item.icon || item.iconError) ? item.app_name.slice(0, 1) : '' }}
                          </base-avatar>
                        </base-tooltip>
                      </div>

                      <div class="app-details">
                        <div :title="item.app_name" class="app-name">
                          <span class="app-name-text">{{ item.app_name }}</span><span
                            v-if="item.maint && viewType === 'standard'"
                            class="status-badge-inline"
                          >
                            维护中
                          </span>
                        </div>
                        <div v-if="viewType === 'standard'" class="app-desc">
                          <base-tooltip
                            :content="item.app_desc || '应用程序'"
                            :disabled="!(item.app_desc && item.app_desc.length > 8)"
                            effect="light"
                            placement="bottom"
                          >
                            <span class="app-desc-text">{{ item.app_desc || '应用程序' }}</span>
                          </base-tooltip>
                        </div>
                      </div>

                      <!-- 收藏按钮 -->
                      <base-icon
                        :name="isFavorite(item) ? 'yishoucang' : 'shoucang'"
                        :title="isFavorite(item) ? '取消收藏' : '收藏'"
                        class="app-collect-icon"
                        @click.stop="toggleFavorite(item)"
                      />

                      <!-- 表单代填编辑按钮 -->
                      <base-icon
                        v-if="item.form_fill_enabled === 1 || (isFavoriteCategory && item.form_fill_enabled !== 0)"
                        class="app-form-fill-icon"
                        name="bianji"
                        title="编辑表单代填"
                        @click.stop="openFormFillDialog(item)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 无应用时显示空状态 -->
            <div v-else class="no-apps-wrapper">
              <div class="no-apps-content">
                <div class="no-apps-image">
                  <img :src="noResultImage" alt="暂无搜索结果">
                </div>
                <div class="no-apps-text">
                  {{ searchQuery ? '暂无搜索结果' : '暂无应用数据，请添加应用' }}
                </div>
              </div>
            </div>
          </base-main>
        </base-container>
      </div>

      <div v-else class="disconnected-content">
        <!-- 未连接：显示提示信息 -->
        <div class="no-connection-wrapper">
          <div class="no-connection-image">
            <img :src="noPowerImage" alt="请先建立安全连接">
          </div>
          <div class="no-connection-text">
            <h3 style="font-size: 16px;">请先建立安全连接</h3>
            <p style="font-size: 12px;">成功连接后可查看授权的应用</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单代填对话框 -->
    <FormFillDialog
      :app-info="currentFormFillApp"
      :visible="formFillDialogVisible"
      @success="handleFormFillSuccess"
      @update:visible="formFillDialogVisible = $event"
    />
  </div>
</template>

<script>
export default {
  name: 'AppPage',
  props: {
    isConnected: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Message } from '@/components/base'
import FormFillDialog from '@/components/base/FormFillDialog.vue'
import agentApi from '@/api/agentApi'
import noPowerImage from '@/assets/no_power.png'
import noResultImage from '@/assets/no_result.png'
import { emitter } from '@/utils/bus.js'
import { clientToken } from '@/permission'

import { getCurrentUser, getFormFillAccount } from '@/api/resource'
import { useRoute } from 'vue-router'
import { useUserStore } from '../../pinia/modules/user'
import { getCurrentHost } from '@/utils/request'

// 接收props
const props = defineProps({
  isConnected: {
    type: Boolean,
    default: false
  }
})

// 计算实际的连接状态：如果用户已认证或不是客户端，则始终为已连接状态
const actualIsConnected = computed(() => {
  // 如果不是客户端环境，直接返回true
  if (!agentApi.isClient()) {
    return true
  }
  // 如果用户已经认证(有token)，直接显示应用
  if (userStore.token && userStore.token.accessToken) {
    return true
  }
  // 如果是客户端环境且用户未认证，使用传入的连接状态
  return props.isConnected
})

// 判断是否有应用数据
const hasApps = computed(() => {
  // 如果还在加载中，不显示空状态
  if (isLoading.value || !isInitialized.value) {
    return true
  }

  if (!filteredCategories.value || filteredCategories.value.length === 0) {
    return false
  }

  // 检查是否有任何分类包含应用
  return filteredCategories.value.some(category =>
    category.apps && category.apps.length > 0
  )
})

const searchQuery = ref('')
const selectedCategory = ref(null)
const categoryList = ref([])
const filteredCategories = ref([])
const defaultActiveIndex = ref('0')
const viewType = ref('standard')
const viewOptions = reactive([
  { 'key': 'standard', 'label': '标准视图' },
  { 'key': 'compact', 'label': '紧凑视图' },
])

// 数据加载状态
const isLoading = ref(false)
const isInitialized = ref(false)

// 收藏和最近访问相关状态
const favoriteApps = ref([])
const recentApps = ref([])

// 表单代填对话框相关状态
const formFillDialogVisible = ref(false)
const currentFormFillApp = ref(null)

// 获取用户特定的存储键
const getUserStorageKey = (baseKey) => {
  const userId = userStore.userInfo?.id || userStore.userInfo?.name || 'anonymous'
  return `${baseKey}_${userId}`
}

const STORAGE_KEYS = {
  FAVORITES: 'app_favorites',
  RECENT: 'app_recent'
}

// WebSocket相关状态
const ws = ref(null)
const wsConnecting = ref(false)

// 显示状态消息
const showMessage = (message, type = 'success', duration = 3000) => {
  Message({
    message,
    type,
    duration // 使用传入的持续时间，默认3秒，特殊消息可以更长
  })
}

// 本地存储管理
const loadFromStorage = (key, defaultValue = []) => {
  try {
    const userKey = getUserStorageKey(key)
    const data = localStorage.getItem(userKey)
    return data ? JSON.parse(data) : defaultValue
  } catch (error) {
    console.error(`加载${key}失败:`, error)
    return defaultValue
  }
}

const saveToStorage = (key, data) => {
  try {
    const userKey = getUserStorageKey(key)
    localStorage.setItem(userKey, JSON.stringify(data))
  } catch (error) {
    console.error(`保存${key}失败:`, error)
  }
}

// 收藏功能
const isFavorite = (app) => {
  return favoriteApps.value.some(fav => fav.id === app.id)
}

const toggleFavorite = (app) => {
  const index = favoriteApps.value.findIndex(fav => fav.id === app.id)
  if (index > -1) {
    // 取消收藏
    favoriteApps.value.splice(index, 1)
    showMessage(`已取消收藏 ${app.app_name}`, 'info')
  } else {
    // 添加收藏
    favoriteApps.value.push({
      id: app.id,
      app_name: app.app_name,
      app_desc: app.app_desc,
      icon: app.icon,
      WebUrl: app.WebUrl,
      maint: app.maint,
      app_type: app.app_type,
      open_config: app.open_config,
      form_fill_enabled: app.form_fill_enabled,
      favoriteTime: Date.now()
    })
    showMessage(`已收藏 ${app.app_name}`, 'success')
  }
  saveToStorage(STORAGE_KEYS.FAVORITES, favoriteApps.value)
  updateFilteredCategories()
}

// 最近访问功能
const addToRecent = (app) => {
  // 移除已存在的记录
  const existingIndex = recentApps.value.findIndex(recent => recent.id === app.id)
  if (existingIndex > -1) {
    recentApps.value.splice(existingIndex, 1)
  }

  // 添加到开头
  recentApps.value.unshift({
    id: app.id,
    app_name: app.app_name,
    app_desc: app.app_desc,
    icon: app.icon,
    WebUrl: app.WebUrl,
    maint: app.maint,
    app_type: app.app_type,
    open_config: app.open_config,
    form_fill_enabled: app.form_fill_enabled, // 添加表单代填字段
    accessTime: Date.now()
  })

  // 限制最近访问数量为8个
  if (recentApps.value.length > 8) {
    recentApps.value = recentApps.value.slice(0, 8)
  }

  saveToStorage(STORAGE_KEYS.RECENT, recentApps.value)
}

const removeFromRecent = (index) => {
  const removedApp = recentApps.value.splice(index, 1)[0]
  if (removedApp) {
    showMessage(`已从最近访问中移除 ${removedApp.app_name}`, 'info')
    saveToStorage(STORAGE_KEYS.RECENT, recentApps.value)
  }
}

const clearRecentApps = () => {
  recentApps.value = []
  saveToStorage(STORAGE_KEYS.RECENT, [])
  showMessage('已清空最近访问', 'info')
}

// 表单代填功能
const openFormFillDialog = (app) => {
  // 检查是否启用了表单代填
  if (app.form_fill_enabled !== 1) {
    showMessage('该应用未启用表单代填功能', 'warning')
    return
  }

  currentFormFillApp.value = app
  formFillDialogVisible.value = true
}

// 处理表单代填成功后的回调
const handleFormFillSuccess = () => {
  // 可以在这里添加成功后的处理逻辑
  console.log('表单代填设置成功')
}

// 显示分类
const showCategoryName = (name) => {
  if (name.length <= 5) {
    return name
  }
  return name.substring(0, 4) + '...'
}

// 计算icon的完整URL
const getFullIconUrl = (icon) => {
  if (!icon) return ''
  // 判断是否为完整URL（http/https/base64/svg等）
  if (/^(https?:)?\/\//.test(icon) || icon.startsWith('data:') || icon.startsWith('blob:') || icon.endsWith('.svg')) {
    return icon
  }
  // 拼接host
  return getCurrentHost() + icon
}

// 连接WebSocket
const connectWebSocket = () => {
  return new Promise((resolve, reject) => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      resolve(ws.value)
      return
    }

    const wsInstance = new WebSocket('ws://localhost:50001')
    wsConnecting.value = true

    wsInstance.onopen = () => {
      logger.log('WebSocket Connected')
      ws.value = wsInstance
      wsConnecting.value = false
      resolve(wsInstance)
    }

    wsInstance.onmessage = (evt) => {
      const response = evt.data
      if (response.startsWith('Ok')) {
        // showMessage('应用启动成功')
      } else if (response.startsWith('Failed')) {
        showMessage(response, 'error')
      }
    }

    wsInstance.onclose = () => {
      logger.log('WebSocket Disconnected')
      ws.value = null
      wsConnecting.value = false
    }

    wsInstance.onerror = (err) => {
      console.error('WebSocket Error:', err)
      // 移除这里的 showMessage 调用
      wsConnecting.value = false
      reject(err)
    }

    setTimeout(() => {
      if (wsConnecting.value) {
        wsConnecting.value = false
        wsInstance.close()
        reject(new Error('连接超时'))
      }
    }, 5000)
  })
}

// 发送打开应用消息
const sendOpenAppMessage = async(appName) => {
  return new Promise((resolve, reject) => {
    let messageReceived = false
    let timeoutId

    const connectAndSend = async() => {
      try {
        const wsInstance = await connectWebSocket()
        const message = {
          action: 3,
          msg: appName
        }

        // 设置超时检查
        timeoutId = setTimeout(() => {
          if (!messageReceived) {
            wsInstance.close()
            reject(new Error('启动超时：未收到响应'))
          }
        }, 3000) // 3秒超时

        // 添加一次性消息监听
        wsInstance.onmessage = (evt) => {
          messageReceived = true
          clearTimeout(timeoutId)

          const response = evt.data
          if (response.startsWith('Ok')) {
            resolve()
          } else {
            reject(new Error(response))
          }
        }

        wsInstance.send(JSON.stringify(message))
        logger.log('发送消息:', message)
      } catch (error) {
        clearTimeout(timeoutId)
        reject(error)
      }
    }

    connectAndSend()
  })
}

// 处理应用打开
const handleAppClick = async(item) => {
  if (((!item.WebUrl && item.app_type !== 'portal') || (!item.WebUrl && item.app_type === 'portal' && item.open_config.enabled === false)) || item.maint) return

  if (item.app_type === 'portal' && item.open_config.enabled === true) { // 门户应用 优先以打开方式为准 如果开启了打开方式应用中心首页地址作废
    item.WebUrl = ''
    item.form_fill_enabled = 0
    if (!agentApi.isClient()) {
      showMessage('请在客户端访问', 'warning')
      return
    }
  }

  // 检查是否启用了表单代填且未设置账户密码
  if (item.form_fill_enabled === 1) {
    try {
      // 获取表单代填账户信息，确保ID作为字符串传递
      const response = await getFormFillAccount(String(item.id))

      if (!response.data || response.data.errcode !== '0' || !response.data.credentials) {
        showMessage('请先设置表单代填账户信息', 'warning')
        openFormFillDialog(item)
        return
      }
    } catch (error) {
      // 如果获取失败，也提示用户设置
      showMessage('请先设置表单代填账户信息', 'warning')
      openFormFillDialog(item)
      return
    }
  }

  // 添加到最近访问
  addToRecent(item)
  if (item.WebUrl.toLowerCase().startsWith('cs:')) {
    const appName = item.WebUrl.substring(3)
    try {
      showMessage('正在启动爱尔企业浏览器...', 'info')
      await sendOpenAppMessage(appName)
      showMessage('启动成功', 'success')
    } catch (error) {
      showMessage(`启动企业浏览器失败：
      检查是否已安装企业浏览器，
      如仍然无法启动，请手动运行企业浏览器访问该应用！`, 'warning', 8000)
    }
  } else {
    if (agentApi.isClient()) { // 客户端形式打开
      const data = await openAppData(item)
      await agentApi.openResource(data)
    } else {
      window.open(item.WebUrl, '_blank')
    }
  }
}

// 新增打开方式，封装调用客户端接口时所要传递的参数
const openAppData = async(item) => {
  const data = {
    Type: 'URL', // 默认是URL
    Data: {
      URL: item.WebUrl,
      OpenExplorer: []
    }
  }
  if (item.open_config.enabled) { // 如果开启了打开方式
    if (item.open_config.open_type === 'browser') { // 浏览器打开
      const OpenExplorer = []
      for (let i = 0; i < item.open_config.browser_configs.length; i++) {
        OpenExplorer.push({
          ExplorerName: item.open_config.browser_configs[i].type,
          ExplorerParam: item.open_config.browser_configs[i].params
        })
      }
      data.Data.OpenExplorer = OpenExplorer
    } else { // app形式打开
      data.Type = 'APP'
      if (item.open_config.open_type === 'client') { // 指定程序
        const appData = {
          Windows: {
            AppName: '',
            AppPath: '',
            AppParam: '',
            NotFoundMsg: ''
          },
          MacOS: {
            AppName: '',
            AppPath: '',
            AppParam: '',
            NotFoundMsg: ''
          },
          Linux: {
            AppName: '',
            AppPath: '',
            AppParam: '',
            NotFoundMsg: ''
          }
        }
        for (let i = 0; i < item.open_config.program_configs.length; i++) {
          switch (item.open_config.program_configs[i].os) {
            case 'windows':
              appData.Windows.AppName = item.open_config.program_configs[i].name
              appData.Windows.AppPath = item.open_config.program_configs[i].path
              appData.Windows.AppParam = item.open_config.program_configs[i].params
              appData.Windows.NotFoundMsg = item.open_config.program_configs[i].notFoundMessage
              break
            case 'macos':
              appData.MacOS.AppName = item.open_config.program_configs[i].bundleId
              appData.MacOS.AppParam = item.open_config.program_configs[i].params
              appData.MacOS.NotFoundMsg = item.open_config.program_configs[i].notFoundMessage
              break
            case 'linux':
              appData.Linux.AppName = item.open_config.program_configs[i].name
              appData.Linux.AppPath = item.open_config.program_configs[i].path
              appData.Linux.AppParam = item.open_config.program_configs[i].params
              appData.Linux.NotFoundMsg = item.open_config.program_configs[i].notFoundMessage
              break
          }
        }
        data.Data = appData
      } else if (item.open_config.open_type === 'system') {
        const appData = {
          Windows: {
            AppName: '',
            AppPath: ''
          },
          MacOS: {
            AppName: '',
            AppPath: ''
          },
          Linux: {
            AppName: '',
            AppPath: ''
          }
        }
        for (let i = 0; i < item.open_config.system_app_configs.length; i++) {
          switch (item.open_config.system_app_configs[i].os) {
            case 'windows':
              appData.Windows.AppName = item.open_config.system_app_configs[i].type
              appData.Windows.AppPath = item.open_config.system_app_configs[i].type
              break
            case 'macos':
              appData.MacOS.AppName = item.open_config.system_app_configs[i].type
              appData.MacOS.AppPath = item.open_config.system_app_configs[i].type
              break
            case 'linux':
              appData.Linux.AppName = item.open_config.system_app_configs[i].type
              appData.Linux.AppPath = item.open_config.system_app_configs[i].type
              break
          }
        }
        data.Data = appData
      }
    }
  }
  return data
}

// 处理刷新按钮点击
const handleRefreshClick = async() => {
  // 在客户端模式下，除了刷新应用数据，还要通知主页面刷新隧道状态
  if (agentApi.isClient()) {
    await clientToken(userStore)
    logger.log('客户端模式：发送隧道状态刷新事件')
    emitter.emit('refreshTunnelStatus', {
      timestamp: Date.now(),
      source: 'app-refresh-btn'
    })
  }

  // 执行原有的刷新逻辑
  await initCategories()
}

// 组件卸载时关闭WebSocket连接
onUnmounted(() => {
  if (ws.value) {
    ws.value.close()
    ws.value = null
  }
})

// 获取随机背景色，基于应用名生成固定颜色
const getRandomColor = (appName) => {
  const colors = [
    '#71BDDF', // 浅蓝
    '#8AB05D', // 浅绿
    '#9571DF', // 浅紫
    '#DF7171', // 浅红
    '#DFC271', // 浅黄
    '#71DFA7', // 薄荷绿
    '#B05D8A', // 浅粉
    '#5D8AB0' // 深蓝
  ]

  let sum = 0
  for (let i = 0; i < appName.length; i++) {
    sum += appName.charCodeAt(i)
  }

  return colors[sum % colors.length]
}

// 处理图标加载失败
const handleIconError = async(item) => {
  // 为当前应用项设置图标加载失败标记
  item.iconError = true
  // 使用 nextTick 确保 DOM 更新
  await nextTick()
}

// 判断是否在收藏分类
const isFavoriteCategory = computed(() => {
  return selectedCategory.value === -1
})

// 更新过滤后的分类列表
const updateFilteredCategories = () => {
  if (selectedCategory.value === -1) {
    // 收藏分类
    if (favoriteApps.value.length > 0) {
      filteredCategories.value = [{
        id: -1,
        name: '我的收藏',
        apps: favoriteApps.value
      }]
    } else {
      filteredCategories.value = [{
        id: -1,
        name: '我的收藏',
        apps: []
      }]
    }
  } else if (selectedCategory.value === null || selectedCategory.value === 0) {
    // 全部分类 - 合并所有应用到一个分类中显示
    const allApps = []
    const appIds = new Set() // 用于去重

    categoryList.value.forEach(category => {
      if (category.apps && category.apps.length > 0) {
        category.apps.forEach(app => {
          // 使用id去重，避免同一应用在多个分类中重复显示
          if (!appIds.has(app.id)) {
            appIds.add(app.id)
            allApps.push(app)
          }
        })
      }
    })

    filteredCategories.value = [{
      id: 0,
      name: '全部应用',
      apps: allApps
    }]
  } else {
    // 特定分类
    const selectedCategories = categoryList.value.filter(category =>
      category.id === selectedCategory.value
    )
    // 如果当前选中的分类不存在于新数据中，回退到"全部"分类
    if (selectedCategories.length === 0) {
      console.warn('当前选中的分类不存在，回退到全部分类')
      selectedCategory.value = null
      defaultActiveIndex.value = '0'
      // 递归调用以应用"全部"分类的逻辑
      updateFilteredCategories()
      return
    }
    filteredCategories.value = selectedCategories
  }
}

// 添加分类切换处理函数
const handleCategoryChange = (categoryId) => {
  if (categoryId === -1) {
    selectedCategory.value = -1
    defaultActiveIndex.value = '-1'
  } else if (categoryId === null || categoryId === 0) {
    selectedCategory.value = null
    defaultActiveIndex.value = '0'
  } else {
    selectedCategory.value = parseInt(categoryId)
    defaultActiveIndex.value = categoryId.toString()
  }
  updateFilteredCategories()
}

// 添加搜索处理函数
const handleSearch = () => {
  if (!searchQuery.value) {
    updateFilteredCategories()
    return
  }

  const searchText = searchQuery.value.toLowerCase().trim()

  if (selectedCategory.value === -1) {
    // 在收藏中搜索
    const filteredFavorites = favoriteApps.value.filter(app =>
      app.app_name.toLowerCase().includes(searchText)
    )
    filteredCategories.value = [{
      id: -1,
      name: '我的收藏',
      apps: filteredFavorites
    }]
  } else {
    // 在所有分类或特定分类中搜索
    if (selectedCategory.value === null || selectedCategory.value === 0) {
      // 在全部分类中搜索 - 合并所有匹配的应用
      const allMatchedApps = []
      const appIds = new Set() // 用于去重

      categoryList.value.forEach(category => {
        if (category.apps && category.apps.length > 0) {
          const matchedApps = category.apps.filter(app =>
            app.app_name.toLowerCase().includes(searchText)
          )
          matchedApps.forEach(app => {
            // 使用id去重，避免同一应用在多个分类中重复显示
            if (!appIds.has(app.id)) {
              appIds.add(app.id)
              allMatchedApps.push(app)
            }
          })
        }
      })

      filteredCategories.value = [{
        id: 0,
        name: '全部应用',
        apps: allMatchedApps
      }]
    } else {
      // 在特定分类中搜索
      const categoriesToSearch = categoryList.value.filter(category => category.id === selectedCategory.value)
      filteredCategories.value = categoriesToSearch
        .map(category => ({
          ...category,
          apps: category.apps.filter(app =>
            app.app_name.toLowerCase().includes(searchText)
          )
        }))
        .filter(category => category.apps.length > 0)
    }
  }
}

const initCategories = async() => {
  isLoading.value = true
  try {
    const { data: apiResponse } = await getCurrentUser()
    logger.log('API返回数据:', apiResponse)

    if (apiResponse.code === 0 && apiResponse.data) {
      const formattedData = apiResponse.data.map((item, index) => ({
        id: index + 1,
        name: item.category,
        apps: item.apps.map(app => ({
          id: app.id,
          app_name: app.portal_show_name ? app.portal_show_name : app.app_name,
          app_desc: app.portal_desc ? app.portal_desc : (app.app_type === 'web' ? 'Web应用' : app.app_type === 'tun' ? '隧道应用' : '应用程序'),
          icon: app.icon,
          maint: app.app_status === 2,
          app_type: app.app_type,
          app_sites: app.app_sites,
          WebUrl: app.WebUrl, // 确保 WebUrl 字段被保留
          form_fill_enabled: app.form_fill_enabled, // 表单代填启用状态
          open_config: app.open_config
        }))
      }))

      logger.log('格式化后的数据:', formattedData)
      categoryList.value = formattedData

      // 验证当前选中的分类是否仍然有效
      if (selectedCategory.value !== null && selectedCategory.value !== -1 && selectedCategory.value !== 0) {
        const categoryExists = formattedData.some(cat => cat.id === selectedCategory.value)
        if (!categoryExists) {
          console.warn('当前选中的分类不存在于新数据中，重置为全部分类')
          selectedCategory.value = null
          defaultActiveIndex.value = '0'
        }
      }

      // 重新计算当前选中分类的过滤内容
      updateFilteredCategories()
    } else if (apiResponse.code === 0 && apiResponse.data === null) {
      // 处理访问策略被禁用的情况，data为null
      categoryList.value = []
      updateFilteredCategories()
    }
  } catch (error) {
    console.error('API调用出错:', error)
  } finally {
    isLoading.value = false
    isInitialized.value = true
  }
}

// 初始化收藏和最近访问数据
const initStorageData = () => {
  // 确保用户信息已加载
  if (!userStore.userInfo?.id && !userStore.userInfo?.name) {
    console.warn('用户信息未加载，跳过存储数据初始化')
    return
  }

  favoriteApps.value = loadFromStorage(STORAGE_KEYS.FAVORITES, [])
  recentApps.value = loadFromStorage(STORAGE_KEYS.RECENT, [])
  logger.log('加载用户收藏应用:', favoriteApps.value.length, '个')
  logger.log('加载用户最近访问:', recentApps.value.length, '个')
  logger.log('当前用户ID:', userStore.userInfo?.id || userStore.userInfo?.name)
}

// 页面加载时读取存储
onMounted(async() => {
  const savedViewType = localStorage.getItem('appViewType')
  if (savedViewType && ['standard', 'compact'].includes(savedViewType)) {
    viewType.value = savedViewType
  }

  // 初始化时设置默认为"全部"分类
  selectedCategory.value = null
  defaultActiveIndex.value = '0'

  await initCategories()

  // 在用户信息加载后初始化本地存储数据
  initStorageData()
})
const userStore = useUserStore()
const route = useRoute()
const cli_query = route.query
// 获取 corpID，在本地文件环境下使用默认值
let corpID = null
try {
  // 检查是否为本地文件协议
  if (!agentApi.isClient()) {
    const req = new XMLHttpRequest()
    req.open('GET', document.location, false)
    req.send(null)
    corpID = req.getResponseHeader('X-Corp-ID')
  }
} catch (error) {
  console.warn('无法获取 X-Corp-ID header，使用默认值:', error)
}
const clineData = {
  action: 0,
  msg: {
    token: userStore.token.accessToken,
    refreshToken: userStore.token.refreshToken,
    realm: corpID || 'default',
  },
  platform: document.location.hostname
}

// 非客户端则通知客户端
if (!agentApi.isClient()) {
  const port = cli_query.wp || 50001
  const websocket = ref({})
  const wsUrl = ref(`ws://127.0.0.1:${port}`)
  const platform = navigator.platform
  // if (platform.indexOf('Mac') === 0 || platform === 'MacIntel') {
  //   wsUrl.value = `wss://127.0.0.1:${port}`
  // }
  const initWebSocket = () => {
    websocket.value = new WebSocket(wsUrl.value)
    websocket.value.onopen = () => {
      logger.log('socket连接成功')
      sendMessage(JSON.stringify(clineData))
      // 拉起客户端
      /*
      if (cli_query.type === 'client') {
        sendMessage(JSON.stringify({
          action: 4,
          msg: '',
          platform: document.location.hostname
        }))
      }
      */
    }
    websocket.value.onmessage = async(e) => {
      try {
        logger.log('收到消息', e.data)
        // 尝试解析JSON消息
        const message = JSON.parse(e.data)
        logger.log('解析后的消息:', message)

        if (message.Type === 'Request') {
          if (message.Action === 'Logout') {
            await userStore.LoginOut()
            logger.log('浏览器退出登录完成')
          } else if (message.Action === 'Login') {
            logger.log('收到Login请求')
          } else {
            logger.log('收到未知Action请求:', message.Action)
          }
        }
      } catch (error) {
        // 如果不是JSON格式
        logger.log('消息解析失败，按字符串处理:', error.message)
      }
    }
    websocket.value.onerror = () => {
      logger.log('socket连接错误:' + wsUrl.value)
      window.location.href = `asecagent://?web=${JSON.stringify(clineData)}`
    }
  }
  // 发送消息
  const sendMessage = (msg) => {
    logger.log(msg, '0')
    websocket.value.send(msg)
  }
  // 关闭链接（在页面销毁时可销毁链接）
  const closeWebSocket = () => {
    logger.log('socket断开链接')
    websocket.value.close()
  }
  logger.log(`asecagent://?web=${JSON.stringify(clineData)}`)
  initWebSocket()
}

// 监听视图变化并持久化存储
watch(() => viewType.value, (newVal) => {
  localStorage.setItem('appViewType', newVal)
})

// 监听用户信息变化，重新加载用户特定的存储数据
watch(() => userStore.userInfo?.id, (newUserId, oldUserId) => {
  if (newUserId && newUserId !== oldUserId) {
    logger.log('用户切换，重新加载存储数据:', { oldUserId, newUserId })
    initStorageData()
    updateFilteredCategories()
  }
}, { immediate: false })

// 监听用户token变化，当用户注销时清理数据
watch(() => userStore.token, (newToken, oldToken) => {
  if (oldToken && !newToken) {
    // 用户注销，清理当前用户数据
    logger.log('用户注销，清理存储数据')
    favoriteApps.value = []
    recentApps.value = []
    updateFilteredCategories()
  }
}, { immediate: false })

// 根据应用数量动态添加网格类名（兼容Chrome 83）
const getGridClass = (category) => {
  const appCount = category.apps ? category.apps.length : 0
  if (viewType.value === 'compact') {
    // 紧凑模式：1-5个应用时限制最大宽度
    if (appCount <= 5) {
      return 'apps-grid-limited'
    }
  } else {
    // 标准模式：1-2个应用时限制最大宽度
    if (appCount <= 2) {
      return 'apps-grid-limited'
    }
  }
  return ''
}

</script>
<style lang="scss" scoped>
.app-page-root {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%; // 确保占满可用宽度
  max-width: 100%; // 防止超出容器
}

.person {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border-radius: 4px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  width: 100%; // 确保占满父容器宽度
  max-width: 100%; // 防止超出

  :deep(.base-header--shadow) {
    box-shadow: none
  }

  .app-header {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    background: #ffffff;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;

    :deep(.base-input) {
      padding: 5px 12px 5px 36px;
    }

    .header-left {
      .page-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2329;
        line-height: 28px;
      }
    }

    .header-right {
      .search-controls {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;

        .search-icon {
          width: 14px;
          height: 14px;
          position: relative;
          transform: rotateX(180deg);
          margin-right: -24px;
          z-index: 1000;
          color: #b3b6c1 !important;
        }

        .search-input {
          width: 200px;
          height: 28px;
          margin-right: 10px;

          :deep(.base-input__wrapper) {
            border-radius: 6px;
            background-color: #f7f8fa;
            border: 1px solid transparent;
            transition: all 0.2s;
            box-sizing: border-box;

            &:hover {
              background-color: #ffffff;
              border-color: #d0d7de;
            }

            &.is-focus {
              background-color: #ffffff;
              border-color: #536ce6;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
          }

          :deep(.base-input__inner) {
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            color: #1f2329;

            &::placeholder {
              color: #8a919f;
            }
          }
        }

        .refresh-btn {
          width: 28px;
          height: 28px;
          padding: 0;
          margin-right: 10px;
          border-radius: 4px;
          background: #f5f5f7;
          color: #686e84;
          display: flex;
          align-items: center;
          justify-content: center;

          .refresh-btn-icon {
            width: 14px;
            height: 14px;
          }

          &:hover {
            background: #ffffff;
            border-color: #d0d7de;
            color: #1f2329;
          }
        }

        .view-select {
          width: 70px;
          height: 20px;

          :deep(.base-select__input) {
            padding: 0px;
            border: none;
          }

          :deep(.base-select__dropdown) {
            width: 88px;
            padding: 7px 7px 3px 7px;
          }

          :deep(.base-option) {
            padding: 4px 8px 4px 8px;
            font-size: 14px;
            font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑";
            background: #f5f5f7;
            border-radius: 4px;
            margin-bottom: 4px;
          }

          :deep(.base-option.is-selected) {
            color: #ffffff;
            background: #536ce6;
            border-radius: 4px;
          }

          :deep(.base-select__wrapper) {
            border-radius: 6px;
            background-color: #f7f8fa;
            border: 1px solid transparent;
            height: 36px;

            &:hover {
              background-color: #ffffff;
              border-color: #d0d7de;
            }
          }
        }
      }
    }

    .search-input {
      width: 200px; // 设置搜索框宽度
      height: 28px;

      :deep(.el-input__wrapper) {
        border-radius: 4px;
        background-color: #f5f7fa;

        &.is-focus {
          background-color: #ffffff;
        }
      }

      :deep(.el-input__inner) {
        height: 32px;
        line-height: 32px;
        font-size: 14px;

        &::placeholder {
          color: #909399;
        }
      }
    }
  }

  .flex-container {
    flex: 1;
    min-height: 0;
    display: flex;
    height: 100%; // 确保容器占满可用高度
    width: 100%; // 确保容器占满可用宽度
    max-width: 100%; // 防止超出父容器

    &.flex-row {
      flex-direction: row;
    }
  }

  .category-aside {
    flex: 0 0 104px; // 固定宽度，不收缩不伸展
    width: 104px; // 明确指定宽度
    height: 100%; // 占满父容器高度
    border-bottom: none;
    // border-right: 1px solid #e5e6eb; // 添加右侧灰色分隔线
    overflow: hidden; // 防止内容溢出
    min-width: 104px; // 确保最小宽度

    :deep(.base-menu--vertical) {
      width: 100%;
      height: 100%; // 确保菜单占满高度
    }

    .category-menu {
      border-right: none;
      background: transparent;
      padding: 0 8px 8px 8px; // 左边距8px，右边距8px，上下8px
      height: 100%; // 菜单高度自适应
      box-sizing: border-box; // 包含padding在内计算高度
      overflow-y: auto; // 如果内容过多，允许垂直滚动
      overflow-x: hidden; // 隐藏水平滚动条

      &::-webkit-scrollbar {
        width: 6px;
      }

      .category-menu-item {
        width: 88px; // 104px - 8px(左) - 8px(右) = 88px可用宽度
        height: 28px;
        flex: 0 0 auto; // 防止菜单项被压缩
      }

      :deep(.base-menu-item__content) {
        padding: 0px;
        height: 28px;
        line-height: 28px;
        justify-content: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%; // 确保内容占满可用宽度
        box-sizing: border-box; // 包含padding在内计算宽度
        text-align: center; // 文本居中对齐
      }

      :deep(.base-menu-item) {
        margin: 8px 4px 8px 0px;
        font-size: 14px;
        color: #4e5969;
        border-radius: 6px;
        transition: all 0.2s ease;
        cursor: pointer;
        width: 88px; // 明确指定菜单项宽度
        min-height: 28px; // 确保最小高度
        display: flex; // 使用flex布局
        align-items: center; // 垂直居中
        justify-content: center; // 水平居中

        // 默认状态

        &:not(.base-menu-item--active) {
          background-color: transparent;
          color: #4e5969;
        }

        // 悬浮状态

        &:hover:not(.base-menu-item--active) {
          background-color: #f5f5f7;

          .base-menu-item__content {
            color: #686e84;
          }
        }

        // 选中状态

        &.base-menu-item--active {
          background-color: #536ce6;
          color: #ffffff;
          font-weight: 500;
        }

        // 点击状态

        &:active {
          background-color: #3370ff;
        }
      }
    }
  }

  .app-main {
    flex: 1; // 占用剩余空间
    min-height: 0;
    min-width: 0; // 防止内容溢出
    width: auto; // 自动计算宽度，不强制指定
    max-width: calc(100% - 104px); // 确保不超出可用空间（固定104px左侧宽度）
    height: 100%; // 占满容器高度
    padding: 10px 0px 24px 8px; // 调整左边距从24px改为8px
    overflow-y: auto;
    overflow-x: hidden; // 防止水平溢出
    box-sizing: border-box; // 包含padding在内计算宽度
    background: #ffffff;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;

    .category-section {
      margin-bottom: 16px; // 恢复分类间的间距，确保视觉分离
      margin-top: 0; // 紧贴最近访问区域，无额外间距

      &:last-child {
        margin-bottom: 0; // 最后一个分类无需底部间距
      }

      /* 分类标题相关样式已移除，由左侧 category-aside 控制 */

      .apps-grid {
        display: -ms-grid;
        display: grid;
        gap: 8px;
        padding: 0 16px 0 0; // 给右边添加小的内边距，防止紧贴滚动条
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;

        &.view-standard {
          // 默认自适应列布局：最小210px，当有多个应用时自适应拉伸
          grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
          gap: 8px;
          justify-content: start;

          // 使用类名而不是:has()选择器 - 当只有1-2个应用时，限制最大宽度

          &.apps-grid-limited {
            grid-template-columns: repeat(auto-fit, minmax(210px, 228px));
          }

          .app-item {
            width: 100%;
            height: 64px;
            background: #f7f7fa;
            border: 1px solid #f2f2f5;
            border-radius: 4px;

            .app-collect-icon {
              width: 16px;
              height: 15px;
              position: absolute;
              color: #b3b6c1 !important;
              display: none;
              top: -4px;
              right: 20px;

              // 已收藏状态的橙色样式

              &.base-icon--yishoucang {
                color: #ffbf00 !important;
              }

              // 未收藏状态始终保持灰色，包括悬停时

              &.base-icon--shoucang {
                color: #b3b6c1 !important;

                &:hover {
                  color: #b3b6c1 !important;
                }
              }
            }

            .app-form-fill-icon {
              width: 16px;
              height: 15px;
              position: absolute;
              color: #b3b6c1 !important;
              display: none;
              top: 26px; // 在收藏按钮下方
              right: 20px; // 与收藏按钮对齐

              &:hover {
                color: #007bff !important;
              }
            }

            .app-content {
              display: flex;
              flex-direction: row;
              text-align: left;
              height: 40px;
              margin: 12px;

              .app-icon {
                margin-bottom: 0;
                margin-right: 12px;
                margin-top: 6px;

                :deep(.avatar-text) {
                  font-size: 14px;
                }
              }

              .app-details {
                display: flex;
                flex-direction: column; // 保持文字内容垂直排列
                justify-content: center; // 垂直居中

                .app-name {
                  font-size: 14px;
                  line-height: 20px;
                  color: #282a33;
                  height: 20px;

                  .app-name-text {
                    display: inline-block;
                    min-width: 56px;
                    height: 20px;
                    max-width: 150px; // 适应228px宽度的卡片
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 14px;
                    font-family: "PingFang SC", "PingFang SC-Medium", "Microsoft YaHei", "微软雅黑";
                    font-weight: 500;
                    text-align: left;
                    color: #282a33;
                    line-height: 20px;
                  }
                }

                .app-desc {
                  margin-top: 2px;
                  font-size: 12px;
                  color: #8a919f;
                  line-height: 16px;
                  min-height: 16px;
                  text-align: left;
                  cursor: pointer;
                  user-select: none;

                  .app-desc-text {
                    display: inline-block;
                    width: 100%;
                    overflow: hidden;
                    font-size: 12px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 150px;
                    cursor: pointer;
                    user-select: none;
                  }
                }
              }
            }

            &:hover:not(.disabled) {
              .app-collect-icon {
                display: block;
              }

              .app-form-fill-icon {
                display: block;
              }
            }

            // 当应用已收藏时，也显示表单代填按钮

            .app-collect-icon.base-icon--yishoucang {
              display: block !important; // 已收藏的图标始终显示
            }
          }
        }

        &.view-compact {
          // 默认自适应列布局：最小110px，当有多个应用时自适应拉伸
          grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
          gap: 8px;

          // 使用类名而不是:has()选择器 - 当只有1-5个应用时，限制最大宽度为120px

          &.apps-grid-limited {
            grid-template-columns: repeat(auto-fit, minmax(110px, 120px));
          }

          .app-item {
            width: 100%;
            height: 64px;
            background: #f7f7fa;
            border: 1px solid #f2f2f5;
            border-radius: 4px;

            .app-collect-icon {
              width: 16px;
              height: 15px;
              position: absolute;
              color: #b3b6c1 !important;
              display: none;
              top: 8px;
              right: 8px;

              // 已收藏状态的橙色样式

              &.base-icon--yishoucang {
                color: #ffbf00 !important;
              }

              // 未收藏状态始终保持灰色，包括悬停时

              &.base-icon--shoucang {
                color: #b3b6c1 !important;

                &:hover {
                  color: #b3b6c1 !important;
                }
              }
            }

            .app-form-fill-icon {
              width: 16px;
              height: 15px;
              position: absolute;
              color: #b3b6c1 !important;
              display: none;
              top: 38px; // 在收藏按钮下方
              right: 8px; // 与收藏按钮对齐

              &:hover {
                color: #007bff !important;
              }
            }

            .app-content {
              display: flex;
              flex-direction: column;
              align-items: center;

              .app-icon {
                margin: 10px 0px 0px 0px;
                -webkit-flex-shrink: 0;
                -ms-flex-negative: 0;
                flex-shrink: 0;

                :deep(.avatar-text) {
                  font-size: 14px;
                }
              }

              .app-details {
                -webkit-box-flex: 1;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
                text-align: left;

                .app-name {
                  font-size: 14px;
                  line-height: 20px;
                  font-weight: 500;
                  max-width: 98px;
                }
              }
            }

            &:hover:not(.disabled) {
              .app-collect-icon {
                display: block;
              }

              .app-form-fill-icon {
                display: block;
              }
            }

            // 当应用已收藏时，也显示表单代填按钮（紧凑视图）

            .app-collect-icon.base-icon--yishoucang {
              display: block !important; // 已收藏的图标始终显示
            }
          }
        }

        .app-item {
          background: #f7f7fa;
          border: 1px solid #f2f2f5;
          border-radius: 8px;
          position: relative;
          cursor: pointer;
          transition: all 0.2s ease;
          overflow: hidden;

          .app-collect-icon {
            width: 16px;
            height: 15px;
            position: absolute;
            color: #b3b6c1 !important;
            display: none;
            transition: color 0.2s ease;

            // 已收藏状态的橙色样式

            &.base-icon--yishoucang {
              color: #ffbf00 !important;
            }

            // 未收藏状态始终保持灰色，包括悬停时

            &.base-icon--shoucang {
              color: #b3b6c1 !important;

              &:hover {
                color: #b3b6c1 !important;
              }
            }
          }

          &:hover:not(.disabled) {
            border-color: #536ce6;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            transform: translateY(-2px);

            .app-collect-icon {
              display: block;
            }

            .app-form-fill-icon {
              display: block;
            }
          }

          .app-content {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            position: relative;

            .app-icon {
              :deep(.base-avatar) {
                color: #ffffff;
                font-size: 16px;
                font-weight: 500;
                border-radius: 8px;

                &.default-avatar {
                  background-color: #f0f2f5 !important;
                  color: #909399;
                }
              }
            }

            .app-details {
              .app-name {
                color: #1f2329;
                font-weight: 500;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          .status-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            background-color: #ff9500;
            color: #ffffff;
            font-weight: 500;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          // 紧凑视图的三角形维护中标签

          .status-badge-compact {
            top: 0;
            left: 0;
            right: auto;
            width: 0;
            height: 0;
            padding: 0;
            border-radius: 0;
            background: transparent;
            box-shadow: none;

            // 创建三角形背景

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 0;
              height: 0;
              border-left: 38px solid #f1f1f2;
              border-bottom: 38px solid transparent;
              z-index: 1;
            }

            // 文字内容

            &::after {
              content: '维护中';
              position: absolute;
              top: 22px;
              left: -3px;
              font-size: 12px;
              color: #686e84;
              font-weight: 500;
              transform: rotate(-45deg);
              transform-origin: left top;
              z-index: 2;
              white-space: nowrap;
            }

            // 隐藏原始文字
            color: transparent;
            font-size: 0;
          }

          // 标准视图的行内维护中标签

          .status-badge-inline {
            height: 20px;
            line-height: 18px;
            display: inline-block;
            position: relative;
            top: -8px;
            margin-left: 10px;
            padding: 1px 4px 1px 4px;
            border-radius: 2px;
            font-size: 12px;
            background-color: #ededf1;
            color: #686e84;
          }

          // 维护中状态（不可点击）

          &.disabled {
            cursor: not-allowed;
            opacity: 0.6;

            &:hover {
              border-color: #e5e6eb;
              box-shadow: none;
              transform: none;
            }

            .app-content {
              .app-icon :deep(.base-avatar) {
                filter: grayscale(100%);
              }

              .app-details .app-name {
                color: #8a919f;
              }
            }
          }
        }
      }
    }

    // 所有分类区域保持一致的间距
    /* .category-section:first-child 特殊处理已移除 */
  }
}

.app-header {
  .el-recent-access {
    padding: 0px 0px 0px 8px;
    width: 56px;
    height: 20px;
    font-size: 14px;
    font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑";
    font-weight: Medium;
    text-align: left;
    color: #282a33;
    line-height: 20px;
  }

  .el-recent-data {
    margin-left: 15px;
    padding-left: 16px;
    height: 20px;
  }

  .el-recent-item {
    margin-top: 2px;
    margin-right: 6px;
    height: 25px;
    padding: 4px 0px 4px 6px;
    background: #f5f6fe;
    border-radius: 4px;
    font-size: 12px;
    font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑";
    font-weight: Regular;
    color: #536ce6;
    cursor: pointer;
  }

  .el-recent-icon {
    opacity: 0.6;
    width: 8px;
    height: 8px;
    margin: 8px 6px 8px 5px;
  }

  .el-recent-clear {
    opacity: 0.6;
    margin-top: 6px;
    position: absolute;
    width: 14px;
    height: 14px;
    right: 20px;
    cursor: pointer;
  }

  .el-recent-empty {
    color: #b3b6c1;
    font-size: 14px;
    height: 20px;
  }
}

// 收藏图标的全局样式 - 移至app-item内部定义以提高特异性
// .app-collect-icon {
//   width: 16px;
//   height: 15px;
//   position: absolute;
//   color: #b3b6c1 !important;
//   display: none;
// }

.base-icon--yishoucang {
  color: #ffbf00 !important;
}

// 响应式布局 宽度小于1200px时 - 仅保留非网格相关样式
@media screen and (max-width: 1200px) {
  .person {
    .app-header {

      .header-right .search-controls {

        .search-input {
          width: 200px;
          height: 28px;
        }
      }
    }

    .app-main {
      padding: 10px 0px 16px 8px; // 调整左边距从16px改为8px，与桌面版保持一致
      flex: 1;
      min-height: 0;

      .apps-grid {
        &.view-compact {
          grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
          gap: 8px;

          // 使用类名而不是:has()选择器 - 当只有1-5个应用时，限制最大宽度

          &.apps-grid-limited {
            grid-template-columns: repeat(auto-fit, minmax(110px, 120px));
          }

          .app-item {
            height: 64px;

            .app-content {
              .app-icon {
                margin-bottom: 8px;

                :deep(.base-avatar) {
                  width: 24px;
                  height: 24px;
                }
              }

              .app-details .app-name {
                font-size: 12px;
                line-height: 16px;
                max-width: 98px;
              }
            }

            .app-collect-icon {
              width: 16px;
              height: 15px;
              position: absolute;
              color: #b3b6c1 !important;
              display: none;
              top: 8px;
              right: 8px;

              // 已收藏状态的橙色样式

              &.base-icon--yishoucang {
                color: #ffbf00 !important;
              }

              // 未收藏状态始终保持灰色，包括悬停时

              &.base-icon--shoucang {
                color: #b3b6c1 !important;

                &:hover {
                  color: #b3b6c1 !important;
                }
              }
            }

            .app-form-fill-icon {
              width: 16px;
              height: 15px;
              position: absolute;
              color: #b3b6c1 !important;
              display: none;
              top: 8px;
              right: 32px; // 在收藏按钮右边

              &:hover {
                color: #007bff !important;
              }
            }

            &:hover:not(.disabled) {
              .app-collect-icon {
                display: block;
              }

              .app-form-fill-icon {
                display: block;
              }
            }

            // 当应用已收藏时，也显示表单代填按钮（另一个紧凑视图）

            .app-collect-icon.base-icon--yishoucang {
              display: block !important; // 已收藏的图标始终显示
            }
          }
        }
      }
    }
  }
}

// 移动端样式调整
@media screen and (max-width: 768px) {
  .person {
    .app-header {
      flex-direction: column;
      align-items: stretch;

      .header-left {
        margin-bottom: 16px;
        display: none;

        .page-title {
          font-size: 18px;
        }
      }

      .header-right .search-controls {
        justify-content: space-between;

        .search-input {
          flex: 1;
          max-width: none;
        }
      }
    }

    .base-container {
      flex-direction: column;

      .category-aside {
        width: 100% !important;
        display: none;

        .category-menu {
          display: flex;
          overflow-x: auto;
          padding: 12px 16px;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */

          &::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
          }

          :deep(.base-menu-item) {
            flex-shrink: 0;
            margin: 0 4px;
            white-space: nowrap;
            min-width: 60px;
            max-width: 120px;

            .category-menu-text {
              max-width: 100px;
            }
          }
        }
      }
    }

    .app-main {
      padding: 10px 0px 16px 8px;

      /* 移动端分类标题样式已移除 */

      .apps-grid {
        &.view-standard {
          // 移动端：保持自适应，但调整样式

          .app-item {
            width: 100%;
            height: 64px;

            .app-content {
              padding: 8px 4px;

              .app-details {
                .app-name {
                  font-size: 11px;
                  line-height: 14px;
                }

                .app-desc {
                  display: none;
                }
              }
            }
          }
        }

        &.view-compact .app-item {
          height: 56px;

          .app-content {
            padding: 8px 12px;

            .app-icon {
              margin-right: 8px;

              :deep(.base-avatar) {
                width: 28px !important;
                height: 28px !important;
              }
            }
          }
        }
      }
    }
  }
}

.tooltip-content {
  width: 200px;
  text-align: center;
}

.web-link {
  color: #536ce6;
  text-decoration: none;
  word-break: break-all;
}

.web-link:hover {
  text-decoration: underline;
}

.el-select__popper {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 2px 20px 0px rgba(46, 60, 128, 0.10);
  margin-left: -10px !important;
  right: 15px;
  top: 110px;
  max-width: 88px;

  .el-select-dropdown__item {
    width: 72px;
    height: 28px;
    border-radius: 4px;
    margin-left: 7px;
    margin-bottom: 4px;
    padding: 0 8px 0 8px;
    font-size: 14px;
    font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑";
    line-height: 20px;
    display: flex;
    align-items: center;
    background: #f5f5f7 !important;
  }

  .el-select-dropdown__item.selected {
    color: #ffffff;
    background: #536ce6 !important;
  }
}

.text-center {
  text-align: center;
}

.web-link {
  color: #536ce6;
  text-decoration: none;
}

.web-link:hover {
  text-decoration: underline;
}

/* 连接状态相关样式 */
.connected-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.disconnected-content {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
}

.no-connection-wrapper {
  text-align: center;
  padding: 40px;

  .no-connection-image {
    margin-bottom: 24px;

    img {
      width: 222px;
      height: 120px;
      opacity: 0.8;
    }
  }

  .no-connection-text {
    h3 {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }
}

/* 无应用状态样式 */
.no-apps-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.no-apps-content {
  text-align: center;

  .no-apps-image {
    margin-bottom: 16px;

    img {
      width: 222px;
      height: 120px;
      opacity: 0.8;
    }
  }

  .no-apps-text {
    font-size: 14px;
    color: #999;
    font-weight: 400;
  }
}
</style>
<style>
.el-message {
  white-space: pre-line !important;
  line-height: 1.5 !important;
  padding: 12px 20px !important;
}
</style>
