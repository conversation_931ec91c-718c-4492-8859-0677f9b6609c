package dto

import (
	"asdsec.com/asec/platform/app/console/common/utils"
	"asdsec.com/asec/platform/pkg/model"
)

type GetReadEventConditionRsp struct {
	FileType      []FileType      `json:"file_type"`
	SensitiveInfo []SensitiveInfo `json:"sensitive_info"`
}

type FileType struct {
	Code  string `json:"code"`
	Name  string `json:"name"`
	Count int    `json:"count"`
}

type SensitiveInfo struct {
	SensitiveRuleId   string `json:"sensitive_rule_id"`
	SensitiveLevel    int    `json:"sensitive_level"`
	SensitiveRuleName string `json:"sensitive_rule_name"`
	Count             int    `json:"count"`
}

type GetReadEventListRsp struct {
	model.CommonPage
	Events     []ReadEvents `json:"events"`
	EventCount int          `json:"event_count"`
}

type ReadEvents struct {
	FileName          string          `gorm:"column:file_name" json:"file_name"`
	FilePath          string          `gorm:"column:file_path" json:"file_path"`
	FileSize          int64           `gorm:"column:file_size" json:"file_size"`
	Activity          string          `gorm:"column:activity" json:"activity"`
	OccurTime         utils.FrontTime `gorm:"column:occur_time;type:timestamptz" json:"occur_time"`
	UserName          string          `gorm:"column:user_name" json:"user_name"`
	SensitiveRuleId   string          `gorm:"column:sensitive_rule_id" json:"sensitive_rule_id"`
	SensitiveLevel    int             `gorm:"column:sensitive_level" json:"sensitive_level"`
	SensitiveRuleName string          `gorm:"column:sensitive_rule_name" json:"sensitive_rule_name"`
	Uuid              string          `gorm:"column:uuid" json:"uuid"`
	Channel           string          `gorm:"column:channel" json:"channel"`
	ChannelType       string          `gorm:"column:channel_type" json:"channel_type"`
	ActivityDesc      string          `gorm:"-" json:"activity_desc"`
	FileSizeDesc      string          `gorm:"-" json:"file_size_desc"`
	FileCategoryId    int64           `gorm:"column:file_category_id;type:int;comment:文件类型" json:"file_category_id"`
	FileCategory      string          `gorm:"column:file_category" json:"file_category"`
	FileTypeName      string          `gorm:"column:file_type_name" json:"file_type_name"`
}

type ProcessItem struct {
	Name         string        `json:"name" gorm:"column:name"`
	Process      string        `json:"process" gorm:"column:process"`
	Software     string        `gorm:"column:software" json:"-"`
	Count        int           `json:"count" gorm:"column:count"`
	Key          string        `gorm:"column:key" json:"-"`
	Children     []ProcessItem `json:"children" gorm:"-"`
	ProcessCount int           `json:"process_count" gorm:"-"`
}

type ProcessRs struct {
	Software    string   `gorm:"column:software" json:"softWare"`
	ProcessList []string `json:"process_list" gorm:"column:process_list;type:[]varchar"`
	Count       int      `json:"count" gorm:"column:count"`
}

type EventFilterRsp struct {
	Id            string            `json:"id"`
	FileTypeCode  []int64           `json:"file_type_code"`
	Process       []string          `json:"process"`
	EnableAllUser int               `json:"enable_all_user"`
	UserIds       []string          `json:"user_ids"`
	UserGroupIds  []string          `json:"user_group_ids"`
	UserInfo      []model.UserEcho  `json:"user_info" gorm:"-"`
	GroupInfo     []model.GroupEcho `json:"group_info" gorm:"-"`
	ReserveDay    int               `json:"reserve_day"`
	ScanContent   int               `json:"scan_content"`
}

type GetProcessListRsp struct {
	model.CommonPage
	Process    []ProcessItem `json:"process"`
	EventCount int           `json:"event_count"`
}
