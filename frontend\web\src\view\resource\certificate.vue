<template>
  <div class="admin-box-m">
    <t-layout>
      <t-header class="asdc-header">
        <t-button theme="primary" class="asdc-primary-but" @click="handleAddNew">
          <template #icon><add-icon /></template>
          新增证书
        </t-button>
        <t-button variant="outline" class="asdc-default-but" @click="getDataList">
          <template #icon>
            <i class="iconfont icon-shuaxin" style="font-size: 12px"/>
          </template>
          刷新
        </t-button>
<!--        <t-button variant="outline" class="asdc-default-but" @click="delData('')">-->
<!--          <template #icon><cloud-upload-icon /></template>-->
<!--          删除-->
<!--        </t-button>-->
        <t-input v-model="search" clearable placeholder="请输入证书名" class="asdc-search" style="margin-top: 19px;" @enter="getDataList" @clear="getDataList">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" @click="getDataList" />
          </template>
        </t-input>
      </t-header>
      <t-content style="background: #ffffff;height: calc(100vh - 130px);padding: 0px 18px 0px 18px;display: inline-flex">
        <t-table
          height="calc(100% - 69px)"
          vertical-align="top"
          row-key="id"
          resizable
          hover
          :loading="loading"
          table-layout="auto"
          disable-data-page="true"
          :columns="columns"
          :data="data"
          :selected-row-keys="selectedRowKeys"
          :pagination="pagination"
          :reserve-selected-row-on-paginate="true"
          @page-change="onPageChange"
          @select-change="onSelectChange"
        >
          <template #operation="{ row }">
            <t-link class="font-class" theme="primary" hover="color" style="margin-right: 24px;font-size: 12px" @click="getSSLDetails(row.id)">
              详情
            </t-link>
            <t-dropdown
              :options="
                [
                  {content: '编辑', value: 'update', onClick: () => upData(row)},
                  {content: '删除', value: 'delete', onClick: () => delData([row.id], row.name)}
                ]
              "
              :popup-props="{placement: 'bottom'}"
            >
              <t-link class="font-class" hover="color" style="top: -5px" theme="primary">...</t-link>
            </t-dropdown>
          </template>
        </t-table>
      </t-content>
    </t-layout>
    <!--    详情展示抽屉-->
    <t-drawer v-model:visible="visible" size="600" destroy-on-close :close-btn="true">
      <template #header>
        <div style="font-size: 14px;font-width: 500;">证书详情</div>
      </template>
      <t-space direction="vertical" style="width: calc(100% - 16px);margin-left: 9px">
        <t-row v-for="(item,index) in details_columns" :key="index" :gutter="20">
          <t-col :span="2">
            <div style="color: #252631;opacity: 0.5">{{ item.label }}</div>
          </t-col>
          <t-col :span="10">
            <div style="color: #252631;word-wrap: break-word;">{{ SSLDetail[item.value] }}</div>
          </t-col>
        </t-row>
      </t-space>
      <template #footer>
        <t-button variant="outline" style="width: 80px" @click="visible = false"> 取消 </t-button>
      </template>
    </t-drawer>
    <!--    新增编辑抽屉-->
    <t-drawer v-model:visible="opvisible" size="600" destroy-on-close :close-btn="true" @close="setInitialFormData">
      <template #header>
        <div style="font-size: 14px;font-weight: 500;">{{ formData.id ? '编辑证书' : '新增证书' }}</div>
      </template>
      <t-space direction="vertical" style="width: calc(100% - 16px);margin-left: 9px">
        <CertificateUpload 
          ref="certificateUploadRef"
          :modelValue="formData"
          @update:modelValue="handleFormDataUpdate"
        />
      </t-space>
      <template #footer>
        <t-button class="asdc-primary-but" style="float: right;width: 80px" @click="onSubmit">确定</t-button>
        <t-button variant="outline" style="width: 80px" @click.stop="opvisible = false;setInitialFormData()"> 取消 </t-button>
      </template>
    </t-drawer>
  </div>
</template>

<script>
export default {
  name: 'Certificate'
}
</script>
<script setup>
import { SearchIcon, AddIcon } from 'tdesign-icons-vue-next'
import { h, reactive, ref } from 'vue'
import { addDomainSSL, delDomainSSL, getDomainSSL, getDomainSSLDetail, upDomainSSL } from '@/api/certificate'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'
import CertificateUpload from '@/components/certificate/CertificateUpload.vue'
import { detailscolumnsconfig, tableColumnsConfig } from './certificateConfig'
import { delApp } from '@/api/resource'

const asdcFrom = ref(null)
const certificateUploadRef = ref(null)
const visible = ref(false)
const opvisible = ref(false)
const loading = ref(false)
const selectedRowKeys = ref([])
const SSLDetail = ref({})
const total = ref(0)
const pagination = reactive({
  current: 1,
  pageSize: 50,
  total: total.value,
  pageSizeOptions: [50, 100, 150, 200],
  showJumper: true,
  totalContent: () => {
    return [
      h('div', { id: 'foo', class: 't-pagination__total', innerHTML: `共${total.value}条` }, []),
      h('button', {
        id: 'delete',
        class: [selectedRowKeys.value.length < 1 ? 't-pagination__total_delete_disabled' : 't-pagination__total_delete'],
        disabled: selectedRowKeys.value.length < 1 ? 'disabled' : null,
        innerHTML: `批量删除`,
        onClick: () => delData('') }, []),
    ]
  }
})
const data = ref([])

const columns = tableColumnsConfig()
const details_columns = detailscolumnsconfig()

const formData = reactive({
  id: '',
  name: '',
  certificate: '',
  private_key: '',
})

const search = ref('')

const onPageChange = (pageInfo, context) => {
  console.log('onPageChange')
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
  getDataList()
}

const onSelectChange = (value) => {
  selectedRowKeys.value = value
}

const getDataList = async() => {
  loading.value = true
  const query = {
    limit: pagination.pageSize,
    offset: (pagination.current - 1) * pagination.pageSize,
    search: search.value.trim()
  }
  const res = await getDomainSSL(query)
  if (res.data.code === 0) {
    data.value = res.data.data.data
    total.value = res.data.data.total_num
    loading.value = false
  } else {
    await MessagePlugin.error('获取信息失败！')
  }
}

const getSSLDetails = async(id) => {
  const res = await getDomainSSLDetail({ id: id })
  if (res.data.code !== 0) {
    await MessagePlugin.error('获取信息失败！')
  } else {
    SSLDetail.value = res.data.data
    visible.value = true
  }
}

const delData = async(ids, names) => {
  let name = ''
  if (!ids) {
    let nameValue = []
    ids = selectedRowKeys.value
    data.value.forEach(item => {
      if (selectedRowKeys.value.includes(item.id)) {
        nameValue.push(item.name)
      }
    })
    name = nameValue.join(',')
  } else {
    name = names
  }
  const query = {
    ids: ids,
    name: name
  }
  const confirmDialog = DialogPlugin.confirm({
    header: '删除证书',
    theme: 'warning',
    body: '删除证书后将无法继续访问关联该证书的应用，是否继续删除？',
    className: 'dle',
    class: 'del',
    confirmBtn: {
      content: '确定',
      theme: 'primary',
      loading: false,
    },
    onConfirm: async() => {
      const res = await delDomainSSL(query)
      if (res.data.code === 0) {
        await MessagePlugin.info('删除成功！')
        await getDataList()
        confirmDialog.hide()
      } else {
        await MessagePlugin.error(res.data.msg)
      }
    },
  })
}

const upData = async(row) => {
  // 先重置数据
  setInitialFormData()
  // 然后设置编辑数据
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    certificate: row.certificate,
    private_key: row.private_key
  })
  opvisible.value = true
}

const handleAddNew = () => {
  setInitialFormData()
  opvisible.value = true
}

const onSubmit = async() => {
  const validate = await certificateUploadRef.value.validate()
  if (!validate) {
    return
  }
  let res = ''
  res = formData.id ? await upDomainSSL(formData) : await addDomainSSL(formData)
  if (res.data.code !== 0) {
    await MessagePlugin.error(res.data.msg)
    return
  }
  await MessagePlugin.info(formData.id ? '修改成功!' : '新增成功')
  opvisible.value = false
  setInitialFormData()
  await getDataList()
}

const setInitialFormData = () => {
  Object.assign(formData, {
    id: '',
    name: '',
    certificate: '',
    private_key: ''
  })
}

const handleFormDataUpdate = (newData) => {
  Object.assign(formData, newData)
}

getDataList()
</script>
<style scoped>

</style>
