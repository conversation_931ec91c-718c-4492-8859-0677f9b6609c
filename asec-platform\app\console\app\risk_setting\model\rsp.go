package model

type GetRiskSettingTotalRsp struct {
	RiskLevelName string `gorm:"column:risk_level_name" json:"risk_level_name"`
	MinScore      int    `gorm:"column:min_score" json:"min_score"`
	MaxScore      int    `gorm:"column:max_score" json:"max_score"`
	Id            int    `gorm:"column:id" json:"id"`
	RiskLevel     int    `gorm:"risk_level" json:"risk_level"`
}
type GetRiskSettingListResp struct {
	DataScoreSetting            []RiskSetting `json:"data_score_setting"`
	IdentifyScoreSetting        []RiskSetting `json:"identify_score_setting"`
	BehaviorScoreSetting        []RiskSetting `json:"behavior_score_setting"`
	BehaviorDataHideSettingList []RiskSetting `json:"behavior_data_hide_setting"`
}

type RiskSetting struct {
	Id                 int    `gorm:"column:id" json:"id"`
	IndicatorType      int    `gorm:"column:indicator_type" json:"indicator_type"`
	IndicatorSubType   int    `gorm:"column:indicator_sub_type" json:"indicator_sub_type"`
	Indicator          string `gorm:"column:indicator" json:"indicator"`
	IndicatorGroupName string `gorm:"column:indicator_group_name" json:"indicator_group_name"`
	IndicatorLevel     string `gorm:"column:indicator_level" json:"indicator_level"`
	Score              int    `gorm:"column:score" json:"score"`
	TypeName           string `gorm:"column:type_name" json:"type_name"`
}

type RiskDataLevel struct {
	RuleName       string `gorm:"column:rule_name" json:"rule_name"`
	SensitiveLevel int    `gorm:"column:sensitive_level" json:"sensitive_level"`
}
