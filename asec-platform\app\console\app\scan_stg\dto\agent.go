package dto

type ScanTaskAgentConfReq struct {
	AgentIds             []string `json:"agent_ids"`
	TaskEnable           int      `json:"task_enable"`
	FileTypeCode         []int64  `json:"file_type_code"`
	ScanPositionType     int      `json:"scan_position_type"`
	ScanDirectory        []string `json:"scan_directory"`
	ScanExcludeDirectory []string `json:"scan_exclude_directory"`
	ScanMinFileSize      int64    `json:"scan_min_file_size"`
	ScanMaxFileSize      int64    `json:"scan_max_file_size"`
	IdleScanSwitch       bool     `json:"idle_scan_switch"`
	ChargingScanSwitch   bool     `json:"charging_scan_switch"`
	MinuteCountLimit     int32    `json:"minute_count_limit"`
	ConfGranularity      int      `json:"conf_granularity"`
	Date                 string   `json:"date"`
}
