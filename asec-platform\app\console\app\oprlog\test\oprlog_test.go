package test

import (
	"asdsec.com/asec/platform/app/console/app/oprlog/dto"
	"asdsec.com/asec/platform/app/console/app/oprlog/repository"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"testing"
)

func TestOprlogRepository(t *testing.T) {
	dao := repository.NewOprlogRepository()
	ctx := context.Background()
	req := dto.ListOperateLogReq{Pagination: model.Pagination{Limit: 100, Offset: 0}, ResourceType: "APPLICATION"}
	list, err := dao.OprLogList(ctx, req)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log(list.Rows)
	t.Log(list.TotalRows)
}
