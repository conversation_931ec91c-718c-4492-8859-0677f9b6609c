<template>
  <div id="app">
    <t-layout>
      <t-header class="asdc-header"></t-header>
      <t-content style="background: #f0f2f5;height: calc(100vh - 130px);padding: 0px 18px 0px 18px;">
        <div v-show="isShow" class="welcome">
          <p class="title">导入应用</p>
          <div class="card-wrap">
            <div class="card first-card" @click="download()">
              <div class="card-main">
                <p class="card-title">第一步</p>
                <p>下载导入表格模板</p>
                <img src="@/assets/demo.png" alt=""/>
              </div>
              <div class="card-foot">
                <div>
                  <img src="@/assets/smallExcel.png" alt=""/>
                  <span>应用导入模板</span>
                </div>
                <el-link :disabled="sdpOptions.length === 0" type="primary">点击下载</el-link>
              </div>
            </div>
            <el-upload
                :disabled="sdpOptions.length === 0"
                class="upload-demo"
                action=""
                :on-change="handleChange"
                :auto-upload="false"
                :show-file-list="false"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            >
              <div class="card">
                <div class="card-main">
                  <p class="card-title">第二步</p>
                  <p>上传填好的表格</p>
                  <img class="second-img" src="@/assets/Excel.png" alt=""/>
                  <p class="info">仅支持xlsx格式文件</p>
                </div>
                <div class="card-foot">
                  <p>
                    <el-link type="primary"><i class="iconfont icon-shangchuan1"></i> 点击上传</el-link>
                  </p>
                </div>
              </div>
            </el-upload>
          </div>
        </div>

        <div v-show="!isShow" class="sheet-wrap" v-loading="loading">
          <div class="sheet-body">
            <p class="sheet-title">
              <i class="iconfont icon-fanhui-2" @click="close"></i><span>导入应用</span>
            </p>
            <div id="luckysheet" ref="hotContainer"></div>
            <div class="sheet-foot">
              <el-button type="primary" @click="verify(false)">数据校验</el-button>
              <el-button class="serve-btn" type="primary" @click="save">保存数据</el-button>
            </div>
          </div>
        </div>
      </t-content>
      <t-footer class="asdc-footer" v-show="isShow"></t-footer>
    </t-layout>
  </div>
</template>
<script>
export default {
  name: 'ImportApp'
}
</script>
<script setup>
import {ref} from 'vue'
import {exportTemplate, importApp} from '@/api/resource'
import {downLoadXls} from '@/utils/excel'
import {ElButton, ElLink, ElLoading, ElMessage} from 'element-plus'
import * as XLSX from 'xlsx'  // 用于解析 Excel
import Handsontable from 'handsontable';
import 'handsontable/dist/handsontable.full.min.css';
import 'handsontable/styles/ht-theme-main.css';
import {getAgentsList} from '@/api/agents'
import {MessagePlugin} from "tdesign-vue-next";

// 响应式状态
const isShow = ref(true)

/*****网关数据********/
const sdpOptions = ref([])
const sdpList = async () => {
  const sdp = await getSDPList()
  if (sdp.length === 0) {
    ElMessage.warning('请先创建网关！')
    return
  }
  for (let i = 0; i < sdp.length; i++) {
    sdpOptions.value.push(sdp[i].app_name)
  }
}
// 获取网关列表
const getSDPList = async () => {
  const query = {
    limit: 500,
    offset: 0,
  }
  const res = await getAgentsList(query)
  if (res.data.code === 0) {
    return res.data?.data?.rows
  }
  return []
}
sdpList()
/*****网关数据********/


/*****下载excel模板********/
const download = async () => {
  loading.value = true
  try {
    const res = await exportTemplate()
    downLoadXls(res)
    loading.value = false
  } catch (err) {
    console.error(err)
    loading.value = false
  }
}
/*****下载excel模板********/
const loading = ref(false);

const hotContainer = ref(null)
const handleChange = async (file) => {
  loading.value = true
  isShow.value = false
  if (!hotContainer.value) return
  const fileFormat = /^.+\.(xlsx)$/
  const fileName = file.name
  if (!fileName || !fileFormat.test(fileName)) {
    loading.value = false
    ElMessage.error('文件格式有误！')
    return
  }
  try {
    // 1. 读取 Excel 文件
    const arrayBuffer = await file.raw.arrayBuffer()
    // 2. 解析 Excel 数据
    const workbook = XLSX.read(arrayBuffer)
    const firstSheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[firstSheetName]
    // 3. 转换为 JSON 数据
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1})
    const headers = jsonData[0]
    //去掉jsonData的第一行
    jsonData.shift()
    // jsonData 数组里面每个元素的长度当不足 headers的长度时 填充空值
    jsonData.forEach(item => {
      while (item.length < headers.length) {
        item.push('')
      }
    })

    // 4. 初始化 Handsontable
    if (hotContainer.value) {
      //先销毁Handsontable\
      if (window.hotInstance != null && typeof window.hotInstance.rootWrapperElement != "undefined") {
        window.hotInstance.destroy()
      }
      let header = [
        'app_type',
        'name',
        'app_status',
        'sdp_list',
        'server_address',
        'publish_address',
        'uri',
        'smart_rewrite',
        'depend_site',
        'show_status',
        'portal_show_name',
        'portal_desc'
      ]

      let data = []
      for (let i = 0; i < jsonData.length; i++) {
        let row = {}
        for (let j = 0; j < jsonData[i].length; j++) {
          row[header[j]] = jsonData[i][j]
        }
        data.push(row)
      }

      let options = {
        data: data,
        height: 620,
        // ...其他配置
        // 或自定义菜单项
        contextMenu: {
          items: {
            remove_row: {
              name: '删除行'
            }
          }
        },
        colHeaders: headers,
        multiColumnSorting: true,
        filters: true,
        rowHeaders: false,
        autoColumnSize: true, // 自适应列大小
        manualRowMove: true,  // 为true时，行可拖拽至指定行
        headerClassName: 'htLeft',
        autoWrapRow: false,  // 自动隐藏行
        autoWrapCol: true,
        manualRowResize: true,  // 允许拖动改变行的高度
        manualColumnResize: true,
        navigableHeaders: true,
        validators: [],
        licenseKey: 'non-commercial-and-evaluation', // 非商业用途声明
      }

      const requireV = (value, callback) => {
        if (value) {
          callback(true);
        } else {
          callback(false);
        }
      }
      const ipDomainValidator = (value, callback) => {
        if (!value) {
          return callback(false)
        } else {
          //写一个正则 要求输入的ip或者 域名格式如下必须是http或者是https协议开头后面跟上正确的IP或者域名，可以加上端口号
          const regex = /^(https?):\/\/(?:(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}|(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(:(6553[0-5]|655[0-2]\d|65[0-4]\d{2}|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{0,3}))?$/;
          if (!regex.test(value)) {
            return callback(false);
          }
        }
        return callback(true)
      }
      const DependSiteValidator = (value, callback) => {
        if (!value) {
          return callback(true)
        } else {
          //写一个正则 要求输入的ip或者 域名格式如下必须是http或者是https协议开头后面跟上正确的IP或者域名，可以加上端口号
          const regex = /^(https?):\/\/(?:(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}|(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(:(6553[0-5]|655[0-2]\d|65[0-4]\d{2}|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{0,3}))?$/;
          if (!regex.test(value)) {
            return callback(false);
          }
        }
        return callback(true)
      }

      const appSiteValidator = (value, callback) => {
        if (!value) {
          return callback(false)
        } else {
          //写一个正则 要求输入的ip或者名格式如下必须是tcp、udp、icmp或者all协议开头后面跟上正确的IP或者域名，可以加上端口号
          const item = value.split(',')
          for (let i = 0; i < item.length; i++) {
            const regex = /^(tcp|udp|icmp|all):(?:(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}|(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(:([\d\s-]+))?$/;
            if (!regex.test(item[i])) {
              return callback(false);
            }
          }
        }
        return callback(true)
      }

      options.colWidths = 126
      options.columns = [
        {
          data: 'app_type',
          validator: function (value, callback) {
            const rowData = this.instance.getDataAtRow(this.row);
            if (rowData[0] === "") {
              return callback(false)
            } else if (rowData[0] !== "WEB应用" && rowData[0] !== "门户应用" && rowData[0] !== "隧道应用") {
              return callback(false)
            }
            return callback(true)
          },
          allowInvalid: true,
          type: 'dropdown',
          source: ['WEB应用', '隧道应用', '门户应用'],
        },
        {
          data: 'name',
          validator: function (value, callback) {
            const allData = this.instance.getData();
            if (value.trim() === "") {
              return callback(false)
            }
            let name = []
            for (let i = 0; i < allData.length; i++) {
              name.push(allData[i][1])
            }
            const nameRepeat = name.filter((item, index) => name.indexOf(item) !== index)
            if (nameRepeat.includes(value)) {
              ElMessage.error('应用名不能重复【' + value + '】')
              return callback(false)
            }
            return callback(true)
          },
          allowInvalid: true
        },
        {
          data: 'app_status',
          type: 'dropdown',
          source: ['启用', '停用', "维护"],
          validators: [
            {
              type: 'required',
              message: '必填项'
            }
          ]
        },
        {
          data: 'sdp_list',
          type: 'dropdown',
          source: sdpOptions.value,
          validator: function (value, callback) {
            const rowData = this.instance.getDataAtRow(this.row);
            if (rowData[0] === "") {
              return callback(false)
            } else if (rowData[0] !== "WEB应用" && rowData[0] !== "门户应用" && rowData[0] !== "隧道应用") {
              return callback(false)
            }
            if (rowData[0] === "门户应用") {
              return callback(true)
            }
            return requireV(value, callback)
          },
          allowInvalid: true
        },
        {
          data: 'server_address',
          type: 'text',
          validator: function (value, callback) {
            const rowData = this.instance.getDataAtRow(this.row);
            if (rowData[0] === "门户应用") {
              return callback(true)
            }
            if (rowData[0] === "隧道应用") {
              return appSiteValidator(value, callback);
            } else {
              return ipDomainValidator(value, callback);
            }
          },
          allowInvalid: true
        },
        {
          data: 'publish_address',
          type: 'text',
          validator: function (value, callback) {
            const rowData = this.instance.getDataAtRow(this.row);
            if (rowData[0] === "门户应用") {
              return callback(true)
            }
            if (rowData[0] === "隧道应用") {
              callback(true)
            } else {
              return ipDomainValidator(value, callback);
            }
          },
          allowInvalid: true
        },
        {
          data: 'uri',
          type: 'text'
        },
        {
          data: 'smart_rewrite',
          type: 'dropdown',
          source: ['是', '否'],
          validator: function (value, callback) {
            const rowData = this.instance.getDataAtRow(this.row);
            if (rowData[0] === "WEB应用") {
              return requireV(value, callback);
            }
            callback(true)
          },
          allowInvalid: true
        },
        {
          data: 'depend_site',
          type: 'text',
          validator: DependSiteValidator, allowInvalid: true
        },
        {
          data: 'show_status',
          type: 'dropdown',
          source: ['是', '否'],
          validator: requireV, allowInvalid: true
        },
        {
          data: 'portal_show_name',
          type: 'text'
        },
        {
          data: 'portal_desc',
          type: 'text'
        }
      ]
      // 保存实例以便后续操作
      window.hotInstance = new Handsontable(hotContainer.value, options)
    }

    // 切换显示状态
    isShow.value = false
    loading.value = false
  } catch (error) {
    console.error('解析 Excel 失败:', error)
    ElMessage.error('解析 Excel 文件失败')
    loading.value = false
  }
}

const hasError = ref(false)
const verify = async (save = false) => {
  hasError.value = false
  return new Promise((resolve) => {
    //获取window.hotInstance 表格里面所有的数据
    let data = window.hotInstance.getData()
    let name = []
    for (let i = 0; i < data.length; i++) {
      name.push(data[i][1])
    }
    const nameRepeat = name.filter((item, index) => name.indexOf(item) !== index)
    name = [...new Set(name)]
    if (name.length !== data.length) {
      hasError.value = true
      ElMessage.error('应用名不能重复【' + nameRepeat.join(',') + '】')
      resolve(false)
      return
    }
    window.hotInstance.validateCells((result) => {
      if (!result) {
        hasError.value = true
        ElMessage.error('请检查数据格式是否正确')
        resolve(false) // 验证失败
      } else {
        if (!save) {//提交的时候 如果格式正确 不提示 直接提交
          ElMessage.success('所有数据格式都正确')
        }
        resolve(true) // 验证通过
      }
    })
  })
}


const save = async () => {
  await verify(true)
  if (!hasError.value) {
    loading.value = true
    //获取window.hotInstance里面表格所有的数据
    const data = window.hotInstance.getData().map(row =>
        row.map(cell => cell === null || cell === undefined ? "" : cell)
    )
    const query = {
      app_data: data
    }
    const res = await importApp(query)
    loading.value = false
    if (res.data.code === 0) {
      await MessagePlugin.info('【' + res.data.data + '】个应用导入成功!')
      close()
    } else {
      ElMessage.error(res.data.msg)
    }
  }

}

const close = () => {
  isShow.value = true
}
</script>

<style scoped>
/* 在此添加组件私有样式 */
#welcome {
  padding: 20px;
}


.card-wrap {
  display: flex;
  justify-content: space-between;
}

.card {
  border: 1px solid #eee;
  padding: 20px;
  text-align: center;
}

.sheet-wrap {
  padding: 20px;
}

#luckysheet {
  height: 500px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #app {
  height: 100%;
  font-family: PingFang SC, PingFang SC-Regular;
}

#app {
  background: #fff;
  border-radius: 5px;
  min-height: 680px;
}

.welcome {
  padding: 120px 120px 0;
  height: 100%;
}

.welcome > .title {
  text-align: center;
  font-size: 20px;
  font-weight: 500;
  color: #3c404d;
  line-height: 28px;
}

.welcome .title .iconfont {
  color: #D9DAE0;
  cursor: pointer;
  margin-left: 5px;
}

.welcome .title .iconfont:hover {
  color: #536ce6;
}

.fz-16 {
  font-size: 16px;
}

.custom-tooltip.el-tooltip__popper {
  line-height: 20px;
  padding: 8px 16px;
}

.card-wrap {
  margin-top: 120px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.card {
  width: 360px;
  height: 360px;
  border: 1px solid #ededf1;
  border-radius: 8px;
  cursor: pointer;
  color: #3C404D;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  position: relative;
}

.card:hover {
  box-shadow: 0px 8px 20px 0px rgba(16, 36, 66, 0.10);
  border-color: #e2e2e6;
}

.first-card {
  margin-right: 32px;
}

.card .card-main {
  background: #f8f8fa;
  padding: 32px;
  padding-bottom: 0;
  border-radius: 8px 8px 0 0;
  height: 298px;
}

.card .card-title {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 10px;
}

.card-main img {
  width: 296px;
  height: 188px;
  /* background: #ffffff; */
  border-radius: 4px 4px 0px 0px;
  margin-top: 24px;
}

.card-main .info {
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #686e84;
  text-align: center;
}

.card-main .second-img {
  width: 64px;
  height: 64px;
  /* background: #ffffff; */
  margin: 40px 0;
}

.card .card-foot {
  width: 360px;
  height: 64px;
  background: #ffffff;
  border: 1px solid #ededf1;
  border-radius: 0px 0px 8px 8px;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: -1px;
  left: -1px;
  text-align: center;
}

.card .card-foot div {
  display: flex;
  align-items: center;
}

.card-foot p {
  width: 100%;
}

.card-foot p:hover {
  color: #536ce6;
}

.card-foot img {
  width: 16px;
  margin-right: 10px;
}

.sheet-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sheet-wrap .sheet-body {
  height: 100%;
  padding: 24px;
}

.sheet-body .sheet-title span {
  font-size: 16px;
  font-weight: 500;
  text-align: left;
  color: #3c404d;
  padding: 0 10px;
  margin-left: 10px;
  position: relative;
}

.sheet-title i {
  cursor: pointer;
  font-size: 16px;
  color: #536ce6;
}

.sheet-title span::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  height: 14px;
  width: 1px;
  background: #b3b6c1;
}

#luckysheet {
  touch-action: none;
  margin: 24px 0;
  padding: 0;
  position: relative;
  width: 100%;
  height: calc(100% - 90px);
  /* border: 1px solid red; */
  /* display: none; */
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #536ce6;
  border-color: #536ce6;
}

.el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: #536ce6;
}

.el-checkbox .el-checkbox__input + .el-checkbox__label {
  color: #3c404d;
}

#action {
  width: 100%;
  margin: 10px auto 0 auto;
}

.luckysheet-freezebar-drop {
  z-index: 100;
}

.luckysheet-scrollbar-ltr {
  z-index: 100;
}

.sheet-foot {
  height: 88px;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-top: 1px solid #ededf1;
}

.sheet-foot .el-button {
  width: 120px;
  height: 40px;
  background: #536ce6;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  text-align: left;
  color: #ffffff;
  border-color: #536ce6;
  text-align: center;
}

.el-button + .el-button {
  margin-left: 16px;
}

.close {
  float: right;
  margin: 6px 6px 0 0;
}

.ml-2 {
  margin-left: 10px;
}

.el-loading-mask.totality-loading .el-loading-spinner {
  display: flex;
  padding: 10px 16px;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0px 2px 8px 0px rgba(16, 36, 66, 0.20);
  width: initial;
  left: 50%;
  transform: translateX(-50%);
  align-items: center;
}

.el-loading-mask.totality-loading .el-loading-spinner svg.circular {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #B3B6C1;
}

.el-loading-mask.totality-loading .el-loading-spinner .el-loading-text {
  margin: 0 0 0 10px;
  font-size: 14px;
  font-weight: 400;
  text-align: left;
  color: #3C404D;
  line-height: 20px;
}

#luckysheet-rightclick-menu {
  padding: 6px 8px;
}

.luckysheet-cols-menu .luckysheet-cols-menuitem {
  border-radius: 4px;
}

.luckysheet-cols-menu .luckysheet-cols-menuitem-hover, .luckysheet-cols-menu .luckysheet-cols-menuitem:hover {
  background: #f6f6f8;
}

.luckysheet-rightgclick-menu .luckysheet-cols-menuitem .luckysheet-cols-menuitem-content {
  padding: 6px 8px;
  color: #3c404d;
  display: flex;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

input.luckysheet-mousedown-cancel {
  margin-right: 5px;
}

input.luckysheet-mousedown-cancel:focus {
  border-color: #536ce6;
}

.luckysheet-cell-selected {
  background: rgba(83, 108, 230, .1);
  border-color: #536ce6 !important;
}

.luckysheet-cols-menu .luckysheet-cols-menuitem {
  padding: 0;
}

.luckysheet-input-box {
  border: 2px #536ce6 solid;
}

.luckysheet-cs-fillhandle {
  background-color: #536ce6;
}

.luckysheet-cols-h-selected {
  border-bottom: 2px solid #536ce6;
  background-color: rgba(83, 108, 230, .1) !important;
}

.luckysheet-rows-h-selected {
  border-right: 2px solid #536ce6;
  background-color: rgba(83, 108, 230, .1) !important;
}

.luckysheet-cols-h-hover, .luckysheet-rows-h-hover {
  background-color: rgba(83, 108, 230, .1);
}

.luckysheet-rows-change-size, .luckysheet-cols-change-size {
  background: #536ce6;
}

.luckysheet-change-size-line {
  background: #536ce6;
}

.el-message-box__status {
  font-size: 18px;
}
</style>
