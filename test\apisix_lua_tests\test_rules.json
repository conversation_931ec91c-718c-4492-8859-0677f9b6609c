{"body_tests": [{"name": "单规则-URL替换测试", "originbody": "http://172.24.21.24:5002/delay", "regex": "http://172.24.21.24:5002", "replace": "https://test24.infogo.net.cn:7443", "expect": "https://test24.infogo.net.cn:7443/delay"}, {"name": "单规则-端口重复测试", "originbody": "访问地址: http://192.168.1.100:8080/api/test", "regex": "http://192.168.1.100:8080", "replace": "https://newdomain.com:9443", "expect": "访问地址: https://newdomain.com:9443/api/test"}, {"name": "单规则-普通字符串替换", "originbody": "服务器地址是 old_server，请访问 old_server/api", "regex": "old_server", "replace": "new_server", "expect": "服务器地址是 new_server，请访问 new_server/api"}, {"name": "单规则-部分匹配避免测试", "originbody": "地址: http://192.168.1.100:8080, 另一个地址: http://192.168.1.100:8081", "regex": "http://192.168.1.100:8080", "replace": "https://newdomain.com:9443", "expect": "地址: https://newdomain.com:9443, 另一个地址: http://192.168.1.100:8081"}, {"name": "多规则-同时替换多个URL", "originbody": "主服务器: http://192.168.1.100:8080/api, 备份服务器: http://192.168.1.200:8080/backup", "rewriterules": [{"regex": "http://192.168.1.100:8080", "replace": "https://main.example.com:9443"}, {"regex": "http://192.168.1.200:8080", "replace": "https://backup.example.com:9443"}], "expect": "主服务器: https://main.example.com:9443/api, 备份服务器: https://backup.example.com:9443/backup"}, {"name": "多规则-转义字符替换", "originbody": "测试: https:\\/\\/testcrm.infogo.net.cn:7443, 测试2: https:\\/\\/kt8logo.oss-cn-beijing.aliyuncs.com, 测试3: https:\\/\\/testcrm.infogo.net.cn:443", "rewriterules": [{"regex": "https:\\/\\/testcrm.infogo.net.cn", "replace": "https://testcrm.infogo.net.cn:7443"}, {"regex": "https:\\/\\/kt8logo.oss-cn-beijing.aliyuncs.com", "replace": "https://REWRITEkt8logo.oss-cn-beijing.aliyuncs.com"}], "expect": "测试: https:\\/\\/testcrm.infogo.net.cn:7443, 测试2: https://REWRITEkt8logo.oss-cn-beijing.aliyuncs.com, 测试3: https://testcrm.infogo.net.cn:7443"}, {"name": "多规则-转义字符替换http", "originbody": "测试: http:\\/\\/crm.infogo.net.cn:7443, 测试2: https:\\/\\/kt8logo.oss-cn-beijing.aliyuncs.com, 测试3: http:\\/\\/crm.infogo.net.cn", "rewriterules": [{"regex": "http:\\/\\/crm.infogo.net.cn", "replace": "https://testcrm.infogo.net.cn:7443"}, {"regex": "https:\\/\\/kt8logo.oss-cn-beijing.aliyuncs.com", "replace": "https://REWRITEkt8logo.oss-cn-beijing.aliyuncs.com"}], "expect": "测试: http:\\/\\/crm.infogo.net.cn:7443, 测试2: https://REWRITEkt8logo.oss-cn-beijing.aliyuncs.com, 测试3: https://testcrm.infogo.net.cn:7443"}, {"name": "多规则-JSON格式内容替换", "originbody": "{\"api_url\":\"http://172.24.21.24:5002/api\",\"ws_url\":\"ws://172.24.21.24:5003/ws\"}", "rewriterules": [{"regex": "http://172.24.21.24:5002", "replace": "https://api.example.com:8443"}, {"regex": "ws://172.24.21.24:5003", "replace": "wss://ws.example.com:8443"}], "expect": "{\"api_url\":\"https://api.example.com:8443/api\",\"ws_url\":\"wss://ws.example.com:8443/ws\"}"}], "header_tests": [{"name": "单规则-Location头部重定向", "originheader": "http://172.24.21.24:5002/redirect", "header_key": "Location", "regex": "http://172.24.21.24:5002", "replace": "https://test24.infogo.net.cn:7443", "expect": "https://test24.infogo.net.cn:7443/redirect"}, {"name": "单规则-Location头部yuyuan测试", "originheader": "https://its.yuyuantm.com.cn/cas//login?service=https%3A%2F%2Foa.yuyuantm.com.cn%2Fyygfcus%2FLogin.jsp", "header_key": "Location", "regex": "https://its.yuyuantm.com.cn", "replace": "https://its-yuyuantm-com-cn-s.yuyuantm.com.cn", "expect": "https://its-yuyuantm-com-cn-s.yuyuantm.com.cn/cas//login?service=https%3A%2F%2Foa.yuyuantm.com.cn%2Fyygfcus%2FLogin.jsp"}, {"name": "单规则-自定义头部URL替换", "originheader": "http://api.internal.com:8080/v1/users", "header_key": "X-API-Endpoint", "regex": "http://api.internal.com:8080", "replace": "https://api.external.com:9443", "expect": "https://api.external.com:9443/v1/users"}, {"name": "单规则-Content-Location头部", "originheader": "http://192.168.1.100:8080/resource/123", "header_key": "Content-Location", "regex": "http://192.168.1.100:8080", "replace": "https://cdn.example.com:8443", "expect": "https://cdn.example.com:8443/resource/123"}, {"name": "多规则-转义字符头部替换", "originheader": "测试: https:\\/\\/testcrm.infogo.net.cn:7443, 测试2: https:\\/\\/kt8logo.oss-cn-beijing.aliyuncs.com", "header_key": "Location", "rewriterules": [{"regex": "https:\\/\\/testcrm.infogo.net.cn", "replace": "https://testcrm.infogo.net.cn:7443"}, {"regex": "https:\\/\\/kt8logo.oss-cn-beijing.aliyuncs.com", "replace": "https://REWRITEkt8logo.oss-cn-beijing.aliyuncs.com"}], "expect": "测试: https:\\/\\/testcrm.infogo.net.cn:7443, 测试2: https://REWRITEkt8logo.oss-cn-beijing.aliyuncs.com"}, {"name": "多规则-复杂头部内容替换", "originheader": "服务器: http://192.168.1.100:8080, 镜像: http://192.168.1.200:8080", "header_key": "X-Server-Info", "rewriterules": [{"regex": "http://192.168.1.100:8080", "replace": "https://main.example.com:9443"}, {"regex": "http://192.168.1.200:8080", "replace": "https://mirror.example.com:9443"}], "expect": "服务器: https://main.example.com:9443, 镜像: https://mirror.example.com:9443"}, {"name": "多规则-Old-buildLoginURL双端口问题测试", "originheader": "https://htportal.infogo.net.cn/auth/login/v1/authorize/oidc/authorize?response_type=code&redirect_uri=https%3A%2F%2Fwiki.infogo.net.cn%3A7443%2Flogin%2F33dcb962-0a75-4fda-88e6-38445830c397%2Fcallback&client_id=ECryowR3uGTExwiadXktnAVxOlkG2yVw", "header_key": "Location", "rewriterules": [{"regex": "https://wiki.infogo.net.cn", "replace": "https://wiki.infogo.net.cn:7443"}, {"regex": "https%3A%2F%2Fwiki.infogo.net.cn", "replace": "https://wiki.infogo.net.cn:7443"}, {"regex": "https://wiki.infogo.net.cn:443", "replace": "https://wiki.infogo.net.cn:7443"}, {"regex": "https%3A%2F%2Fwiki.infogo.net.cn%3A443", "replace": "https%3A%2F%2Fwiki.infogo.net.cn%3A7443"}], "expect": "https://htportal.infogo.net.cn/auth/login/v1/authorize/oidc/authorize?response_type=code&redirect_uri=https%3A%2F%2Fwiki.infogo.net.cn%3A7443%2Flogin%2F33dcb962-0a75-4fda-88e6-38445830c397%2Fcallback&client_id=ECryowR3uGTExwiadXktnAVxOlkG2yVw"}, {"name": "多规则-buildLoginURL双端口问题测试", "originheader": "https://htportal.infogo.net.cn/auth/login/v1/authorize/oidc/authorize?response_type=code&redirect_uri=https%3A%2F%2Fwiki.infogo.net.cn%3A7443%2Flogin%2F33dcb962-0a75-4fda-88e6-38445830c397%2Fcallback&client_id=ECryowR3uGTExwiadXktnAVxOlkG2yVw", "header_key": "Location", "rewriterules": [{"regex": "https://wiki.infogo.net.cn", "replace": "https://wiki.infogo.net.cn:7443"}, {"regex": "https%3A%2F%2Fwiki.infogo.net.cn", "replace": "https%3A%2F%2Fwiki.infogo.net.cn%3A7443"}, {"regex": "https://wiki.infogo.net.cn:443", "replace": "https://wiki.infogo.net.cn:7443"}, {"regex": "https%3A%2F%2Fwiki.infogo.net.cn%3A443", "replace": "https%3A%2F%2Fwiki.infogo.net.cn%3A7443"}], "expect": "https://htportal.infogo.net.cn/auth/login/v1/authorize/oidc/authorize?response_type=code&redirect_uri=https%3A%2F%2Fwiki.infogo.net.cn%3A7443%2Flogin%2F33dcb962-0a75-4fda-88e6-38445830c397%2Fcallback&client_id=ECryowR3uGTExwiadXktnAVxOlkG2yVw"}]}