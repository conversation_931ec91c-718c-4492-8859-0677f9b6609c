package service

import (
	"asdsec.com/asec/platform/app/console/app/scan_stg/constants"
	"asdsec.com/asec/platform/app/console/app/scan_stg/dto"
	"asdsec.com/asec/platform/app/console/app/scan_stg/repository"
	userDto "asdsec.com/asec/platform/app/console/app/user/dto"
	userService "asdsec.com/asec/platform/app/console/app/user/service"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/pkg/aerrors"
	"context"
	"sync"
)

var ScanStgServiceImpl ScanStgService

// ScanStgServiceInit 单例对象
var ScanStgServiceInit sync.Once

type ScanStgService interface {
	GetScanStgDetail(ctx context.Context, id string) (dto.GetScanStgDetailRsp, error)
	GetScanStgList(ctx context.Context, req dto.GetCommonListReq) (dto.GetScanStgListRsp, error)
	CreateScanStg(ctx context.Context, req dto.CreateScanStgReq) aerrors.AError
	UpdateScanStg(ctx context.Context, req dto.UpdateScanStgReq) aerrors.AError
	DeleteScanStg(ctx context.Context, id string) error
}

type scanStgService struct {
	db repository.ScanStgRepository
}

func (s scanStgService) GetScanStgDetail(ctx context.Context, id string) (dto.GetScanStgDetailRsp, error) {
	data, err := s.db.GetScanStgDetail(ctx, id)
	if err != nil {
		return dto.GetScanStgDetailRsp{}, err
	}

	infoRsp, err := userService.GetUserService().UserComponentEcho(
		ctx,
		userDto.UserComponentEchoReq{UserIds: data.UserIds, GroupIds: data.UserGroupIds},
	)
	if err != nil {
		return dto.GetScanStgDetailRsp{}, err
	}
	data.UserInfo = infoRsp.UserEcho
	data.GroupInfo = infoRsp.GroupEcho
	return data, err
}

func (s scanStgService) GetScanStgList(ctx context.Context, req dto.GetCommonListReq) (dto.GetScanStgListRsp, error) {
	return s.db.GetScanStgList(ctx, req)
}

func (s scanStgService) CreateScanStg(ctx context.Context, req dto.CreateScanStgReq) aerrors.AError {
	data, err := s.db.FindScanStgByName(ctx, req.StrategyName)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	if data.Id != "" {
		return aerrors.New("scan strategy name repeat.", constants.ScanStgNameRepeatErr)
	}
	return s.db.CreateScanStg(ctx, req)
}

func (s scanStgService) UpdateScanStg(ctx context.Context, req dto.UpdateScanStgReq) aerrors.AError {
	data, err := s.db.FindScanStgByName(ctx, req.StrategyName)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	if data.Id != "" && data.Id != req.Id {
		return aerrors.New("scan strategy name repeat.", constants.ScanStgNameRepeatErr)
	}
	return s.db.UpdateScanStg(ctx, req)
}

func (s scanStgService) DeleteScanStg(ctx context.Context, id string) error {
	return s.db.DeleteScanStg(ctx, id)
}

func GetScanStgService() ScanStgService {
	ScanStgServiceInit.Do(func() {
		ScanStgServiceImpl = &scanStgService{db: repository.NewScanStgRepository()}
	})
	return ScanStgServiceImpl
}
