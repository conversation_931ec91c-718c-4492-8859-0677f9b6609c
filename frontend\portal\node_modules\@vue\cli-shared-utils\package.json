{"name": "@vue/cli-shared-utils", "version": "4.5.19", "description": "shared utilities for vue-cli packages", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-shared-utils"}, "keywords": ["vue", "cli", "cli-shared-utils"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-shared-utils#readme", "dependencies": {"@achrinza/node-ipc": "9.2.2", "@hapi/joi": "^15.0.1", "chalk": "^2.4.2", "execa": "^1.0.0", "launch-editor": "^2.2.1", "lru-cache": "^5.1.1", "open": "^6.3.0", "ora": "^3.4.0", "read-pkg": "^5.1.1", "request": "^2.88.2", "semver": "^6.1.0", "strip-ansi": "^6.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "bef7a67566585876d56fa0e41b364675467bba8f"}