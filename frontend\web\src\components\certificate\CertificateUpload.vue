<template>
  <div class="certificate-upload">
    <t-form :data="formData" :rules="rules" label-align="top" ref="certificateForm">
      <t-form-item label="" name="" v-if="showTips">
        <div class="info-tips">
          <i class="iconfont icon-shuoming"></i>
          <span>数字证书为web应用（启用cname模式）提供数据HTTPS访问</span>
        </div>
      </t-form-item>
      
      <t-form-item label="证书名称" name="name" v-if="showName">
        <t-input 
          v-model="formData.name" 
          placeholder="请输入证书名称" 
          :maxlength="128"
          style="width: 100%;"
        />
      </t-form-item>
      
      <t-form-item label="证书" name="certificate">
        <div style="width: 100%;">
          <t-textarea 
            v-model="formData.certificate" 
            placeholder="支持上传 .pem 和 .crt 格式的证书文件，上传后会自动解析填充到此处&#10;也可以直接在此处添加、编辑、删除证书文本内容"
            :autosize="{ minRows: 6, maxRows: 12 }"
            style="width: 100%; display: block;"
          />
          <div style="margin-top: 8px; text-align: left; width: 100%; clear: both;">
            <t-upload
              v-model="certificateFiles"
              :accept="'.pem,.crt'"
              :multiple="false"
              :auto-upload="false"
              :show-upload-progress="false"
              :max="1"
              @change="handleCertificateFileChange"
            >
              <t-button variant="outline" size="small">
                <template #icon>
                  <i class="iconfont icon-shangchuan" style="font-size: 12px;"></i>
                </template>
                上传证书文件 (.pem, .crt)
              </t-button>
            </t-upload>
          </div>
        </div>
      </t-form-item>
      
      <t-form-item label="证书私钥" name="private_key">
        <div style="width: 100%;">
          <t-textarea 
            v-model="formData.private_key" 
            placeholder="支持上传 .pem 和 .key 格式的私钥文件，上传后会自动解析填充到此处&#10;也可以直接在此处添加、编辑、删除证书私钥文本内容"
            :autosize="{ minRows: 6, maxRows: 12 }"
            style="width: 100%; display: block;"
          />
          <div style="margin-top: 8px; text-align: left; width: 100%; clear: both;">
            <t-upload
              v-model="privateKeyFiles"
              :accept="'.pem,.key'"
              :multiple="false"
              :auto-upload="false"
              :show-upload-progress="false"
              :max="1"
              @change="handlePrivateKeyFileChange"
            >
              <t-button variant="outline" size="small">
                <template #icon>
                  <i class="iconfont icon-shangchuan" style="font-size: 12px;"></i>
                </template>
                上传私钥文件 (.pem, .key)
              </t-button>
            </t-upload>
          </div>
        </div>
      </t-form-item>
    </t-form>
  </div>
</template>

<script>
export default {
  name: 'CertificateUpload'
}
</script>

<script setup>
import { reactive, ref, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      name: '',
      certificate: '',
      private_key: ''
    })
  },
  rules: {
    type: Object,
    default: () => ({
      name: [
        { required: true, message: '请输入证书名称', type: 'error', trigger: 'blur' }
      ],
      certificate: [
        { required: true, message: '请输入证书内容', type: 'error', trigger: 'blur' }
      ],
      private_key: [
        { required: true, message: '请输入证书私钥', type: 'error', trigger: 'blur' }
      ]
    })
  },
  showTips: {
    type: Boolean,
    default: true
  },
  showName: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue'])

const certificateForm = ref()
const certificateFiles = ref([])
const privateKeyFiles = ref([])
const isUpdatingFromParent = ref(false) // 标记是否来自父组件的更新

const formData = reactive({
  id: '',
  name: '',
  certificate: '',
  private_key: ''
})

// 监听 props 变化，立即同步数据
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    isUpdatingFromParent.value = true
    formData.id = newVal.id || ''
    formData.name = newVal.name || ''
    formData.certificate = newVal.certificate || ''
    formData.private_key = newVal.private_key || ''
    isUpdatingFromParent.value = false
  }
}, { deep: true, immediate: true })

// 监听 formData 变化并向父组件发送，但排除来自父组件的更新
watch(formData, (newVal) => {
  if (!isUpdatingFromParent.value) {
    emit('update:modelValue', { ...newVal })
  }
}, { deep: true })

// 解析证书文件内容
const parseCertificateContent = (content) => {
  // 查找证书内容
  const certMatch = content.match(/-----BEGIN CERTIFICATE-----[\s\S]*?-----END CERTIFICATE-----/g)
  if (certMatch && certMatch.length > 0) {
    return certMatch[0].trim()
  }
  
  // 如果没找到标准格式，返回原内容
  return content.trim()
}

// 解析私钥文件内容
const parsePrivateKeyContent = (content) => {
  // 查找各种私钥格式
  const patterns = [
    /-----BEGIN PRIVATE KEY-----[\s\S]*?-----END PRIVATE KEY-----/g,
    /-----BEGIN RSA PRIVATE KEY-----[\s\S]*?-----END RSA PRIVATE KEY-----/g,
    /-----BEGIN EC PRIVATE KEY-----[\s\S]*?-----END EC PRIVATE KEY-----/g,
    /-----BEGIN ENCRYPTED PRIVATE KEY-----[\s\S]*?-----END ENCRYPTED PRIVATE KEY-----/g
  ]
  
  for (const pattern of patterns) {
    const match = content.match(pattern)
    if (match && match.length > 0) {
      return match[0].trim()
    }
  }
  
  // 如果没找到标准格式，返回原内容
  return content.trim()
}

// 读取文件内容
const readFileContent = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(e)
    reader.readAsText(file)
  })
}

// 处理证书文件上传
const handleCertificateFileChange = async (value) => {
  if (value && value.length > 0) {
    const file = value[0].raw || value[0]
    
    try {
      const content = await readFileContent(file)
      const parsedContent = parseCertificateContent(content)
      
      if (parsedContent.includes('-----BEGIN CERTIFICATE-----')) {
        formData.certificate = parsedContent
        MessagePlugin.success('证书文件解析成功')
        
        // 如果证书名称为空，尝试从文件名生成
        if (!formData.name) {
          const fileName = file.name.replace(/\.(pem|crt)$/i, '')
          formData.name = fileName
        }
      } else {
        MessagePlugin.warning('文件内容不是有效的证书格式')
      }
    } catch (error) {
      console.error('读取证书文件失败:', error)
      MessagePlugin.error('读取证书文件失败')
    }
  }
}

// 处理私钥文件上传
const handlePrivateKeyFileChange = async (value) => {
  if (value && value.length > 0) {
    const file = value[0].raw || value[0]
    
    try {
      const content = await readFileContent(file)
      const parsedContent = parsePrivateKeyContent(content)
      
      if (parsedContent.includes('-----BEGIN') && parsedContent.includes('PRIVATE KEY-----')) {
        formData.private_key = parsedContent
        MessagePlugin.success('私钥文件解析成功')
      } else {
        MessagePlugin.warning('文件内容不是有效的私钥格式')
      }
    } catch (error) {
      console.error('读取私钥文件失败:', error)
      MessagePlugin.error('读取私钥文件失败')
    }
  }
}

// 验证表单
const validate = async () => {
  if (!certificateForm.value) return false
  return await certificateForm.value.validate()
}

// 重置表单
const resetForm = () => {
  formData.id = ''
  formData.name = ''
  formData.certificate = ''
  formData.private_key = ''
  certificateFiles.value = []
  privateKeyFiles.value = []
  certificateForm.value?.clearValidate()
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm,
  formData
})
</script>

<style scoped>
.certificate-upload {
  width: 100%;
}

.certificate-upload .info-tips {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #e6f4ff;
  border: 1px solid #91caff;
  border-radius: 6px;
  font-size: 14px;
  color: #000;
  width: 100%;
  box-sizing: border-box;
}

.certificate-upload .info-tips .iconfont {
  color: #1976d2;
  margin-right: 8px;
  font-size: 16px;
}

.certificate-upload .info-tips span {
  line-height: 1.4;
}

.certificate-upload .t-upload {
  display: inline-block;
}

.certificate-upload .t-upload .t-upload__trigger {
  display: inline-block;
}

/* 文件上传区域样式 */
.certificate-upload :deep(.t-upload__dragger) {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  text-align: center;
  background: #fafafa;
  transition: border-color 0.3s;
}

.certificate-upload :deep(.t-upload__dragger:hover) {
  border-color: #0052d9;
}

/* 上传文件列表样式 */
.certificate-upload :deep(.t-upload__file-list) {
  margin-top: 8px;
}

.certificate-upload :deep(.t-upload__file) {
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
}

/* 上传按钮图标对齐 */
.certificate-upload .t-button .iconfont {
  margin-right: 4px;
}
</style>
