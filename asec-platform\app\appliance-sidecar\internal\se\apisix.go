package se

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"asdsec.com/asec/platform/app/console/app/watermark/constants"
	"asdsec.com/asec/platform/pkg/apisix"
	"asdsec.com/asec/platform/pkg/biz/dto"
	"github.com/jinzhu/copier"

	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/common/hostparser/hosts"
	"asdsec.com/asec/platform/app/appliance-sidecar/common/hostparser/parser"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"google.golang.org/grpc"
)

// 全局虚拟IP设置变量
var (
	virtualIPGlobalSettingsMutex sync.RWMutex
	virtualIPGlobalSettings      *pb.VirtualIPGlobalSettings
)

// 应用配置缓存相关变量
var (
	appConfigCacheMutex sync.RWMutex
	appConfigHashCache  = make(map[string]string) // appId -> configHash
)

// 平台域名缓存变量
var (
	platformHostCacheMutex sync.RWMutex
	platformHostCache      string
)

func SyncApisixApp(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   SyncApisixAppPlat,
		RunType:      common.SimpleSend,
		WaitSecond:   30,
		RandomOffset: 3,
	}
	common.Send(param)
}

func SyncApisixCrt(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   SyncApisixCrtPlat,
		RunType:      common.SimpleSend,
		WaitSecond:   30,
		RandomOffset: 3,
	}
	common.Send(param)
}

func SyncWebWatermark(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   SyncWebWatermarkPlat,
		RunType:      common.SimpleSend,
		WaitSecond:   30,
		RandomOffset: 3,
	}
	common.Send(param)
}

func SyncWebWatermarkPlat(conn *grpc.ClientConn, ctx context.Context) error {
	req := pb.WebGatewayWatermarkReq{}
	resp, err := pb.NewAppClient(conn).WebGatewayWatermark(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("SyncApisixWatermarkPlat err %v", err)
		return err
	}
	waterConf := resp.WatermarkConf
	waterPluginConf, err := structWatermarkConfReq(waterConf, waterConf.AppIds)
	if err != nil {
		global.Logger.Sugar().Errorf("SyncApisixAppPlat err %v", err)
		return err
	}
	err = doUpdateGlobalRule(waterPluginConf)
	if err != nil {
		return err
	}
	return nil
}

// SyncVirtualIPPoolsPlat 同步虚拟IP池到平台
func SyncVirtualIPPools(ctx context.Context, wg *sync.WaitGroup) {
	syncInterval := global.Conf.VirtualIPConfig.SyncInterval
	if syncInterval <= 0 {
		syncInterval = 30 // 默认30秒
	}

	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   SyncVirtualIPPoolsPlat,
		RunType:      common.SimpleSend,
		WaitSecond:   syncInterval,
		RandomOffset: 3,
	}
	common.Send(param)
}

func SyncVirtualIPPoolsPlat(conn *grpc.ClientConn, ctx context.Context) error {
	// global.Logger.Sugar().Debugf("开始同步虚拟IP池配置，设备ID: %d", global.ApplianceID)

	// 1. 获取虚拟IP池配置
	req := pb.WebGatewayVirtualIPPoolsReq{
		ApplianceId: strconv.FormatUint(global.ApplianceID, 10),
	}

	resp, err := pb.NewAppClient(conn).WebGatewayVirtualIPPools(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("获取虚拟IP池配置失败: %v", err)
		return err
	}

	// global.Logger.Sugar().Debugf("从平台获取到 %d 个虚拟IP池配置", len(resp.Pools))
	if resp.GlobalSettings != nil {
		global.Logger.Sugar().Debugf("全局虚拟IP设置: 启用=%v, 最大时长=%d小时, 描述=%s",
			resp.GlobalSettings.Enabled, resp.GlobalSettings.GlobalMaxDuration, resp.GlobalSettings.Description)

		// 更新全局虚拟IP设置
		virtualIPGlobalSettingsMutex.Lock()
		virtualIPGlobalSettings = resp.GlobalSettings
		virtualIPGlobalSettingsMutex.Unlock()
	} else {
		global.Logger.Sugar().Warn("未获取到全局虚拟IP设置")

		// 清空全局虚拟IP设置
		virtualIPGlobalSettingsMutex.Lock()
		virtualIPGlobalSettings = nil
		virtualIPGlobalSettingsMutex.Unlock()
	}

	// 2. 计算新配置的哈希值，用于快速比较
	newConfigHash := calculateConfigHash(resp.Pools, resp.GlobalSettings)

	configPath := global.Conf.VirtualIPConfig.ConfigFilePath
	if configPath == "" {
		configPath = "./virtualip_config.json"
	}

	// 3. 读取现有配置文件，检查是否需要更新
	oldConfigData, readErr := os.ReadFile(configPath)
	if readErr == nil {
		// 文件存在，尝试解析现有配置
		var oldConfig DynamicConfig
		if err := json.Unmarshal(oldConfigData, &oldConfig); err == nil {
			// 检查配置哈希是否相同
			if oldConfig.ConfigHash == newConfigHash {
				global.Logger.Sugar().Debug("虚拟IP配置无变化，跳过更新")
				return nil
			}
		}
		global.Logger.Sugar().Debug("检测到虚拟IP配置变化，需要更新")
	} else if !os.IsNotExist(readErr) {
		global.Logger.Sugar().Warnf("读取现有配置文件失败: %v", readErr)
	} else {
		global.Logger.Sugar().Debug("配置文件不存在，将创建新文件")
	}

	// 4. 转换为tun-server需要的JSON格式
	dynamicConfig := convertToDynamicConfig(resp.Pools, resp.GlobalSettings)
	// global.Logger.Sugar().Debugf("转换后配置启用状态: %v，池数量: %d", dynamicConfig.Enabled, len(dynamicConfig.Pools))

	// 5. 设置配置哈希
	dynamicConfig.ConfigHash = newConfigHash

	// 6. 序列化新配置
	newConfigData, err := json.MarshalIndent(dynamicConfig, "", "  ")
	if err != nil {
		global.Logger.Sugar().Errorf("序列化虚拟IP配置失败: %v", err)
		return err
	}

	// 7. 创建目录（如果不存在）
	if dir := filepath.Dir(configPath); dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			global.Logger.Sugar().Errorf("创建配置目录失败: %v", err)
			return err
		}
	}

	// 8. 写入新的配置文件
	// global.Logger.Sugar().Debugf("写入配置文件，大小: %d 字节", len(newConfigData))
	err = os.WriteFile(configPath, newConfigData, 0644)
	if err != nil {
		global.Logger.Sugar().Errorf("写入虚拟IP配置文件失败: %v", err)
		return err
	}

	global.Logger.Sugar().Debugf("虚拟IP配置同步完成,共%d个池,配置文件: %s", len(dynamicConfig.Pools), configPath)
	return nil
}

// CalculateAppConfigHash 计算应用配置的哈希值，用于检测配置变更
func CalculateAppConfigHash(appGateway dto.ApplicationGatewayDto, webConf apisix.CompatibleConfig, healthConf apisix.HealthConfig) string {
	hasher := sha256.New()

	// Web兼容配置
	webConfBytes, _ := json.Marshal(webConf)
	hasher.Write(webConfBytes)

	// 健康检查配置
	healthConfBytes, _ := json.Marshal(healthConf)
	hasher.Write(healthConfBytes)

	return hex.EncodeToString(hasher.Sum(nil))
}

// IsAppConfigChanged 检查应用配置是否发生变更（不更新缓存）
func IsAppConfigChanged(appId string, newConfigHash string) bool {
	appConfigCacheMutex.RLock()
	oldHash, exists := appConfigHashCache[appId]
	appConfigCacheMutex.RUnlock()

	return !exists || oldHash != newConfigHash
}

// UpdateAppConfigHash 更新应用配置哈希缓存（仅在同步成功后调用）
func UpdateAppConfigHash(appId string, configHash string) {
	appConfigCacheMutex.Lock()
	appConfigHashCache[appId] = configHash
	appConfigCacheMutex.Unlock()
}

func SyncApisixAppPlat(conn *grpc.ClientConn, ctx context.Context) error {
	applianceId := strconv.FormatUint(global.ApplianceID, 10)
	req := pb.WebGatewayRsAppReq{ApplianceId: applianceId}
	resp, err := pb.NewAppClient(conn).WebGatewayRsApp(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("SyncApisixAppPlat err %v", err)
		return err
	}
	existRouteMap := make(map[string]bool)
	for _, v := range resp.Apps {
		var appGateway dto.ApplicationGatewayDto
		err = copier.Copy(&appGateway, &v)
		if err != nil {
			global.Logger.Sugar().Errorf("copy app req err %v", err)
			return err
		}
		var healthConf apisix.HealthConfig //健康检查配置
		var webConf apisix.CompatibleConfig
		// 预处理JSON数据，处理空字符串字段兼容性问题
		processedJSON := preprocessCompatibleConfigJSON(v.WebCompatibleConfig)

		err = json.Unmarshal(processedJSON, &webConf)
		if err != nil {
			global.Logger.Sugar().Errorf("json unmarshal err %v", err)
			return err
		}

		err = json.Unmarshal(v.HealthConfig, &healthConf)
		if err != nil {
			global.Logger.Sugar().Errorf("healthConf json unmarshal err %v", err)
			return err
		}

		// 计算当前配置的哈希值
		configHash := CalculateAppConfigHash(appGateway, webConf, healthConf)

		// 构建 apisix upstream 和 route 配置
		upstreamReq, routeReq, err := structApisixRouteReq(appGateway, webConf, healthConf)
		if err != nil {
			global.Logger.Sugar().Errorf("get apisix req err %v", err)
			return err
		}

		// 检查配置是否发生变更
		if IsAppConfigChanged(v.Id, configHash) {
			global.Logger.Sugar().Debugf("检测到应用 %s (%s) 配置变更，开始同步到 apisix", appGateway.AppName, v.Id)

			// 1. 先创建/更新 Upstream
			upstreamId := v.Id
			upstreamJsonData, err := json.Marshal(upstreamReq)
			if err != nil {
				global.Logger.Sugar().Errorf("marshal upstream json failed. err=%v", err)
				return err
			}

			upstreamEndpoint := fmt.Sprintf("%s%s/%s", apisix.ApisixEndpoint, apisix.ApisixUpstream, upstreamId)
			upstreamRes, upstreamCode, err := apisix.SendRequestToApisix(upstreamEndpoint, apisix.PutMethod, apisix.XApisixKey, bytes.NewBuffer(upstreamJsonData))
			if err != nil || upstreamCode >= 300 {
				global.Logger.Sugar().Errorf("send upstream request to apisix failed.endpoint=%v, response_code=%d, reqres=%s,err=%v,jsonData=%s",
					upstreamEndpoint, upstreamCode, upstreamRes, err, upstreamJsonData)
				// 同步失败时不更新哈希缓存，确保下次还会重试
				if err != nil {
					return err
				}
				return errors.New("failed to send upstream request to apisix")
			}

			global.Logger.Sugar().Infof("应用 %s (%s) upstream 同步到 apisix 成功", appGateway.AppName, upstreamId)

			// 2. 再创建/更新 Route，使用 upstream_id 关联
			routeReq.UpstreamId = upstreamId
			routeJsonData, err := json.Marshal(routeReq)
			if err != nil {
				global.Logger.Sugar().Errorf("marshal route json failed. err=%v", err)
				return err
			}

			routeEndpoint := fmt.Sprintf("%s%s/%s", apisix.ApisixEndpoint, apisix.ApisixRoute, v.Id)
			routeRes, routeCode, err := apisix.SendRequestToApisix(routeEndpoint, apisix.PutMethod, apisix.XApisixKey, bytes.NewBuffer(routeJsonData))
			if err != nil || routeCode >= 300 {
				global.Logger.Sugar().Errorf("send route request to apisix failed.endpoint=%v, response_code=%d, reqres=%s,err=%v,jsonData=%s",
					routeEndpoint, routeCode, routeRes, err, routeJsonData)
				// 同步失败时不更新哈希缓存，确保下次还会重试
				if err != nil {
					return err
				}
				return errors.New("failed to send route request to apisix")
			}

			global.Logger.Sugar().Infof("应用 %s (%s) route 同步到 apisix 成功", appGateway.AppName, upstreamId)
			// 只有在同步成功后才更新哈希缓存
			UpdateAppConfigHash(v.Id, configHash)
			global.Logger.Sugar().Debugf("应用 %s (%s) route 配置同步到 apisix 成功", appGateway.AppName, v.Id)
		} else {
			global.Logger.Sugar().Debugf("应用 %s (%s) 配置无变化，跳过同步", appGateway.AppName, v.Id)
		}

		existRouteMap[v.Id] = true

		// 处理依赖站点路由
		if containsRule(webConf.DefaultRule, apisix.ApisixDependSiteRule) && webConf.DependSite != nil {
			// 获取主应用的插件配置和健康检查配置以便继承
			// 只有在主应用启用健康检查时才传递健康检查配置
			var inheritedChecks apisix.Checks
			if healthConf.Enable == "1" && healthConf.Config != nil {
				inheritedChecks = upstreamReq.Checks
				global.Logger.Sugar().Debugf("依赖站点路由将继承主应用的健康检查配置")
			} else {
				global.Logger.Sugar().Debugf("主应用健康检查已禁用，依赖站点路由不继承健康检查配置")
			}
			err := syncDependSiteRoutes(v.Id, appGateway, webConf, existRouteMap, routeReq.Plugins, inheritedChecks)
			if err != nil {
				global.Logger.Sugar().Errorf("同步依赖站点路由失败: %v", err)
				// 继续处理其他应用，不返回错误
			}
		}
	}

	err = doDelete(apisix.ApisixRoute, existRouteMap)
	if err != nil {
		global.Logger.Sugar().Errorf("DoDelRoute err %v", err)
		return err
	}
	return nil
}

func SyncApisixCrtPlat(conn *grpc.ClientConn, ctx context.Context) error {
	req := pb.WebGatewayRsCrtReq{}
	resp, err := pb.NewAppClient(conn).WebGatewayRsCrt(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("SyncApisixCrtPlat err %v", err)
		return err
	}
	existCrtMap := make(map[string]bool)
	for _, v := range resp.Crts {
		apisixReq := apisix.ApisixCrtReq{Cert: v.Cert, Key: v.Key, Snis: v.Domain}
		jsonData, err := json.Marshal(apisixReq)
		if err != nil {
			global.Logger.Sugar().Errorf("marshal json failed. err=%v", err)
			return err
		}
		jsonDataStr := strings.ReplaceAll(string(jsonData), "\\\\", "\\")
		existCrtMap[v.Id] = true
		endpoint := fmt.Sprintf("%s%s/%s", apisix.ApisixEndpoint, apisix.ApisixCrtRoute, v.Id)

		_, code, err := apisix.SendRequestToApisix(endpoint, apisix.PutMethod, apisix.XApisixKey, bytes.NewBuffer([]byte(jsonDataStr)))
		if err != nil || code >= 300 {
			global.Logger.Sugar().Errorf("send request to apisix failed.endpoint=%v, response_code=%d, err=%v", endpoint, code, err)
			if err != nil {
				return err
			}
			return errors.New("failed to send request to apisix")
		}
	}
	err = doDelete(apisix.ApisixCrtRoute, existCrtMap)
	if err != nil {
		global.Logger.Sugar().Errorf("DoDelCrt err %v", err)
		return err
	}
	return nil
}

func doDelete(routeRul string, existRouteUrl map[string]bool) error {
	endpoint := fmt.Sprintf("%s%s", apisix.ApisixEndpoint, routeRul)
	rspBodyByte, code, err := apisix.SendRequestToApisix(endpoint, apisix.GetMethod, apisix.XApisixKey, nil)
	if err != nil || code >= 300 {
		global.Logger.Sugar().Errorf("send request to apisix failed.endpoint=%v, response_code=%d, err=%v", endpoint, code, err)
		if err != nil {
			return err
		}
		return errors.New("failed to send request to apisix")
	}

	var rspBody apisix.ApisixListResp
	err = json.Unmarshal(rspBodyByte, &rspBody)
	if err != nil {
		global.Logger.Sugar().Errorf("parse apisix response failed. err=%v", err)
		return err
	}
	for _, v := range rspBody.List {
		if !existRouteUrl[v.Value.Id] {
			// 包含自定义保留路由标识则不被sidecar的自动同步删除
			if strings.Contains(v.Value.Name, apisix.ReserveRouteName) {
				continue
			}
			delEndpoint := fmt.Sprintf("%s%s/%s", apisix.ApisixEndpoint, routeRul, v.Value.Id)
			_, code, err := apisix.SendRequestToApisix(delEndpoint, apisix.DeleteMethod, apisix.XApisixKey, nil)
			if err != nil || code >= 300 {
				global.Logger.Sugar().Errorf("send request to apisix failed.endpoint=%v, response_code=%d, err=%v", delEndpoint, code, err)
				if err != nil {
					return err
				}
				return errors.New("del failed to send request to apisix")
			}
		}
	}
	return nil
}

func doUpdateGlobalRule(upPluginConf apisix.WebWatermarkPlugin) error {
	endpoint := fmt.Sprintf("%s%s", apisix.ApisixEndpoint, constants.ApisixGlobalRuleRoute)
	rspByte, code, err := apisix.SendRequestToApisix(endpoint, apisix.GetMethod, apisix.XApisixKey, nil)
	if err != nil || code >= 300 {
		global.Logger.Sugar().Errorf("send request to apisix failed.endpoint=%v, response_code=%d, err=%v", endpoint, code, err)
		if err != nil {
			return err
		}
		return errors.New("del failed to send request to apisix")
	}

	var rspBody apisix.ApisixRsp
	err = json.Unmarshal(rspByte, &rspBody)
	if err != nil {
		global.Logger.Sugar().Errorf("parse apisix response failed. err=%v", err)
		return err
	}
	rspBody.Value.Plugins[constants.ApisixWatermarkPlugin] = upPluginConf
	// 更改全局插件
	jsonData, err := json.Marshal(rspBody.Value)
	if err != nil {
		global.Logger.Sugar().Errorf("marshal json failed. err=%v", err)
		return err
	}
	_, code, err = apisix.SendRequestToApisix(endpoint, apisix.PutMethod, apisix.XApisixKey, bytes.NewBuffer(jsonData))
	if err != nil || code >= 300 {
		global.Logger.Sugar().Errorf("send request to apisix failed.endpoint=%v, response_code=%d, err=%v", endpoint, code, err)
		if err != nil {
			return err
		}
	}
	return nil
}

func structApisixRouteReq(req dto.ApplicationGatewayDto, webConf apisix.CompatibleConfig, healthConf apisix.HealthConfig) (upstream apisix.ApisixUpstreamReq, route apisix.ApisixRouteReq, err error) {
	platformHost := GetPlatformHost()
	global.Logger.Sugar().Debugf("网关连接平台地址为: %s", platformHost)

	var nodes []apisix.Node
	addressSplit := strings.Split(req.ServerAddress, ":")
	port := apisix.HttpSchemaPort
	if req.ServerSchema == apisix.HttpsSchema {
		port = apisix.HttpsSchemaPort
	}
	if len(addressSplit) == 2 {
		port, err = strconv.Atoi(addressSplit[1])
		if err != nil {
			return apisix.ApisixUpstreamReq{}, apisix.ApisixRouteReq{}, err
		}
	}

	// 动态提取主域名和端口
	mainDomain, mainPort := extractDomainAndPort(req.PublishAddress, req.PublishSchema)
	nodes = append(nodes, apisix.Node{Host: addressSplit[0], Port: port, Weight: 1})
	hasHealthCheckRule := (healthConf.Enable == "1" && healthConf.Config != nil)
	// 健康检查需要多节点才执行
	if hasHealthCheckRule {
		nodes = append(nodes, apisix.Node{Host: addressSplit[0], Port: port, Weight: 2})
	}
	timeout := global.Conf.WebGWConfig.Timeout
	if timeout <= 0 {
		timeout = apisix.ApisixDefaultTimeOut
	}

	// 默认HTTP方法列表
	defaultMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "CONNECT", "TRACE", "PURGE"}
	// 检查是否启用跨域功能
	methods := defaultMethods
	hasCrossDomainRule := containsRule(webConf.DefaultRule, apisix.ApisixCrossDomainRule)

	// 简化逻辑：如果default_rule中包含cross_domain，就是网关处理跨域
	if hasCrossDomainRule {
		// 网关处理跨域：添加OPTIONS方法
		methods = append(methods, "OPTIONS")
		global.Logger.Sugar().Debugf("启用网关跨域处理，添加OPTIONS方法")
	}
	// 判断是否需要设置 PassHost 为 rewrite
	passHost := "pass"      // 默认使用 pass
	var upstreamHost string // 当 pass_host 为 rewrite 时需要设置的目标主机
	if containsRule(webConf.DefaultRule, apisix.ApisixHostsRule) && webConf.Hosts != "" {
		passHost = "rewrite"         // 只有配置了自定义Host时才使用 rewrite
		upstreamHost = webConf.Hosts // 设置 upstream_host 为自定义的 Host 值
		global.Logger.Sugar().Debugf("启用自定义Host配置: pass_host=%s, upstream_host=%s", passHost, upstreamHost)
	} else {
		// 当 pass_host="pass" 时，upstream_host 字段为空，APISIX 会忽略此字段
		global.Logger.Sugar().Debugf("使用默认Host透传: pass_host=%s", passHost)
	}

	// 默认启用WebSocket
	enableWebSocket := true

	// 构建 Upstream 配置
	upstream = apisix.ApisixUpstreamReq{
		Name:          req.Id,
		Type:          apisix.ApisixDefaultUpstreamType,
		PassHost:      passHost,
		UpstreamHost:  upstreamHost,
		Scheme:        req.ServerSchema,
		Timeout:       apisix.Timeout{Send: timeout, Connect: apisix.ApisixDefaultTimeOut, Read: timeout},
		KeepalivePool: apisix.KeepalivePool{Size: 320, Requests: 1000, IdleTimeout: 60},
		Nodes:         nodes,
	}

	// 构建 Route 配置，使用 upstream_id 关联
	route = apisix.ApisixRouteReq{
		Name:            req.AppName,
		Status:          1,
		Methods:         methods,
		Uri:             req.Uri,
		Host:            mainDomain,
		EnableWebsocket: enableWebSocket,
		// 不再包含 Upstream 配置，而是使用 upstream_id 关联
		// UpstreamId 将在调用时设置
	}

	// 健康检查配置的构建
	if hasHealthCheckRule {
		upstream.Checks = apisix.Checks{
			Active: apisix.Active{
				Type:    healthConf.Config.Protocol,
				Host:    addressSplit[0],
				Port:    port,
				VerifyCertificate: false,
				Timeout: healthConf.Config.Timeout,
				Unhealthy: apisix.Unhealthy{
					HttpFailures: healthConf.Config.FailNums,
					TcpFailures:  healthConf.Config.FailNums,
					Timeouts:     healthConf.Config.UnHealthIntervals,
					Interval:     healthConf.Config.UnHealthIntervals,
				},
				Healthy:  apisix.Healthy{
					HttpStatuses: healthConf.Config.HealthCode,
					Interval: healthConf.Config.HealthIntervals,
					Success: healthConf.Config.SuccessNum,
				},
				HttpPath: healthConf.Config.Path,
			},
		}
	}

	plugins := make(map[string]any) // 处理跨域插件配置
	if hasCrossDomainRule {
		// 网关处理跨域：使用空的CORS配置，APISIX使用默认值
		plugins[apisix.ApisixCorsPlugin] = map[string]any{}
		global.Logger.Sugar().Debugf("启用CORS插件（网关处理跨域），使用空配置")
	} else {
		// 应用处理跨域（默认情况）：使用完整的CORS插件配置
		plugins[apisix.ApisixCorsPlugin] = apisix.CorsPlugin
		global.Logger.Sugar().Debugf("使用默认CORS配置（应用处理跨域）")
	}

	//response rewrite plugin
	var rspFilterCfg []dto.FilterConfig

	//response header cfg
	var rspHeaderUpdateCfg []dto.UpdateConfig
	var rspHeaderAddCfg []string
	var rspHeaderRemoveCfg []string
	rspHeaderSetCfg := make(map[string]string)
	//proxy-rewrite
	proxyRewriteAddCfg := make(map[string]string)
	proxyRewriteSetCfg := make(map[string]string)

	// 检查是否启用bypass降级模式
	bypassMode := containsRule(webConf.DefaultRule, apisix.ApisixBypassRule)

	for _, v := range webConf.DefaultRule {
		switch v {
		case apisix.ApisixUrlSmartRewriteRule:
			// 调用封装的函数生成智能改写规则
			headerUpdates, filterConfigs := generateSmartRewriteRules(req)
			rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, headerUpdates...)
			rspFilterCfg = append(rspFilterCfg, filterConfigs...)

		case apisix.ApisixUrlManualRewriteRule:
			// 处理手动URL改写规则
			if len(webConf.URLManualRewrite) > 0 {
				manualHeaderUpdates, manualFilterConfigs := generateManualRewriteRules(webConf.URLManualRewrite)
				rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, manualHeaderUpdates...)
				rspFilterCfg = append(rspFilterCfg, manualFilterConfigs...)
				global.Logger.Sugar().Debugf("处理手动URL改写规则，共%d条规则", len(webConf.URLManualRewrite))
			}

		case apisix.ApisixDependSiteRule:
			global.Logger.Sugar().Debugf("处理依赖站点规则: %s", v)
		case apisix.ApisixUrlControlRule:
			global.Logger.Sugar().Debugf("处理URL控制规则: %s", v)
		case apisix.ApisixCrossDomainRule:
			global.Logger.Sugar().Debugf("处理跨域规则: %s", v)
		case apisix.ApisixSingleSignOnRule:
			global.Logger.Sugar().Debugf("处理单点登录规则: %s", v)
		case apisix.ApisixHeaderConfigRule:
			global.Logger.Sugar().Debugf("处理请求头配置规则: %s", v)
		case apisix.ApisixHostsRule:
			global.Logger.Sugar().Debugf("处理自定义Host规则: %s", v)
		case apisix.ApisixSourceIPGetRule:
			global.Logger.Sugar().Debugf("处理源IP获取规则: %s", v)
		case apisix.ApisixSourceIPInsertRule:
			global.Logger.Sugar().Debugf("处理源IP插入规则: %s", v)
		case apisix.ApisixUriPath:
			global.Logger.Sugar().Debugf("开启应用路径规则: %s", v)
		case apisix.ApisixBypassRule:
			global.Logger.Sugar().Debugf("处理降级模式规则: %s", v)
		default:
			global.Logger.Sugar().Warnf("unsupport default rule: %s", v)
		}
	}
	for _, v := range webConf.HeaderConfig {
		if v.Key != "" {
			switch v.Field {
			case apisix.ResponseHeaderField:
				switch v.Operation {
				case apisix.HeaderOptSet:
					rspHeaderSetCfg[v.Key] = v.Value
				case apisix.HeaderOptUpdate:
					// 响应头支持真正的update操作
					rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
						HeaderKey:     v.Key,
						HeaderOrigin:  "", // 将在asec-response-rewrite插件中动态获取
						HeaderReplace: v.Value,
					})
					global.Logger.Sugar().Debugf("响应头update操作: %s -> %s", v.Key, v.Value)
				case apisix.HeaderOptRemove:
					// 响应头删除操作
					rspHeaderRemoveCfg = append(rspHeaderRemoveCfg, v.Key)
					global.Logger.Sugar().Debugf("响应头remove操作: %s", v.Key)
				default:
					rspHeaderAddCfg = append(rspHeaderAddCfg, fmt.Sprintf("%s:%s", v.Key, v.Value))
				}
			case apisix.RequestHeaderField:
				// 对于请求头，proxy-rewrite插件只支持set和add，将update转换为set
				if v.Operation == apisix.HeaderOptSet || v.Operation == apisix.HeaderOptUpdate {
					proxyRewriteSetCfg[v.Key] = v.Value
				} else {
					proxyRewriteAddCfg[v.Key] = v.Value
				}
			default:
				global.Logger.Sugar().Warnf("unsupport route cfg type rule: %s", v.Field)
			}
		}
	}

	rspHeaderCfg := dto.ResponseRewriteHeaderConfig{
		Update: rspHeaderUpdateCfg,
		Set:    rspHeaderSetCfg,
		Add:    rspHeaderAddCfg,
		Remove: rspHeaderRemoveCfg,
	}

	responseRewriteCfg := dto.ResponseRewritePlugin{
		BodyBase64: apisix.ResponseRewriteBodyBase64,
		Disable:    apisix.ResponseRewriteDisable,
		Headers:    rspHeaderCfg,
		Filters:    rspFilterCfg,
	}

	plugins[apisix.ApisixResponseRewritePlugin] = responseRewriteCfg
	//resp process
	responseProcessCfg := dto.ResponseProcessPlugin{
		Disable: false,
	}
	plugins[apisix.ApisixResponseProcessPlugin] = responseProcessCfg

	// 处理自定义Host配置 - 优先级高于header_config中的Host配置
	if containsRule(webConf.DefaultRule, apisix.ApisixHostsRule) && webConf.Hosts != "" {
		// 当使用 upstream pass_host=rewrite 时，APISIX 会自动设置 Host 头部为 upstream_host 的值
		// 因此不需要在 proxy-rewrite 插件中再次设置 Host 头部，避免冲突
		global.Logger.Sugar().Debugf("使用upstream pass_host=rewrite模式，Host将由APISIX自动设置为: %s", webConf.Hosts)
	}

	//proxy rewrite
	proxyRewriteSetCfg[apisix.HeaderOrigin] = fmt.Sprintf("%s://%s", req.ServerSchema, req.ServerAddress)
	proxyRewriteSetCfg[apisix.HeaderAcceptEncoding] = ""
	proxyRewriteCfg := dto.ProxyRewritePlugin{
		Headers: dto.ProxyRewriteHeaderCfg{Set: proxyRewriteSetCfg, Add: proxyRewriteAddCfg},
	}
	plugins[apisix.ApisixProxyRewritePlugin] = proxyRewriteCfg
	//ext-plugin-post-req
	var extPluginPostReqConf []dto.ExtPluginConf
	strategyConfByte, err := json.Marshal(dto.StrategyPluginConf{
		Enable:           true,
		AllowDegradation: bypassMode,
	})
	if err != nil {
		global.Logger.Sugar().Errorf("marshal strategy conf failed. err=%v", err)
		return apisix.ApisixUpstreamReq{}, apisix.ApisixRouteReq{}, err
	}
	extPluginPostReqConf = append(extPluginPostReqConf, dto.ExtPluginConf{
		Name:  apisix.StrategyPluginName,
		Value: string(strategyConfByte),
	})
	extPluginPostReq := dto.ExtPluginPostReqPlugin{
		Conf:             extPluginPostReqConf,
		AllowDegradation: bypassMode,
	}
	plugins[apisix.ApisixExtPluginPostReq] = extPluginPostReq

	if bypassMode {
		global.Logger.Sugar().Debugf("启用bypass降级模式，auth插件和ext-plugin-post-req插件的allow_degradation设置为true")
	}

	// 处理依赖站点配置
	if containsRule(webConf.DefaultRule, apisix.ApisixDependSiteRule) && webConf.DependSite != nil {
		// 生成依赖站点过滤规则（包含Location头部和响应体内容重写）
		dependHeaderUpdates, dependFilters, err := generateDependSiteFilters(webConf.DependSite, mainDomain, mainPort, req.PublishSchema)
		if err != nil {
			global.Logger.Sugar().Errorf("生成依赖站点过滤规则失败: %v", err)
		} else if len(dependFilters) > 0 || len(dependHeaderUpdates) > 0 {
			// 添加到asec-response-rewrite插件中
			if responseRewritePlugin, exists := plugins[apisix.ApisixResponseRewritePlugin]; exists {
				if responseRewriteMap, ok := responseRewritePlugin.(dto.ResponseRewritePlugin); ok {
					// 将现有的filters转换为[]any
					var existingFilters []any
					for _, filter := range responseRewriteMap.Filters {
						existingFilters = append(existingFilters, map[string]any{
							"options": filter.Options,
							"scope":   filter.Scope,
							"regex":   filter.Regex,
							"replace": filter.Replace,
						})
					}

					// 添加依赖站点响应体过滤规则
					for _, filter := range dependFilters {
						existingFilters = append(existingFilters, map[string]any{
							"regex":   filter.Regex,
							"replace": filter.Replace,
							"options": filter.Options,
							"scope":   filter.Scope,
						})
					}

					// 添加依赖站点Location头部更新规则
					responseRewriteMap.Headers.Update = append(responseRewriteMap.Headers.Update, dependHeaderUpdates...)

					// 更新responseRewriteMap并重新设置到plugins中
					responseRewriteMap.Filters = convertToFilterConfigs(existingFilters)
					plugins[apisix.ApisixResponseRewritePlugin] = responseRewriteMap
				}
			}
		}
	}

	// 处理源IP插入配置 - 使用自定义插件
	if containsRule(webConf.DefaultRule, apisix.ApisixSourceIPInsertRule) && webConf.SourceIPInsert != nil && webConf.SourceIPInsert.Header != "" {
		global.Logger.Sugar().Debugf("启用源IP插入配置, 请求头: %s, 方向: %s, 分隔符: %s, 位置: %d",
			webConf.SourceIPInsert.Header, webConf.SourceIPInsert.Direction, webConf.SourceIPInsert.Separator, webConf.SourceIPInsert.Position)

		// 使用自定义源IP插入插件
		pluginConfig := apisix.SourceIPInsertPlugin{
			Header:    webConf.SourceIPInsert.Header,
			Direction: webConf.SourceIPInsert.Direction,
			Separator: webConf.SourceIPInsert.Separator,
			Position:  webConf.SourceIPInsert.Position,
		}

		// 设置默认值
		if pluginConfig.Direction == "" {
			pluginConfig.Direction = "left"
		}
		if pluginConfig.Separator == "" {
			pluginConfig.Separator = ","
		}
		if pluginConfig.Position <= 0 {
			pluginConfig.Position = 1
		}

		plugins[apisix.ApisixSourceIPInsertPlugin] = pluginConfig
		global.Logger.Sugar().Debugf("已配置自定义源IP插入插件: %+v", pluginConfig)
	}

	// 处理源IP获取配置（用于日志审计）
	if containsRule(webConf.DefaultRule, apisix.ApisixSourceIPGetRule) && webConf.SourceIPGet != nil && webConf.SourceIPGet.Source != "" {
		// global.Logger.Sugar().Debugf("启用源IP获取，头部: %s, 可信IP: %s", webConf.SourceIPGet.Source, webConf.SourceIPGet.TrustIPs)

		// 解析可信任IP列表
		var trustIPs []string
		if webConf.SourceIPGet.TrustIPs != "" {
			ipList := strings.Split(webConf.SourceIPGet.TrustIPs, ",")
			for _, ip := range ipList {
				trimmedIP := strings.TrimSpace(ip)
				if trimmedIP != "" {
					trustIPs = append(trustIPs, trimmedIP)
				}
			}
		}

		// 转换头部名称格式
		sourceHeader := convertHeaderNameToRealIPFormat(webConf.SourceIPGet.Source)

		// 解析recursive参数
		recursive := webConf.SourceIPGet.Recursive == "1" || strings.ToLower(webConf.SourceIPGet.Recursive) == "true"

		// 配置real-ip插件，供后续拓展使用
		// realIPConfig := map[string]any{
		// 	"source":            sourceHeader,
		// 	"trusted_addresses": trustIPs,
		// 	"recursive":         recursive,
		// }
		//前端只配置source，其他参数使用默认值
		realIPConfig := map[string]any{
			"source": sourceHeader,
		}
		plugins[apisix.ApisixRealIPPlugin] = realIPConfig

		// global.Logger.Sugar().Debugf("已配置源IP获取: source=%s, trusted_addresses=%v, recursive=%v (false=提取最左边第一个IP)",
		// 	sourceHeader, trustIPs, recursive)
		global.Logger.Sugar().Debugf("已配置源IP获取: source=%s, recursive=%v", sourceHeader, recursive)
	}
	// 构建认证服务 URI
	authServiceURI := fmt.Sprintf("https://%s/auth/login/v1/token_verify", platformHost)
	// 初始化认证插件配置（根据bypass模式设置allow_degradation）
	forwardAuthConfig := map[string]any{
		"uri":               authServiceURI,
		"allow_degradation": bypassMode,
		"client_headers": []string{
			"Location",
			"Accept-Encoding",
			"Access-Control-Allow-Origin",
			"Access-Control-Allow-Credentials",
			"Origin",
			"Cookie",
		},
		"keepalive":         true,
		"keepalive_pool":    5,
		"keepalive_timeout": 60000,
		"request_headers": []string{
			"Cookie",
			"Accept-Encoding",
			"Access-Control-Allow-Origin",
			"Access-Control-Allow-Credentials",
			"Origin",
		},
		"request_method":   "GET",
		"ssl_verify":       false,
		"timeout":          3000,
		"upstream_headers": []string{},
	}

	// 处理URL控制配置 - 根据类型决定认证插件是否禁用
	urlControlEnabled := containsRule(webConf.DefaultRule, apisix.ApisixUrlControlRule) && webConf.URLControl != nil
	if urlControlEnabled {
		global.Logger.Sugar().Debugf("检测到URL控制规则，类型: %s, 配置: %s",
			webConf.URLControl.Type, webConf.URLControl.Text)

		switch webConf.URLControl.Type {
		case "free_auth":
			if webConf.URLControl.Text != "" {
				// 免认证：保持认证插件启用，仅配置 URL 控制插件处理免认证路径
				// asec-url-control 插件会根据路径匹配动态设置虚拟用户头部
				plugins[apisix.ApisixUrlControlPlugin] = map[string]any{
					"free_auth_paths": webConf.URLControl.Text,
				}
				global.Logger.Sugar().Debugf("免认证模式：保持%s插件启用，配置免认证路径: %s", apisix.ApisixForwardPlugin, webConf.URLControl.Text)
			}
		case "forbid_access":
			if webConf.URLControl.Text != "" {
				// 禁止访问：禁用认证插件，由asec-url-control插件设置虚拟用户头部和禁止访问标识
				global.Logger.Sugar().Debugf("禁止访问模式：已禁用%s插件，路径: %s", apisix.ApisixForwardPlugin, webConf.URLControl.Text)

				// 配置asec-url-control插件处理禁止访问路径
				plugins[apisix.ApisixUrlControlPlugin] = map[string]any{
					"forbid_access_paths": webConf.URLControl.Text,
				}
				global.Logger.Sugar().Debugf("已配置禁止访问路径到%s插件: %s", apisix.ApisixUrlControlPlugin, webConf.URLControl.Text)
			}

		default:
			global.Logger.Sugar().Warnf("未知的URL控制类型: %s", webConf.URLControl.Type)
		}
	} else {
		global.Logger.Sugar().Debugf("未启用URL控制规则，将使用正常认证流程")
	}

	// 处理单点登录配置
	if containsRule(webConf.DefaultRule, apisix.ApisixSingleSignOnRule) && webConf.SingleSignOn != nil {
		global.Logger.Sugar().Debugf("启用单点登录规则，类型: %s", webConf.SingleSignOn.Type)

		// 根据SSO类型处理不同的配置
		switch webConf.SingleSignOn.Type {
		case apisix.SingleSignOnTypeMicroApp:
			// 获取管理平台地址
			global.Logger.Sugar().Debugf("微应用认证使用平台域名: %s", platformHost)
			// 从缓存获取SSO配置
			ssoConfig := getSSOConfigForMicroApp(webConf)
			if ssoConfig == nil {
				global.Logger.Sugar().Warnf("无法获取微应用SSO配置，跳过微应用认证配置，继续处理其他配置")
				// 不返回错误，跳过微应用配置，继续处理其他SSO类型或其他配置
				break
			}

			// 根据IDP类型确定office_app_type
			var officeAppType string
			switch ssoConfig.IdpType {
			case "dingtalk":
				officeAppType = "dingtalk"
			case "qiyewx", "wechat":
				officeAppType = "qiyewx"
			case "feishu":
				officeAppType = "feishu"
			case "zhezhending":
				officeAppType = "zhezhending"
			default:
				global.Logger.Sugar().Warnf("未知的IDP类型: %s，跳过微应用配置，继续处理其他配置", ssoConfig.IdpType)
				// 不返回错误，跳过微应用配置，继续处理其他SSO类型或其他配置
			}

			// 构建微应用检测配置
			microAppDetection := map[string]any{
				"detection_enabled": true,                // 启用微应用检测
				"office_app_type":   officeAppType,       // dingtalk/qiyewx/feishu/zhezhending
				"platform_host":     platformHost,        // 管理平台地址
				"corp_id":           ssoConfig.CorpId,    // 企业ID（钉钉/企微/飞书）
				"app_id":            ssoConfig.AppId,     // 应用ID（钉钉clientId/企微agentId/飞书AppID）
				"app_secret":        ssoConfig.AppSecret, // 应用Secret（用于令牌验证）
				"idp_id":            ssoConfig.IdpId,     // IDP ID
			}

			// 将微应用配置添加到认证插件配置中
			forwardAuthConfig["micro_app_detection"] = microAppDetection

			global.Logger.Sugar().Debugf("微应用SSO配置：办公应用=%s, 管理平台=%s, 企业ID=%s, 应用ID=%s, IDP类型=%s, IDP_ID=%s",
				officeAppType,
				platformHost,
				ssoConfig.CorpId,
				ssoConfig.AppId,
				ssoConfig.IdpType,
				ssoConfig.IdpId)

		case "oauth2":
			global.Logger.Sugar().Debugf("OAuth2单点登录配置（待实现）")
			// TODO: 实现OAuth2配置

		case "cas":
			global.Logger.Sugar().Debugf("CAS单点登录配置（待实现）")
			// TODO: 实现CAS配置

		case apisix.SingleSignOnTypeFillForms:
			formFillURI := fmt.Sprintf("https://%s/console/v1/form-fill", platformHost)

			// 解析表单代填配置
			formConfig, err := parseFormFillConfig(webConf.SingleSignOn.Config)
			if err != nil {
				global.Logger.Sugar().Errorf("解析表单代填配置失败: %v", err)
				break
			}

			// 配置 asec-form-fill 插件
			plugins[apisix.ApisixFormFillPlugin] = map[string]any{
				"enable":      true,
				"uri":         formFillURI,
				"app_id":      req.Id,
				"form_config": formConfig,
				"_meta": map[string]any{
					"priority": 881,
				},
			}
			global.Logger.Sugar().Debugf("表单代填配置完成，应用ID: %s, 接口地址: %s, 代填类型: %s, 登录URL: %s",
				req.Id, formFillURI, formConfig["recognize_patterns"], formConfig["login_url"])

		default:
			global.Logger.Sugar().Warnf("未知的单点登录类型: %s", webConf.SingleSignOn.Type)
		}
	} else {
		global.Logger.Sugar().Debugf("未启用单点登录规则")
	}

	// 确保所有路由都包含asec-forward-auth插件（启用或禁用状态）
	// 这是核心安全机制，确保没有路由遗漏认证检查
	plugins[apisix.ApisixForwardPlugin] = forwardAuthConfig
	if forwardAuthConfig["_meta"] != nil {
		global.Logger.Sugar().Debugf("路由插件配置完成 - %s: 已禁用（禁止访问模式）", apisix.ApisixForwardPlugin)
	} else {
		global.Logger.Sugar().Debugf("路由插件配置完成 - %s: 已启用（正常认证模式）", apisix.ApisixForwardPlugin)
	}

	// 检查虚拟IP功能全局开关，如果启用则添加asec-virtual-ip插件到路由
	virtualIPGlobalSettingsMutex.RLock()
	globalVirtualIPSettings := virtualIPGlobalSettings
	virtualIPGlobalSettingsMutex.RUnlock()

	if globalVirtualIPSettings != nil && globalVirtualIPSettings.Enabled {
		// 获取虚拟IP服务地址 - 必须通过环境变量配置
		virtualIPServiceHost := os.Getenv("ASEC_VIRTUAL_IP_HOST")
		if virtualIPServiceHost == "" {
			virtualIPServiceHost = "asec_virtual_ip_host"
			global.Logger.Sugar().Warnf("环境变量 ASEC_VIRTUAL_IP_HOST 未设置, 使用容器hosts映射: %s", virtualIPServiceHost)
		} else {
			global.Logger.Sugar().Debugf("使用环境变量虚拟IP服务地址: %s", virtualIPServiceHost)
		}

		// 获取虚拟IP服务端口 - 可选环境变量，默认50015
		virtualIPServicePort := 50015
		if portEnv := os.Getenv("ASEC_VIRTUAL_IP_PORT"); portEnv != "" {
			if port, err := strconv.Atoi(portEnv); err == nil {
				virtualIPServicePort = port
				global.Logger.Sugar().Debugf("使用环境变量虚拟IP服务端口: %d", virtualIPServicePort)
			} else {
				global.Logger.Sugar().Warnf("环境变量 ASEC_VIRTUAL_IP_PORT 格式错误: %s, 使用默认端口: %d", portEnv, virtualIPServicePort)
			}
		}

		// 构建虚拟IP插件配置
		virtualIPConfig := map[string]any{
			"virtual_ip_service_host": virtualIPServiceHost,
			"virtual_ip_service_port": virtualIPServicePort,
			"timeout":                 3000,
			"_meta": map[string]any{
				"priority": 1998,
			},
		}

		plugins[apisix.ApisixVirtualIPPlugin] = virtualIPConfig
		global.Logger.Sugar().Debugf("路由插件配置完成 - %s: 已启用，服务地址=%s:%d，优先级=1990（在%s之后）",
			apisix.ApisixVirtualIPPlugin, virtualIPServiceHost, virtualIPServicePort, apisix.ApisixForwardPlugin)
	} else {
		global.Logger.Sugar().Debugf("虚拟IP功能未启用，跳过%s插件配置", apisix.ApisixVirtualIPPlugin)
	}

	// // 添加HTTP到HTTPS重定向插件
	// redirectConfig := map[string]any{
	// 	"http_to_https":       true, // 启用HTTP到HTTPS重定向
	// 	"encode_uri":          true, // 对URI进行URL编码
	// 	"append_query_string": true, // 追加查询字符串
	// 	"ret_code":            302,  // 使用302重定向，可选301永久重定向
	// }
	// plugins["redirect"] = redirectConfig
	// global.Logger.Sugar().Debugf("路由插件配置完成 - redirect: 已启用HTTP到HTTPS重定向")

	route.Plugins = plugins
	return upstream, route, nil
}

func structWatermarkConfReq(config *pb.Watermark, appIdList []string) (apisix.WebWatermarkPlugin, error) {
	var pluginConf apisix.WatermarkPluginConf
	err := copier.Copy(&pluginConf, &config.FontStyle)
	if err != nil {
		return apisix.WebWatermarkPlugin{}, err
	}

	if config.Secret && config.Enable {
		pluginConf.Secret = true
		pluginConf.Alpha = 99
	}
	//struct user
	pluginConf.EffectiveUsers = apisix.UserInfo{UserIds: config.UserIds, GroupIds: config.GroupIds, EnableAll: config.UserAll}
	//struct exclude user
	pluginConf.ExcludeUsers = apisix.UserInfo{UserIds: config.ExcludeUserIds, GroupIds: config.ExcludeGroupIds, EnableAll: config.ExcludeUserAll}
	//struct app
	var appConf apisix.EffectiveApps
	if config.EnableAllApp || config.EnableAllAppGroup {
		appConf.EnableAll = true
	} else {
		appIds := make([]string, 0)
		appIds = append(appIds, config.AppIds...)
		appIds = append(appIds, appIdList...)
		appConf.AppIds = appIds
	}
	pluginConf.EffectiveApps = appConf
	//content
	content := make([]string, 0)
	for _, v := range config.ContentConfig {
		if constants.WebWatermarkSupportMap[v] {
			content = append(content, v)
		} else {
			global.Logger.Sugar().Warnf("get not support column of web watermark config, column:%s", v)
		}
	}
	pluginConf.Content = content
	return apisix.WebWatermarkPlugin{WatermarkPluginConf: pluginConf, Enable: config.Enable}, nil
}

func SyncHosts(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   FetchAndSyncHosts,
		RunType:      common.SimpleSend,
		WaitSecond:   30,
		RandomOffset: 3,
	}
	common.Send(param)
}

// FetchAndSyncHosts 获取并同步云端主机名
func FetchAndSyncHosts(conn *grpc.ClientConn, ctx context.Context) error {
	// 1. 获取云端的 hosts 记录
	req := pb.WebGatewayHostsReq{}
	resp, err := pb.NewAppClient(conn).WebGatewayHosts(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("获取云端hosts记录失败: %v", err)
		return err
	}

	// 2. 生成新的 hosts 内容到临时文件
	tempContent := generateHostsContent(resp.Hosts)
	tempFile := "./sidecar_hosts.temp"

	// 3. 检查内容是否有变化（忽略时间戳）
	oldContent, err := os.ReadFile(tempFile)
	if err == nil && compareHostsContent(string(oldContent), tempContent) {
		global.Logger.Sugar().Debug("hosts 内容无变化，跳过更新")
		return nil
	}

	// 4. 写入新的临时文件
	err = os.WriteFile(tempFile, []byte(tempContent), 0644)
	if err != nil {
		global.Logger.Sugar().Errorf("写入临时文件失败: %v", err)
		return err
	}

	// 5. 加载系统 hosts 文件
	// hostsfile, err := hosts.LoadHostsFile(hosts.GetSystemHostsPath())
	hostsfile, err := hosts.LoadHostsFile(hosts.GetSystemHostsPath())
	if err != nil {
		global.Logger.Sugar().Errorf("加载hosts文件失败: %v", err)
		return err
	}

	// 6. 更新系统 hosts 文件中的我们的块
	err = updateHostsBlock(hostsfile, tempContent)
	if err != nil {
		global.Logger.Sugar().Errorf("更新hosts块失败: %v", err)
		return err
	}

	// 7. 保存更新后的 hosts 文件
	err = hostsfile.SaveHostsFile(hosts.GetSystemHostsPath())
	if err != nil {
		global.Logger.Sugar().Errorf("保存hosts文件失败: %v", err)
		return err
	}

	global.Logger.Sugar().Debug("检测到host文件变化, hosts文件同步完成")
	return nil
}

// compareHostsContent 比较两个 hosts 内容是否相同（忽略时间戳）
func compareHostsContent(oldContent, newContent string) bool {
	// 分割内容为行
	oldLines := strings.Split(oldContent, "\n")
	newLines := strings.Split(newContent, "\n")

	// 如果行数不同，内容不同
	if len(oldLines) != len(newLines) {
		return false
	}

	// 比较每一行（忽略时间戳行）
	for i := 0; i < len(oldLines); i++ {
		oldLine := oldLines[i]
		newLine := newLines[i]

		// 跳过时间戳行
		if strings.Contains(oldLine, "Last updated:") || strings.Contains(newLine, "Last updated:") {
			continue
		}

		// 其他行必须完全相同
		if oldLine != newLine {
			return false
		}
	}

	return true
}

// generateHostsContent 生成新的 hosts 内容
func generateHostsContent(hosts []*pb.WebGatewayHosts) string {
	var sb strings.Builder
	timestamp := time.Now().Format("2006-01-02 15:04:05")

	sb.WriteString("# === BEGIN SIDECAR MANAGED BLOCK ===\n")
	sb.WriteString(fmt.Sprintf("# Last updated: %s\n", timestamp))

	for _, h := range hosts {
		sb.WriteString(fmt.Sprintf("%s %s\n", h.Host, strings.Join(h.Address, " ")))
	}

	sb.WriteString("# === END SIDECAR MANAGED BLOCK ===\n")
	return sb.String()
}

// updateHostsBlock 更新 hosts 文件中的块
func updateHostsBlock(hostsfile *hosts.HostsFile, newContent string) error {
	// 找到我们的块的开始和结束
	beginMarker := "# === BEGIN SIDECAR MANAGED BLOCK ==="
	endMarker := "# === END SIDECAR MANAGED BLOCK ==="

	var newEntries []parser.HostsEntry
	var inOurBlock bool
	var foundBlock bool

	// 遍历现有条目
	for _, entry := range hostsfile.Entries {
		if entry.IsCommentLine && strings.Contains(entry.Comment, beginMarker) {
			inOurBlock = true
			foundBlock = true
			continue
		}

		if entry.IsCommentLine && strings.Contains(entry.Comment, endMarker) {
			inOurBlock = false
			continue
		}

		if !inOurBlock {
			newEntries = append(newEntries, entry)
		}
	}

	// 添加新的块内容
	for _, line := range strings.Split(newContent, "\n") {
		if line == "" {
			continue
		}

		entry := parser.HostsEntry{
			IsCommentLine: strings.HasPrefix(line, "#"),
			IsEmptyLine:   line == "",
			Comment:       line,
		}

		if !entry.IsCommentLine {
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				entry.IP = parts[0]
				entry.Domains = parts[1:]
				entry.Comment = ""
			}
		}

		newEntries = append(newEntries, entry)
	}

	// 如果之前没有找到块，确保在文件末尾添加一个空行
	if !foundBlock {
		newEntries = append(newEntries, parser.HostsEntry{
			IsEmptyLine: true,
		})
	}

	hostsfile.Entries = newEntries
	return nil
}

func (d DedicatedUserData) toDynamicDedicatedConfig() DynamicDedicatedConfig {
	return DynamicDedicatedConfig(d)
}

// 辅助函数：转换头部名称为real-ip插件格式
func convertHeaderNameToRealIPFormat(headerName string) string {
	// 将常见的头部名称转换为real-ip插件要求的格式
	headerMap := map[string]string{
		"X-Real-IP":           "http_x_real_ip",
		"X-Forwarded-For":     "http_x_forwarded_for",
		"CF-Connecting-IP":    "http_cf_connecting_ip",
		"X-Client-IP":         "http_x_client_ip",
		"X-Original-IP":       "http_x_original_ip",
		"Proxy-Client-IP":     "http_proxy_client_ip",
		"WL-Proxy-Client-IP":  "http_wl_proxy_client_ip",
		"Remote-Addr":         "remote_addr", // 直接使用nginx变量
		"True-Client-IP":      "http_true_client_ip",
		"X-Forwarded":         "http_x_forwarded",
		"Forwarded-For":       "http_forwarded_for",
		"X-Cluster-Client-IP": "http_x_cluster_client_ip",
	}

	if realIPFormat, exists := headerMap[headerName]; exists {
		return realIPFormat
	}

	// 如果是自定义头部，转换为http_xxx格式
	normalized := strings.ToLower(strings.ReplaceAll(headerName, "-", "_"))
	if !strings.HasPrefix(normalized, "http_") {
		normalized = "http_" + normalized
	}
	return normalized
}

// preprocessCompatibleConfigJSON 预处理CompatibleConfig的JSON，将空字符串的结构体字段转换为null
func preprocessCompatibleConfigJSON(jsonData []byte) []byte {
	// 将可能为空字符串的结构体字段转换为null，避免JSON反序列化错误
	jsonStr := string(jsonData)

	// 处理各种结构体字段的空字符串情况
	replacements := map[string]string{
		`"depend_site":""`:    `"depend_site":null`,
		`"source_ip_get":""`:  `"source_ip_get":null`,
		`"source_ip_set":""`:  `"source_ip_set":null`,
		`"cross_domain":""`:   `"cross_domain":null`,
		`"url_control":""`:    `"url_control":null`,
		`"single_sign_on":""`: `"single_sign_on":null`,
		`"websocket":""`:      `"websocket":null`,
		// 处理带空格的情况
		`"depend_site": ""`:    `"depend_site": null`,
		`"source_ip_get": ""`:  `"source_ip_get": null`,
		`"source_ip_set": ""`:  `"source_ip_set": null`,
		`"cross_domain": ""`:   `"cross_domain": null`,
		`"url_control": ""`:    `"url_control": null`,
		`"single_sign_on": ""`: `"single_sign_on": null`,
		`"websocket": ""`:      `"websocket": null`,
	}

	for old, new := range replacements {
		jsonStr = strings.ReplaceAll(jsonStr, old, new)
	}
	return []byte(jsonStr)
}

// generateSmartRewriteRules 生成智能改写规则
func generateSmartRewriteRules(req dto.ApplicationGatewayDto) ([]dto.UpdateConfig, []dto.FilterConfig) {
	var rspHeaderUpdateCfg []dto.UpdateConfig
	var rspFilterCfg []dto.FilterConfig

	// 服务器地址和发布地址的解析
	publishUrlWithinPort := fmt.Sprintf("%s://%s", req.PublishSchema, req.PublishAddress)

	// 确定服务器协议的默认端口
	serverDefaultPort := 80
	if req.ServerSchema == "https" {
		serverDefaultPort = 443
	}

	// 确定发布协议的默认端口
	publishDefaultPort := 80
	if req.PublishSchema == "https" {
		publishDefaultPort = 443
	}

	// 解析服务器地址
	serverHost := req.ServerAddress
	serverPort := ""
	if strings.Contains(req.ServerAddress, ":") {
		parts := strings.Split(req.ServerAddress, ":")
		serverHost = parts[0]
		serverPort = parts[1]
	}

	// 解析发布地址
	publishHost := req.PublishAddress
	publishPort := ""
	if strings.Contains(req.PublishAddress, ":") {
		parts := strings.Split(req.PublishAddress, ":")
		publishHost = parts[0]
		publishPort = parts[1]
	}

	global.Logger.Sugar().Debugf("智能改写规则生成 - 服务器地址: %s://%s, 发布地址: %s, 服务器主机: %s, 服务器端口: %s, 默认端口: %d",
		req.ServerSchema, req.ServerAddress, publishUrlWithinPort, serverHost, serverPort, serverDefaultPort)

	// 1. 精确匹配完整URL（避免端口叠加）
	if serverPort != "" {
		// 服务器地址本身就包含端口，只替换完全匹配的URL
		fullServerUrl := fmt.Sprintf("%s://%s:%s", req.ServerSchema, serverHost, serverPort)
		// global.Logger.Sugar().Debugf("生成服务器带端口的精确匹配规则: %s -> %s", fullServerUrl, publishUrlWithinPort)

		rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
			HeaderKey:     "Location",
			HeaderOrigin:  fullServerUrl,
			HeaderReplace: publishUrlWithinPort,
		})
		rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
			Options: apisix.ResponseRewriteFilterOption,
			Scope:   apisix.ResponseRewriteFilterScope,
			Regex:   fullServerUrl,
			Replace: publishUrlWithinPort,
		})

		// URL编码版本
		encodedFullServerUrl := url.QueryEscape(fullServerUrl)
		// global.Logger.Sugar().Debugf("生成服务器带端口的URL编码规则: %s -> %s", encodedFullServerUrl, publishUrlWithinPort)

		rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
			HeaderKey:     "Location",
			HeaderOrigin:  encodedFullServerUrl,
			HeaderReplace: publishUrlWithinPort,
		})
		rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
			Options: apisix.ResponseRewriteFilterOption,
			Scope:   apisix.ResponseRewriteFilterScope,
			Regex:   encodedFullServerUrl,
			Replace: url.QueryEscape(publishUrlWithinPort),
		})
	} else {
		// 服务器地址不包含端口，需要处理默认端口和无端口的情况
		// 使用精确匹配避免端口叠加问题，利用Lua插件的智能处理能力

		// 1. 匹配无端口的URL（精确匹配，避免误匹配带端口的URL）
		serverUrlNoPort := fmt.Sprintf("%s://%s", req.ServerSchema, serverHost)
		global.Logger.Sugar().Debugf("生成服务器无端口的精确匹配规则: %s -> %s (启用智能端口处理)", serverUrlNoPort, publishUrlWithinPort)

		rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
			HeaderKey:     "Location",
			HeaderOrigin:  serverUrlNoPort,
			HeaderReplace: publishUrlWithinPort,
		})
		// 生成精确匹配规则，Lua插件会智能处理端口问题
		rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
			Options: apisix.ResponseRewriteFilterOption,
			Scope:   apisix.ResponseRewriteFilterScope,
			Regex:   serverUrlNoPort, // Lua插件会进行智能端口感知处理
			Replace: publishUrlWithinPort,
		})

		// 2. 匹配默认端口的URL（精确匹配）
		serverUrlWithDefaultPort := fmt.Sprintf("%s://%s:%d", req.ServerSchema, serverHost, serverDefaultPort)
		// global.Logger.Sugar().Debugf("生成服务器默认端口的精确匹配规则: %s -> %s", serverUrlWithDefaultPort, publishUrlWithinPort)

		rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
			HeaderKey:     "Location",
			HeaderOrigin:  serverUrlWithDefaultPort,
			HeaderReplace: publishUrlWithinPort,
		})
		rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
			Options: apisix.ResponseRewriteFilterOption,
			Scope:   apisix.ResponseRewriteFilterScope,
			Regex:   serverUrlWithDefaultPort,
			Replace: publishUrlWithinPort,
		})

		// URL编码版本
		encodedServerUrlNoPort := url.QueryEscape(serverUrlNoPort)
		// global.Logger.Sugar().Debugf("生成服务器无端口的URL编码规则: %s -> %s", encodedServerUrlNoPort, publishUrlWithinPort)

		rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
			HeaderKey:     "Location",
			HeaderOrigin:  encodedServerUrlNoPort,
			HeaderReplace: publishUrlWithinPort,
		})
		rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
			Options: apisix.ResponseRewriteFilterOption,
			Scope:   apisix.ResponseRewriteFilterScope,
			Regex:   encodedServerUrlNoPort,
			Replace: url.QueryEscape(publishUrlWithinPort),
		})

		encodedServerUrlWithDefaultPort := url.QueryEscape(serverUrlWithDefaultPort)
		// global.Logger.Sugar().Debugf("生成服务器默认端口的URL编码规则: %s -> %s", encodedServerUrlWithDefaultPort, publishUrlWithinPort)

		rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
			HeaderKey:     "Location",
			HeaderOrigin:  encodedServerUrlWithDefaultPort,
			HeaderReplace: publishUrlWithinPort,
		})
		rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
			Options: apisix.ResponseRewriteFilterOption,
			Scope:   apisix.ResponseRewriteFilterScope,
			Regex:   encodedServerUrlWithDefaultPort,
			Replace: url.QueryEscape(publishUrlWithinPort),
		})
	}

	// 3. 处理发布地址带有非标准端口的反向情况
	// 如果发布地址带有非标准端口，需要处理响应中可能出现的发布域名（无端口或默认端口）
	if publishPort != "" {
		publishPortInt, err := strconv.Atoi(publishPort)
		if err == nil && publishPortInt != publishDefaultPort {
			// 发布地址带有非标准端口，需要添加反向规则

			// 3.1 处理响应中出现无端口的发布域名
			publishUrlNoPort := fmt.Sprintf("%s://%s", req.PublishSchema, publishHost)
			// global.Logger.Sugar().Debugf("生成发布域名无端口的补充规则: %s -> %s", publishUrlNoPort, publishUrlWithinPort)

			rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
				HeaderKey:     "Location",
				HeaderOrigin:  publishUrlNoPort,
				HeaderReplace: publishUrlWithinPort,
			})
			rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
				Options: apisix.ResponseRewriteFilterOption,
				Scope:   apisix.ResponseRewriteFilterScope,
				Regex:   publishUrlNoPort,
				Replace: publishUrlWithinPort,
			})

			// 3.2 处理响应中出现默认端口的发布域名
			publishUrlWithDefaultPort := fmt.Sprintf("%s://%s:%d", req.PublishSchema, publishHost, publishDefaultPort)
			// global.Logger.Sugar().Debugf("生成发布域名默认端口的替换规则: %s -> %s", publishUrlWithDefaultPort, publishUrlWithinPort)

			rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
				HeaderKey:     "Location",
				HeaderOrigin:  publishUrlWithDefaultPort,
				HeaderReplace: publishUrlWithinPort,
			})
			rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
				Options: apisix.ResponseRewriteFilterOption,
				Scope:   apisix.ResponseRewriteFilterScope,
				Regex:   publishUrlWithDefaultPort,
				Replace: publishUrlWithinPort,
			})

			// URL编码版本
			encodedPublishUrlNoPort := url.QueryEscape(publishUrlNoPort)
			// global.Logger.Sugar().Debugf("生成发布域名无端口的URL编码规则: %s -> %s", encodedPublishUrlNoPort, publishUrlWithinPort)

			rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
				HeaderKey:     "Location",
				HeaderOrigin:  encodedPublishUrlNoPort,
				HeaderReplace: publishUrlWithinPort,
			})
			rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
				Options: apisix.ResponseRewriteFilterOption,
				Scope:   apisix.ResponseRewriteFilterScope,
				Regex:   encodedPublishUrlNoPort,
				Replace: url.QueryEscape(publishUrlWithinPort),
			})

			encodedPublishUrlWithDefaultPort := url.QueryEscape(publishUrlWithDefaultPort)
			// global.Logger.Sugar().Debugf("生成发布域名默认端口的URL编码规则: %s -> %s", encodedPublishUrlWithDefaultPort, publishUrlWithinPort)

			rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
				HeaderKey:     "Location",
				HeaderOrigin:  encodedPublishUrlWithDefaultPort,
				HeaderReplace: publishUrlWithinPort,
			})
			rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
				Options: apisix.ResponseRewriteFilterOption,
				Scope:   apisix.ResponseRewriteFilterScope,
				Regex:   encodedPublishUrlWithDefaultPort,
				Replace: url.QueryEscape(publishUrlWithinPort),
			})
		}
	}

	// 去重处理：移除重复的 HeaderOrigin
	uniqueHeaderUpdateCfg := make([]dto.UpdateConfig, 0, len(rspHeaderUpdateCfg))
	uniqueFilterCfg := make([]dto.FilterConfig, 0, len(rspFilterCfg))
	seenHeaderOrigins := make(map[string]bool)
	seenFilterRegex := make(map[string]bool)

	for _, cfg := range rspHeaderUpdateCfg {
		if !seenHeaderOrigins[cfg.HeaderOrigin] {
			seenHeaderOrigins[cfg.HeaderOrigin] = true
			uniqueHeaderUpdateCfg = append(uniqueHeaderUpdateCfg, cfg)
		} else {
			global.Logger.Sugar().Debugf("智能改写规则去重：跳过重复的 HeaderOrigin: %s", cfg.HeaderOrigin)
		}
	}

	for _, cfg := range rspFilterCfg {
		if !seenFilterRegex[cfg.Regex] {
			seenFilterRegex[cfg.Regex] = true
			uniqueFilterCfg = append(uniqueFilterCfg, cfg)
		} else {
			global.Logger.Sugar().Debugf("智能改写规则去重：跳过重复的 FilterRegex: %s", cfg.Regex)
		}
	}

	global.Logger.Sugar().Debugf("智能改写规则生成完成，去重前: %d 个头部更新规则和 %d 个过滤规则, 去重后: %d 个头部更新规则和 %d 个过滤规则",
		len(rspHeaderUpdateCfg), len(rspFilterCfg), len(uniqueHeaderUpdateCfg), len(uniqueFilterCfg))
	return uniqueHeaderUpdateCfg, uniqueFilterCfg
}

// generateManualRewriteRules 生成手动改写规则
func generateManualRewriteRules(manualRewrites []apisix.URLManualRewrite) ([]dto.UpdateConfig, []dto.FilterConfig) {
	var rspHeaderUpdateCfg []dto.UpdateConfig
	var rspFilterCfg []dto.FilterConfig

	for _, rewrite := range manualRewrites {
		if rewrite.Before == "" || rewrite.After == "" {
			global.Logger.Sugar().Warnf("跳过无效的手动改写规则：before='%s', after='%s'", rewrite.Before, rewrite.After)
			continue
		}

		// 对before和after进行URL编码处理
		encodedBefore := url.QueryEscape(rewrite.Before)
		encodedAfter := url.QueryEscape(rewrite.After)

		// 1. 添加响应头Location改写规则（原始规则）
		rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
			HeaderKey:     "Location",
			HeaderOrigin:  rewrite.Before,
			HeaderReplace: rewrite.After,
		})

		// 2. 添加响应体内容改写规则（原始规则）
		rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
			Options: apisix.ResponseRewriteFilterOption,
			Scope:   apisix.ResponseRewriteFilterScope,
			Regex:   rewrite.Before,
			Replace: rewrite.After,
		})

		// 3. 只在编码后与原始值不同时，才添加URL编码规则，避免重复
		if encodedBefore != rewrite.Before {
			// 添加响应头Location改写规则（URL编码规则）
			rspHeaderUpdateCfg = append(rspHeaderUpdateCfg, dto.UpdateConfig{
				HeaderKey:     "Location",
				HeaderOrigin:  encodedBefore,
				HeaderReplace: rewrite.After,
			})
		}

		// 4. 只在编码后与原始值不同时，才添加URL编码的body规则
		if encodedBefore != rewrite.Before || encodedAfter != rewrite.After {
			rspFilterCfg = append(rspFilterCfg, dto.FilterConfig{
				Options: apisix.ResponseRewriteFilterOption,
				Scope:   apisix.ResponseRewriteFilterScope,
				Regex:   encodedBefore,
				Replace: encodedAfter,
			})
		}

		global.Logger.Sugar().Debugf("添加手动改写规则: %s -> %s (编码后: %s -> %s)",
			rewrite.Before, rewrite.After, encodedBefore, encodedAfter)
	}

	return rspHeaderUpdateCfg, rspFilterCfg
}

// SyncPlatformHost 同步平台域名到缓存
func SyncPlatformHost(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   SyncPlatformHostPlat,
		RunType:      common.SimpleSend,
		WaitSecond:   300, // 5分钟同步一次
		RandomOffset: 30,  // 随机偏移30秒
	}
	common.Send(param)
}

// SyncPlatformHostPlat 从平台同步域名配置
func SyncPlatformHostPlat(conn *grpc.ClientConn, ctx context.Context) error {
	applianceId := strconv.FormatUint(global.ApplianceID, 10)
	applianceIdUint64, err := strconv.ParseUint(applianceId, 10, 64)
	if err != nil {
		global.Logger.Sugar().Errorf("解析网关ID失败: %v", err)
		return err
	}

	req := pb.WebGatewayPlatformDomainReq{ApplianceId: applianceIdUint64}
	resp, err := pb.NewAppClient(conn).WebGatewayPlatformDomain(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("从平台获取域名配置失败: %v", err)
		// 如果RPC调用失败，尝试从环境变量获取并缓存
		envHost := os.Getenv("ASEC_PLATFORM_HOST")
		if envHost != "" {
			updatePlatformHostCache(envHost)
			global.Logger.Sugar().Warnf("RPC获取域名失败，使用环境变量: %s", envHost)
			return nil
		}
		return err
	}

	if resp.Success && resp.PlatformDomain != "" {
		updatePlatformHostCache(resp.PlatformDomain)
		global.Logger.Sugar().Debugf("成功从平台同步域名: %s", resp.PlatformDomain)
	} else {
		global.Logger.Sugar().Warnf("平台返回域名为空，消息: %s", resp.Message)
		// 如果平台返回空域名，尝试使用环境变量
		envHost := os.Getenv("ASEC_PLATFORM_HOST")
		if envHost != "" {
			updatePlatformHostCache(envHost)
			global.Logger.Sugar().Warnf("平台域名为空，使用环境变量: %s", envHost)
		}
	}

	return nil
}

// updatePlatformHostCache 更新平台域名缓存
func updatePlatformHostCache(host string) {
	platformHostCacheMutex.Lock()
	defer platformHostCacheMutex.Unlock()

	platformHostCache = host
}

// GetPlatformHost 获取平台域名，优先从缓存读取
func GetPlatformHost() string {
	platformHostCacheMutex.RLock()

	// 检查缓存是否有值
	if platformHostCache != "" {
		host := platformHostCache
		platformHostCacheMutex.RUnlock()
		return host
	}

	platformHostCacheMutex.RUnlock()

	// 缓存为空，从环境变量获取并缓存
	envHost := os.Getenv("ASEC_PLATFORM_HOST")
	if envHost != "" {
		updatePlatformHostCache(envHost)
		global.Logger.Sugar().Debugf("缓存为空，使用环境变量更新缓存: %s", envHost)
		return envHost
	}

	// 如果环境变量也没有，返回默认值
	defaultHost := "asec_platform_host"
	global.Logger.Sugar().Warnf("无法获取平台域名，使用默认值: %s", defaultHost)
	return defaultHost
}

// parseFormFillConfig 解析表单代填配置
func parseFormFillConfig(configData any) (map[string]any, error) {
	if configData == nil {
		return nil, errors.New("表单代填配置为空")
	}

	// 将配置转换为JSON字符串再解析，确保类型正确
	configBytes, err := json.Marshal(configData)
	if err != nil {
		return nil, fmt.Errorf("序列化配置失败: %v", err)
	}

	var formConfig map[string]any
	err = json.Unmarshal(configBytes, &formConfig)
	if err != nil {
		return nil, fmt.Errorf("反序列化配置失败: %v", err)
	}

	// 验证必要字段
	formType, ok := formConfig["recognize_patterns"].(string)
	if !ok || formType == "" {
		return nil, errors.New("recognize_patterns 字段缺失或无效")
	}

	loginURL, ok := formConfig["login_url"].(string)
	if !ok || loginURL == "" {
		return nil, errors.New("login_url 字段缺失或无效")
	}

	// 验证表单类型
	if formType != "smart" && formType != "precise" {
		return nil, fmt.Errorf("不支持的表单类型: %s", formType)
	}

	// 处理智能代填配置
	if formType == "smart" {
		// 验证智能代填必要字段
		fillAuthType, ok := formConfig["login_type"].(string)
		if !ok || fillAuthType == "" {
			return nil, errors.New("智能代填缺少 login_type 配置")
		}

		// 验证认证类型
		validAuthTypes := []string{"username_password", "phone_password", "email_password", "email_prefix_password", "custom_password"}
		isValidAuthType := false
		for _, validType := range validAuthTypes {
			if fillAuthType == validType {
				isValidAuthType = true
				break
			}
		}
		if !isValidAuthType {
			return nil, fmt.Errorf("不支持的认证类型: %s", fillAuthType)
		}

		// 构建data配置，包含login_type和默认的auto_login配置
		data := map[string]any{
			"login_type":     fillAuthType,
			"auto_login":     "1",   // 默认开启自动登录
			"auto_login_ped": "300", // 默认自动登录周期300秒
		}

		// 如果前端传入了auto_login相关配置，使用前端的配置
		if autoLogin, exists := formConfig["auto_login"]; exists {
			data["auto_login"] = autoLogin
		}
		if autoLoginPed, exists := formConfig["auto_login_ped"]; exists {
			data["auto_login_ped"] = autoLoginPed
		}

		formConfig["data"] = data

		global.Logger.Sugar().Debugf("智能代填配置: 认证类型=%s, 自动登录=%s, 自动登录周期=%s秒",
			fillAuthType, data["auto_login"], data["auto_login_ped"])

	} else if formType == "precise" {
		// 处理精确代填配置
		// 验证必要字段
		usernameInput, ok := formConfig["username_input"].(string)
		if !ok || usernameInput == "" {
			return nil, errors.New("精确代填缺少 username_input 配置")
		}

		passwordInput, ok := formConfig["password_input"].(string)
		if !ok || passwordInput == "" {
			return nil, errors.New("精确代填缺少 password_input 配置")
		}

		loginButton, ok := formConfig["login_button"].(string)
		if !ok || loginButton == "" {
			return nil, errors.New("精确代填缺少 login_button 配置")
		}

		// 获取输入框对应的值类型
		usernameValue, _ := formConfig["username_value"].(string)
		passwordValue, _ := formConfig["password_value"].(string)

		// 构建data配置，映射前端字段到后端期望的字段
		data := map[string]any{
			"account_input_type": "fixed", // 固定类型
			"pwd_input_type":     "fixed", // 固定类型
			"submit_input_type":  "fixed", // 固定类型
			"username_input":     usernameInput,
			"password_input":     passwordInput,
			"login_button":       loginButton,
			"username_value":     usernameValue,
			"password_value":     passwordValue,
		}

		formConfig["data"] = data

		global.Logger.Sugar().Debugf("精确代填配置: 用户名输入框=%s, 密码输入框=%s, 登录按钮=%s, 用户名值=%s, 密码值=%s",
			usernameInput, passwordInput, loginButton, usernameValue, passwordValue)
	}

	// 添加内部标识，便于插件识别
	formConfig["_internal_type"] = "sidecar_form_fill"
	formConfig["_config_version"] = "1.0"

	return formConfig, nil
}

// extractDomainAndPort 从发布地址中提取主域名和端口
// publishAddress: 发布地址，格式为 "domain:port" 或 "domain"
// publishSchema: 发布协议，"http" 或 "https"
// 返回值: (主域名, 主端口)
func extractDomainAndPort(publishAddress, publishSchema string) (string, int) {
	pubAddressSplit := strings.Split(publishAddress, ":")
	mainDomain := pubAddressSplit[0]
	var mainPort int

	if len(pubAddressSplit) == 2 {
		port, err := strconv.Atoi(pubAddressSplit[1])
		if err != nil {
			global.Logger.Sugar().Errorf("解析主端口失败: %v, 使用默认端口", err)
			// 解析失败时使用默认端口
			if publishSchema == "https" {
				mainPort = 443
			} else {
				mainPort = 80
			}
		} else {
			mainPort = port
		}
	} else {
		// 如果没有指定端口，根据协议确定默认端口
		if publishSchema == "https" {
			mainPort = 443
		} else {
			mainPort = 80
		}
	}

	return mainDomain, mainPort
}
