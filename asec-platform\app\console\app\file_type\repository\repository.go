package repository

import (
	"asdsec.com/asec/platform/app/console/app/file_type/dto"
	"asdsec.com/asec/platform/app/console/app/read_event/constants"
	filterDto "asdsec.com/asec/platform/app/console/app/read_event/dto"
	filterRepo "asdsec.com/asec/platform/app/console/app/read_event/repository"
	"asdsec.com/asec/platform/app/console/common"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/read_event"
	"asdsec.com/asec/platform/pkg/pb_conf"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"github.com/lib/pq"
	"gorm.io/gorm"
	"sort"
	"strconv"
)

// FileTypeRepository 接口定义
type FileTypeRepository interface {
	CreateFileType(ctx context.Context, req model.FileType) aerrors.AError
	UpdateFileType(ctx context.Context, req model.FileType) aerrors.AError
	DeleteFileType(ctx context.Context, req dto.DeleteFileTypeReq) error
	GetFileTypeTree(ctx context.Context, keyword string) ([]model.FileType, error)
	FindFileTypeByName(ctx context.Context, name string) (model.FileType, aerrors.AError)
	FindFileTypeByCode(ctx context.Context, code int64) (int64, aerrors.AError)
}

// NewFileTypeRepository 创建接口实现接口实现
func NewFileTypeRepository() FileTypeRepository {
	return &fileTypeRepository{}
}

const (
	fileTypeConfCenter = "file_type"
	childCode          = 1000
)

type fileTypeRepository struct {
}

func (f fileTypeRepository) CreateFileType(ctx context.Context, req model.FileType) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	if len(req.Suffix) == 0 {
		req.Suffix = make([]string, 0)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		err = tx.Create(&req).Error
		if err != nil {
			return err
		}
		err = commonApi.PushCommonAgentConf(
			tx,
			pb_conf.FileType{Code: strconv.FormatInt(req.Code, 10), Suffix: req.Suffix},
			fileTypeConfCenter, strconv.FormatInt(req.Code, 10),
			conf_center.AddConf,
			conf_center.GlobalConf,
			nil, nil, nil, nil)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (f fileTypeRepository) UpdateFileType(ctx context.Context, req model.FileType) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	if len(req.Suffix) == 0 {
		req.Suffix = make([]string, 0)
	}
	var eCount int64
	err = db.Model(read_event.EventFilter{}).
		Joins("INNER JOIN tb_file_type on tb_file_type.code = any(tb_event_filter.file_type_code)").
		Where("tb_file_type.id = ?", req.Id).Count(&eCount).Error

	err = db.Transaction(func(tx *gorm.DB) error {
		err = tx.Where("id = ?", req.Id).Select("name", "description", "suffix").Updates(&req).Error
		if err != nil {
			return err
		}
		err = commonApi.PushCommonAgentConf(
			tx,
			pb_conf.FileType{Code: strconv.FormatInt(req.Code, 10), Suffix: req.Suffix},
			fileTypeConfCenter, strconv.FormatInt(req.Code, 10),
			conf_center.UpdateConf,
			conf_center.GlobalConf,
			nil, nil, nil, nil)
		if err != nil {
			return err
		}
		if eCount > 0 {
			filterConf, err := filterRepo.NewEventFilterRepository().GetReadEventFilter(ctx)
			if err != nil {
				return err
			}
			err = filterRepo.NewEventFilterRepository().UpdateReadEventFilter(ctx, filterDto.UpdateEventFilterReq{
				Id:            filterConf.Id,
				FilterType:    constants.GlobalAgentFilter,
				Process:       filterConf.Process,
				FileTypeCode:  filterConf.FileTypeCode,
				EnableAllUser: filterConf.EnableAllUser,
				UserIds:       filterConf.UserIds,
				UserGroupIds:  filterConf.UserGroupIds,
				ReserveDay:    filterConf.ReserveDay,
			})
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (f fileTypeRepository) DeleteFileType(ctx context.Context, req dto.DeleteFileTypeReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		code := dbutil.EscapeForLike(strconv.FormatInt(req.Code, 10)) + "%"
		err = tx.Where("code::text like ? and built_in != 1", code).Delete(&model.FileType{}).Error
		if err != nil {
			return err
		}
		err = conf_center.ConfChange(conf_center.ConfChangeReq{
			ConfBizId: strconv.FormatInt(req.Code, 10), ConfType: fileTypeConfCenter, Tx: tx, RedisCli: global.SysRedisClient,
			ChangeType: conf_center.DelConf})
		if err != nil {
			return err
		}
		filterConf, err := filterRepo.NewEventFilterRepository().GetReadEventFilter(ctx)
		if err != nil {
			return err
		}
		err = filterRepo.NewEventFilterRepository().UpdateReadEventFilter(ctx, filterDto.UpdateEventFilterReq{
			Id:            filterConf.Id,
			FilterType:    constants.GlobalAgentFilter,
			Process:       filterConf.Process,
			FileTypeCode:  filterConf.FileTypeCode,
			EnableAllUser: filterConf.EnableAllUser,
			UserIds:       filterConf.UserIds,
			UserGroupIds:  filterConf.UserGroupIds,
			ReserveDay:    filterConf.ReserveDay,
		})
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func (f fileTypeRepository) GetFileTypeTree(ctx context.Context, keyword string) ([]model.FileType, error) {
	res := make([]model.FileType, 0)
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return res, err
	}
	allRet := make([]model.FileType, 0)
	if keyword != "" {
		lKeyword := "%" + dbutil.EscapeForLike(keyword) + "%"
		db = db.Where("name like ? or suffix && ?", lKeyword, pq.Array([]string{keyword}))
	}
	err = db.Model(&model.FileType{}).
		Select("*,CASE WHEN (array_length(suffix ,1) = 0 or suffix <@ '{}') THEN true else false END as is_type").
		Order("parent_id desc").Find(&allRet).Error
	if err != nil {
		return res, err
	}
	res = commonApi.FillChildren(allRet)
	sort.Slice(res, func(i, j int) bool {
		if res[i].CreateTime == (res[j].CreateTime) {
			return res[i].Code < res[j].Code
		} else {
			return res[i].CreateTime.Before(res[j].CreateTime)
		}
	})
	return res, nil
}
func (f fileTypeRepository) FindFileTypeByName(ctx context.Context, name string) (model.FileType, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model.FileType{}, aerrors.NewWithError(err, common.OperateError)
	}
	var res model.FileType
	err = db.Model(&model.FileType{}).Where("name = ?", name).Find(&res).Error
	if err != nil {
		return model.FileType{}, aerrors.NewWithError(err, common.OperateError)
	}
	return res, nil
}

func (f fileTypeRepository) FindFileTypeByCode(ctx context.Context, code int64) (int64, aerrors.AError) {
	var res model.FileType
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	if code == 0 {
		err = db.Model(&model.FileType{}).Where("parent_id = 0").Order("code desc").Limit(1).Find(&res).Error
		if err != nil {
			return 0, aerrors.NewWithError(err, common.OperateError)
		}
		return res.Code + 1, nil
	}
	err = db.Model(&model.FileType{}).Where("code >= ? and code < ?", code*childCode, (code+1)*childCode).
		Order("code desc").Limit(1).Find(&res).Error
	if err != nil {
		return 0, aerrors.NewWithError(err, common.OperateError)
	}
	if res.Id != 0 {
		return res.Code + 1, nil
	}
	return code*1000 + 1, nil
}
