package repository

import (
	"context"
	"encoding/json"
	"fmt"

	"asdsec.com/asec/platform/app/console/app/log/seven_access_log/constants"
	"asdsec.com/asec/platform/app/console/app/log/seven_access_log/dto"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"github.com/fatih/structs"
	"github.com/jinzhu/copier"
	"github.com/mssola/user_agent"
	"gorm.io/gorm"
)

type salRepository struct {
}

func (s salRepository) GetSensitiveLogList(ctx context.Context, req dto.GetSensitiveLogListReq) (dto.GetSensitiveLogListRsp, error) {
	var pageReq model.Pagination
	err := copier.Copy(&pageReq, &req)
	if err != nil {
		return dto.GetSensitiveLogListRsp{}, err
	}
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return dto.GetSensitiveLogListRsp{}, err
	}
	if pageReq.Search != "" {
		if req.SearchColumn != "" {
			pageReq.SearchColumns = []string{req.SearchColumn}
		} else {
			pageReq.SearchColumns = []string{"user_name", "app_name", "activity", "url", "file_name", "sensitive_info", "strategy_action"}
		}
	}
	var data []dto.SevenAccessLog
	db = db.Model(dto.SevenAccessLog{}).
		Select("uuid,access_time,user_name,app_name,app_type," +
			"url,strategy_action,file_name,sensitive_info,activity").Where("empty(app_id) = 0 AND empty(sensitive_info) = 0").Order("access_time desc")
	if req.StartTime != "" || req.EndTime != "" {
		db = db.Where("access_time >= ? and access_time <= ?", req.StartTimeStamp, req.EndTimeStamp)
	}

	pageReq, err = model.Paginate(&data, &pageReq, db)
	if err != nil {
		return dto.GetSensitiveLogListRsp{}, err
	}
	var rsp []dto.SensitiveLogListItem
	for _, v := range data {
		var item dto.SensitiveLogListItem
		err = copier.Copy(&item, &v)
		if err != nil {
			return dto.GetSensitiveLogListRsp{}, err
		}
		item.AccessTime = v.AccessTime.Format("2006-01-02 15:04:05")
		item.SensitiveInfo = make([]dto.SensitiveItem, 0)
		if v.SensitiveInfo != "" {
			err = json.Unmarshal([]byte(v.SensitiveInfo), &item)
			if err != nil {
				return dto.GetSensitiveLogListRsp{}, err
			}
		}
		rsp = append(rsp, item)
	}
	return dto.GetSensitiveLogListRsp{
		CommonPage: model.CommonPage{CurrentPage: pageReq.Page, PageSize: pageReq.Limit, TotalNum: int(pageReq.TotalRows)},
		Data:       rsp,
	}, nil
}

func (s salRepository) GetSensitiveLogDetail(ctx context.Context, uuid string) (dto.SensitiveLogDetail, error) {
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return dto.SensitiveLogDetail{}, err
	}
	var data dto.SevenAccessLog
	err = db.Model(dto.SevenAccessLog{}).Where("uuid = ?", uuid).Find(&data).Error
	if err != nil {
		return dto.SensitiveLogDetail{}, err
	}
	var rsp dto.SensitiveLogDetail
	err = copier.Copy(&rsp, &data)
	if err != nil {
		return dto.SensitiveLogDetail{}, err
	}
	rsp.AccessTime = data.AccessTime.Format("2006-01-02 15:04:05")
	rsp.SensitiveInfo = make([]dto.SensitiveItem, 0)
	if data.SensitiveInfo != "" {
		err = json.Unmarshal([]byte(data.SensitiveInfo), &rsp)
		if err != nil {
			return dto.SensitiveLogDetail{}, err
		}
	}
	// 填充request/response header
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.SensitiveLogDetail{}, err
	}
	var headerConf []dto.SevenAccessLogConf
	err = pgDb.Model(dto.SevenAccessLogConf{}).Where("enable").Find(&headerConf).Error
	if err != nil {
		return dto.SensitiveLogDetail{}, err
	}
	valueMap := structs.Map(data)

	requestHeader := make(map[string]string)
	responseHeader := make(map[string]string)
	for _, v := range headerConf {
		value := fmt.Sprintf("%v", valueMap[v.Key])
		if valueMap[v.Key] == "" {
			value = "-"
		}
		if v.Type == constants.RequestHeaderField {
			requestHeader[v.HeaderName] = value
		}
		if v.Type == constants.ResponseHeaderField {
			responseHeader[v.HeaderName] = value
		}

	}
	rsp.RequestHeader = requestHeader
	rsp.ResponseHeader = responseHeader
	return rsp, nil
}

func (s salRepository) GetSevenAccessLogConf(ctx context.Context) ([]dto.SevenAccessLogConf, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	var res []dto.SevenAccessLogConf
	err = db.Model(dto.SevenAccessLogConf{}).Order("key asc").Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (s salRepository) UpdateSevenAccessLogConf(ctx context.Context, req dto.UpdateSevenAccessLogConfReq) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	var enableColumn []dto.SevenAccessLogConf
	err = db.Model(dto.SevenAccessLogConf{}).Where("key in ?", req.EnableColumn).Find(&enableColumn).Error
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(dto.SevenAccessLogConf{}).Where("key in ?", req.EnableColumn).Update("enable", true).Error
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		err = tx.Model(dto.SevenAccessLogConf{}).Where("key not in ?", req.EnableColumn).Update("enable", false).Error
		return err
	})
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

//func structKafkaPluginReq(enableColumns []dto.SevenAccessLogConf, serverHost string) dto.AsecKafkaLogPlugin {
//	res := dto.AsecKafkaLogPlugin{
//		KafkaTopic:         constants.KafkaSevenAccessLogTopic,
//		Brokers:            []dto.Broker{{Host: serverHost, Port: constants.KafkaSevenAccessLogPort}},
//		StaticResourceConf: dto.StaticResourceConf{Enable: constants.DefaultStaticResourceEnable, FileTypes: constants.DefaultStaticResource},
//	}
//	logFormatMap := constants.DefaultLogFormatMap
//	for _, v := range enableColumns {
//		switch v.Key {
//		case constants.ReqBody:
//			res.IncludeReqBody = true
//			break
//		case constants.RspBody:
//			res.IncludeRespBody = true
//			break
//		default:
//			logFormatMap[v.Key] = v.ColumnName
//		}
//	}
//	res.LogFormat = logFormatMap
//	return res
//}

func (s salRepository) GetSevenAccessLogList(ctx context.Context, req dto.GetSevenAccessLogListReq) (dto.GetSevenAccessLogListRsp, error) {
	var pageReq model.Pagination
	err := copier.Copy(&pageReq, &req)
	if err != nil {
		return dto.GetSevenAccessLogListRsp{}, err
	}
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return dto.GetSevenAccessLogListRsp{}, err
	}
	if pageReq.Search != "" {
		pageReq.SearchColumns = []string{"user_name", "strategy_name", "url", "app_name"}
	}
	var data []dto.SevenAccessLog
	db = db.Model(dto.SevenAccessLog{}).
		Select("uuid,access_time,user_name,req_user_agent,app_name,app_type," +
			"url,strategy_name,strategy_action").Where("app_id != ''")
	if req.Order != "" {
		db = db.Order(req.Order)
	} else {
		db = db.Order("access_time desc")
	}
	if req.StartTime != "" || req.EndTime != "" {
		db = db.Where("access_time >= ? and access_time <= ?", req.StartTimeStamp, req.EndTimeStamp)
	}

	pageReq, err = model.Paginate(&data, &pageReq, db)
	if err != nil {
		return dto.GetSevenAccessLogListRsp{}, err
	}
	var rsp []dto.SevenAccessLogListItem
	for _, v := range data {
		var item dto.SevenAccessLogListItem
		err = copier.Copy(&item, &v)
		if err != nil {
			return dto.GetSevenAccessLogListRsp{}, err
		}
		ua := user_agent.New(v.ReqUserAgent)
		item.AccessTime = v.AccessTime.Format("2006-01-02 15:04:05")
		item.Browser, _ = ua.Browser()
		rsp = append(rsp, item)
	}
	return dto.GetSevenAccessLogListRsp{
		CommonPage: model.CommonPage{CurrentPage: pageReq.Page, PageSize: pageReq.Limit, TotalNum: int(pageReq.TotalRows)},
		Data:       rsp,
	}, nil
}

func (s salRepository) GetSevenAccessLogDetail(ctx context.Context, uuid string) (dto.SevenAccessLogDetail, error) {
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return dto.SevenAccessLogDetail{}, err
	}
	var data dto.SevenAccessLog
	err = db.Model(dto.SevenAccessLog{}).Where("uuid = ?", uuid).Find(&data).Error
	if err != nil {
		return dto.SevenAccessLogDetail{}, err
	}
	var rsp dto.SevenAccessLogDetail
	err = copier.Copy(&rsp, &data)
	if err != nil {
		return dto.SevenAccessLogDetail{}, err
	}
	ua := user_agent.New(data.ReqUserAgent)
	rsp.Browser, _ = ua.Browser()
	rsp.AccessTime = data.AccessTime.Format("2006-01-02 15:04:05")
	// 填充request/response header
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.SevenAccessLogDetail{}, err
	}
	var headerConf []dto.SevenAccessLogConf
	err = pgDb.Model(dto.SevenAccessLogConf{}).Where("enable").Find(&headerConf).Error
	if err != nil {
		return dto.SevenAccessLogDetail{}, err
	}
	valueMap := structs.Map(data)

	requestHeader := make(map[string]string)
	responseHeader := make(map[string]string)
	for _, v := range headerConf {
		value := fmt.Sprintf("%v", valueMap[v.Key])
		if valueMap[v.Key] == "" {
			value = "-"
		}
		if v.Type == constants.RequestHeaderField {
			requestHeader[v.HeaderName] = value
		}
		if v.Type == constants.ResponseHeaderField {
			responseHeader[v.HeaderName] = value
		}

	}
	rsp.RequestHeader = requestHeader
	rsp.ResponseHeader = responseHeader
	return rsp, nil
}

type SalRepository interface {
	GetSevenAccessLogList(ctx context.Context, req dto.GetSevenAccessLogListReq) (dto.GetSevenAccessLogListRsp, error)
	GetSevenAccessLogDetail(ctx context.Context, uuid string) (dto.SevenAccessLogDetail, error)
	GetSevenAccessLogConf(ctx context.Context) ([]dto.SevenAccessLogConf, error)
	UpdateSevenAccessLogConf(ctx context.Context, req dto.UpdateSevenAccessLogConfReq) aerrors.AError

	GetSensitiveLogList(ctx context.Context, req dto.GetSensitiveLogListReq) (dto.GetSensitiveLogListRsp, error)
	GetSensitiveLogDetail(ctx context.Context, uuid string) (dto.SensitiveLogDetail, error)
}

func NewSalRepository() SalRepository {
	return salRepository{}
}
