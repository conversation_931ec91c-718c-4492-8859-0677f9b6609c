package service

import (
	"context"
	"encoding/json"

	"asdsec.com/asec/platform/app/auth/internal/common"

	pb "asdsec.com/asec/platform/api/auth/v1/admin"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/jinzhu/copier"
)

func (s *AdminService) CreateAuthPolicy(ctx context.Context, req *pb.CreateAuthPolicyRequest) (*pb.CreateAuthPolicyReply, error) {
	var param dto.CreateAuthPolicyParam
	if err := copier.Copy(&param, req); err != nil {
		return &pb.CreateAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	var err error
	param.AuthEnhancement, err = json.Marshal(req.AuthEnhancement)
	if err != nil {
		return &pb.CreateAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	param.CorpId = corpId
	if err := s.authPolicy.CreateAuthPolicy(ctx, param); err != nil {
		return &pb.CreateAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateAuthPolicyReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) UpdateAuthPolicy(ctx context.Context, req *pb.UpdateAuthPolicyRequest) (*pb.UpdateAuthPolicyReply, error) {
	var param dto.UpdateAuthPolicyParam
	if err := copier.Copy(&param, req); err != nil {
		return &pb.UpdateAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	var err error
	param.AuthEnhancement, err = json.Marshal(req.AuthEnhancement)
	if err != nil {
		return &pb.UpdateAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdateAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	param.CorpId = corpId
	if err := s.authPolicy.UpdateAuthPolicy(ctx, param); err != nil {
		return &pb.UpdateAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.UpdateAuthPolicyReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) ListAuthPolicy(ctx context.Context, req *pb.ListAuthPolicyRequest) (*pb.ListAuthPolicyReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListAuthPolicyReply{}, err
	}

	limit, offset := common.GetLimitOffset(req.Limit, req.Offset)
	resp, err := s.authPolicy.ListAuthPolicy(ctx, corpId, req.RootGroupId, int(limit), int(offset))
	if err != nil {
		return &pb.ListAuthPolicyReply{}, err
	}
	var result []*pb.ListAuthPolicyReply_AuthPolicy
	for _, p := range resp.AuthPolicies {
		var groupInfo []*pb.ListAuthPolicyReply_GroupInfo
		for _, g := range p.GroupInfoList {
			groupInfo = append(groupInfo, &pb.ListAuthPolicyReply_GroupInfo{
				Id:   g.ID,
				Name: g.Name,
				Path: g.Path,
			})
		}
		var userInfo []*pb.ListAuthPolicyReply_UserInfo
		for _, u := range p.UserInfoList {
			userInfo = append(userInfo, &pb.ListAuthPolicyReply_UserInfo{
				Id:          u.ID,
				Name:        u.Name,
				DisplayName: u.DisplayName,
				Path:        u.Path,
			})
		}
		var idpInfoMap pb.ListAuthPolicyReply_IdpInfoMap
		if err := copier.Copy(&idpInfoMap, &p.IdpInfoMap); err != nil {
			return &pb.ListAuthPolicyReply{}, err
		}

		var temp pb.ListAuthPolicyReply_AuthPolicy
		if err := copier.Copy(&temp, p); err != nil {
			return &pb.ListAuthPolicyReply{}, err
		}
		temp.Id = p.ID
		temp.IdpInfoMap = &idpInfoMap
		temp.GroupInfoList = groupInfo
		temp.UserInfoList = userInfo
		result = append(result, &temp)
	}
	return &pb.ListAuthPolicyReply{AuthPolicyList: result, Count: uint32(resp.Count)}, nil
}

func (s *AdminService) DeleteAuthPolicy(ctx context.Context, req *pb.DeleteAuthPolicyRequest) (*pb.DeleteAuthPolicyReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.DeleteAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	if err := s.authPolicy.DeleteAuthPolicy(ctx, corpId, req.Id, req.Name); err != nil {
		return &pb.DeleteAuthPolicyReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.DeleteAuthPolicyReply{Status: pb.StatusCode_SUCCESS}, nil
}
