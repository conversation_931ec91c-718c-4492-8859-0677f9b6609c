package dto

import "asdsec.com/asec/platform/pkg/model/auth_model"

type UpdateSwitchReq struct {
	SwitchUsers
	ModuleCode int `form:"agent_module" json:"agent_module" binding:"required,min=1"` // 终端模块编码
}

type SwitchUsers struct {
	UserIds      []string `form:"user_ids" json:"user_ids" binding:"omitempty"`
	UserGroupIds []string `form:"user_group_ids" json:"user_group_ids"`
	UserRoleIds  []string `form:"user_role_ids" json:"user_role_ids" binding:"omitempty"`
	AgentIds     []string `form:"agent_ids" json:"agent_ids"`
	IsAllUser    bool     `form:"is_all_user" json:"is_all_user"`
	IsAllAgent   bool     `form:"is_all_agent" json:"is_all_agent"`
}

type UpdateModuleSwitchReq struct {
	ModuleCode   int  `form:"agent_module" json:"agent_module" binding:"required,min=1"` // 终端模块编码
	ModuleSwitch bool `form:"module_switch" json:"module_switch"`                        //开关
}

type ModuleSwitchAgents struct {
	ApplianceId uint64 `gorm:"column:appliance_id" db:"appliance_id" json:"appliance_id"`
	AppName     string `gorm:"column:app_name" db:"app_name" json:"app_name"`
	AppPlat     string `gorm:"column:app_plat" db:"app_plat" json:"app_plat"`
	UserName    string `gorm:"column:user_name" db:"user_name" json:"user_name"`
	SourceType  string `gorm:"column:source_type;not null" json:"source_type"` // 来源类型，租户下唯一
	Switch      bool   `gorm:"-" json:"module_switch"`
}

type GroupAndAgents struct {
	Id         string           `json:"id"`
	Name       string           `json:"name"`
	AppPlat    string           `json:"app_plat"`
	UserName   string           `json:"user_name"`
	Switch     bool             `json:"module_switch"`
	IsDir      bool             `json:"is_dir"`
	SourceType string           `json:"source_type"`
	Children   []GroupAndAgents `gorm:"-" json:"children"`
}

type UserGroups struct {
	auth_model.TbUserGroup
	SourceType string `gorm:"column:source_type;not null" json:"source_type"` // 来源类型，租户下唯一
}

type EffectUserReq struct {
	ModuleCode int    `form:"agent_module" json:"agent_module" binding:"required,min=1"` // 终端模块编码
	GroupId    string `form:"group_id" json:"group_id" binding:"omitempty,min=1"`
	GroupName  string `form:"group_name" json:"group_name"`
	UserName   string `form:"user_name" json:"user_name"`
	AgentName  string `form:"agent_name" json:"agent_name"`
	Platform   string `form:"platform" json:"platform"  binding:"required"`
}
