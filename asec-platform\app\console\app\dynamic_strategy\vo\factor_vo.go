package vo

import (
	model "asdsec.com/asec/platform/pkg/model/strategy_model"
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
)

type FactorListResp struct {
	// 因子id
	Id string `json:"id"`
	// 因子名称
	FactorName string `json:"factor_name"`
	// 因子描述
	FactorDesc string `json:"factor_desc"`
	// 因子类型
	FactorType string `json:"factor_type"`
	// 输入类型
	InputType string `json:"input_type"`
	// 因子属性
	FactorAttr string `json:"factor_attr"`
	// 前端组件
	FrontComponent string `json:"front_component"`
	// 因子操作
	FactorOp pgtype.JSONB `json:"factor_op"`
	// 因子条件值
	FactorValue pgtype.JSONB `json:"factor_value"`
	// 条件icon图标
	FactorIcon string `json:"factor_icon"`
	ValueName  string `json:"value_name"`
}

type CreateFactorTimeReq struct {
	// 间隔类型(1每天/2每周)
	IntervalType int `gorm:"column:interval_type" json:"interval_type,omitempty" binding:"required"`
	// 周日是否选择(下同)
	Sunday    bool `gorm:"column:sunday" json:"sunday,omitempty"`
	Monday    bool `gorm:"column:monday" json:"monday,omitempty"`
	Tuesday   bool `gorm:"column:tuesday" json:"tuesday,omitempty"`
	Wednesday bool `gorm:"column:wednesday" json:"wednesday,omitempty"`
	Thursday  bool `gorm:"column:thursday" json:"thursday,omitempty"`
	Friday    bool `gorm:"column:friday" json:"friday,omitempty"`
	Saturday  bool `gorm:"column:saturday" json:"saturday,omitempty"`
	// 每日时间起
	DailyStartTime string `gorm:"column:daily_start_time" json:"daily_start_time,omitempty"`
	// 每日事件止
	DailyEndTime string `gorm:"column:daily_end_time" json:"daily_end_time,omitempty"`
	// 是否全天生效
	AllDayEnable bool   `gorm:"column:all_day_enable" json:"all_day_enable,omitempty" `
	GapName      string `gorm:"column:gap_name" json:"gap_name,omitempty" binding:"required"`
}

type FactorTimeListResp struct {
	Id      string `gorm:"column:id" json:"id,omitempty"`
	GapName string `gorm:"column:gap_name" json:"gap_name,omitempty"`
}

type FactorTimeListPageReq struct {
	Limit  int    `json:"limit" binding:"required,min=1,max=999999"`
	Offset int    `json:"offset" binding:"min=0"`
	Search string `json:"search"`
}

type FactorTimeListPageResp struct {
	TotalNum       int              `json:"total_num"`
	SourceListData []SourceListData `json:"source_list_data"`
}

type SourceListData struct {
	model.FactorTime
	// 引用状态 1是 2否
	Referenced int `gorm:"referenced" json:"referenced"`
}

type UpdateFactorTimeReq struct {
	// id
	Id string `json:"id" binding:"required"`
	CreateFactorTimeReq
}

type CreateFactorIpReq struct {
	IpName    string   `gorm:"column:ip_name" json:"ip_name" binding:"required"`
	IpDesc    string   `gorm:"column:ip_desc" json:"ip_desc"`
	IpContent []string `gorm:"column:ip_content" json:"ip_content" binding:"required"`
}

type ListReq struct {
	Limit  int    `json:"limit" binding:"required,min=1,max=1000"`
	Offset int    `json:"offset" binding:"min=0"`
	Search string `json:"search"`
}

type FactorIpListResp struct {
	TotalNum         int                `json:"total_num"`
	FactorIpListData []FactorIpListData `json:"factor_ip_list_data"`
}

type FactorIpListData struct {
	model.FactorIp
	// 引用状态 1是 2否
	Referenced int `gorm:"referenced" json:"referenced"`
}

type UpdateFactorIpReq struct {
	// id
	Id string `json:"id" binding:"required"`
	CreateFactorIpReq
}

type DelReq struct {
	Ids   []string `json:"ids"`
	Names []string `json:"names"`
}

type CreateNetLocationReq struct {
	NetLocationName string   `gorm:"column:net_location_name" json:"net_location_name" binding:"required"`
	NetLocationDesc string   `gorm:"column:net_location_desc" json:"net_location_desc"`
	PublicIp        []string `gorm:"column:public_ip" json:"public_ip"`
	PrivateIp       []string `gorm:"column:private_ip" json:"private_ip"`
	Dns             []string `gorm:"column:dns" json:"dns"`
	WifiSsd         []string `gorm:"column:wifi_ssd" json:"wifi_ssd"`
}

type NetLocationListResp struct {
	TotalNum            int                   `json:"total_num"`
	NetLocationListData []NetLocationListData `json:"net_location_list_data"`
}

type NetLocationListData struct {
	model.FactorNetLocation
	// 引用状态 1是 2否
	Referenced int `json:"referenced"`
}

type UpdateNetLocationReq struct {
	// id
	Id string `json:"id" binding:"required"`
	CreateNetLocationReq
}

type CreateFactorProcessReq struct {
	SoftwareName string   `gorm:"column:software_name" json:"software_name" binding:"required"`
	ProcessName  []string `gorm:"column:process_name" json:"process_name" binding:"required"`
	ProcessDesc  string   `gorm:"column:process_desc" json:"process_desc"`
}

type FactorProcessListResp struct {
	TotalNum              int                     `json:"total_num"`
	FactorProcessListData []FactorProcessListData `json:"factor_process_list_data"`
}

type FactorProcessListData struct {
	model.FactorProcess
	// 引用状态 1是 2否
	Referenced int `json:"referenced"`
}

type UpdateFactorProcessReq struct {
	// id
	Id string `json:"id" binding:"required"`
	CreateFactorProcessReq
}

type FactorIpResp struct {
	Id        string         `json:"id"`
	IpName    string         `json:"ip_name"`
	IpContent pq.StringArray `gorm:"type:string" json:"ip_content"`
}

type FactorProcessResp struct {
	Id           string         `json:"id"`
	SoftwareName string         `gorm:"column:software_name" json:"software_name"`
	ProcessName  pq.StringArray `gorm:"column:process_name;type:varchar[]" json:"process_name"`
}

type FactorNetLocationResp struct {
	Id              string         `json:"id"`
	NetLocationName string         `gorm:"column:net_location_name" json:"net_location_name"`
	PublicIp        pq.StringArray `gorm:"column:public_ip;type:varchar[]" json:"public_ip"`
	PrivateIp       pq.StringArray `gorm:"column:private_ip;type:varchar[]" json:"private_ip"`
	Dns             pq.StringArray `gorm:"column:dns;type:varchar[]" json:"dns"`
	WifiSsd         pq.StringArray `gorm:"column:wifi_ssd;type:varchar[]" json:"wifi_ssd"`
}
