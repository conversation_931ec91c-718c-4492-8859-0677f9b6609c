package common

import (
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	model2 "asdsec.com/asec/platform/pkg/model"
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"time"
)

type AddSensitiveElemTagReqData struct {
	Name string `json:"name"`
}

type IDReqData struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type SnowflakeIDReqData struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type SensitiveElemTag struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type QuerySensitiveElemReqData struct {
	Offset   int    `form:"offset" json:"offset"`
	Limit    int    `form:"limit" json:"limit"`
	Search   string `form:"search" json:"search"`
	BuiltIn  int    `form:"built_in" json:"built_in" binding:"required"` //1为内置敏感元素，2为自定义敏感元素
	Category int    `form:"category" json:"category"`
}

type QuerySensitiveElemTotal struct {
	CustomSum   int64 `json:"custom_sum"`
	InternalSum int64 `json:"internal_sum"`
}

type SensitiveElem struct {
	Id                   int           `gorm:"id" json:"id"`
	Category             int8          `gorm:"category" json:"category"`
	Description          string        `gorm:"description" json:"description"`
	SensitiveElementName string        `gorm:"sensitive_element_name" json:"sensitive_element_name"`
	RelatedCount         int           `gorm:"related_count" json:"related_count"`
	BuiltIn              int8          `gorm:"built_in" json:"built_in"`
	Value                pgtype.JSONB  `gorm:"value;type:string" json:"value"`
	Tags                 pq.Int64Array `gorm:"tags;type:int" json:"tags"`
	CorpId               string        `gorm:"corp_id" json:"corp_id"`
	CreatedAt            time.Time     `gorm:"created_at" json:"created_at"`
	UpdatedAt            time.Time     `gorm:"updated_at" json:"updated_at"`
}

type QuerySensitiveElemRespData struct {
	SensitiveElem []SensitiveElem `json:"sensitive_elem"`
}

type Tag struct {
	Id   int    `gorm:"id" json:"id"`
	Name string `gorm:"name" json:"name"`
}

type BaseInformation struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type QuerySensitiveStrategyRespData struct {
	TotalNum          int                         `json:"total_num"`
	SensitiveStrategy []SensitiveStrategyCompData `json:"sensitive_strategy"`
	AssocRuleList     []BaseInformation           `json:"assoc_rule_list"`
}

type SensitiveElemBaseJson struct {
	ElementName string       `json:"elem_name" binding:"required,min=1,max=255,regexEnum=COMMON_NAME"`
	Type        int          `json:"type" binding:"required,min=1,max=3"`
	Desc        string       `json:"desc"`
	Value       pgtype.JSONB `json:"expr" binding:"required,min=1"`
	BuiltIn     int          `json:"built_in"`
}

type SensitiveElemReqData struct {
	Id   int   `json:"id"`
	Tags []int `json:"tags"`
	SensitiveElemBaseJson
}

type AlgorithmReqData struct {
	ElemId    int `json:"elem_id"`
	ElemCount int `json:"elem_count"`
}

type AlgorithmValue struct {
	Threshold int `json:"threshold"`
	// 值类型(用于算法类型敏感:1关键词;2正则)
	ValueType int `json:"value_type"`
	// 敏感元素值
	Value []string `json:"value"`
	// 算法模型id
	ElemId int `json:"elem_id"`
}

type SensitiveStrategyFileNameRule struct {
	SensitiveElementCode []int  `json:"sensitive_element_codes"`
	Operator             string `json:"operator"`
	Count                int    `json:"count"`
}

type SensitiveStrategyCompData struct {
	ID string `json:"id"`
	SensitiveStrategyBaseData
}

type SensitiveStrategyBaseData struct {
	RuleName         string                          `json:"rule_name" binding:"required,min=1,max=255,regexEnum=COMMON_NAME"`
	SensitiveLevel   int                             `json:"sensitive_level" binding:"required,min=1,max=4"`
	RuleDescription  string                          `json:"rule_description"`
	FileEncrypted    int                             `json:"file_encrypted" binding:"required,min=1,max=2"`
	MinFileSize      int                             `json:"min_file_size"`
	MinFileSizeUnit  string                          `json:"min_file_size_unit"`
	MaxFileSize      int                             `json:"max_file_size"`
	MaxFileSizeUnit  string                          `json:"max_file_size_unit"`
	FileTypeCode     []int64                         `json:"file_type_code"`
	CheckFileSuffix  int                             `json:"check_file_suffix" binding:"required,min=1,max=2"`
	FileNameRule     []SensitiveStrategyFileNameRule `json:"filename_rule"`
	FileNameOperator string                          `json:"filename_operator"`
	CategoryID       string                          `json:"category_id"`
	Category         string                          `json:"category"`
	Enable           int                             `json:"enable" binding:"required,min=1,max=2"`
	AssocRule        []model.AlertRulePartialDB      `json:"assoc_rule"`
	// 来源id
	SourceId []string `json:"source_id"`
	// 识别方式 1文件来源 2文件名称 3文件类型 4文件大小
	IdentifyWay []int32 `json:"identify_way" binding:"required"`
	CorpId      string
	// 内容识别operator
	ContentOperator string `json:"content_operator"`
	// 内容识别规则
	ContentRule []SensitiveStrategyFileNameRule `json:"content_rule"`
}

type SensitiveStrategyTplBaseData struct {
	SnowflakeIDReqData
	RuleName         string                          `json:"rule_name"`
	SensitiveLevel   int                             `json:"sensitive_level"`
	RuleDescription  string                          `json:"rule_description"`
	FileEncrypted    int                             `json:"file_encrypted"`
	MinFileSize      int                             `json:"min_file_size"`
	MinFileSizeUnit  string                          `json:"min_file_size_unit"`
	MaxFileSize      int                             `json:"max_file_size"`
	MaxFileSizeUnit  string                          `json:"max_file_size_unit"`
	FileTypeCode     []model.FileTypeDB              `json:"file_type"`
	CheckFileSuffix  int                             `json:"check_file_suffix"`
	FileNameRule     []SensitiveStrategyFileNameRule `json:"filename_rule"`
	FileNameOperator string                          `json:"file_operator"`
	CategoryID       string                          `json:"category_id"`
	Category         string                          `json:"category"`
	Enable           int                             `json:"enable"`
	AssocRule        []model.AlertRulePartialDB      `json:"assoc_rule"`
	IdentifyWay      pq.Int32Array                   `gorm:"column:identify_way;type:int4" json:"identify_way"`
}

type QuerySensitiveStrategyBaseReqData struct {
	Offset int `form:"offset" json:"offset"`
	Limit  int `form:"limit" json:"limit"`
}

type QuerySensitiveStrategyReqData struct {
	model2.Pagination
	Level      []int  `form:"level" json:"level"`
	CategoryId string `form:"category_id" json:"category_id"`
	BuiltIn    int    `form:"built_in" json:"built_in" binding:"required"`
}

type ChangeSensitiveStrategyReqData struct {
	Id string `json:"id"`
	SensitiveStrategyBaseData
}

type QuerySensStrategyTemplateRespData struct {
	RuleTypeName string                         `json:"rule_type_name"`
	Strategy     []SensitiveStrategyTplBaseData `json:"strategy"`
}

type ElemSummerResp struct {
}

type StrategySummaryResp struct {
	Id       uint64 `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	TypeName string `gorm:"column:name;type_name:varchar(255);comment:规则名称" json:"type_name"` //产品项目资料
	Count    int64  `gorm:"-" json:"count"`
}

type StrategyTypesResp struct {
	CategoryId int64  `gorm:"column:category_id;type:integer;comment:种类ID" json:"category_id"`
	Category   string `gorm:"column:category;type:varchar(255);comment:种类描述" json:"category"`
}

type QuerySensitiveElemListRsp struct {
	model2.CommonPage
	Data []SensitiveElem `json:"data"`
}

type DelSensitiveStgListReq struct {
	Ids   []string `json:"ids"`
	Names []string `json:"names"`
}

type UpdateStgBatchReq struct {
	DelSensitiveStgListReq
	Enable int      `json:"enable" required:"binding"`
	Names  []string `json:"names"`
}
