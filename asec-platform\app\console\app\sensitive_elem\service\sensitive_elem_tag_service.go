package service

import (
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/repository"
	"context"
	"sync"
)

// public
type SensitiveElemTagServiceImpl interface {
	All(ctx context.Context) ([]model.SensitiveElemTagDB, error)
	Query(ctx context.Context, limit int, offset int32) ([]model.SensitiveElemTagDB, error)
	Find(ctx context.Context, id int) (model.SensitiveElemTagDB, error)
	Add(ctx context.Context, name string) error
	Delete(ctx context.Context, id int) error
}

func GetSensitiveElemTagService() SensitiveElemTagServiceImpl {
	sensitiveElemTagServInitPriv.Do(func() {
		sensitiveElemTagInstance = &sensitiveElemTagServicePriv{repository.NewSensitiveElemTabRepository()}
	})
	return sensitiveElemTagInstance
}

// private
var sensitiveElemTagServInitPriv sync.Once
var sensitiveElemTagInstance SensitiveElemTagServiceImpl

type sensitiveElemTagServicePriv struct {
	db repository.SensitiveElemTagReposImpl
}

func (self *sensitiveElemTagServicePriv) All(ctx context.Context) ([]model.SensitiveElemTagDB, error) {
	result, err := self.db.All(ctx)
	if err != nil {
		return nil, err
	}
	return result, err
}

func (self *sensitiveElemTagServicePriv) Add(ctx context.Context, name string) error {

	return self.db.Add(ctx, name)
}

func (self *sensitiveElemTagServicePriv) Delete(ctx context.Context, id int) error {
	return self.db.Delete(ctx, id)
}

func (self *sensitiveElemTagServicePriv) Query(ctx context.Context, limit int, offset int32) ([]model.SensitiveElemTagDB, error) {
	return self.db.Query(ctx, limit, offset)
}

func (self *sensitiveElemTagServicePriv) Find(ctx context.Context, id int) (model.SensitiveElemTagDB, error) {
	return self.db.Find(ctx, id)
}
