package api

import (
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
)

// GetSensitiveCategoryList godoc
// @Summary 查询敏感数据分类列表
// @Schemes
// @Description 查询敏感数据分类列表
// @Tags        sensitive category
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_category/list [GET]
// @success     200 {object} common.Response{data=[]model.GetSensitiveCategoryListRsp} "ok"
func GetSensitiveCategoryList(ctx *gin.Context) {
	stgName := ctx.Query("stg_name")
	respData, err := service.GetSensitiveCategoryService().GetSensitiveCategoryList(ctx, stgName)
	if err != nil {
		global.SysLog.Error("get sensitive category list error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.OkWithData(ctx, respData)
}

// AddSensitiveCategory godoc
// @Summary 新增敏感数据分类
// @Schemes
// @Description 新增敏感数据分类
// @Tags        sensitive category
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_category [POST]
// @success     200 {object} common.Response{} "ok"
func AddSensitiveCategory(ctx *gin.Context) {
	var req model.AddSensitiveCategoryReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	// 确认是否存在重名
	data, err := service.GetSensitiveCategoryService().GetSensitiveCategoryByName(ctx, req.Name)
	if err != nil {
		global.SysLog.Error("add SensitiveCategory error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	if data.Id != "" {
		common.Fail(ctx, common.CreateSensitiveCategoryErr)
		return
	}
	err = service.GetSensitiveCategoryService().AddSensitiveCategory(ctx, req)
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.SensitiveCategory,
			OperationType:  common.OperateCreate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	if err != nil {
		global.SysLog.Error("add SensitiveCategory error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// UpdateSensitiveCategory godoc
// @Summary 编辑敏感数据分类
// @Schemes
// @Description 编辑敏感数据分类
// @Tags        sensitive category
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_category [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateSensitiveCategory(ctx *gin.Context) {
	var req model.UpdateSensitiveCategoryReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	data, err := service.GetSensitiveCategoryService().GetSensitiveCategoryByName(ctx, req.Name)
	if err != nil {
		global.SysLog.Error("update SensitiveCategory error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	if data.Id != "" && data.Id != req.Id {
		common.Fail(ctx, common.UpdateSensitiveCategoryErr)
		return
	}
	//日志操作
	var errorLog = ""
	err = service.GetSensitiveCategoryService().UpdateSensitiveCategory(ctx, req)
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.SensitiveCategory,
			OperationType:  common.OperateUpdate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	if err != nil {
		global.SysLog.Error("update SensitiveCategory error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// DelSensitiveCategory godoc
// @Summary 删除敏感数据分类
// @Schemes
// @Description 删除敏感数据分类
// @Tags        sensitive category
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_category [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelSensitiveCategory(ctx *gin.Context) {
	var req model.DelSensitiveCategoryReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	//dlp策略查询是否引用该分类，或者引用了该分类下的敏感数据
	check, err := service.GetSensitiveCategoryService().CheckDelCondition(ctx, req.Id, req.Type)
	if err != nil {
		global.SysLog.Error("del SensitiveCategory error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	if !check {
		common.Fail(ctx, common.DelSensitiveCategoryErr)
		return
	}
	//日志操作
	var errorLog = ""
	err = service.GetSensitiveCategoryService().DeleteSensitiveCategory(ctx, req)
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.SensitiveCategory,
			OperationType:  common.OperateDelete,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	if err != nil {
		global.SysLog.Error("delete SensitiveCategory error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// GetSensitiveCategoryCondition godoc
// @Summary 根据
// @Schemes
// @Description 查询敏感数据分类列表
// @Tags        sensitive category
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_category/condition [GET]
// @success     200 {object} common.Response{data=[]model.GetSensitiveCategoryListRsp} "ok"
func GetSensitiveCategoryCondition(ctx *gin.Context) {
	startTimeStr := ctx.Query("start_time")
	endTimeStr := ctx.Query("end_time")
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, time.Local)
	if err != nil {
		common.Fail(ctx, common.ParamError)
		return
	}
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, time.Local)
	if err != nil {
		common.Fail(ctx, common.ParamError)
		return
	}
	if startTime.After(endTime) {
		common.Fail(ctx, common.ParamError)
		return
	}
	respData, err := service.GetSensitiveCategoryService().GetCategoryCondition(ctx, startTime, endTime)
	if err != nil {
		global.SysLog.Error("get sensitive category error", zap.Error(err))
		common.Fail(ctx, common.QueryDataTypeErr)
		return
	}
	common.OkWithData(ctx, respData)
}

// CreateStgByTemp godoc
// @Summary 根据敏感数据模板创建敏感数据
// @Schemes
// @Description 根据敏感数据模板创建敏感数据
// @Tags        sensitive category
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_category/temp [POST]
// @success     200 {object} common.Response{} "ok"
func CreateStgByTemp(ctx *gin.Context) {
	var req model.CreateStgByTempReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	err := service.GetSensitiveCategoryService().CreateStgByTemp(ctx, req)
	if err != nil {
		global.SysLog.Error("get sensitive category error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.SensitiveStrategyType,
			OperationType:  common.OperateCreate,
			Representation: req.Names,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
}
