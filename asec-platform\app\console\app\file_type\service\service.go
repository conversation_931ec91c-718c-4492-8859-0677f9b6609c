package service

import (
	"asdsec.com/asec/platform/app/console/app/file_type/dto"
	"asdsec.com/asec/platform/app/console/app/file_type/repository"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"github.com/jinzhu/copier"
	"sync"
	"time"
)

var FileTypeImpl FileTypeService

// FileTypeInit 单例对象
var FileTypeInit sync.Once

type FileTypeService interface {
	CreateFileType(ctx context.Context, req dto.CreateFileTypeReq) aerrors.AError
	UpdateFileType(ctx context.Context, req dto.UpdateFileTypeReq) aerrors.AError
	DeleteFileType(ctx context.Context, req dto.DeleteFileTypeReq) error
	GetFileTypeTree(ctx context.Context, keyword string) ([]model.FileType, error)
}

const (
	CustomBuiltIn         = 2
	FileTypeNameDuplicate = "FileTypeNameDuplicateErr"
)

type fileTypeService struct {
	db repository.FileTypeRepository
}

func (f fileTypeService) CreateFileType(ctx context.Context, req dto.CreateFileTypeReq) aerrors.AError {
	item, err := f.db.FindFileTypeByName(ctx, req.Name)
	if err != nil {
		return err
	}
	if item.Id > 0 {
		return aerrors.New("file type repeat", FileTypeNameDuplicate)
	}
	var cReq model.FileType
	copyErr := copier.Copy(&cReq, &req)
	if err != nil {
		return aerrors.NewWithError(copyErr, common.OperateError)
	}
	code, err := f.db.FindFileTypeByCode(ctx, req.ParentCode)
	if err != nil {
		return aerrors.NewWithError(copyErr, common.OperateError)
	}
	cReq.Code = code
	cReq.BuiltIn = CustomBuiltIn
	cReq.CreateTime = time.Now()
	err = f.db.CreateFileType(ctx, cReq)
	if err != nil {
		return err
	}
	return nil
}

func (f fileTypeService) UpdateFileType(ctx context.Context, req dto.UpdateFileTypeReq) aerrors.AError {
	item, err := f.db.FindFileTypeByName(ctx, req.Name)
	if err != nil {
		return err
	}
	if item.Id > 0 && item.Id != req.Id {
		return aerrors.New("file type repeat", FileTypeNameDuplicate)
	}
	var cReq model.FileType
	copyErr := copier.Copy(&cReq, &req)
	if err != nil {
		return aerrors.NewWithError(copyErr, common.OperateError)
	}
	err = f.db.UpdateFileType(ctx, cReq)
	if err != nil {
		return err
	}
	return nil
}

func (f fileTypeService) DeleteFileType(ctx context.Context, req dto.DeleteFileTypeReq) error {
	return f.db.DeleteFileType(ctx, req)
}

func (f fileTypeService) GetFileTypeTree(ctx context.Context, keyword string) ([]model.FileType, error) {
	return f.db.GetFileTypeTree(ctx, keyword)
}

func GetFileTypeService() FileTypeService {
	FileTypeInit.Do(func() {
		FileTypeImpl = &fileTypeService{db: repository.NewFileTypeRepository()}
	})
	return FileTypeImpl
}
