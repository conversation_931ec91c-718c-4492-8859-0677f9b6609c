package service

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	"asdsec.com/asec/platform/app/console/app/notification/dto"
	"asdsec.com/asec/platform/app/console/app/notification/repository"
	"asdsec.com/asec/platform/pkg/model/notification_model"
	"context"
	"sync"
)

var NotificationServiceImpl NotificationService

var NotificationServiceInit sync.Once

type notificationService struct {
	db repository.NotificationRepository
}

func (a notificationService) BatchDeleteNotification(c context.Context, req vo.DelReq) error {
	return a.db.BatchDeleteNotification(c, req)
}

func (a notificationService) UpdateNotification(c context.Context, req dto.NotificationReq) error {
	return a.db.UpdateNotification(c, req)
}

func (a notificationService) DeleteNotification(c context.Context, id string) error {
	return a.db.DeleteNotification(c, id)
}

func (a notificationService) GetNotificationList(ctx context.Context, req dto.ListReq) (dto.NotificationListResp, error) {
	return a.db.GetNotificationList(ctx, req)
}

func (a notificationService) CreateNotification(ctx context.Context, req dto.NotificationReq) error {
	return a.db.CreateNotification(ctx, req)
}

func (a notificationService) GetNotificationDetail(ctx context.Context, id string) (notification_model.Notification, error) {
	return a.db.GetNotificationDetail(ctx, id)
}

type NotificationService interface {
	CreateNotification(ctx context.Context, req dto.NotificationReq) error
	GetNotificationDetail(ctx context.Context, id string) (notification_model.Notification, error)
	GetNotificationList(ctx context.Context, req dto.ListReq) (dto.NotificationListResp, error)
	UpdateNotification(c context.Context, req dto.NotificationReq) error
	DeleteNotification(c context.Context, id string) error
	BatchDeleteNotification(c context.Context, req vo.DelReq) error
}

func GetNotificationService() NotificationService {
	NotificationServiceInit.Do(func() {
		NotificationServiceImpl = &notificationService{db: repository.NewNotificationRepository()}
	})
	return NotificationServiceImpl
}
