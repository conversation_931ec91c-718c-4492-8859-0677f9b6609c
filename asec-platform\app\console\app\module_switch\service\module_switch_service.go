package service

import (
	"asdsec.com/asec/platform/app/console/app/module_switch/dto"
	"asdsec.com/asec/platform/app/console/app/module_switch/repository"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"strconv"
	"strings"
	"sync"
	"time"
)

var SwitchServiceImpl SwitchService

// SwitchServiceInit 单例对象
var SwitchServiceInit sync.Once

type SwitchService interface {
	UpsetSwitch(ctx context.Context, req dto.UpdateSwitchReq) error
	UpdateModuleSwitch(ctx context.Context, req dto.UpdateModuleSwitchReq) error
	GetUnbindAgent(ctx context.Context, search string, platform string) ([]dto.ModuleSwitchAgents, error)
	GetGroupAndAgents(ctx context.Context, req dto.EffectUserReq) ([]dto.GroupAndAgents, error)
	GetGroupAndAllAgents(ctx context.Context, req dto.EffectUserReq) ([]dto.GroupAndAgents, error)
	GetModuleSwitch(ctx context.Context) ([]model.ModuleSwitch, error)
	GetDataSurveySwitch(ctx context.Context) (model.ModuleSwitch, error)
}

type switchService struct {
	db repository.SwitchRepository
}

func (u *switchService) GetDataSurveySwitch(ctx context.Context) (model.ModuleSwitch, error) {
	return u.db.GetDataSurveySwitch(ctx)
}

func (u *switchService) GetModuleSwitch(ctx context.Context) ([]model.ModuleSwitch, error) {
	return u.db.GetModuleSwitch(ctx)
}

const (
	UnbindGroup    = "unbindAgents"
	UnbindRootName = "未绑定用户终端"
)

func (u *switchService) GetGroupAndAgents(ctx context.Context, req dto.EffectUserReq) ([]dto.GroupAndAgents, error) {
	data, err := u.db.GetGroupAndAgents(ctx, req)
	if err != nil {
		return []dto.GroupAndAgents{}, err
	}
	agentName := req.AgentName
	userName := req.UserName
	groupName := req.GroupName
	if userName != "" && agentName == "" { // 只查询用户名的情况
		return data, nil
	}
	if groupName != "" && agentName == "" { //只查询组织的情况
		if !strings.Contains(UnbindRootName, groupName) {
			return data, nil
		}
	}
	unbindAgents, err := u.db.GetUnbindAgent(ctx, agentName, req.Platform)
	if err != nil {
		return []dto.GroupAndAgents{}, err
	}
	var unbindChild []dto.GroupAndAgents
	selectAgent := u.db.GetSwitchedAgent(ctx, req.ModuleCode, true)
	selectMap := make(map[string]bool, 0)
	for _, v := range selectAgent {
		selectMap[v] = true
	}
	dirSelect := u.db.GetDirSwitch(ctx, UnbindGroup, req.ModuleCode)
	for _, v := range unbindAgents {
		isSelect := false
		agentId := strconv.FormatUint(v.ApplianceId, 10)
		if _, ok := selectMap[agentId]; ok {
			isSelect = true
		}
		if dirSelect {
			isSelect = true
		}
		tmp := dto.GroupAndAgents{
			Id:       agentId,
			Name:     v.AppName,
			AppPlat:  v.AppPlat,
			UserName: v.UserName,
			Switch:   isSelect,
			IsDir:    false,
		}
		unbindChild = append(unbindChild, tmp)
	}
	tmp := dto.GroupAndAgents{
		Id:         UnbindGroup,
		Name:       UnbindRootName,
		IsDir:      true,
		Children:   unbindChild,
		SourceType: UnbindGroup,
		Switch:     dirSelect,
	}
	if agentName != "" && len(tmp.Children) == 0 {
		return data, nil
	}
	data = append(data, tmp)
	return data, nil
}

func (u *switchService) GetGroupAndAllAgents(ctx context.Context, req dto.EffectUserReq) ([]dto.GroupAndAgents, error) {
	data, err := u.db.GetGroupAndAllAgents(ctx, req)
	if err != nil {
		return []dto.GroupAndAgents{}, err
	}
	agentName := req.AgentName
	userName := req.UserName
	groupName := req.GroupName
	if userName != "" && agentName == "" { // 只查询用户名的情况
		return data, nil
	}
	if groupName != "" && agentName == "" { //只查询组织的情况
		if !strings.Contains(UnbindRootName, groupName) {
			return data, nil
		}
	}
	unbindAgents, err := u.db.GetUnbindAgent(ctx, agentName, req.Platform)
	if err != nil {
		return []dto.GroupAndAgents{}, err
	}
	selectAgent := u.db.GetSwitchedAgent(ctx, req.ModuleCode, true)
	selectMap := make(map[string]bool, 0)
	for _, v := range selectAgent {
		selectMap[v] = true
	}
	dirSelect := u.db.GetDirSwitch(ctx, UnbindGroup, req.ModuleCode)
	for _, v := range unbindAgents {
		isSelect := false
		agentId := strconv.FormatUint(v.ApplianceId, 10)
		if _, ok := selectMap[agentId]; ok {
			isSelect = true
		}
		if dirSelect {
			isSelect = true
		}
		tmp := dto.GroupAndAgents{
			Id:         agentId,
			Name:       v.AppName,
			AppPlat:    v.AppPlat,
			UserName:   v.UserName,
			Switch:     isSelect,
			IsDir:      false,
			SourceType: "agents",
		}
		data = append(data, tmp)
	}
	tmp := dto.GroupAndAgents{
		Id:         UnbindGroup,
		Name:       UnbindRootName,
		IsDir:      true,
		SourceType: UnbindGroup,
		Switch:     dirSelect,
	}
	if agentName != "" && len(tmp.Children) == 0 {
		return data, nil
	}
	data = append(data, tmp)

	// 遍历筛选出Switch字段为true的项
	var filteredData []dto.GroupAndAgents
	for _, item := range data {
		if item.Switch {
			filteredData = append(filteredData, item)
		}
	}

	return filteredData, nil
}

func (u *switchService) GetUnbindAgent(ctx context.Context, search string, platform string) ([]dto.ModuleSwitchAgents, error) {
	return u.db.GetUnbindAgent(ctx, search, platform)
}

func (u *switchService) UpdateModuleSwitch(ctx context.Context, req dto.UpdateModuleSwitchReq) error {
	tmp := model.ModuleSwitch{
		ModuleCode: req.ModuleCode,
		Switch:     req.ModuleSwitch,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	err := u.db.UpdateModuleSwitch(ctx, tmp)
	if err != nil {
		global.SysLog.Error(err.Error())
		return err
	}
	return nil
}

func (u *switchService) UpsetSwitch(ctx context.Context, req dto.UpdateSwitchReq) error {
	// 前端会传分组及其子分组，所以这里不需要在查询了
	//var userGroupIds []string
	//if len(req.UserGroupIds) > 0 {
	//	for _, v := range req.UserGroupIds {
	//		groups := u.db.GetChildUserGroup(ctx, v)
	//		for _, g := range groups {
	//			userGroupIds = append(userGroupIds, g.ID)
	//		}
	//		userGroupIds = append(userGroupIds, v)
	//	}
	//}
	moduleSwitch := model.AgentModuleSwitch{
		ModuleCode: req.ModuleCode,
		AgentIds:   req.AgentIds,
		UserIds:    req.UserIds,
		RoleIds:    req.UserRoleIds,
		GroupIds:   req.UserGroupIds,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	return u.db.UpdateSwitch(ctx, moduleSwitch)
}

func GetSwitchService() SwitchService {
	SwitchServiceInit.Do(func() {
		SwitchServiceImpl = &switchService{db: repository.NewSwitchRepository()}
	})
	return SwitchServiceImpl
}
