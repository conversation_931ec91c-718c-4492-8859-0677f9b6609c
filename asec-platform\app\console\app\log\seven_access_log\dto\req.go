package dto

type GetLogListCommonReq struct {
	Limit          int    `form:"limit" json:"limit,omitempty;query:limit"`
	Offset         int    `form:"offset" json:"offset,omitempty;query:offset"`
	Search         string `form:"search" json:"search"`
	StartTime      string `json:"start_time"`
	EndTime        string `json:"end_time"`
	StartTimeStamp int64  `json:"-"`
	EndTimeStamp   int64  `json:"-"`
}

type GetSevenAccessLogListReq struct {
	GetLogListCommonReq
	Order string `json:"order"`
}

type UpdateSevenAccessLogConfReq struct {
	EnableColumn []string `json:"enable_column"`
	ServerHost   string   `json:"server_host"`
}

type GetSensitiveLogListReq struct {
	GetLogListCommonReq
	SearchColumn string `json:"search_column"`
}
