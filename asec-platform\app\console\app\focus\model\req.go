package model

type FocusOnReq struct {
	Type   string `form:"type" json:"type"`
	Limit  int    `form:"limit" json:"limit"`
	Offset int    `form:"offset" json:"offset"`
}
type FocusOnCommonReq struct {
	Type     string   `form:"type" json:"type"`
	Key      string   `json:"key" binding:"required"`
	Status   int      `json:"status"` // 1-关注 2-取消关注
	UserTags []string `json:"user_tags"`
}

type GetFocusUserListReq struct {
	Limit  int    `form:"limit" json:"limit"`
	Offset int    `form:"offset" json:"offset"`
	Search string `form:"search" json:"search"`
}
