package api

import (
	"asdsec.com/asec/platform/app/console/app/module_switch/dto"
	"asdsec.com/asec/platform/app/console/app/module_switch/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	opModel "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// UpsetAgentModuleSwitch godoc
// @Summary 添加或修改模块开关
// @Schemes
// @Description 添加或修改终端模块开关
// @Tags        ModuleSwitch
// @Produce     application/json
// @Param       req body dto.UpdateSwitchReq true "查询参数"
// @Success     200
// @Router      /v1/module_switch/agent [PUT]
// @success     200 {object} common.Response{}
func UpsetAgentModuleSwitch(c *gin.Context) {
	var req dto.UpdateSwitchReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetSwitchService().UpsetSwitch(c, req)
	if err != nil {
		global.SysLog.Error("UpsetAgentModuleSwitch err", zap.Error(err))
		common.Fail(c, common.UpsetAgentModuleSwitchErr)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := opModel.Oprlog{
			ResourceType:   common.ModuleSwitchTYpe,
			OperationType:  common.OperateUpdate,
			Representation: "生效用户/终端",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	common.Ok(c)
}

// UpdateModuleSwitch godoc
// @Summary 修改模块总开关
// @Schemes
// @Description 修改模块总开关
// @Tags        ModuleSwitch
// @Produce     application/json
// @Param       req body dto.UpdateModuleSwitchReq true "查询参数"
// @Success     200
// @Router      /v1/module_switch [PUT]
// @success     200 {object} common.Response{}
func UpdateModuleSwitch(c *gin.Context) {
	var req dto.UpdateModuleSwitchReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetSwitchService().UpdateModuleSwitch(c, req)
	if err != nil {
		global.SysLog.Error("UpdateModuleSwitch err", zap.Error(err))
		common.Fail(c, common.UpdateModuleSwitchErr)
		return
	}
	// 记录操作日志
	var errorLog = ""
	var Representation = ""
	var OperationType = common.OperateDisable
	if req.ModuleCode == 5 {
		Representation = "客户端自保护"
	} else if req.ModuleCode == 2 {
		Representation = "模块开关-数据安全"
	} else if req.ModuleCode == 6 {
		Representation = "模块开关-无UI模式"
	}
	if req.ModuleSwitch {
		OperationType = common.OperateEnable
	}
	defer func() {
		oplog := opModel.Oprlog{
			ResourceType:   common.ApplianceResourceType,
			OperationType:  OperationType,
			Representation: Representation,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	common.Ok(c)
}

// GetMainModuleSwitch godoc
// @Summary 获取模块总开关
// @Schemes
// @Description 获取模块总开关
// @Tags        ModuleSwitch
// @Produce     application/json
// @Success     200
// @Router      /v1/module_switch [GET]
// @success     200 {object} common.Response{}
func GetMainModuleSwitch(c *gin.Context) {
	data, err := service.GetSwitchService().GetModuleSwitch(c)
	if err != nil {
		global.SysLog.Error("GetMainModuleSwitch err", zap.Error(err))
		common.Fail(c, common.UpdateModuleSwitchErr)
		return
	}
	common.OkWithData(c, data)
}

// GetAllModuleSwitchAgents godoc
// @Summary 查询模块开关的所有用户以及终端
// @Schemes
// @Description 查询模块开关的所有用户以及终端
// @Tags        ModuleSwitch
// @Produce     application/json
// @Param       req body dto.EffectUserReq true "查询参数"
// @Success     200
// @Router      /v1/agents/tree [GET]
// @success     200 {object} common.Response{}
func GetAllModuleSwitchAgents(c *gin.Context) {
	var req dto.EffectUserReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetSwitchService().GetGroupAndAgents(c, req)
	if err != nil {
		global.SysLog.Error("GetGroupAndAgents err", zap.Error(err))
		common.Fail(c, common.GetModuleSwitchErr)
		return
	}
	common.OkWithData(c, data)
}

func GetAllModuleSwitch(c *gin.Context) {
	var req dto.EffectUserReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetSwitchService().GetGroupAndAllAgents(c, req)
	if err != nil {
		global.SysLog.Error("GetGroupAndAllAgents err", zap.Error(err))
		common.Fail(c, common.GetModuleSwitchErr)
		return
	}
	common.OkWithData(c, data)
}

// GetDataSurveySwitch godoc
// @Summary 查询数据调查的模块开关
// @Schemes
// @Description 查询数据调查的模块开关
// @Tags        DataSurveySwitch
// @Produce     application/json
// @Success     200
// @Router      /v1/module_switch/events [GET]
// @success     200 {object} common.Response{}
func GetDataSurveySwitch(c *gin.Context) {
	data, err := service.GetSwitchService().GetDataSurveySwitch(c)
	if err != nil {
		global.SysLog.Error("GetGroupAndAgents err", zap.Error(err))
		common.Fail(c, common.GetModuleSwitchErr)
		return
	}
	common.OkWithData(c, data)
}
