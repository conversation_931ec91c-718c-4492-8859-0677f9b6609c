package service

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"asdsec.com/asec/platform/app/console/app/appliancemgt/common"
	applianceDto "asdsec.com/asec/platform/app/console/app/appliancemgt/dto"
	"asdsec.com/asec/platform/app/console/app/appliancemgt/service"
	"asdsec.com/asec/platform/app/console/app/offline_upgrade/constants"
	"asdsec.com/asec/platform/app/console/app/offline_upgrade/dto"
	"asdsec.com/asec/platform/app/console/app/offline_upgrade/repository"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/crypt"
	"asdsec.com/asec/platform/pkg/utils"
	"github.com/gin-gonic/gin"
)

var UpgradeServiceImpl UpgradeService

// UpgradeServiceInit 单例对象
var UpgradeServiceInit sync.Once

type UpgradeService interface {
	PkgSegmentedUpload(ctx *gin.Context, req dto.PkgUploadReq) error
}

type upgradeService struct {
	db repository.UpgradeRepository
}

const (
	//linuxFilePath = "D:\\data\\asdsec\\upload\\tmp"
	linuxFilePath = "/data/asdsec/upload/tmp"
	tarGzFile     = ".tar.gz"
	//winTestFilePath = "F:\\test"
)

func (u upgradeService) PkgSegmentedUpload(ctx *gin.Context, req dto.PkgUploadReq) error {
	// 临时目录不存在则创建
	if _, err := os.Stat(linuxFilePath); err != nil {
		if os.IsNotExist(err) {
			err := os.MkdirAll(linuxFilePath, 0755)
			if err != nil {
				return err
			}
		}
	}
	// 清除临时目录(
	global.SysLog.Sugar().Infof("clean directory :%v", linuxFilePath)
	err := utils.CleanAllInDir(linuxFilePath)
	if err != nil {
		global.SysLog.Sugar().Errorf("clean directory :%v error  %v", linuxFilePath, err)
		return err
	}

	// 添加defer清理逻辑，确保无论成功还是失败都会清理临时文件
	defer func() {
		global.SysLog.Sugar().Infof("清理临时目录: %v", linuxFilePath)
		if cleanErr := utils.CleanAllInDir(linuxFilePath); cleanErr != nil {
			global.SysLog.Sugar().Errorf("清理临时目录失败: %v, 错误: %v", linuxFilePath, cleanErr)
		}
	}()

	// 文件保存在临时目录
	file := filepath.Join(linuxFilePath, req.FileName)
	global.SysLog.Sugar().Infof("PkgSegmentedUpload save to temp dir %s", file)
	err = ctx.SaveUploadedFile(req.ChunkFile, file)
	if err != nil {
		return err
	}
	decryptFileName := strings.Replace(req.FileName, ".pkg", ".tar.gz", -1)
	decryptFileName = filepath.Join(linuxFilePath, decryptFileName) //安装包路径
	global.SysLog.Sugar().Infof("PkgSegmentedUpload start decrypt to file %s", decryptFileName)
	err = crypt.DecryptFile(file, decryptFileName, constants.PkgAESKey)
	if err != nil {
		global.SysLog.Sugar().Errorf("DecryptFile :%v error  %v", file, err)
		return errors.New("解析升级包文件失败")
	}
	global.SysLog.Sugar().Infof("PkgSegmentedUpload start to replace files...")
	// 解压安装包到临时目录
	err = utils.DecompressTarGz(decryptFileName, linuxFilePath)
	if err != nil {
		return err
	}

	dstFile := strings.TrimSuffix(decryptFileName, tarGzFile) //解压包路径

	// 打开目录
	fsF := os.DirFS(dstFile)

	// 获取目录列表
	files, err := fs.ReadDir(fsF, ".")
	if err != nil {
		return err
	}

	filePath := req.FilePath
	if req.FilePath == "" {
		filePath = constants.AgentDir
	}

	// 查找 agents 目录
	var agentsDir string
	for _, tmpFile := range files {
		//todo 分类 平台和网关
		switch tmpFile.Name() {
		case constants.AsecAgentFileName:
			// 验证升级包版本，防止降级
			err = u.validateUpgradePackageVersions(filepath.Join(dstFile, tmpFile.Name()))
			if err != nil {
				global.SysLog.Sugar().Errorf("Version validation failed: %v", err)
				return err
			}

			// 清除旧的目录，先遍历安装包里面的目录,找到一个删除一个.比如找到安装包里面的windows目录,则删除平台目录下的windows目录.
			dirs, err := os.ReadDir(filepath.Join(dstFile, tmpFile.Name()))
			if err != nil {
				return err
			}
			for _, dir := range dirs {
				if dir.IsDir() {
					platDir := filepath.Join(filePath, dir.Name())
					global.SysLog.Sugar().Infof("PkgSegmentedUpload start to clean dir %s", platDir)
					err = utils.CleanAllInDir(platDir)
					if err != nil {
						global.SysLog.Sugar().Errorf("clean directory :%v error  %v", filePath, err)
						return err
					}
				}
			}
			//拷贝安装包里文件到平台客户端目录
			agentsDir = filepath.Join(dstFile, tmpFile.Name())
			global.SysLog.Sugar().Infof("copy agents dir %s to %s", agentsDir, filePath)
			err = utils.CopyDir(agentsDir, filePath)
			if err != nil {
				return err
			}
			_ = service.GetAppService().SetUpgradeLatestVersion()
		case constants.AsecGatewayFileName:
			filePath := req.FilePath
			if req.FilePath == "" {
				filePath = constants.GatewayDir
			}

			// 检查目录是否存在，不存在则创建，主要是针对windows下目录不存在情况
			// if _, err := os.Stat(filePath); err != nil {
			// 	if os.IsNotExist(err) {
			// 		if err := os.MkdirAll(filePath, 0755); err != nil {
			// 			global.SysLog.Sugar().Errorf("Failed to create directory %s: %v", filePath, err)
			// 			return err
			// 		}
			// 		global.SysLog.Sugar().Infof("Created directory: %s", filePath)
			// 	} else {
			// 		global.SysLog.Sugar().Errorf("Failed to check directory %s: %v", filePath, err)
			// 		return err
			// 	}
			// }
			//清除旧的目录，即gateway目录下的文件与文件夹
			global.SysLog.Sugar().Infof("PkgSegmentedUpload start to clean dir %s", filePath)
			err = utils.CleanAllInDir(filePath)
			if err != nil {
				global.SysLog.Sugar().Errorf("clean directory :%v error  %v", filePath, err)
				return err
			}
			//拷贝安装包文件到目标目录
			destFile := filepath.Join(filePath, filepath.Base(decryptFileName))
			//删除安装包，避免等下打包时命名冲突
			if err := os.Remove(decryptFileName); err != nil {
				global.SysLog.Sugar().Warnf("Failed to remove decrypted file %s: %v", decryptFileName, err)
			}
			//压缩安装包，为了减少一层gateway目录
			if err := utils.CompressTarGz(dstFile, decryptFileName); err != nil {
				global.SysLog.Sugar().Errorf("Failed to compress directory %s: %v", dstFile, err)
				return err
			}
			global.SysLog.Sugar().Infof("copy package file from %s to %s", decryptFileName, destFile)
			err = utils.CopyFile(decryptFileName, destFile)
			if err != nil {
				return err
			}
			break
		default:
			global.SysLog.Sugar().Infof("Obtain the decompressed file:%v", tmpFile.Name())
		}
	}

	return nil
}

func GetUpgradeService() UpgradeService {
	UpgradeServiceInit.Do(func() {
		UpgradeServiceImpl = &upgradeService{db: repository.NewUpgradeRepository()}
	})
	return UpgradeServiceImpl
}

// validateUpgradePackageVersions 验证升级包中的版本，防止降级
func (u upgradeService) validateUpgradePackageVersions(agentsPath string) error {
	// 遍历agents目录下的平台目录
	platforms, err := os.ReadDir(agentsPath)
	if err != nil {
		return fmt.Errorf("读取agents目录失败: %v", err)
	}

	for _, platformDir := range platforms {
		if !platformDir.IsDir() {
			continue
		}

		platformName := platformDir.Name()
		// 检查config.json文件
		configPath := filepath.Join(agentsPath, platformName, "config.json")
		if _, err := os.Stat(configPath); os.IsNotExist(err) {
			continue // 跳过没有config.json的目录
		}

		// 读取并解析config.json
		configData, err := os.ReadFile(configPath)
		if err != nil {
			return fmt.Errorf("读取%s平台配置文件失败: %v", platformName, err)
		}

		var configs []applianceDto.AgentUpgradeConfig
		err = json.Unmarshal(configData, &configs)
		if err != nil {
			return fmt.Errorf("解析%s平台配置文件失败: %v", platformName, err)
		}

		// 获取升级包中的最新版本
		var packageLatestVersion string
		for _, config := range configs {
			if config.NextVersion == "" { // NextVersion为空表示这是最新版本
				packageLatestVersion = config.Version
				break
			}
		}

		if packageLatestVersion == "" {
			return fmt.Errorf("%s平台升级包中未找到最新版本信息", platformName)
		}

		// 获取当前系统中该平台的最新版本
		currentLatestVersion, err := u.getCurrentPlatformLatestVersion(platformName)
		if err != nil {
			global.SysLog.Sugar().Warnf("获取%s平台当前版本失败，跳过版本检查: %v", platformName, err)
			continue // 如果获取当前版本失败，跳过检查，允许上传
		}

		// 比较版本，防止降级
		if currentLatestVersion != "" && !common.CheckNewVersion(packageLatestVersion, currentLatestVersion) {
			return fmt.Errorf("版本验证失败：%s平台的升级包版本(%s)不能低于当前系统版本(%s)，请上传更新的版本",
				platformName, packageLatestVersion, currentLatestVersion)
		}

		global.SysLog.Sugar().Infof("%s平台版本检查通过：%s -> %s", platformName, currentLatestVersion, packageLatestVersion)
	}

	return nil
}

// getCurrentPlatformLatestVersion 获取当前平台的最新版本
func (u upgradeService) getCurrentPlatformLatestVersion(platform string) (string, error) {
	ctx := context.Background()

	// 通过appliancemgt服务获取升级策略
	upgradePolicy, err := service.GetAppService().GetUpgradePolicyDetail(ctx, "0", platform)
	if err != nil {
		return "", fmt.Errorf("获取%s平台升级策略失败: %v", platform, err)
	}

	return upgradePolicy.LatestVersion, nil
}

// 检查解密后的文件是否是有效的 tar.gz 文件
func isValidGzip(filePath string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Println("Failed to open file:", err)
		return false
	}
	defer file.Close()

	// 尝试创建 gzip Reader
	_, err = gzip.NewReader(file)
	if err != nil {
		fmt.Println("Not a valid gzip file:", err)
		return false
	}
	return true
}
