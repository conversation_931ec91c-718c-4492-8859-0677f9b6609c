<!-- //应用图标上传组件-->
<template>
  <div class="SingleSignOn">

    <t-form-item name="account">
      <t-checkbox v-model="ssoConf.enableSso">启用单点登录</t-checkbox>
    </t-form-item>

    <div id="sso-config" v-if="ssoConf.enableSso"
         style="background:#fafbfc;border-radius:8px;padding:18px 24px 8px 24px;margin-bottom:18px;box-shadow:0 1px 4px rgba(0,0,0,0.03)">
      <div class="form-row" id="sso-type-selection">
        <label style="font-weight:500;margin-bottom:10px;display:block;">单点登录方式</label>
        <div class="button-group" style="display:inline-flex;background:#f1f5f9;border-radius:8px;padding:4px;gap:2px;">
          <button v-if="appType ==='web'" type="button" class="button-group-item webvpn-only"
                  :class="{ active: ssoConf.oauthType === 'micro_application' }"
                  data-value="office" @click="selectSSOType('micro_application')"
                  style="padding:8px 16px;border:none;border-radius:6px;background:transparent;color:#6b7280;font-size:12px;font-weight:500;cursor:pointer;transition:all 0.2s ease;white-space:nowrap;">
            办公应用
          </button>
          <button type="button" class="button-group-item"
                  :class="{ active: ssoConf.oauthType === 'oauth2' }"
                  data-value="oauth2" @click="selectSSOType('oauth2')"
                  style="padding:8px 16px;border:none;border-radius:6px;background:transparent;color:#6b7280;font-size:12px;font-weight:500;cursor:pointer;transition:all 0.2s ease;white-space:nowrap;">
            OAuth2
          </button>
          <button v-if="appType ==='web'" type="button" class="button-group-item webvpn-only"
                  :class="{ active: ssoConf.oauthType === 'fill_forms' }"
                  data-value="fill_forms" @click="selectSSOType('fill_forms')"
                  style="padding:8px 16px;border:none;border-radius:6px;background:transparent;color:#6b7280;font-size:12px;font-weight:500;cursor:pointer;transition:all 0.2s ease;white-space:nowrap;">
            账号代填
          </button>
        </div>
      </div>


      <!-- 办公应用配置 -->
      <div id="sso-office" class="form-row webvpn-only" v-show="ssoConf.oauthType === 'micro_application'">
        <label style="font-weight:500;margin-bottom:8px;display:block;">选择办公应用</label>
        <div style="display:grid;grid-template-columns:1fr;gap:8px;margin-top:10px;">
          <!-- 企业微信 -->
          <div class="office-app-card" id="qiyewx"
               :class="{ selected: ssoConf.officeApp === 'qiyewx' }" data-value="qiyewx"
               @click="selectOfficeApp('qiyewx')">
            <div
                style="display:flex;align-items:center;gap:12px;padding:10px 14px;border:2px solid #e5e7eb;border-radius:8px;cursor:pointer;transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);background:#fff;position:relative;overflow:hidden;">
              <div
                  style="width:36px;height:36px;background:linear-gradient(135deg, #07c160 0%, #05a050 100%);border-radius:8px;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px rgba(7,193,96,0.2);flex-shrink:0;">
                <svg viewBox="0 0 24 24" fill="white">
                  <use xlink:href="#icon-qiwei"/>
                </svg>
              </div>
              <div style="flex:1;min-width:0;">
                <div style="font-weight:600;color:#1f2937;font-size:13px;margin-bottom:1px;">企业微信</div>
                <div style="font-size:11px;color:#6b7280;line-height:1.3;">腾讯企业级办公平台，支持即时通讯、文档协作
                </div>
              </div>
              <div class="check-icon"
                   style="width:18px;height:18px;border:2px solid #d1d5db;border-radius:50%;display:flex;align-items:center;justify-content:center;transition:all 0.3s ease;flex-shrink:0;">
                <div style="width:8px;height:8px;background:#07c160;border-radius:50%;display:none;"></div>
              </div>
            </div>
          </div>

          <!-- 钉钉 -->
          <div class="office-app-card" id="dingtalk" :class="{ selected: ssoConf.officeApp === 'dingtalk' }"
               data-value="dingtalk" @click="selectOfficeApp('dingtalk')">
            <div
                style="display:flex;align-items:center;gap:12px;padding:10px 14px;border:2px solid #e5e7eb;border-radius:8px;cursor:pointer;transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);background:#fff;position:relative;overflow:hidden;">
              <div
                  style="width:36px;height:36px;background:linear-gradient(135deg, #1890ff 0%, #0070f3 100%);border-radius:8px;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px rgba(24,144,255,0.2);flex-shrink:0;">
                <svg viewBox="0 0 24 24" fill="white">
                  <use xlink:href="#icon-dingding"/>
                </svg>
              </div>
              <div style="flex:1;min-width:0;">
                <div style="font-weight:600;color:#1f2937;font-size:13px;margin-bottom:1px;">钉钉</div>
                <div style="font-size:11px;color:#6b7280;line-height:1.3;">阿里巴巴数字化办公平台，智能移动办公</div>
              </div>
              <div class="check-icon"
                   style="width:18px;height:18px;border:2px solid #d1d5db;border-radius:50%;display:flex;align-items:center;justify-content:center;transition:all 0.3s ease;flex-shrink:0;">
                <div style="width:8px;height:8px;background:#1890ff;border-radius:50%;display:none;"></div>
              </div>
            </div>
          </div>

          <!-- 飞书 -->
          <div class="office-app-card" id="feishu" :class="{ selected: ssoConf.officeApp === 'feishu' }"
               data-value="feishu" @click="selectOfficeApp('feishu')">
            <div
                style="display:flex;align-items:center;gap:12px;padding:10px 14px;border:2px solid #e5e7eb;border-radius:8px;cursor:pointer;transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);background:#fff;position:relative;overflow:hidden;">
              <div
                  style="width:36px;height:36px;background:linear-gradient(135deg, #00d4aa 0%, #00b894 100%);border-radius:8px;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px rgba(0,212,170,0.2);flex-shrink:0;">
                <svg viewBox="0 0 24 24" fill="white">
                  <use xlink:href="#icon-feishu1"/>
                </svg>
              </div>
              <div style="flex:1;min-width:0;">
                <div style="font-weight:600;color:#1f2937;font-size:13px;margin-bottom:1px;">飞书</div>
                <div style="font-size:11px;color:#6b7280;line-height:1.3;">字节跳动协作办公平台，高效团队协作</div>
              </div>
              <div class="check-icon"
                   style="width:18px;height:18px;border:2px solid #d1d5db;border-radius:50%;display:flex;align-items:center;justify-content:center;transition:all 0.3s ease;flex-shrink:0;">
                <div style="width:8px;height:8px;background:#00d4aa;border-radius:50%;display:none;"></div>
              </div>
            </div>
          </div>
        </div>

      </div>


      <!-- OAuth2配置 -->
      <div id="sso-oauth2" class="form-row" v-show="ssoConf.oauthType  === 'oauth2'">
        <div style="display:flex;gap:24px;margin-bottom:18px;">
          <div style="flex:1;">
            <label style="font-weight:500;"><span class="required">*</span>Client ID:</label>
            <div style="position:relative;margin-top:6px;">
              <input type="text" style="width:100%;background:#f9fafb;color:#6b7280;cursor:default;padding-right:80px;" readonly
                     v-model="ssoConf.appid" placeholder="系统自动生成">
              <!-- <button type="button"
                      style="position:absolute;right:48px;top:50%;transform:translateY(-50%);background:none;border:none;color:#2563eb;cursor:pointer;padding:4px;border-radius:4px;display:flex;align-items:center;justify-content:center;"
                      @click="generateAppCredentials" title="重新生成">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                  <path d="M21 3v5h-5"/>
                  <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                  <path d="M3 21v-5h5"/>
                </svg>
              </button> -->
              <button type="button" class="copy-btn" @click="copyToClipboard(ssoConf.appid)" title="复制">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div style="display:flex;gap:24px;margin-bottom:18px;">
          <div style="flex:1;">
            <label style="font-weight:500;"><span class="required">*</span>Client Secret:</label>
            <div style="position:relative;margin-top:6px;">
              <input type="text" style="width:100%;background:#f9fafb;color:#6b7280;cursor:default;padding-right:80px;" readonly
                     v-model="ssoConf.appSecret" placeholder="系统自动生成">
              <!-- <button type="button"
                      style="position:absolute;right:48px;top:50%;transform:translateY(-50%);background:none;border:none;color:#2563eb;cursor:pointer;padding:4px;border-radius:4px;display:flex;align-items:center;justify-content:center;"
                      @click="generateAppCredentials" title="重新生成">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                  <path d="M21 3v5h-5"/>
                  <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                  <path d="M3 21v-5h5"/>
                </svg>
              </button> -->
              <button type="button" class="copy-btn" @click="copyToClipboard(ssoConf.appSecret)" title="复制">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div style="margin-bottom:18px;">
          <div style="display:flex;align-items:center;gap:8px;margin-bottom:6px;">
            <label style="font-weight:500;"><span class="required">*</span> 回调地址:
              <span id="callback-toggle-text" style="color:#2563eb;cursor:pointer;font-size:12px;"
                    @click=" callbackArray = !callbackArray">🔗 {{ callbackArray ? '收起配置' : '多地址配置' }}</span>
            </label>
          </div>
          <div id="single-callback-input" v-if="!callbackArray">
            <input type="text" id="main-callback-url" v-model="ssoConf.callback" style="width:100%;"
                   placeholder="请输入地址，例如：https://www.domain.com/sso/login.html">
          </div>
          <div id="multiple-callback-config" v-if="callbackArray"
               style="background:#f8fafc;border:1px solid #e2e8f0;border-radius:8px;padding:16px;margin-top:8px;">
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
              <span style="font-weight:500;color:#374151;">回调地址列表</span>
              <button type="button" @click="callbackArray = false"
                      style="background:none;border:none;color:#6b7280;cursor:pointer;font-size:18px;">×
              </button>
            </div>
            <div style="margin-bottom:12px;">
                      <textarea
                          v-model="ssoConf.callback"
                          id="callback-urls-textarea"
                          style="width:100%;height:150px;resize:vertical;border:1px solid #d1d5db;border-radius:6px;padding:12px;font-size:12px;font-family:monospace;"
                          placeholder="请输入回调地址，每行一个&#10;例如：&#10;https://domain1.com/callback&#10;https://domain2.com/callback&#10;https://sso.example.com/oauth/callback"
                      ></textarea>
            </div>
            <div id="callback-summary" style="font-size:12px;color:#6b7280;">{{ getCallbackSummary() }}</div>
          </div>
        </div>

        <!-- OAuth2配置说明 -->
        <div v-if="ssoConf.appid && ssoConf.appSecret" style="margin-top:24px;">
          <div style="display:flex;align-items:center;gap:8px;margin-bottom:12px;">
            <label style="font-weight:500;">对接说明:
              <span style="color:#2563eb;cursor:pointer;font-size:12px;"
                    @click="oidcConfigCollapsed = !oidcConfigCollapsed">
                {{ oidcConfigCollapsed ? '展开说明' : '收起说明' }}
              </span>
            </label>
          </div>
          <div v-if="!oidcConfigCollapsed" 
               style="background:#f8fafc;border:1px solid #e2e8f0;border-radius:8px;padding:16px;">
            <div style="font-size:12px;color:#6b7280;margin-bottom:12px;">
              适用场景：应用可通过OAuth2协议对接Asec平台，实现登录Asec后免登录至应用<br>
              以下是标准OAuth2流程的接口说明：
            </div>
            
            <div class="oidc-config-item">
              <div class="config-label">授权接口</div>
              <div class="config-value" style="position:relative;padding-right:40px;">
                {{ oidcBaseUrl }}/authorize
                <button type="button" class="copy-btn" @click="copyToClipboard(oidcBaseUrl + '/authorize')" title="复制">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                </button>
              </div>
              <div class="config-desc">
                <strong>请求方式：</strong>GET<br>
                <strong>请求参数：</strong>client_id、response_type=code、redirect_uri、scope（可选）、state（可选）<br>
                <strong>说明：</strong>OAuth2授权码流程的授权接口，用户在此完成登录并授权
              </div>
            </div>

            <div class="oidc-config-item">
              <div class="config-label">获取令牌接口</div>
              <div class="config-value" style="position:relative;padding-right:40px;">
                {{ oidcBaseUrl }}/token
                <button type="button" class="copy-btn" @click="copyToClipboard(oidcBaseUrl + '/token')" title="复制">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                </button>
              </div>
              <div class="config-desc">
                <strong>请求方式：</strong>POST<br>
                <strong>请求头：</strong>content-type: application/json<br>
                <strong>请求体：</strong>{"client_id":"", "client_secret":"", "code":"", "grant_type":"authorization_code", "redirect_uri":""}<br>
                <strong>说明：</strong>使用授权码code换取访问令牌access_token
              </div>
            </div>

            <div class="oidc-config-item" style="border-bottom:none;">
              <div class="config-label">获取用户信息接口</div>
              <div class="config-value" style="position:relative;padding-right:40px;">
                {{ oidcBaseUrl }}/userinfo
                <button type="button" class="copy-btn" @click="copyToClipboard(oidcBaseUrl + '/userinfo')" title="复制">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                </button>
              </div>
              <div class="config-desc">
                <strong>请求方式：</strong>GET<br>
                <strong>请求格式：</strong>Bearer Token认证<br>
                <strong>请求头：</strong>Authorization: Bearer {access_token}<br>
                <strong>说明：</strong>使用访问令牌获取用户基本信息: 用户唯一标识sub, 首选用户名preferred_username邮箱email等信息
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- 账号代填配置 -->
      <div id="sso-autofill" class="form-row webvpn-only" v-show="ssoConf.oauthType ==='fill_forms'">
        <label style="font-weight:500;margin-bottom:12px;display:block;">账号代填配置</label>

        <!-- 识别模式选择 -->
        <div style="margin-bottom:18px;">
          <div style="display:flex;gap:24px;">
            <t-radio-group v-model="ssoConf.recognize_patterns">
              <t-radio value="smart">智能识别模式
                <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    系统将根据页面规则自动识别登录页面的用户名和密码控件，<br/>
                    并根据设置的用户名密码规则进行自动填写登录。<br/>
                    适用于90%的普通登录界面（无下拉选择或验证码干扰组件界面）。<br/>
                    初次登录成功后，当账号密码发生变化时可在应用中心更新点击登录信息。
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming" />
                  </svg>
                </el-tooltip>
              </t-radio>
              <t-radio value="precise">精确识别模式
                <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    自定义登录页面的用户名/密码输入框，登录按钮等控件规则便于精确填写，<br/>
                    适用于较复杂的登录界面（含有类似下拉选择、验证码等复杂的登录界面）。
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming" />
                  </svg>
                </el-tooltip>
              </t-radio>
            </t-radio-group>
          </div>
        </div>
        <!-- 登录页面地址 -->
        <div style="margin-bottom:18px;">
          <div style="display:flex;align-items:center;gap:8px;margin-bottom:6px;">
            <label style="font-weight:500;"><span class="required">*</span> 登录页面地址:
              <span id="login-toggle-text" style="color:#2563eb;cursor:pointer;font-size:12px;"
                    @click="autofillArray = !autofillArray">🔗 {{ autofillArray ? '收起配置' : '多地址配置' }}</span>
            </label>
          </div>
          <div id="single-login-input" v-if="!autofillArray">
            <input type="text" v-model="ssoConf.login_url" id="main-login-url" style="width:100%;"
                   placeholder="请输入登录标准URL地址">
          </div>
          <div id="multiple-login-config" v-if="autofillArray"
               style="background:#f8fafc;border:1px solid #e2e8f0;border-radius:8px;padding:16px;margin-top:8px;">
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
              <span style="font-weight:500;color:#374151;">登录页面地址列表</span>
              <button type="button" @click=" autofillArray = false"
                      style="background:none;border:none;color:#6b7280;cursor:pointer;font-size:18px;">×
              </button>
            </div>
            <div style="margin-bottom:12px;">
                        <textarea id="login-urls-textarea"
                                  v-model="ssoConf.login_url"
                                  style="width:100%;height:140px;resize:vertical;border:1px solid #d1d5db;border-radius:6px;padding:12px;font-size:12px;font-family:monospace;"
                                  placeholder="请输入登录页面地址，每行一个&#10;例如：&#10;https://login.example.com/login&#10;https://sso.example.com/auth&#10;https://portal.example.com/signin"
                        ></textarea>
            </div>
            <div id="login-summary" style="font-size:12px;color:#6b7280;">{{
                ssoConf.login_url.length > 0 ? '已配置' + ssoConf.login_url.split('\n').filter(url => url.trim()).length + '个地址' : '暂无配置地址'
              }}</div>
          </div>
        </div>
        
        <!-- 智能识别模式配置 -->
        <div id="smart-autofill-config" v-if="ssoConf.recognize_patterns === 'smart'">
          <!-- 登录账号 -->
          <div style="margin-bottom:18px;">
            <label style="font-weight:500;margin-bottom:6px;display:block;"><span class="required">*</span>
              登录账号:</label>
            <div style="display:flex;gap:12px;">
              <select style="flex:1;" v-model="ssoConf.login_type">
                <!-- <option value="username_password">使用系统用户名与系统密码登录</option>
                <option value="phone_password">使用手机号与系统密码登录</option>
                <option value="email_password">使用邮箱账号与系统密码登录</option>
                <option value="email_prefix_password">使用邮箱账号前缀与系统密码登录</option> -->
                <option value="custom_password">使用自定义账号密码</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 精确识别模式配置 -->
        <div id="precise-autofill-config" v-if="ssoConf.recognize_patterns === 'precise'">
          <!-- 控件配置 -->
          <div style="margin-bottom:18px;">
            <div style="color:#374151;font-weight:500;font-size:12px;margin-bottom:12px;display:flex;align-items:center;">
              控件配置：配置登录页面的用户名、密码输入框以及登录按钮
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  配置说明：<br/>
                  1. 按F12打开开发者工具，在Elements标签页中查找目标元素<br/>
                  2. 支持多种选择器格式：<br/>
                  &nbsp;&nbsp;• ID选择器：#user_name（对应id="user_name"）<br/>
                  &nbsp;&nbsp;• 类选择器：.username（对应class="username"）<br/>
                  &nbsp;&nbsp;• 属性选择器：input[name="username"]<br/>
                  &nbsp;&nbsp;• 标签选择器：button[type="submit"]<br/>
                  3. 建议优先使用ID选择器，具有唯一性且定位准确
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                  <use xlink:href="#icon-shuoming" />
                </svg>
              </el-tooltip>
            </div>
            <div style="background:#f9fafb;border:1px solid #e5e7eb;border-radius:8px;padding:16px;">
              <!-- 用户名输入框 -->
              <div
                  style="display:grid;grid-template-columns:120px 1fr;gap:12px;align-items:center;margin-bottom:12px;">
                <label style="font-weight:500;color:#374151;">用户名输入框</label>
                <el-input v-model="ssoConf.username_input" type="text" placeholder="如：username、#user、input[name='username']"
                       >
                </el-input>
              </div>

              <!-- 密码输入框 -->
              <div
                  style="display:grid;grid-template-columns:120px 1fr;gap:12px;align-items:center;margin-bottom:12px;">
                <label style="font-weight:500;color:#374151;">密码输入框</label>
                <el-input  type="text" placeholder="如：password、#pwd、input[type='password']" v-model="ssoConf.password_input"
                       >
                </el-input>
              </div>

              <!-- 登录按钮 -->
              <div
                  style="display:grid;grid-template-columns:120px 1fr;gap:12px;align-items:center;margin-bottom:12px;">
                <label style="font-weight:500;color:#374151;">登录按钮</label>
                <el-input type="text" v-model="ssoConf.login_button" placeholder="如：#loginBtn、.login-button、button[type='submit']"
                >
                </el-input>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 自动登录配置（智能识别和精确识别模式共用） -->
        <div style="margin-bottom:18px;">
          <label style="font-weight:500;margin-bottom:6px;display:block;">自动登录配置:</label>
          <div style="background:#f9fafb;border:1px solid #e5e7eb;border-radius:8px;padding:16px;">
            <div style="display:flex;align-items:center;gap:12px;margin-bottom:12px;">
              <t-checkbox v-model="ssoConf.auto_login">启用自动登录</t-checkbox>
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  开启后，系统会在登录页面自动填写用户名和密码并执行登录操作
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;color: #347ACB">
                  <use xlink:href="#icon-shuoming" />
                </svg>
              </el-tooltip>
            </div>
            <div v-if="ssoConf.auto_login" style="display:flex;align-items:center;gap:12px;">
              <label style="font-weight:500;color:#374151;">自动登录周期:</label>
              <el-input-number 
                v-model="ssoConf.auto_login_ped" 
                :min="60" 
                :max="3600" 
                :step="60" 
                style="width:120px"
              />
              <span style="color:#6b7280;font-size:12px;">秒</span>
              <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                <template #content>
                  设置自动登录的检查周期，单位为秒。系统会在此周期内检查登录状态并自动登录
                </template>
                <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;color: #347ACB">
                  <use xlink:href="#icon-shuoming" />
                </svg>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
export default {
  name: 'SingleSignOn'
}
</script>
<script setup>
import {ref, watch, onMounted, computed} from 'vue'
import {ElMessage} from "element-plus";
import useClipboard from "vue-clipboard3";
import { getBaseUrl, fetchAccessConfig, getBaseUrlSync } from '@/utils/accessConfig'

const props = defineProps({
  appType: {
    type: String,
    required: true,
    default: 'tun'
  },
  apiData: {
    type: Object,
    required: false,
    default: {}
  }
})


const ssoConf = ref({
  enableSso: false, //启用单点登录
  oauthType: props.appType === 'tun' ? 'oauth2' : 'micro_application', //单点登录方式  办公应用、oauth2、账号代填
  officeApp: 'qiyewx', //办公应用类型
  appid: '', //oauth2 中的appid
  appSecret: '', //oauth2 中的secret
  callback: '', //oauth2 中的回调地址
  recognize_patterns: "smart", //识别模式  smart智能识别 precise 精确识别
  login_url: '', //智能识别模式登录地址
  login_type: 'custom_password', //登录方式
  auto_login: true, //自动登录开关
  auto_login_ped: 300, //自动登录周期(秒)
  username_input: '',//用户名输入框
  username_value: '',//用户名输入框对应的值
  password_input: '',//密码输入框
  password_value: '',//密码输入框对应的值
  login_button: '', //登录按钮
})

// OIDC配置相关
const oidcConfigCollapsed = ref(true) // 默认收起OIDC配置预览

// 计算属性：获取OIDC基础URL（同步版本，用于模板显示）
const oidcBaseUrl = computed(() => {
  const baseHost = getBaseUrlSync()
  return `${baseHost}/auth/login/v1/authorize/oidc`
})

// 生成随机字符串
const generateRandomString = (length = 32) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 生成APP凭证
const generateAppCredentials = () => {
  ssoConf.value.appid = generateRandomString(32)
  ssoConf.value.appSecret = generateRandomString(64)
  // ElMessage.success('已重新生成APP ID和APP Secret')
}

// 组件挂载时初始化
onMounted(async () => {
  // 先获取接入配置
  await fetchAccessConfig()
  
  // 只有在启用单点登录且选择OAuth2模式且没有appid时，才自动生成
  if (ssoConf.value.enableSso && ssoConf.value.oauthType === 'oauth2' && !ssoConf.value.appid) {
    generateAppCredentials()
  }
})

// 监听单点登录启用状态和OAuth2模式切换，自动生成凭证
watch([() => ssoConf.value.enableSso, () => ssoConf.value.oauthType], ([enableSso, oauthType]) => {
  // 只有在启用单点登录且选择OAuth2模式且没有appid时，才自动生成
  if (enableSso && oauthType === 'oauth2' && !ssoConf.value.appid) {
    generateAppCredentials()
  }
})

watch(
    () => props.apiData,
    (newVal) => {
      if (newVal &&
          newVal.id !== undefined &&
          newVal.id !== null &&
          newVal.id !== '' &&
          newVal.app_type !== 'portal' &&
          newVal.web_compatible_config.default_rule.includes('single_sign_on')) {
        ssoConf.value.enableSso = newVal.web_compatible_config.default_rule.includes('single_sign_on')
        ssoConf.value.oauthType = newVal.web_compatible_config.single_sign_on.type
        if (ssoConf.value.oauthType === 'micro_application') {
          ssoConf.value.officeApp = newVal.web_compatible_config.single_sign_on.config.idp_type
        }
        if (ssoConf.value.oauthType === 'oauth2') {
          ssoConf.value.appid = newVal.web_compatible_config.single_sign_on.config.app_id
          ssoConf.value.appSecret = newVal.web_compatible_config.single_sign_on.config.app_secret
          ssoConf.value.callback = newVal.web_compatible_config.single_sign_on.config.callback
          // 编辑时默认展开OIDC配置预览
          oidcConfigCollapsed.value = false
        }
        if (ssoConf.value.oauthType === 'fill_forms') {
          const keyArr = ['recognize_patterns', 'login_url', 'login_type', 'auto_login', 'auto_login_ped', 'username_input', 'password_input', 'login_button']
          keyArr.forEach(key => {
            if (key in newVal.web_compatible_config.single_sign_on.config) {
              let value = newVal.web_compatible_config.single_sign_on.config[key]
              
              // 转换字符串布尔值为布尔类型
              if (key === 'auto_login') {
                if (typeof value === 'string') {
                  value = value === '1' || value === 'true'
                }
              }
              // 转换字符串数字为数字类型
              else if (key === 'auto_login_ped') {
                if (typeof value === 'string' && !isNaN(value)) {
                  value = parseInt(value)
                }
              }
              
              ssoConf.value[key] = value
            }
          })
          ssoConf.value.username_value = ''
          ssoConf.value.password_value = ''
        }
      } else if (newVal &&
          newVal.app_type !== 'portal' &&
          (!newVal.web_compatible_config || !newVal.web_compatible_config.default_rule.includes('single_sign_on'))) {
        // 如果不是编辑模式或没有单点登录配置，不自动生成凭证
        // 用户需要手动选择启用单点登录并选择OAuth2模式
      }
    },
    { immediate: true, deep: true }
);




const validate = async () => {
  let validateR = true
  if (ssoConf.value.enableSso) {
    if (ssoConf.value.oauthType === 'oauth2') {
      if (!ssoConf.value.appid || !ssoConf.value.appSecret || !ssoConf.value.callback) {
        ElMessage.error({
          message: '请填写完整的OAuth2配置信息',
        })
        validateR = false
      }
            
      // 验证回调地址格式
      const callbackValidation = validateCallbackUrls()
      if (!callbackValidation.valid) {
        ElMessage.error({
          message: '回调地址格式错误：' + callbackValidation.errors.join('; '),
        })
        validateR = false
      }
    } else if (ssoConf.value.oauthType === 'fill_forms') { //表单代填
      if (!ssoConf.value.login_url) {
        ElMessage.error({
          message: '请填写完整登录页面地址',
        })
        validateR = false
      }
    }
  }
  return validateR
}

defineExpose({ssoConf, validate})

const callbackArray = ref(false)
const autofillArray = ref(false)

const selectSSOType = async (type) => {
  ssoConf.value.oauthType = type
}


const {toClipboard} = useClipboard()

// 获取回调地址摘要信息
const getCallbackSummary = () => {
  if (!ssoConf.value.callback || ssoConf.value.callback.trim() === '') {
    return '暂无配置地址'
  }
  
  // 按换行符分割并过滤空行
  const addresses = ssoConf.value.callback.split('\n')
    .map(addr => addr.trim())
    .filter(addr => addr.length > 0)
  
  if (addresses.length === 0) {
    return '暂无有效地址'
  } else if (addresses.length === 1) {
    return `已配置 1 个地址`
  } else {
    return `已配置 ${addresses.length} 个地址`
  }
}

// 验证回调地址格式
const validateCallbackUrls = () => {
  if (!ssoConf.value.callback || ssoConf.value.callback.trim() === '') {
    return { valid: true, errors: [] }
  }
  
  const addresses = ssoConf.value.callback.split('\n')
    .map(addr => addr.trim())
    .filter(addr => addr.length > 0)
  
  const errors = []
  let totalLength = 0
  
  addresses.forEach((addr, index) => {
    // 验证URL格式
    try {
      new URL(addr)
    } catch (e) {
      errors.push(`第 ${index + 1} 行地址格式不正确: ${addr}`)
      return
    }
    
    // 验证协议
    if (!addr.startsWith('http://') && !addr.startsWith('https://')) {
      errors.push(`第 ${index + 1} 行地址必须以http://或https://开头: ${addr}`)
      return
    }
  })
  
  // 检查总长度限制（考虑换行符）
  const joinedAddresses = addresses.join('\n')
  if (joinedAddresses.length > 255) {
    errors.push(`回调地址总长度超过255字符限制，当前长度: ${joinedAddresses.length}`)
  }
  
  return { valid: errors.length === 0, errors }
}

const copyToClipboard = async (txt) => {
  if (!txt) {
    ElMessage.error({
      message: '内容为空',
    })
    return
  }
  try {
    await toClipboard(txt.toString())
    ElMessage.success({
      message: '复制成功',
    })
  } catch (e) {
    ElMessage.error({
      message: '复制失败请重试',
    })
  }
}


//设置 钉钉、飞书、企微三个的选中样式
const selectOfficeApp = (value) => {
  ssoConf.value.officeApp = value

  // 获取所有办公应用卡片
  document.querySelectorAll('.office-app-card').forEach(card => {
    card.classList.remove('selected');
    const cardDiv = card.querySelector('div');
    const checkIcon = card.querySelector('.check-icon');
    const checkDot = card.querySelector('.check-icon div');

    if (cardDiv) {
      cardDiv.style.borderColor = '#e5e7eb';
      cardDiv.style.background = '#fff';
      cardDiv.style.transform = 'translateY(0)';
      cardDiv.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
    }

    if (checkIcon) {
      checkIcon.style.borderColor = '#d1d5db';
      checkIcon.style.background = 'transparent';
      checkIcon.style.transform = 'scale(1)';
      checkDot && (checkDot.style.display = 'none');
    }
  });
  const element = document.getElementById(value);
  element.classList.add('selected');

  const cardDiv = element.querySelector('div');
  const checkIcon = element.querySelector('.check-icon');
  const checkDot = element.querySelector('.check-icon div');
  let themeColor, bgColor;
  switch (value) {
    case 'qiyewx':
      themeColor = '#07c160';
      bgColor = 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)';
      break;
    case 'dingtalk':
      themeColor = '#1890ff';
      bgColor = 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)';
      break;
    case 'feishu':
      themeColor = '#00d4aa';
      bgColor = 'linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%)';
      break;
    default:
      themeColor = '#2563eb';
      bgColor = 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)';
  }

  if (cardDiv) {
    cardDiv.style.borderColor = themeColor;
    cardDiv.style.background = bgColor;
  }

  if (checkIcon) {
    checkIcon.style.borderColor = themeColor;
    checkIcon.style.background = themeColor;
    if (checkDot) checkDot.style.display = 'block';
  }

};


</script>
<style scoped>
/* 必填项样式 */
.SingleSignOn .required {
  color: red;
}

/* 通用复制按钮样式 */
.SingleSignOn .copy-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #2563eb;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 办公应用卡片基础样式 */
.SingleSignOn .office-app-card {
  position: relative;
}

.SingleSignOn .office-app-card > div {
  position: relative;
  overflow: hidden;
}

/* 未选中卡片效果 */
.SingleSignOn .office-app-card:not(.selected) {
  opacity: 0.75;
  filter: saturate(0.8);
}

/* 选中卡片效果 */
.SingleSignOn .office-app-card.selected {
  opacity: 1;
  filter: saturate(1);
}

/* 不同办公应用选中样式 */
.SingleSignOn #qiyewx.selected > div {
  border: 2px solid rgb(7, 193, 96) !important;
  background: linear-gradient(135deg, rgb(240, 253, 244) 0%, rgb(220, 252, 231) 100%) !important;
}

.SingleSignOn #dingtalk.selected > div {
  border: 2px solid rgb(24, 144, 255) !important;
  background: linear-gradient(135deg, rgb(239, 246, 255) 0%, rgb(219, 234, 254) 100%) !important;
}

.SingleSignOn #feishu.selected > div {
  border: 2px solid rgb(0, 212, 170) !important;
  background: linear-gradient(135deg, rgb(240, 253, 250) 0%, rgb(204, 251, 225) 100%) !important;
}

/* 悬停和选中动画 */
.SingleSignOn .office-app-card:hover:not(.selected) > div {
  border-color: #cbd5e1 !important;
  background: #f8fafc !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08) !important;
}

.SingleSignOn .office-app-card.selected > div {
  transform: translateY(-3px) !important;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12) !important;
}

.SingleSignOn .office-app-card.selected > div::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.SingleSignOn .office-app-card.selected .check-icon {
  border-color: currentColor !important;
  background: currentColor !important;
  transform: scale(1.1) !important;
}

.SingleSignOn .office-app-card.selected .check-icon > div {
  background: white !important;
  display: block !important;
  animation: checkPulse 0.6s ease-out;
}

/* 按钮组样式 */
.SingleSignOn .button-group {
  position: relative;
}

.SingleSignOn .button-group-item {
  position: relative;
  z-index: 1;
}

.SingleSignOn .button-group-item:hover:not(.active) {
  background: rgba(59, 130, 246, 0.08) !important;
  color: #374151 !important;
}

.SingleSignOn .button-group-item.active {
  background: #2563eb !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
  transform: translateY(-1px) !important;
}

.SingleSignOn .button-group-item.active:hover {
  background: #1d4ed8 !important;
}

/* 表单控件通用样式 */
.SingleSignOn input[type="text"],
.SingleSignOn input[type="url"],
.SingleSignOn select,
.SingleSignOn textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.2s ease;
  background: white;
  color: #1f2937;
}

.SingleSignOn input[type="text"]:focus,
.SingleSignOn input[type="url"]:focus,
.SingleSignOn select:focus,
.SingleSignOn textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #eff6ff;
  transform: translateY(-1px);
}

.SingleSignOn input[type="text"]:hover:not(:focus),
.SingleSignOn input[type="url"]:hover:not(:focus),
.SingleSignOn select:hover:not(:focus),
.SingleSignOn textarea:hover:not(:focus) {
  border-color: #9ca3af;
}

.SingleSignOn input[type="text"]::placeholder,
.SingleSignOn input[type="url"]::placeholder,
.SingleSignOn textarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* OIDC配置信息样式 */
.SingleSignOn .oidc-config-item {
  padding: 12px 0;
  border-bottom: 1px solid #e5e7eb;
}

.SingleSignOn .oidc-config-item:last-child {
  border-bottom: none;
}

.SingleSignOn .config-label {
  font-weight: 600;
  color: #1f2937;
  font-size: 13px;
  margin-bottom: 4px;
}

.SingleSignOn .config-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #2563eb;
  background: #f1f5f9;
  padding: 6px 8px;
  border-radius: 4px;
  margin-bottom: 4px;
  word-break: break-all;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.SingleSignOn .config-value:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

.SingleSignOn .config-desc {
  font-size: 11px;
  color: #6b7280;
  line-height: 1.4;
}

/* 过渡动画 */
.SingleSignOn .oidc-config-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.SingleSignOn input[type="url"]::placeholder,
.SingleSignOn textarea::placeholder {
  color: #9ca3af;
}

</style>
