package api

import (
	"asdsec.com/asec/platform/app/console/app/oprlog/dto"
	"asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/web"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"net/url"
	"time"
)

// ReadOprLogListReq 查询列表请求
type ReadOprLogListReq struct {
	//CorpId uint64 `json:"tenant_id" binding:"required,gte=1"`      //最小值1
	CurPage  int64  `json:"page" binding:"required,gte=1"`           //最小值1
	PageSize int64  `json:"limit" binding:"required,gte=1,lte=1000"` //最小值1,最大值1000
	Start    int64  `json:"start" binding:"required,gt=1468489661"`  //2016-07-14 17:47:41
	End      int64  `json:"end" binding:"required,gt=1468489661"`    //2016-07-14 17:47:41
	User     string `json:"user" binding:"excludesall=~!#$%\\^&*()+{}\"'<>?,max=128"`
	Ip       string `json:"ip" binding:"ipip,max=128"` //自定义验证 IpAndipSegfunc
}

type ReadOprLogListResp struct {
	Total   int64           `json:"total_items"`
	HasNext bool            `json:"has_next"`
	Info    *[]model.Oprlog `json:"info"`
}

// ExportReq 导出请求
type ExportReq struct {
	//CorpId uint64 `json:"tenant_id" binding:"required,gte=1"`     //最小值1
	Start int64  `json:"start" binding:"required,gt=1468489661"` //2016-07-14 17:47:41
	End   int64  `json:"end" binding:"required,gt=1468489661"`   //2016-07-14 17:47:41
	User  string `json:"user" binding:"excludesall=~!#$%\\^&*()+{}\"'<>?,max=128"`
	Ip    string `json:"ip" binding:"ipip,max=128"` //自定义验证 IpAndipSegfunc
}

// WriteOprlogReq 写操作日志请求
type WriteOprlogReq struct {
	//CorpId uint64 `json:"tenant_id" binding:"required,gte=1"`
	Module  string `json:"module" binding:"required,min=1,max=128"`
	Opr     string `json:"opr" binding:"required,min=1,max=128"`
	Desc    string `json:"desc" binding:"required,min=1,max=256"`
	Success bool   `json:"success" `
	User    string `json:"user" binding:"required,min=1,max=128"` //这个从认证信息中取
	//Time     int64  `json:"time" binding:"required,gt=1468489661"`
	Ip string `json:"ip" binding:"required,ip"`
}

func WriteOprlog(c *gin.Context) {
	var oprlogReq WriteOprlogReq
	//参数检查及绑定
	if err := c.ShouldBindJSON(&oprlogReq); err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	oplog := model.Oprlog{
		CorpId:        web.GetAdminCorpId(c),
		ResourceType:  oprlogReq.Module,
		OperationType: oprlogReq.Opr,
		//Desc:           oprlogReq.Desc,
		//Success:        oprlogReq.Success,
		//User:           oprlogReq.User,
		AdminEventTime: time.Now().Unix(),
		IpAddress:      oprlogReq.Ip,
	}
	_, err := service.GetOprlogService().Create(c, oplog)
	if err != nil {
		global.SysLog.Error("Create oplog fail", zap.Error(err),
			zap.String("tenantId", web.GetAdminCorpId(c)))
		common.Fail(c, common.DBOperateError)
		return
	}
	common.OkWithData(c, "ok")
}

// ReadOprLogList 查询操作日志
// @Summary     查询当前搜索条件的操作日志
// @Description 可以按照时间、ip和用户筛选
// @Tags        Logs
// @Accept      application/json
// @Produce     application/json
// @Param       object body dto.ListOperateLogReq false "查询操作日志参数"
// @Success     200
// @Router      /v1/operate_log/list [POST]
func ReadOprLogList(c *gin.Context) {
	var req dto.ListOperateLogReq
	//参数检查及绑定
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.StartTime != "" {
		start, err := time.Parse("2006-01-02T15:04:05.000Z", req.StartTime)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.StartTimestamp = start.UnixMilli()
	}
	if req.EndTime != "" {
		end, err := time.Parse("2006-01-02T15:04:05.000Z", req.EndTime)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.EndTimestamp = end.UnixMilli()
	}
	if req.EndTimestamp < req.StartTimestamp {
		common.FailWithMessage(c, -1, "非法的请求参数:结束时间不能小于开始时间")
		return
	}

	//查询符合条件的操作日志
	data, err := service.GetOprlogService().OprLogList(c, req)
	if err != nil {
		global.SysLog.Error("OprLogList fail", zap.Error(err))
		common.Fail(c, common.DBOperateError)
		return
	}
	common.OkWithData(c, data)
}

type ResourceTypes struct {
	ResourceType string `json:"resource_type"`
	Name         string `json:"name"`
}

// OprResourceTypes 查询操作日志被操作对象类型
// @Summary     查询操作日志被操作对象类型
// @Description 查询操作日志被操作对象类型
// @Tags        Logs
// @Accept      application/json
// @Produce     application/json
// @Success     200
// @Router      /v1/operate_log/types [GET]
// @success     200 {object} common.Response{data=[]dto.OptResourceType}
func OprResourceTypes(c *gin.Context) {
	res, err := service.GetOprlogService().GetOptResourceList(c)
	if err != nil {
		global.SysLog.Error("get opt resource list error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, res)
}

// CreateResourceType 创建日志操作类型映射
// @Summary     创建日志操作类型映射
// @Description 创建日志操作类型映射
// @Tags        Logs
// @Accept      application/json
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/opt/resource_type [POST]
// @success     200 {object} common.Response{}
func CreateResourceType(c *gin.Context) {
	var optResourceReq dto.OptResourceTypeReq
	err := c.ShouldBindJSON(&optResourceReq)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err = service.GetOprlogService().CreateOptResource(c, optResourceReq)
	if err != nil {
		global.SysLog.Error("create opt resource error", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// Export 导出操作日志
// @Summary     导出当前搜索条件的操作日志为excel
// @Description 可以按照时间、ip和用户筛选，导出为excel
// @Tags        操作日志
// @Accept      application/json
// @Produce     application/octet-stream
// @Param       object query ExportReq false "导出操作日志参数"
// @Success     200
// @Router      /v1/operate_log/export [get]
func Export(c *gin.Context) {
	var oprlogReq ExportReq
	//参数检查及绑定
	if err := c.ShouldBindQuery(&oprlogReq); err != nil {
		global.SysLog.Error("param err", zap.Any("req", oprlogReq), zap.Error(err), zap.String("tenantId", web.GetAdminCorpId(c)))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	//从DB读取所有符合条件的操作日志
	info, count, err := readOprLogListFromDb(c, web.GetAdminCorpId(c), oprlogReq)
	if err != nil {
		global.SysLog.Error("readOprLogListFromDb fail", zap.Error(err),
			zap.String("tenantId", web.GetAdminCorpId(c)))
		common.Fail(c, common.DBOperateError)
		return
	}
	//生成excel文件
	xlsx := excelize.NewFile()
	makeExcel(xlsx, info, count, oprlogReq)
	//设置http下载头
	filename := fmt.Sprint(url.QueryEscape(global.GetResource(c, common.OprLogExcelName)), "_", time.Now().Format("20060102150405"), ".xlsx")
	c.Header("Content-Type", "application/ms-excel")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	//回写到web 流媒体 形成下载
	_ = xlsx.Write(c.Writer)
}

// 从db中读取所有符合条件的操作日志，翻页读取直到读完，每页500个
func readOprLogListFromDb(c context.Context, tenantId string, oprlogReq ExportReq) (*[]model.Oprlog, int64, error) {
	var info *[]model.Oprlog
	//var curPage int64 = 1
	//var pageSize int64 = constants.ExportReqOneLimit
	//count, info, err := service.GetOprlogService().OprLogList(c, tenantId,
	//	curPage, pageSize, oprlogReq.Start,
	//	oprlogReq.End, oprlogReq.User, oprlogReq.Ip)
	//if err != nil {
	//	return nil, 0, err
	//}
	//curPage = curPage + 1
	//for (curPage-1)*pageSize <= count {
	//	_, temp, err := service.GetOprlogService().OprLogList(c, tenantId,
	//		curPage, pageSize, oprlogReq.Start,
	//		oprlogReq.End, oprlogReq.User, oprlogReq.Ip)
	//	if err != nil {
	//		return nil, 0, err
	//	}
	//	*info = append(*info, *temp...)
	//	curPage = curPage + 1
	//}
	return info, 1, nil
}

// 时间戳转时间字符串
func timestamp2String(timestamp int64) string {
	t := time.Unix(timestamp, 0)           //2017-08-30 16:19:19 +0800 CST
	return t.Format("2006-01-02 15:04:05") //2015-06-15 08:52:32
}

// 将操作日志导出为excel
func makeExcel(xlsx *excelize.File, info *[]model.Oprlog, count int64, oprlogReq ExportReq) {
	//设置列宽
	xlsx.SetColWidth("Sheet1", "A", "H", 20)
	//第一行展示一个标题
	xlsx.MergeCell("Sheet1", "A1", "H1")
	xlsx.SetCellValue("Sheet1", "A1", "操作日志")
	style_title, _ := xlsx.NewStyle(`{"font":{"bold":true, "family": "Calibri","size": 15},
	"alignment":{"horizontal":"center","vertical":"center"}}`)
	xlsx.SetCellStyle("Sheet1", "A1", "H1", style_title)
	//汇总信息
	style_bold, _ := xlsx.NewStyle(`{"font":{"bold":true, "family": "Calibri","size": 10}}`)
	xlsx.SetCellValue("Sheet1", "A2", "汇总信息")
	xlsx.SetCellStyle("Sheet1", "A2", "A2", style_bold)
	xlsx.MergeCell("Sheet1", "B2", "H2")

	style_left, _ := xlsx.NewStyle(`{"border":[{"type":"right","color":"#000000","style":1}],
	"font":{"family": "Calibri","size": 10},"alignment":{"horizontal":"left","vertical":"center"}}`)
	xlsx.SetCellValue("Sheet1", "B2",
		fmt.Sprint("符合查询条件的记录总数为", count, "条，当前共导出", count, "条"))
	xlsx.SetCellStyle("Sheet1", "B2", "B2", style_left)

	//展示查询条件
	style_subtitle, _ := xlsx.NewStyle(`{"font":{"bold":true, "family": "Calibri","size": 12},
	"alignment":{"horizontal":"center","vertical":"center"}}`)
	xlsx.MergeCell("Sheet1", "A3", "H3")
	xlsx.SetCellValue("Sheet1", "A3", "查询条件")
	xlsx.SetCellStyle("Sheet1", "A3", "A3", style_subtitle)
	xlsx.SetCellStyle("Sheet1", "A4", "A7", style_bold)
	xlsx.MergeCell("Sheet1", "B4", "H4")
	xlsx.MergeCell("Sheet1", "B5", "H5")
	xlsx.MergeCell("Sheet1", "B6", "H6")
	xlsx.MergeCell("Sheet1", "B7", "H7")
	xlsx.SetCellStyle("Sheet1", "B4", "B4", style_left)
	xlsx.SetCellValue("Sheet1", "A4", "时间")
	xlsx.SetCellValue("Sheet1", "A5", "时间排序")
	xlsx.SetCellValue("Sheet1", "A6", "用户名")
	xlsx.SetCellValue("Sheet1", "A7", "ip地址")
	xlsx.SetCellValue("Sheet1", "B4", fmt.Sprint(timestamp2String(oprlogReq.Start),
		"-", timestamp2String(oprlogReq.End)))
	xlsx.SetCellValue("Sheet1", "B5", "降序")
	if oprlogReq.User == "" {
		xlsx.SetCellValue("Sheet1", "B6", "所有")
	} else {
		xlsx.SetCellValue("Sheet1", "B6", oprlogReq.User)
	}
	if oprlogReq.Ip == "" {
		xlsx.SetCellValue("Sheet1", "B7", "所有")
	} else {
		xlsx.SetCellValue("Sheet1", "B7", oprlogReq.Ip)
	}

	//展示查询结果
	xlsx.MergeCell("Sheet1", "A8", "H8")
	xlsx.SetCellValue("Sheet1", "A8", "查询结果")
	xlsx.SetCellStyle("Sheet1", "A8", "A8", style_subtitle)

	//颜色
	style4, _ := xlsx.NewStyle(`{"border":[{"type":"right","color":"#000000","style":1}],
	"fill":{"type":"pattern","color":["#99CCFF"],"pattern":1}}`)
	xlsx.SetCellStyle("Sheet1", "A9", "H9", style4)
	xlsx.SetCellValue("Sheet1", "A9", "序号")
	xlsx.SetCellValue("Sheet1", "B9", "操作时间")
	xlsx.SetCellValue("Sheet1", "C9", "用户")
	xlsx.SetCellValue("Sheet1", "D9", "ip地址")
	xlsx.SetCellValue("Sheet1", "E9", "操作类型")
	xlsx.SetCellValue("Sheet1", "F9", "操作对象")
	xlsx.SetCellValue("Sheet1", "G9", "操作描述")
	xlsx.SetCellValue("Sheet1", "H9", "操作结果")

	i := 9   //单元格真实编号
	idx := 0 //输出首列编号
	for _, v := range *info {
		i++
		idx++
		xlsx.SetCellStyle("Sheet1", fmt.Sprint("A", i), fmt.Sprint("H", i), style_left)
		xlsx.SetCellValue("Sheet1", fmt.Sprint("A", i), idx)
		xlsx.SetCellValue("Sheet1", fmt.Sprint("B", i), timestamp2String(v.AdminEventTime))
		//xlsx.SetCellValue("Sheet1", fmt.Sprint("C", i), v.User)
		xlsx.SetCellValue("Sheet1", fmt.Sprint("D", i), v.IpAddress)
		xlsx.SetCellValue("Sheet1", fmt.Sprint("E", i), v.OperationType)
		xlsx.SetCellValue("Sheet1", fmt.Sprint("F", i), v.ResourceType)
		//xlsx.SetCellValue("Sheet1", fmt.Sprint("G", i), v.Desc)
		//if v.Success {
		//	xlsx.SetCellValue("Sheet1", fmt.Sprint("H", i), "成功")
		//} else {
		//	xlsx.SetCellValue("Sheet1", fmt.Sprint("H", i), "失败")
		//}
	}
}
