package api

import (
	"asdsec.com/asec/platform/app/console/app/offline_upgrade/dto"
	"asdsec.com/asec/platform/app/console/app/offline_upgrade/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PkgSegmentedUpload godoc
// @Summary 上传升级包
// @Schemes
// @Description 上传升级包
// @Tags        ModuleSwitch
// @Produce     application/json
// @Param       req body dto.PkgUploadReq true "上传升级包参数"
// @Success     200
// @Router      /v1/module_switch/agent [PUT]
// @success     200 {object} common.Response{}
func PkgSegmentedUpload(c *gin.Context) {
	var req dto.PkgUploadReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	form, err := c.FormFile("chunk_file")
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	req.ChunkFile = form
	err = service.GetUpgradeService().PkgSegmentedUpload(c, req)
	if err != nil {
		global.SysLog.Error("UploadUpgradePkg err", zap.Error(err))
		// 使用具体的错误信息返回给前端
		common.FailWithMessage(c, -1, err.Error())
		return
	}
	//file, err := form.Open()
	//if err != nil {
	//	common.Fail(c, common.OperateError)
	//	return
	//}
	//req.ChunkFile = file
	//defer file.Close()
	//err = service.GetUpgradeService().PkgSegmentedUpload(c, req)
	//if err != nil {
	//	global.SysLog.Error("UploadUpgradePkg err", zap.Error(err))
	//	common.Fail(c, common.OperateError)
	//	return
	//}
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.PlatformUpgradeTYpe,
			OperationType:  common.OperateUpdate,
			Representation: "升级包" + req.FileName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	common.Ok(c)
}
