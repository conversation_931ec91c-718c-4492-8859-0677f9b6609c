package repository

import (
	"asdsec.com/asec/platform/app/console/app/offline_upgrade/dto"
	"context"
)

// UpgradeRepository 接口定义
type UpgradeRepository interface {
	PkgSegmentedUpload(ctx context.Context, req dto.PkgUploadReq) error
	GetCurrentSystemVersion(ctx context.Context, name string) (string, error)
	GetLatestAgentVersion(ctx context.Context, platform string) (string, error)
}

// NewUpgradeRepository 创建接口实现接口实现
func NewUpgradeRepository() UpgradeRepository {
	return &upgradeRepository{}
}

type upgradeRepository struct {
}

func (u upgradeRepository) PkgSegmentedUpload(ctx context.Context, req dto.PkgUploadReq) error {
	//TODO implement me
	panic("implement me")
}

func (u upgradeRepository) GetCurrentSystemVersion(ctx context.Context, name string) (string, error) {
	//TODO implement me
	panic("implement me")
}

func (u upgradeRepository) GetLatestAgentVersion(ctx context.Context, platform string) (string, error) {
	//TODO implement me
	panic("implement me")
}
