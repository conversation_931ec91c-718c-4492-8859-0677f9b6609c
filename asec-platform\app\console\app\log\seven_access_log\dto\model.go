package dto

import (
	"time"
)

type SevenAccessLog struct {
	Uuid           string    `json:"uuid" gorm:"column:uuid"`
	AccessTime     time.Time `json:"access_time" gorm:"column:access_time"`
	UserId         string    `json:"user_id" gorm:"column:user_id"`
	UserName       string    `json:"user_name" gorm:"column:user_name"`
	AppId          string    `json:"app_id" gorm:"column:app_id"`
	AppName        string    `json:"app_name" gorm:"column:app_name"`
	Url            string    `json:"url" gorm:"column:url"`
	StrategyId     string    `json:"strategy_id" gorm:"column:strategy_id"`
	StrategyName   string    `json:"strategy_name" gorm:"column:strategy_name"`
	StrategyAction string    `json:"strategy_action" gorm:"column:strategy_action"`
	ClientIp       string    `json:"client_ip" gorm:"column:client_ip"`
	Host           string    `json:"host" gorm:"column:host"`
	Uri            string    `json:"uri" gorm:"column:uri"`
	Pid            string    `json:"pid" gorm:"column:pid"`
	Etag           string    `json:"etag" gorm:"column:etag"`
	ServerAddr     string    `json:"server_addr" gorm:"column:server_addr"`
	Schema         string    `json:"schema" gorm:"column:schema"`
	AppType        string    `json:"app_type" gorm:"app_type"`

	ReqAccept           string `json:"req_accept" gorm:"column:req_accept" structs:"req_accept"`
	ReqAcceptEncoding   string `json:"req_accept_encoding" gorm:"column:req_accept_encoding" structs:"req_accept_encoding"`
	ReqAcceptLanguage   string `json:"req_accept_language" gorm:"column:req_accept_language" structs:"req_accept_language"`
	ReqBody             string `json:"req_body" gorm:"column:req_body" structs:"req_body"`
	ReqContentEncoding  string `json:"req_content_encoding" gorm:"column:req_content_encoding" structs:"req_content_encoding"`
	ReqContentLength    string `json:"req_content_length" gorm:"column:req_content_length" structs:"req_content_length"`
	ReqContentType      string `json:"req_content_type" gorm:"column:req_content_type" structs:"req_content_type"`
	ReqCookie           string `json:"req_cookie" gorm:"column:req_cookie" structs:"req_cookie"`
	ReqMethod           string `json:"req_method" gorm:"column:req_method" structs:"req_method"`
	ReqOrigin           string `json:"req_origin" gorm:"column:req_origin" structs:"req_origin"`
	ReqProto            string `json:"req_proto" gorm:"column:req_proto" structs:"req_proto"`
	ReqQueryArgs        string `json:"req_query_args" gorm:"column:req_query_args" structs:"req_query_args"`
	ReqReferer          string `json:"req_referer" gorm:"column:req_referer" structs:"req_referer"`
	ReqTransferEncoding string `json:"req_transfer_encoding" gorm:"column:req_transfer_encoding" structs:"req_transfer_encoding"`
	ReqUserAgent        string `json:"req_user_agent" gorm:"column:req_user_agent" structs:"req_user_agent"`
	ReqXForwardedFor    string `json:"req_x_forwarded_for" gorm:"column:req_x_forwarded_for" structs:"req_x_forwarded_for"`
	ReqXRequestWith     string `json:"req_x_request_with" gorm:"column:req_x_request_with" structs:"req_x_request_with"`

	RspBody string `json:"rsp_body" gorm:"column:rsp_body"`

	RspCacheControl       string `json:"rsp_cache_control" gorm:"column:rsp_cache_control" structs:"rsp_cache_control"`
	RspContentDisposition string `json:"rsp_content_disposition" gorm:"column:rsp_content_disposition" structs:"rsp_content_disposition"`
	RspContentEncoding    string `json:"rsp_content_encoding" gorm:"column:rsp_content_encoding" structs:"rsp_content_encoding"`
	RspContentLength      string `json:"rsp_content_length" gorm:"column:rsp_content_length" structs:"rsp_content_length"`
	RspContentType        string `json:"rsp_content_type" gorm:"column:rsp_content_type" structs:"rsp_content_type"`
	RspCookie             string `json:"rsp_cookie" gorm:"column:rsp_cookie" structs:"rsp_cookie"`
	RspDate               string `json:"rsp_date" gorm:"column:rsp_date" structs:"rsp_date"`
	RspLastModified       string `json:"rsp_last_modified" gorm:"column:rsp_last_modified" structs:"rsp_last_modified"`
	RspLocation           string `json:"rsp_location" gorm:"column:rsp_location" structs:"rsp_location"`
	RspServer             string `json:"rsp_server" gorm:"column:rsp_server" structs:"rsp_server"`
	RspSetCookie          string `json:"rsp_set_cookie" gorm:"column:rsp_set_cookie" structs:"rsp_set_cookie"`
	RspStatusCode         int32  `json:"rsp_status_code" gorm:"column:rsp_status_code" structs:"rsp_status_code"`
	RspTransferEncoding   string `json:"rsp_transfer_encoding" gorm:"column:rsp_transfer_encoding" structs:"rsp_transfer_encoding"`
	RspUpstreamCode       int32  `json:"rsp_upstream_code" gorm:"column:rsp_upstream_code" structs:"rsp_upstream_code"`

	Activity       string `json:"activity" gorm:"column:activity"`
	FileCategoryId string `json:"file_category_id" gorm:"column:file_category_id" `
	SensitiveInfo  string `json:"sensitive_info" gorm:"column:sensitive_info" `
	FileName       string `json:"file_name" gorm:"column:file_name"`
}

func (SevenAccessLog) TableName() string {
	return "tb_seven_access_log"
}

type SevenAccessLogConf struct {
	ColumnName string    `json:"column_name" gorm:"column:column_name;type:varchar(255);not null"`
	Key        string    `json:"key" gorm:"column:key;type:varchar(255);not null"`
	Type       string    `json:"type" gorm:"column:type;type:varchar(255);not null"`
	HeaderName string    `json:"header_name" gorm:"column:header_name;type:varchar(255);not null"`
	Enable     bool      `json:"enable" gorm:"enable"`
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at;type:timestamptz(6);default:now()"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"column:updated_at;type:timestamptz(6);default:now()"`
}

func (SevenAccessLogConf) TableName() string {
	return "tb_seven_access_log_conf"
}
