package service

import (
	"context"
	"encoding/base64"
	"errors"
	"strings"
	"sync"

	"asdsec.com/asec/platform/app/console/app/form_fill/dto"
	"asdsec.com/asec/platform/app/console/app/form_fill/repository"
	"github.com/go-kratos/kratos/v2/log"
)

var FormFillServiceImpl FormFillService
var FormFillServiceInit sync.Once

// FormFillService 表单代填服务接口
type FormFillService interface {
	// 获取表单代填账户凭证
	GetFormFillAccount(ctx context.Context, appID, userID string) (*dto.GetFormFillAccountResp, error)

	// 更新表单代填账户
	UpdateFormFillAccount(ctx context.Context, req dto.UpdateFormFillAccountReq, userID string, corpID int64) error
}

// formFillService 表单代填服务实现
type formFillService struct {
	repo repository.FormFillRepository
}

// GetFormFillService 获取表单代填服务实例
func GetFormFillService() FormFillService {
	FormFillServiceInit.Do(func() {
		FormFillServiceImpl = &formFillService{
			repo: repository.GetFormFillRepository(),
		}
	})
	return FormFillServiceImpl
}

// GetFormFillAccount 获取表单代填账户凭证
func (s *formFillService) GetFormFillAccount(ctx context.Context, appID, userID string) (*dto.GetFormFillAccountResp, error) {
	// 从数据库获取解密后的账户信息
	account, err := s.repo.GetDecryptedAccountByAppIDAndUserID(ctx, appID, userID)
	if err != nil {
		return &dto.GetFormFillAccountResp{
			ErrCode: "500",
			ErrMsg:  "获取账户信息失败",
		}, err
	}

	// 如果账户不存在，返回不存在的提示
	if account == nil {
		return &dto.GetFormFillAccountResp{
			ErrCode: "404",
			ErrMsg:  "账户不存在，请先设置表单代填账户",
		}, nil
	}

	// 编码凭证为 base64(username:password)
	// 这里使用解密后的密码
	credentials := base64.StdEncoding.EncodeToString([]byte(account.Username + ":" + account.Password))

	return &dto.GetFormFillAccountResp{
		ErrCode:     "0",
		ErrMsg:      "success",
		Credentials: credentials,
	}, nil
}

// UpdateFormFillAccount 更新表单代填账户
func (s *formFillService) UpdateFormFillAccount(ctx context.Context, req dto.UpdateFormFillAccountReq, userID string, corpID int64) error {
	log.Infof("开始更新表单代填账户: appID=%s, userID=%s", req.AppID, userID)

	// 解码凭证
	credentialsBytes, err := base64.StdEncoding.DecodeString(req.Credentials)
	if err != nil {
		log.Errorf("凭证解码失败: appID=%s, userID=%s, err=%v", req.AppID, userID, err)
		return errors.New("凭证格式错误")
	}

	credentials := string(credentialsBytes)
	parts := strings.Split(credentials, ":")
	if len(parts) != 2 {
		log.Errorf("凭证格式错误: appID=%s, userID=%s, credentials=%s", req.AppID, userID, credentials)
		return errors.New("凭证格式错误，应为username:password")
	}

	username := parts[0]
	password := parts[1]

	log.Infof("解析凭证成功: appID=%s, userID=%s, username=%s", req.AppID, userID, username)

	// 更新或创建账户
	err = s.repo.UpsertAccount(ctx, corpID, req.AppID, userID, username, password)
	if err != nil {
		log.Errorf("更新账户失败: appID=%s, userID=%s, username=%s, err=%v", req.AppID, userID, username, err)
		return err
	}

	log.Infof("更新表单代填账户成功: appID=%s, userID=%s, username=%s", req.AppID, userID, username)
	return nil
}
