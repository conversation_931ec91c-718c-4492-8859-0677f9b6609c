package api

import (
	v1 "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/console/app/module_switch/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// UpdateNetworkFilterSettings godoc
// @Summary 查询数据调查的模块开关
// @Schemes
// @Description 查询数据调查的模块开关
// @Tags        NetworkFilterSwitch
// @Produce     application/json
// @Success     200
// @Router      /v1/module_switch/nf_settings [POST]
// @success     200 {object} common.Response{}
func UpdateNetworkFilterSettings(c *gin.Context) {
	var req v1.NetworkFilterSettings
	if err := c.ShouldBind(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetNFSettingsService().UpdateNFSettings(c, &req)
	if err != nil {
		global.SysLog.Error("UpdateNFSettings err", zap.Error(err))
		return
	}
	common.Ok(c)
}
