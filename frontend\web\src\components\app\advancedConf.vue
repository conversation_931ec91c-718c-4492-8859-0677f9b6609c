<!-- //应用图标上传组件-->
<template>
  <div class="advanced">

    <t-form-item name="healthCheck">
      <t-checkbox v-model="conf.healthCheck">应用健康检测</t-checkbox>
    </t-form-item>

    <t-form v-if="conf.healthCheck" ref="advance" :data="conf" label-align="top" :rules="rules">
      <div id="health-check-card" class="health-check-container">
        <!-- 第一行：基础检测配置 -->

        <!-- 第三行：高级配置 -->
        <div class="health-advanced-grid">
          <div>
            <div class="input-with-unit">
              <t-form-item name="healthCheckConfig.interval_time" label="探测间隔(秒/次)">
                <t-input style="width: 120px !important;" v-model.number="conf.healthCheckConfig.interval_time"></t-input>
              </t-form-item>
            </div>
          </div>
          <div>
            <div class="input-with-unit">
              <t-form-item name="healthCheckConfig.timeout" label="超时时间(秒)">
                <t-input style="width: 120px !important;" v-model.number="conf.healthCheckConfig.timeout"></t-input>
              </t-form-item>
            </div>
          </div>
        </div>



        <t-form-item name="healthCheckConfig.protocol" label="检测协议">
          <t-select v-model="conf.healthCheckConfig.protocol" style="width:100%;">
            <t-option value="http" label="HTTP"></t-option>
            <t-option value="https" label="HTTPS"></t-option>
          </t-select>
        </t-form-item>

        <!-- 第二行：探测路径 -->
        <div class="health-path-row">
          <t-form-item name="healthCheckConfig.path">
            <template #label>
              <div style="display:inline-flex">
                探测路径
                <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    长度限制为1-80个字符，<br/>只能使用字母、数字、'-'、'/'、'%'、'.'、'#'、'&'、'='这些字符
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming"/>
                  </svg>
                </el-tooltip>
              </div>
            </template>
            <t-input type="text" v-model="conf.healthCheckConfig.path" style="width:100%;"
                     placeholder="如：/path"></t-input>
          </t-form-item>
        </div>

        <!-- 第三行：高级配置 -->
        <div class="health-advanced-grid">
          <div>
            <div class="input-with-unit">
              <t-form-item name="healthCheckConfig.health_intervals" label="健康间隔">
                <t-input type="text" style="width: 120px !important;" v-model.number="conf.healthCheckConfig.health_intervals"></t-input>
                <span>秒</span>
              </t-form-item>
            </div>
          </div>
          <div>
            <div class="input-with-unit">
              <t-form-item label="" name="healthCheckConfig.success_num">
                <span>成功</span>
                <t-input type="text"  style="width: 120px !important;" v-model.number="conf.healthCheckConfig.success_num"></t-input>
                <span>次</span>
              </t-form-item>
            </div>
          </div>
        </div>

        <div class="health-advanced-grid">
          <div>
            <div class="input-with-unit">
              <t-form-item name="healthCheckConfig.un_health_intervals" label="异常间隔">
                <t-input type="text"  style="width: 120px !important;" v-model.number="conf.healthCheckConfig.un_health_intervals"></t-input>
                <span>秒</span>
              </t-form-item>
            </div>
          </div>
          <div>
            <div class="input-with-unit">
              <t-form-item label="" name="healthCheckConfig.fail_nums">
                <span>失败</span>
                <t-input type="text"  style="width: 120px !important;" v-model.number="conf.healthCheckConfig.fail_nums"></t-input>
                <span>次</span>
              </t-form-item>
            </div>
          </div>
        </div>

        <!-- 第四行：响应状态码 -->
        <div class="health-status-row">
          <t-form-item name="healthCheckConfig.health_code">
            <template #label>
              <div style="display:inline-flex">
                响应状态码
                <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
                  <template #content>
                    当请求成功且返回指定状态码时认为该后端服务器状态正常
                  </template>
                  <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
                    <use xlink:href="#icon-shuoming"/>
                  </svg>
                </el-tooltip>
              </div>
            </template>
            <t-tag-input v-model="conf.healthCheckConfig.health_code"
                         @change="(val) => { conf.healthCheckConfig.health_code = convertToNumberArray(val) }"
                         clearable placeholder="按回车输入多个状态吗"/>

          </t-form-item>
        </div>
      </div>
    </t-form>
    <!--    <t-form-item name="account">-->
    <!--      <t-checkbox v-model="conf.allowSelfApply">允许用户自助申请此应用</t-checkbox>-->
    <!--    </t-form-item>-->


    <!--    <div id="self-apply-config" v-if="conf.allowSelfApply" style="background:#fafbfc;border-radius:8px;padding:18px 24px 8px 24px;margin-bottom:18px;box-shadow:0 1px 4px rgba(0,0,0,0.03);">-->
    <!--      <div id="self-apply-content">-->
    <!--        <div class="form-row">-->
    <!--          <div style="font-weight:500;margin-bottom:6px;">应用权限审批人</div>-->
    <!--          <div style="display:flex;align-items:center;gap:18px;margin-bottom:8px;">-->
    <!--            <label><input type="radio" name="approver-type" checked onchange="toggleApproverType('person')"> 指定人员</label>-->
    <!--            <label><input type="radio" name="approver-type" onchange="toggleApproverType('role')"> 部门角色</label>-->
    <!--          </div>-->
    <!--          <div id="approver-person-row" style="margin-bottom:8px;">-->
    <!--            <input type="text" placeholder="请选择" style="width:220px;">-->
    <!--            <button type="button" class="edit-btn">编辑</button>-->
    <!--          </div>-->
    <!--          <div id="approver-role-row" style="display:none;margin-bottom:8px;">-->
    <!--            <input type="text" placeholder="请选择部门角色" style="width:220px;">-->
    <!--            <button type="button" class="edit-btn">编辑</button>-->
    <!--          </div>-->
    <!--        </div>-->
    <!--        <div class="form-row">-->
    <!--          <div style="font-weight:500;margin-bottom:6px;"><span class="required">*</span>授权策略 </div>-->
    <!--          <div style="display:flex;align-items:center;gap:18px;margin-bottom:8px;">-->
    <!--            <label><input type="radio" name="policy-type" checked onchange="togglePolicyType('add')"> 添加到已有内网访问策略</label>-->
    <!--            <label><input type="radio" name="policy-type" onchange="togglePolicyType('auto')"> 自动创建内网访问策略</label>-->
    <!--          </div>-->
    <!--          <div id="policy-add-row" style="margin-bottom:8px;">-->
    <!--            <input type="text" placeholder="请选择或输入策略" style="width:220px;">-->
    <!--          </div>-->
    <!--          <div id="policy-auto-row" style="display:none;margin-bottom:8px;">-->
    <!--            <input type="text" placeholder="自动创建策略名称" style="width:220px;">-->
    <!--          </div>-->
    <!--        </div>-->
    <!--        <div class="form-row">-->
    <!--          <div style="font-weight:500;margin-bottom:6px;">申请通知方式</div>-->
    <!--          <label style="margin-right:18px;"><input type="checkbox" checked> 邮件</label>-->
    <!--          <label><input type="checkbox" checked> IM消息</label>-->
    <!--          <span style="color:#888;font-size:13px;margin-left:12px;">如需配置IM渠道，请<a href="#" style="color:#2563eb;">点击前往</a></span>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->

    <!-- 应用bypass -->
    <t-form-item name="bypassSwitch" v-if="appType === 'web'">
      <t-checkbox v-model="conf.bypassSwitch">应用bypass
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            启用后，当认证服务异常时，允许降级访问应用
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming"/>
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>

    <!-- URL路径白名单 -->
    <t-form-item name="whitelistSwitch" v-if="appType === 'web' ">
      <t-checkbox v-model="conf.whitelistSwitch" @change="onWhitelistChange">URL路径白名单
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            配置允许访问的URL路径，支持通配符匹配
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming"/>
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>

    <div id="url-white-textarea" style="margin-top:6px;" v-if="conf.whitelistSwitch">
                <textarea
                    v-model="conf.urlWhiteList"
                    style="width:100%;height:100px;resize:vertical;color:#374151;background:#ffffff;border:1px solid #d1d5db;border-radius:6px;padding:12px;font-size:12px;"
                    placeholder="请输入站点地址，一行一个，支持通配符*？
路径地址需在应用设置>访问地址基础上进行配置
示例：*/Mail*"
                ></textarea>
    </div>

    <!-- URL路径黑名单 -->
    <t-form-item name="blacklistSwitch" v-if="appType === 'web' ">
      <t-checkbox v-model="conf.blacklistSwitch" @change="onBlacklistChange">URL路径黑名单
        <el-tooltip effect="light" placement="right-start" popper-class="popper-shadow">
          <template #content>
            配置禁止访问的URL路径，支持通配符匹配
          </template>
          <svg aria-hidden="true" class="icon" style="height: 14px;width: 14px;margin-left: 5px;color: #347ACB">
            <use xlink:href="#icon-shuoming"/>
          </svg>
        </el-tooltip>
      </t-checkbox>
    </t-form-item>

    <div id="url-black-textarea" style="margin-top:6px;" v-if="conf.blacklistSwitch">
                <textarea
                    v-model="conf.urlBlackList"
                    style="width:100%;height:100px;resize:vertical;color:#374151;background:#ffffff;border:1px solid #d1d5db;border-radius:6px;padding:12px;font-size:12px;"
                    placeholder="请输入禁止访问的站点地址，一行一个，支持通配符*？
路径地址需在应用设置>访问地址基础上进行配置
示例：*/admin*"
                ></textarea>
    </div>


  </div>
</template>

<script>
export default {
  name: 'AdvancedConf'
}
</script>
<script setup>
import {ref, watch, onMounted, reactive} from 'vue'
import _ from 'lodash'

const props = defineProps({
  apiData: {
    type: Object,
    required: false,
    default: {}
  },
  appType: {
    type: String,
    required: true,
    default: 'tun'
  },
})

const convertToNumberArray = (values) => {
  if (!Array.isArray(values)) return [];
  return values.map(val => {
    const num = Number(val);
    return isNaN(num) ? val : num;
  }).filter(val => typeof val === 'number' && !isNaN(val));
};

// 白名单和黑名单互斥逻辑
const onWhitelistChange = (checked) => {
  if (checked) {
    conf.value.blacklistSwitch = false;
    conf.value.urlBlackList = "";
  }
}

const onBlacklistChange = (checked) => {
  if (checked) {
    conf.value.whitelistSwitch = false;
    conf.value.urlWhiteList = "";
  }
}

const conf = ref(
    {
      healthCheck: false,
      bypassSwitch: false,
      whitelistSwitch: false,
      blacklistSwitch: false,
      urlWhiteList: "",
      urlBlackList: "",
      healthCheckConfig: {
        protocol: 'https',//协议
        timeout:5, //超时时间
        path: '/index.html', //探测路径
        interval_time: 15, //检测频率
        health_intervals: 5, //健康间隔
        health_code: [200], //相应状态码
        success_num: 3, //异常次数
        un_health_intervals:5,
        fail_nums:3
      }
    }
)
const rePath = (val) =>
    new Promise((resolve) => {
      const timer = setTimeout(() => {
        let flag = true
        if (val.length < 1 || val.length > 80 || !/^[a-zA-Z0-9\-_\/\%\.\#\&\=]+$/.test(val)) {
          flag = false
        }
        resolve(flag);
        clearTimeout(timer);
      });
    });

const requireNum = (val) =>
    new Promise((resolve) => {
      const timer = setTimeout(() => {
        let flag = true
        if (val === "" ||  val < 1 || !Number.isInteger(val)) {
          flag = false
        }
        resolve(flag);
        clearTimeout(timer);
      });
    });


const rules = {
  'healthCheckConfig.interval_time': [
    {required: true, message: '探测间隔必填！', trigger: 'blur'},
    {number: true, message: '探测间隔必须为数字值', type: 'error', trigger: 'blur'},
  ],
  'healthCheckConfig.protocol': [
    {required: true, message: '检测协议必填！', trigger: 'change'}
  ],
  'healthCheckConfig.path': [
    {required: true, message: '探测路径必填！', trigger: 'blur'},
    {
      validator: rePath,
      message: '长度限制为1-80个字符，只能使用字母、数字、\'-\'、\'/\'、\'%\'、\'.\'、\'#\'、\'&\'、\'=\'这些字符'
    },
  ],
  'healthCheckConfig.timeout': [
    {required: true, message: '请填入一个正整数', trigger: 'blur'},
    {
      validator: requireNum,
      trigger: 'blur',
      message: '请填入一个正整数'
    },
  ],
  'healthCheckConfig.health_intervals': [
    {required: true, message: '请填入一个正整数', trigger: 'blur'},
    {
      validator: requireNum,
      trigger: 'blur',
      message: '请填入一个正整数'
    },
  ],
  'healthCheckConfig.success_num': [
    {
      validator: requireNum,
      trigger: 'blur',
      message: '请填入一个正整数'
    },
  ],
  'healthCheckConfig.un_health_intervals': [
    {required: true, message: '请填入一个正整数', trigger: 'blur'},
    {
      validator: requireNum,
      trigger: 'blur',
      message: '请填入一个正整数'
    },
  ],
  'healthCheckConfig.fail_nums': [
    {
      validator: requireNum,
      trigger: 'blur',
      message: '请填入一个正整数'
    },
  ],
  'healthCheckConfig.health_code': [
    {required: true, message: '响应状态码必填！', trigger: 'blur'}
  ],
};


const advance = ref()
const validate = async () => {
  let validateR = true
  if (conf.value.healthCheck) {
    const formInstance = advance.value
    if (!formInstance) return false
    try {
      const r = await formInstance.validate()
      if (r !== true) {
        return false
      }
    } catch (error) {
      console.error('校验异常:', error)
      return false
    }
  }
  return validateR
}


watch(
    () => props.apiData,
    (newVal) => {
      if (newVal && newVal.id !== undefined &&
          newVal.app_type !== 'portal' &&
          newVal.id !== null && newVal.id !== '') {
        const apiDataCopy = _.cloneDeep(newVal)
        
        // 应用bypass - 检查default_rule中是否包含bypass
        conf.value.bypassSwitch = apiDataCopy.web_compatible_config.default_rule.includes('bypass')
        
        // URL路径白名单 - 检查default_rule中是否包含url_control，且类型为free_auth
        const hasUrlControl = apiDataCopy.web_compatible_config.default_rule.includes('url_control')
        const urlControlType = apiDataCopy.web_compatible_config.url_control?.type || ''
        
        conf.value.whitelistSwitch = hasUrlControl && urlControlType === 'free_auth'
        conf.value.urlWhiteList = (hasUrlControl && urlControlType === 'free_auth') ? 
          (apiDataCopy.web_compatible_config.url_control.text || '') : ''
        
        // URL路径黑名单 - 检查default_rule中是否包含url_control，且类型为forbid_access  
        conf.value.blacklistSwitch = hasUrlControl && urlControlType === 'forbid_access'
        conf.value.urlBlackList = (hasUrlControl && urlControlType === 'forbid_access') ? 
          (apiDataCopy.web_compatible_config.url_control.text || '') : ''
        
        // 应用健康检测
        conf.value.healthCheck = apiDataCopy.health_config.enable == 1
        conf.value.healthCheckConfig = apiDataCopy.health_config.config
      }
    },
    {immediate: true, deep: true}
);
defineExpose({conf, validate})
</script>
<style scoped>
/* 标签样式 */
.advanced label {
  display: block;
  font-size: 12px;
  color: #374151;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 表单控件通用样式 */
.advanced input[type="text"],
.advanced input[type="url"],
.advanced select,
.advanced textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.2s ease;
  background: white;
  color: #1f2937;
}

.advanced input[type="text"]:focus,
.advanced input[type="url"]:focus,
.advanced select:focus,
.advanced textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #eff6ff;
  transform: translateY(-1px);
}

.advanced input[type="text"]:hover:not(:focus),
.advanced input[type="url"]:hover:not(:focus),
.advanced select:hover:not(:focus),
.advanced textarea:hover:not(:focus) {
  border-color: #9ca3af;
}

.advanced input[type="text"]::placeholder,
.advanced input[type="url"]::placeholder,
.advanced textarea::placeholder {
  color: #9ca3af;
}

/* 健康检测容器样式 */
.advanced .health-check-container {
  background: #f6f8fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 18px;
}

/* 基础健康检测布局网格 */
.advanced .health-basic-grid {
  display: grid;
  grid-template-columns: 110px 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
  align-items: end;
}

/* 高级配置行布局 */
.advanced .health-advanced-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
  align-items: end;
}

/* 探测路径行样式 */
.advanced .health-path-row {
  margin-bottom: 16px;
}

/* 响应状态码行样式 */
.advanced .health-status-row {
  margin-bottom: 0;
}

/* 输入框带单位样式 */
.advanced .input-with-unit {
  display: flex;
  align-items: center;
  gap: 8px;
}

.advanced .input-with-unit input {
  width: 100px !important;
}

.advanced .input-with-unit span {
  color: #6b7280;
  font-size: 12px;
  white-space: nowrap;
}

</style>
