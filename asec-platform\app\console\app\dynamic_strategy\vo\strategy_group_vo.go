package vo

type CreateGroupReq struct {
	GroupName string `json:"group_name" binding:"required"`
}

type DeleteGroupReq struct {
	GroupId      string `json:"group_id" binding:"required"`
	DeleteInside bool   `json:"delete_inside"`
}

type UpdateGroupReq struct {
	GroupName string `json:"group_name" binding:"required"`
	GroupId   string `json:"group_id" binding:"required"`
}

type GroupListResp struct {
	GroupName     string          `json:"group_name"`
	ChildrenGroup []ChildrenGroup `json:"children_group"`
}

type ChildrenGroup struct {
	GroupId       string `json:"group_id"`
	GroupName     string `json:"group_name"`
	Priority      int32  `json:"priority"`
	StrategyCount int32  `json:"strategy_count"`
}

type GroupMoveReq struct {
	GroupId      string `json:"group_id" binding:"required"`
	DestPriority int32  `json:"dest_priority"`
	Where        string `json:"where" binding:"oneof=before after ''"`
}
