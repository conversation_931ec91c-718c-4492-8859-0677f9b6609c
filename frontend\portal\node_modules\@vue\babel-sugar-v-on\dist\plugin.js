"use strict";function _interopDefault(a){return a&&"object"==typeof a&&"default"in a?a["default"]:a}var camelCase=_interopDefault(require("camelcase")),syntaxJsx=_interopDefault(require("@babel/plugin-syntax-jsx"));const cachedCamelCase=(()=>{const a=Object.create(null);return b=>(a[b]||(a[b]=camelCase(b)),a[b])})(),equalCamel=(a,b)=>a===b||a===cachedCamelCase(b),keyModifiers=["ctrl","shift","alt","meta"],keyCodes={esc:27,tab:9,enter:13,space:32,up:38,left:37,right:39,down:40,delete:[8,46]},keyNames={esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:" ",up:["Up","ArrowUp"],left:["Left","ArrowLeft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete"]};function index(a){function b(a){return p.ifStatement(a,p.returnStatement(p.nullLiteral()))}function c(a,b=[]){return p.callExpression(a,b)}function d(a){return p.memberExpression(p.identifier("$event"),p.identifier(a))}function e(a){return p.unaryExpression("!",a)}function f(a,b){return p.binaryExpression("!==",a,b)}function g(a,b){return p.logicalExpression("&&",a,b)}function h(a,b){return p.logicalExpression("||",a,b)}function i(){return p.binaryExpression("in",p.stringLiteral("button"),p.identifier("$event"))}function j(a){return p.arrowFunctionExpression([p.identifier("$event")],p.blockStatement(a instanceof Array?a:[a]))}function k(a){const b=a.get("name");let c=p.isJSXNamespacedName(b)?`${b.get("namespace.name").node}:${b.get("name.name").node}`:b.get("name").node;camelCase(c);let d,e;if([c,...d]=c.split("_"),[c,e]=c.split(":"),!equalCamel(c,"v-on")||!e)return{isInvalid:!1};if(!p.isJSXExpressionContainer(a.get("value")))throw new Error("Only expression container is allowed on v-on directive.");const f=a.get("value.expression");return{expression:f.node,modifiers:d,event:e}}function l(a){let{modifiers:e,isInvalid:f,expression:g,event:i}=k(a);"click"===i&&e.includes("right")&&(e=e.filter(a=>"right"!==a),i="contextmenu"),"click"===i&&e.includes("middle")&&(i="mouseup");let l=!1;if(f)return;if(!e||0===e.length)return{event:i,expression:g,isNative:l};const n=[],o=[],r=[];for(const c of e)if(q[c]){const a=q[c]();o.push(p.isExpression(a)?p.expressionStatement(a):a),keyCodes[c]&&r.push(c)}else if("exact"===c)o.push(b(keyModifiers.filter(a=>!e.includes(a)).map(a=>d(a+"Key")).reduce((a,b)=>a?h(a,b):b)));else if("capture"===c||"once"===c||"passive"===c)continue;else"native"===c?l=!0:r.push(c);return(-1<e.indexOf("capture")&&(i="!"+i),-1<e.indexOf("once")&&(i="~"+i),-1<e.indexOf("passive")&&(i="&"+i),r.length&&n.push(m(r)),o.length&&n.push(...o),0===n.length)?{event:i,expression:g,isNative:l}:(n.push(p.returnStatement(c(g,[p.identifier("$event")]))),{event:i,expression:j(n),isNative:l})}function m(a){return b(a.map(n).reduce((a,b)=>g(a,b),e(i())))}function n(a){const b=parseInt(a,10);if(b)return f(d("keyCode"),p.numericLiteral(b));const c=keyCodes[a],e=keyNames[a];return p.callExpression(p.memberExpression(p.thisExpression(),p.identifier("_k")),[d("keyCode"),p.stringLiteral(`${a}`),c?Array.isArray(c)?p.arrayExpression(c.map(a=>p.numericLiteral(a))):p.numericLiteral(c):p.identifier("undefined"),d("key"),e?Array.isArray(e)?p.arrayExpression(e.map(a=>p.stringLiteral(a))):p.stringLiteral(`${e}`):p.identifier("undefined")])}function o(a,b,c,d){"~"!==a[0]&&"!"!==a[0]&&"&"!==a[0]?d.push(p.jSXAttribute(p.jSXIdentifier(`${c?"nativeOn":"on"}-${a}`),p.jSXExpressionContainer(b))):d.push(p.jSXSpreadAttribute(p.objectExpression([p.objectProperty(p.identifier("on"),p.objectExpression([p.objectProperty(p.stringLiteral(a),b)]))])))}const p=a.types,q={stop:()=>c(d("stopPropagation")),prevent:()=>c(d("preventDefault")),self:()=>b(f(d("target"),d("currentTarget"))),ctrl:()=>b(e(d("ctrlKey"))),shift:()=>b(e(d("shiftKey"))),alt:()=>b(e(d("altKey"))),meta:()=>b(e(d("metaKey"))),left:()=>b(g(i(),f(d("button"),p.numericLiteral(0)))),middle:()=>b(g(i(),f(d("button"),p.numericLiteral(1)))),right:()=>b(g(i(),f(d("button"),p.numericLiteral(2))))};return{inherits:syntaxJsx,visitor:{Program(a){a.traverse({JSXAttribute(a){const{event:b,expression:c,isNative:d}=l(a);b&&(a.remove(),o(b,c,d,a.parentPath.node.attributes))}})}}}}module.exports=index;
