package repository

import (
	v1 "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/common"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/constants"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	commonErr "asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	model2 "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/lib/pq"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"sort"
	"strconv"
	"strings"
	"time"
)

// public

type SensitiveStrategyRepositoryImpl interface {
	AllBuiltInTemplate(ctx context.Context) ([]model.SensitiveStrategyDB, error)
	GetSgList(ctx context.Context, inData common.QuerySensitiveStrategyReqData) (model2.Pagination, error)
	QueryAssocStrategy(ctx context.Context, id int) ([]model.SensitiveStrategyDB, error)
	Find(ctx context.Context, id string) (model.SensitiveStrategyDB, error)
	FindByNL(ctx context.Context, name string, level int, builtType int) (model.SensitiveStrategyDB, error)
	Add(ctx context.Context, elem common.SensitiveStrategyBaseData, db *gorm.DB) (model.DdrScore, error)
	Change(ctx context.Context, elem common.ChangeSensitiveStrategyReqData) error
	Delete(ctx context.Context, id string, db *gorm.DB) error
	QueryAssocRule(ctx context.Context, ids []string) ([]model.AlertRulePartialDB, error)
	StrategySummary(ctx context.Context) ([]common.StrategySummaryResp, error)
	FindFileTypeByCode(ctx context.Context, code []int64) ([]model.FileTypeDB, error)
	DelSensitiveStgList(ctx context.Context, ids []string) aerrors.AError
	UpdateEnableStgBatch(ctx context.Context, ids []string, Enable int) error
}

func NewSensitiveStrategyRepository() SensitiveStrategyRepositoryImpl {
	snowflakeIDMaker = snowflake.NewSnowFlake()
	return &sensitiveStrategyReposPriv{}
}

// private
type sensitiveStrategyReposPriv struct {
}

func (self *sensitiveStrategyReposPriv) DelSensitiveStgList(ctx context.Context, ids []string) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.ReturnWithError(err, commonErr.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		err = tx.Where("id in ?", ids).Delete(model.SensitiveStrategyDB{}).Error
		if err != nil {
			return err
		}
		err = tx.Where("indicator in ? AND indicator_type = 1 AND indicator_sub_type = 1", ids).
			Delete(model2.RiskScoreConfig{}).Error
		if err != nil {
			return err
		}
		for _, id := range ids {
			changeReq := conf_center.ConfChangeReq{
				ConfBizId:  id,
				ConfType:   "sensitive_strategy",
				Tx:         tx,
				RedisCli:   global.SysRedisClient,
				ChangeType: conf_center.DelConf}
			err = conf_center.ConfChange(changeReq)
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return aerrors.ReturnWithError(err, commonErr.OperateError)
	}
	return nil
}

var snowflakeIDMaker *snowflake.SnowFlake

func (self *sensitiveStrategyReposPriv) StrategySummary(ctx context.Context) ([]common.StrategySummaryResp, error) {
	var result []common.StrategySummaryResp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&model.SensitiveRuleTypeDB{}).Find(&result).Error
	if err != nil {
		return result, err
	}
	for k, v := range result {
		var tmp int64
		err = db.Model(&model.SensitiveStrategyDB{}).Where("category_id = ?", v.Id).Count(&tmp).Error
		if tmp > 0 {
			result[k].Count = tmp
		}
	}
	return result, nil
}

func (self *sensitiveStrategyReposPriv) GetSgList(ctx context.Context, req common.QuerySensitiveStrategyReqData) (retPage model2.Pagination, err error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model2.Pagination{}, err
	}
	_ = copier.Copy(&retPage, &req)
	if req.CategoryId != "" {
		db = db.Where("tb_sensitive_strategy.category_id = ?", req.CategoryId)
	}
	if req.BuiltIn != 0 {
		db = db.Where("tb_sensitive_strategy.built_in = ? AND tb_sensitive_category.built_in = ?", req.BuiltIn, req.BuiltIn)
	}
	if req.Search != "" {
		retPage.SearchColumns = []string{"tb_sensitive_strategy.rule_name"}
	}
	ruleRelationSql := `
		SELECT array (select unnest (array_agg(distinct jsonb_build_object('id',tb_alert_rule.id,'name',tb_alert_rule.name)) filter(where tb_alert_rule.id is not null))) AS rule,
		tb_sensitive_strategy.id as sensitive_id
		FROM tb_sensitive_strategy LEFT JOIN tb_alert_rule
		ON (tb_sensitive_strategy.id = any(tb_alert_rule.sensitive_ids) OR tb_sensitive_strategy.category_id = any(tb_alert_rule.sensitive_category))
		GROUP BY tb_sensitive_strategy.id
	`
	db = db.Model(model.SensitiveStrategyDB{}).Select("tb_sensitive_strategy.*,tb_sensitive_category.name as category_name," +
		"tb_sensitive_category.icon_code as category_icon,ruleT.rule AS assoc_rule,tb_sensitive_category.created_at as c_created_at").
		Joins("LEFT JOIN tb_sensitive_category on tb_sensitive_strategy.category_id = tb_sensitive_category.id").
		Joins(fmt.Sprintf("LEFT JOIN (%s) ruleT on ruleT.sensitive_id = tb_sensitive_strategy.id", ruleRelationSql)).
		Group("tb_sensitive_strategy.id,tb_sensitive_category.name,tb_sensitive_category.icon_code,ruleT.rule,tb_sensitive_category.created_at").
		Order("tb_sensitive_strategy.create_at,tb_sensitive_strategy.rule_name desc")
	var rsp []model.SensitiveStrategyList
	retPage, err = model2.Paginate(&rsp, &retPage, db)
	categoryStgMap := make(map[string][]model.SensitiveStrategy)
	categoryRsMap := make(map[string]model.GetSensitiveStrategyListRsp)
	var realSgList []model.SensitiveStrategy
	for _, v := range rsp {
		var tmp model.SensitiveStrategy
		copier.Copy(&tmp, &v)
		// json序列化
		var fileNameRule []model.FilenameRule
		err = json.Unmarshal(v.FileNameRule, &fileNameRule)
		if err != nil {
			global.SysLog.Sugar().Errorf("unmarshal filename rule failed. err=%v", err)
			return model2.Pagination{}, err
		}
		var contentRule []model.FilenameRule
		err = json.Unmarshal(v.ContentRule, &contentRule)
		if err != nil {
			global.SysLog.Sugar().Errorf("unmarshal content rule failed. err=%v", err)
			return model2.Pagination{}, err
		}
		var alertRule []model.AssocRule
		for _, data := range v.AssocRule.Elements {
			var tmpAlertRule model.AssocRule
			err = json.Unmarshal(data.Bytes, &tmpAlertRule)
			if err != nil {
				global.SysLog.Sugar().Errorf("unmarshal alert rule failed. err=%v", err)
				return model2.Pagination{}, err
			}
			alertRule = append(alertRule, tmpAlertRule)
		}
		tmp.AssocRule = alertRule
		tmp.ContentRule = contentRule
		tmp.FilenameRule = fileNameRule
		realSgList = append(realSgList, tmp)

		stgList := make([]model.SensitiveStrategy, 0)
		if v, ok := categoryStgMap[v.CategoryID]; ok {
			stgList = append(stgList, v...)
		}
		stgList = append(stgList, tmp)
		categoryStgMap[v.CategoryID] = stgList

		categoryRsMap[v.CategoryID] = model.GetSensitiveStrategyListRsp{
			CategoryId:            v.CategoryID,
			CategoryIcon:          v.CategoryIcon,
			Category:              v.CategoryName,
			CCreatedAt:            v.CCreatedAt,
			SensitiveStrategyList: make([]model.SensitiveStrategy, 0),
		}
	}
	res := make([]model.GetSensitiveStrategyListRsp, 0)
	for categoryId, stgList := range categoryStgMap {
		tmp := categoryRsMap[categoryId]
		tmp.SensitiveStrategyList = stgList
		sort.Slice(stgList, func(i, j int) bool {
			return stgList[i].CreateAt.Before(stgList[j].CreateAt)
		})
		res = append(res, tmp)
	}
	sort.Slice(res, func(i, j int) bool {
		if res[i].CCreatedAt == res[j].CCreatedAt {
			return strings.Compare(res[i].Category, res[j].Category) > 0
		}
		return res[i].CCreatedAt.Before(res[j].CCreatedAt)
	})
	retPage.Rows = res
	retPage.Limit = req.Limit
	return retPage, nil
}

func (self *sensitiveStrategyReposPriv) Find(ctx context.Context, id string) (model.SensitiveStrategyDB, error) {
	var result model.SensitiveStrategyDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&result).Where("id=?", id).Find(&result).Error
	if len(result.Id) == 0 {
		return result, errors.New("Not Found")
	}
	if err != nil {
		return result, err
	}
	return result, err
}

func (self *sensitiveStrategyReposPriv) FindByNL(ctx context.Context, name string, level int, builtType int) (model.SensitiveStrategyDB, error) {
	var result model.SensitiveStrategyDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&result).Where("rule_name=? and sensitive_level=? and built_in = ?", name, level, builtType).Find(&result).Error
	if err != nil {
		return result, err
	}
	return result, err
}

func (self *sensitiveStrategyReposPriv) Add(ctx context.Context, elem common.SensitiveStrategyBaseData, db *gorm.DB) (model.DdrScore, error) {
	//判断是否存在同命名规则
	data, err := self.FindByNL(ctx, elem.RuleName, elem.SensitiveLevel, constants.CustomBuiltInType)
	if err != nil {
		return model.DdrScore{}, err
	}

	if int(data.SensitiveLevel) == elem.SensitiveLevel {
		return model.DdrScore{}, errors.New("rules already exist")
	}

	if err != nil {
		return model.DdrScore{}, err
	}

	fileNameRuleJson, err := json.Marshal(elem.FileNameRule)
	if err != nil {
		return model.DdrScore{}, err
	}

	contentRuleJson, err := json.Marshal(elem.ContentRule)
	if err != nil {
		return model.DdrScore{}, err
	}

	id, err := snowflakeIDMaker.GetId()
	if err != nil {
		return model.DdrScore{}, err
	}

	var item model.SensitiveStrategyDB
	item = model.SensitiveStrategyDB{
		Id:                 strconv.FormatUint(id, 10),
		RuleName:           elem.RuleName,
		RuleDescription:    elem.RuleDescription,
		SensitiveLevel:     int16(elem.SensitiveLevel),
		CheckFileEncrypted: int16(elem.FileEncrypted),
		MaxFileSize:        int32(elem.MaxFileSize),
		MaxFileSizeUnit:    elem.MaxFileSizeUnit,
		MinFileSize:        int32(elem.MinFileSize),
		MinFileSizeUnit:    elem.MinFileSizeUnit,
		FileTypeCode:       elem.FileTypeCode,
		CheckFileSuffix:    int16(elem.CheckFileSuffix),
		Enable:             int16(elem.Enable),
		FileNameOperator:   elem.FileNameOperator,
		FileNameRule:       fileNameRuleJson,
		RuleOperator:       "or",
		BuiltIn:            2,
		CreateAt:           time.Now(),
		UpdateAt:           time.Now(),
		CategoryId:         elem.CategoryID,
		SourceId:           elem.SourceId,
		IdentifyWay:        elem.IdentifyWay,
		ContentRule:        contentRuleJson,
		ContentOperator:    elem.ContentOperator,
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		err2 := db.Create(&item).Error
		if err2 != nil {
			return err2
		}
		confReq, err2 := strategyToConfReq(item, elem.FileNameRule, elem.ContentRule, tx, conf_center.AddConf)
		if err2 != nil {
			return err2
		}
		err2 = conf_center.ConfChange(confReq)
		if err2 != nil {
			return err2
		}
		return nil
	})

	return model.DdrScore{Id: item.Id, SensitiveLevel: elem.SensitiveLevel}, err
}

func (self *sensitiveStrategyReposPriv) Delete(ctx context.Context, id string, db *gorm.DB) error {
	temp := model.SensitiveStrategyDB{}
	err := db.Model(model.SensitiveStrategyDB{}).Where("id = ?", id).Find(&temp).Error
	if err != nil {
		return err
	}
	//判断是否为内置元素
	if temp.BuiltIn == 1 {
		return errors.New("this element is a built-in element")
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		err2 := db.Where("id=?", id).Delete(&model.SensitiveStrategyDB{}, id).Error
		if err2 != nil {
			return err2
		}
		changeReq := conf_center.ConfChangeReq{
			ConfBizId:  id,
			ConfType:   "sensitive_strategy",
			Tx:         tx,
			RedisCli:   global.SysRedisClient,
			ChangeType: conf_center.DelConf}
		err2 = conf_center.ConfChange(changeReq)
		if err2 != nil {
			return err2
		}
		return nil
	})
	return err

}

func (self *sensitiveStrategyReposPriv) Change(ctx context.Context, elem common.ChangeSensitiveStrategyReqData) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	//判断是否同名
	data, err := self.FindByNL(ctx, elem.RuleName, int(elem.SensitiveLevel), constants.CustomBuiltInType)
	if err != nil {
		return err
	}
	if elem.Id != data.Id && int(data.SensitiveLevel) == elem.SensitiveLevel && data.BuiltIn == 2 {
		return errors.New("rules already exist")
	}

	temp := model.SensitiveStrategyDB{}
	err = db.Model(&temp).Where("id=?", elem.Id).Find(&temp).Error
	if err != nil {
		return err
	}
	if temp.BuiltIn == 1 {
		return errors.New("this element is a built-in element")
	}

	fileNameRuleJson, err := json.Marshal(elem.FileNameRule)
	if err != nil {
		return err
	}
	contentRuleJson, err := json.Marshal(elem.ContentRule)
	if err != nil {
		return err
	}

	var item model.SensitiveStrategyDB
	item = model.SensitiveStrategyDB{
		Id:                 elem.Id,
		RuleName:           elem.RuleName,
		RuleDescription:    elem.RuleDescription,
		SensitiveLevel:     int16(elem.SensitiveLevel),
		CheckFileEncrypted: int16(elem.FileEncrypted),
		MaxFileSize:        int32(elem.MaxFileSize),
		MaxFileSizeUnit:    elem.MaxFileSizeUnit,
		MinFileSize:        int32(elem.MinFileSize),
		MinFileSizeUnit:    elem.MinFileSizeUnit,
		FileTypeCode:       elem.FileTypeCode,
		CheckFileSuffix:    int16(elem.CheckFileSuffix),
		Enable:             int16(elem.Enable),
		FileNameOperator:   elem.FileNameOperator,
		FileNameRule:       fileNameRuleJson,
		RuleOperator:       "or",
		CategoryId:         elem.CategoryID,
		Category:           elem.Category,
		SourceId:           elem.SourceId,
		IdentifyWay:        elem.IdentifyWay,
		ContentRule:        contentRuleJson,
		ContentOperator:    elem.ContentOperator,
		BuiltIn:            CustomBuiltValue,
		UpdateAt:           time.Now(),
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		err2 := tx.Where("id = ?", item.Id).Select("rule_name", "sensitive_level", "rule_description", "enable", "check_file_suffix",
			"min_file_size", "min_file_size_unit", "max_file_size", "max_file_size_unit", "file_type_code", "check_file_encrypted", "rule_operator",
			"filename_operator", "content_operator", "update_at", "category_id", "filename_rule", "content_rule", "built_in", "source_id", "identify_way").
			Updates(&item).Error
		if err2 != nil {
			return err
		}
		changeReq, err2 := strategyToConfReq(item, elem.FileNameRule, elem.ContentRule, tx, conf_center.UpdateConf)
		if err2 != nil {
			return err2
		}
		err2 = conf_center.ConfChange(changeReq)
		if err2 != nil {
			return err2
		}
		return nil
	})
	return err
}

func strategyToConfReq(strategy model.SensitiveStrategyDB, fileNameRule []common.SensitiveStrategyFileNameRule, contentRule []common.SensitiveStrategyFileNameRule, db *gorm.DB, changeType conf_center.ConfChangeType) (conf_center.ConfChangeReq, error) {

	nameRulesPb := getSensitiveElementRule(fileNameRule)
	contentRulesPb := getSensitiveElementRule(contentRule)

	fileTypeCodes := utils.SliceMap(strategy.FileTypeCode, func(t1 int64) uint32 {
		return uint32(t1)
	})

	identifyWays := utils.SliceMap(strategy.IdentifyWay, func(t1 int32) uint32 {
		return uint32(t1)
	})
	sensitiveStrategyPb := v1.SensitiveStrategy{
		RuleId:             strategy.Id,
		RuleName:           strategy.RuleName,
		RulePriority:       0,
		SensitiveLevel:     uint32(strategy.SensitiveLevel),
		CheckFileEncrypted: uint32(strategy.CheckFileEncrypted),
		CheckFileSuffix:    uint32(strategy.CheckFileSuffix),
		MinFileSize:        strategy.MinFileSize,
		MaxFileSize:        strategy.MaxFileSize,
		MinFileSizeUnit:    strategy.MinFileSizeUnit,
		MaxFileSizeUnit:    strategy.MaxFileSizeUnit,
		FileTypeCode:       fileTypeCodes,
		RuleOperator:       strategy.RuleOperator,
		FilenameOperator:   strategy.FileNameOperator,
		FilenameRule:       nameRulesPb,
		ContentOperator:    strategy.ContentOperator,
		ContentRule:        contentRulesPb,
		CategoryId:         strategy.CategoryId,
		Category:           strategy.Category,
		IdentifyWay:        identifyWays,
		SourceId:           strategy.SourceId,
		Enable:             uint32(strategy.Enable),
	}
	marshal, err := proto.Marshal(&sensitiveStrategyPb)
	if err != nil {
		return conf_center.ConfChangeReq{}, err
	}
	changeReq := conf_center.ConfChangeReq{
		ConfBizId:       strategy.Id,
		ConfType:        "sensitive_strategy",
		ConfData:        marshal,
		ConfGranularity: 1,
		Tx:              db,
		RedisCli:        global.SysRedisClient,
		ChangeType:      changeType,
	}
	return changeReq, nil
}

func getSensitiveElementRule(rule []common.SensitiveStrategyFileNameRule) []*v1.SensitiveElementRule {
	var rulesPb []*v1.SensitiveElementRule
	if len(rule) <= 0 {
		return rulesPb
	}
	for _, r := range rule {
		codes := utils.SliceMap(r.SensitiveElementCode, func(t1 int) uint32 {
			return uint32(t1)
		})
		rPb := &v1.SensitiveElementRule{
			Count:                 int32(r.Count),
			Operator:              r.Operator,
			SensitiveElementCodes: codes,
		}
		rulesPb = append(rulesPb, rPb)
	}
	return rulesPb
}

func (self *sensitiveStrategyReposPriv) AllBuiltInTemplate(ctx context.Context) ([]model.SensitiveStrategyDB, error) {
	var result []model.SensitiveStrategyDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	err = db.Model(&result).Where("built_in=1").Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, err
}

func (self *sensitiveStrategyReposPriv) QueryAssocRule(ctx context.Context, ids []string) ([]model.AlertRulePartialDB, error) {
	var result []model.AlertRulePartialDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	err = db.Model(&model.AlertRulePartialDB{}).
		Where("sensitive_ids && ?", pq.StringArray{strings.Join(ids, ",")}).
		Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, err
}

func (self *sensitiveStrategyReposPriv) FindFileTypeByCode(ctx context.Context, code []int64) ([]model.FileTypeDB, error) {
	var result []model.FileTypeDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(&result).Select("*").Where("code in ?", code).Find(&result).Error
	return result, err
}

func (self *sensitiveStrategyReposPriv) QueryAssocStrategy(ctx context.Context, id int) ([]model.SensitiveStrategyDB, error) {
	var strategyDB []model.SensitiveStrategyDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	whereStmt := fmt.Sprintf("obj->'sensitive_element_codes' @> '[%d]'", id)
	err = db.Model(&model.SensitiveStrategyDB{}).
		Select("*").
		Joins("CROSS JOIN jsonb_array_elements(filename_rule) AS obj").
		Where(whereStmt).
		Find(&strategyDB).Error

	if err != nil {
		return nil, err
	}
	return strategyDB, err
}

func (self *sensitiveStrategyReposPriv) UpdateEnableStgBatch(ctx context.Context, ids []string, enable int) error {
	var res []model.SensitiveStrategyDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	err = db.Model(model.SensitiveStrategyDB{}).Where("id in ?", ids).Find(&res).Error
	if err != nil {
		return err
	}
	for i, _ := range res {
		res[i].Enable = int16(enable)
	}
	return db.Transaction(func(tx *gorm.DB) error {
		for _, item := range res {
			err := db.Where("id = ?", item.Id).Select("rule_name", "sensitive_level", "rule_description", "enable", "check_file_suffix",
				"min_file_size", "min_file_size_unit", "max_file_size", "max_file_size_unit", "file_type_code", "check_file_encrypted", "rule_operator",
				"filename_operator", "content_operator", "update_at", "category_id", "filename_rule", "content_rule", "built_in", "source_id", "identify_way").
				Updates(&item).Error
			if err != nil {
				return err
			}
			var fileNameRule []common.SensitiveStrategyFileNameRule
			err = json.Unmarshal(item.FileNameRule, &fileNameRule)
			if err != nil {
				return err
			}

			var contentRule []common.SensitiveStrategyFileNameRule
			err = json.Unmarshal(item.ContentRule, &contentRule)
			if err != nil {
				return err
			}
			changeReq, err := strategyToConfReq(item, fileNameRule, contentRule, db, conf_center.UpdateConf)
			if err != nil {
				return err
			}
			err = conf_center.ConfChange(changeReq)
			if err != nil {
				return err
			}
		}
		return nil
	})

}
