UPDATE tb_application set health_config = '{"config": {"path": "/index.html", "timeout": 3, "protocol": "https", "fail_nums": 3, "health_code": [200], "success_num": 3, "interval_time": 15, "health_intervals": 5, "un_health_intervals": 5}, "enable": "0"}';

update tb_application set open_config = '{"enabled": false, "open_type": "browser", "supported_os": ["windows", "macos", "linux"], "per_os_config": false, "browser_configs": [{"type": "Default", "params": ""}], "program_configs": [{"os": "windows", "name": "", "path": "", "params": "", "bundleId": "", "showMoreConfig": false, "notFoundMessage": ""}], "not_found_message": "", "show_not_found_tip": false, "system_app_configs": [{"os": "windows", "type": "remote_desktop"}]}'