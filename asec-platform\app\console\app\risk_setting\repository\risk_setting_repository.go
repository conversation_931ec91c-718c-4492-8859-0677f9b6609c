package repository

import (
	"asdsec.com/asec/platform/app/console/app/risk_setting/constants"
	"asdsec.com/asec/platform/app/console/app/risk_setting/model"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"sort"
	"strconv"
	"strings"
)

type RiskSettingRepository interface {
	GetRiskSettingTotal(ctx context.Context) ([]model.GetRiskSettingTotalRsp, error)
	GetRiskSettingList(ctx context.Context) (model.GetRiskSettingListResp, error)
	UpdateRiskSetting(ctx context.Context, req *model.UpdateRiskScoreSettingReq) error
	CascadeCreatRiskSetting(ctx context.Context, indicator string, indicatorType string, defaultScore int, corpId string, db *gorm.DB) error
	CascadeDeleteRiskSetting(ctx context.Context, indicatorType string, indicator string, db *gorm.DB) error
}

// NewAppRepository 创建接口实现接口实现
func NewAppRepository() RiskSettingRepository {
	return &riskSettingRepository{}
}

type riskSettingRepository struct {
}

func (r *riskSettingRepository) CascadeDeleteRiskSetting(ctx context.Context, indicatorType string, indicator string, db *gorm.DB) error {
	strList := strings.Split(indicatorType, "_")
	indicatorTypeInt, err := strconv.Atoi(strList[0])
	if err != nil {
		return err
	}
	indicatorSubTypeInt, err := strconv.Atoi(strList[1])
	if err != nil {
		return err
	}
	req := modelTable.RiskScoreConfig{Indicator: indicator, IndicatorType: indicatorTypeInt, IndicatorSubType: indicatorSubTypeInt}
	err = db.Model(modelTable.RiskScoreConfig{}).
		Where("indicator = ? and indicator_type = ? and indicator_sub_type = ?", indicator, indicatorTypeInt, indicatorSubTypeInt).
		Delete(&req).Error
	return err
}

func (r *riskSettingRepository) CascadeCreatRiskSetting(ctx context.Context, indicator string, indicatorType string, defaultScore int, corpId string, db *gorm.DB) (err error) {
	req := modelTable.RiskScoreConfig{Score: defaultScore, CorpId: corpId, Indicator: indicator}
	strList := strings.Split(indicatorType, "_")
	req.IndicatorType, err = strconv.Atoi(strList[0])
	if err != nil {
		return err
	}
	req.IndicatorSubType, err = strconv.Atoi(strList[1])
	if err != nil {
		return err
	}
	switch indicatorType {
	case constants.IdentifyScoreIndicator:
		req.TypeName = constants.IdentifyIndicatorTypeName
	case constants.DataScoreIndicator:
		req.TypeName = constants.DataScoreIndicatorTypeName
	case constants.SendEventScoreIndicator:
		req.TypeName = constants.SendIndicatorTypeName
	default:
		global.SysLog.Error("不支持的级联新增类型", zap.Error(err))
	}
	err = db.Model(modelTable.RiskScoreConfig{}).Create(&req).Error
	return
}

func (r *riskSettingRepository) GetRiskSettingTotal(ctx context.Context) ([]model.GetRiskSettingTotalRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	var riskSettingTotal []model.GetRiskSettingTotalRsp
	err = db.Model(modelTable.RiskLevelConfig{}).Find(&riskSettingTotal).Error
	return riskSettingTotal, err
}

func (r *riskSettingRepository) GetRiskSettingList(ctx context.Context) (model.GetRiskSettingListResp, error) {
	var res model.GetRiskSettingListResp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return res, err
	}

	var riskSettingTotal []modelTable.RiskScoreConfig
	err = db.Model(modelTable.RiskScoreConfig{}).
		Group("id,indicator_type,indicator_sub_type").
		Order("indicator_sub_type DESC").
		Find(&riskSettingTotal).Error
	if err != nil {
		global.SysLog.Sugar().Errorln("get risk setting list failed. err=%v", err)
		return model.GetRiskSettingListResp{}, err
	}
	itemIdMap := make(map[string][]string)
	for _, v := range riskSettingTotal {
		key := fmt.Sprintf("%d_%d", v.IndicatorType, v.IndicatorSubType)
		idList := itemIdMap[key]

		idList = append(idList, v.Indicator)
		itemIdMap[key] = idList
	}

	// 获取子类型下的具体每一列的名称
	typeNameMap := make(map[string]string)
	for key, value := range itemIdMap {
		for _, itemId := range value {
			var nameStrList string
			nameStrList, err = GetLastLevelNameList(ctx, itemId, key)
			if err != nil {
				global.SysLog.Sugar().Errorln("get name failed. err=%v", err)
				return model.GetRiskSettingListResp{}, err
			}
			keyStr := fmt.Sprintf("%s_%s", key, itemId)
			typeNameMap[keyStr] = nameStrList
		}
	}

	// 将子类型的名称补充
	var dataScoreSettingList []model.RiskSetting
	var identifyScoreSettingList []model.RiskSetting
	var behaviorScoreSettingList []model.RiskSetting
	var behaviorDataHideSettingList []model.RiskSetting
	for _, v := range riskSettingTotal {
		var item model.RiskSetting
		copier.Copy(&item, &v)

		key := fmt.Sprintf("%d_%d", v.IndicatorType, v.IndicatorSubType)

		realKey := fmt.Sprintf("%s_%s", key, v.Indicator)

		typeName := typeNameMap[realKey]
		item.IndicatorGroupName = typeName
		if key == constants.DataScoreIndicator {
			item.IndicatorGroupName = strings.SplitN(typeName, "_", 2)[1]
			item.IndicatorLevel = strings.SplitN(typeName, "_", 2)[0]
			dataScoreSettingList = append(dataScoreSettingList, item)
		}
		if key == constants.IdentifyScoreIndicator {
			identifyScoreSettingList = append(identifyScoreSettingList, item)
		}
		if key == constants.SendEventScoreIndicator {
			behaviorScoreSettingList = append(behaviorScoreSettingList, item)
		}
		if key == constants.DataHideScoreIndicator {
			behaviorDataHideSettingList = append(behaviorDataHideSettingList, item)
		}
	}
	//根据score排序
	sort.Slice(behaviorScoreSettingList, func(i, j int) bool {
		return behaviorScoreSettingList[i].Score < behaviorScoreSettingList[j].Score
	})
	sort.Slice(dataScoreSettingList, func(i, j int) bool {
		return dataScoreSettingList[i].Score < dataScoreSettingList[j].Score
	})
	sort.Slice(identifyScoreSettingList, func(i, j int) bool {
		return identifyScoreSettingList[i].Score < identifyScoreSettingList[j].Score
	})
	sort.Slice(behaviorDataHideSettingList, func(i, j int) bool {
		return behaviorDataHideSettingList[i].Score < behaviorDataHideSettingList[j].Score
	})
	res.BehaviorScoreSetting = behaviorScoreSettingList
	res.DataScoreSetting = dataScoreSettingList
	res.IdentifyScoreSetting = identifyScoreSettingList
	res.BehaviorDataHideSettingList = behaviorDataHideSettingList
	return res, err
}

func (r *riskSettingRepository) UpdateRiskSetting(ctx context.Context, req *model.UpdateRiskScoreSettingReq) error {
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return pgDb.Model(modelTable.RiskScoreConfig{}).Where("id = ?", req.Id).Update("score", req.Score).Error
}

func GetLastLevelNameList(ctx context.Context, fatherId string, constId string) (name string, err error) {
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		return "", err
	}
	switch constId {
	case constants.DataScoreIndicator:
		var riskDataLevel model.RiskDataLevel
		pgDb.Table("tb_sensitive_strategy").Select("rule_name,sensitive_level").Where("id = ?", fatherId).Find(&riskDataLevel)
		name = fmt.Sprintf("%d_%s", riskDataLevel.SensitiveLevel, riskDataLevel.RuleName)
	case constants.IdentifyScoreIndicator:
		pgDb.Model(modelTable.UserTagConfig{}).Select("name").Where("id = ?", fatherId).Find(&name)
	case constants.SendEventScoreIndicator:
		pgDb.Model(modelTable.ChannelType{}).Select("channel_name").Where("channel = ?", fatherId).Find(&name)
	case constants.DataHideScoreIndicator:
		name = constants.DataHideMap[fatherId]
	default:
		global.SysLog.Error("不支持的配置类型", zap.Error(err))
	}
	return
}
