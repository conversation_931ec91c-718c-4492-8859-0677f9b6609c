export const detailscolumnsconfig = () => {
  return [
    {
      label: '证书名称：',
      value: 'name',
    }, {
      label: '绑定域名：',
      value: 'domain',
    }, {
      label: '加密算法：',
      value: 'signature_algorithm',
    }, {
      label: '颁发厂商：',
      value: 'issue_agency',
    }, {
      label: '证书指纹：',
      value: 'certificate_fingerprint',
    }, {
      label: '签发时间：',
      value: 'issue_time',
    }, {
      label: '到期时间：',
      value: 'expire_time',
    }]
}

export const tableColumnsConfig = () => {
  return [
    {
      colKey: 'serial-number',
      width: 50,
    },
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 46,
    },
    {
      colKey: 'name',
      title: '证书名称',
    },
    {
      colKey: 'domain',
      title: '绑定域名',
    },
    {
      colKey: 'expire_time',
      title: '过期时间',
    },
    {
      colKey: 'operation',
      title: '操作',
      width: '150',
      foot: '-',
    },
  ]
}

export const formColumnsConfig = () => {
  return [
    {
      label: '',
      name: '',
      type: 'info',
      placeholder: '数字证书为web应用（启用cname模式）提供数据HTTPS访问',
    },
    {
      label: '证书名称',
      name: 'name',
      type: 'input',
      len: 128,
      placeholder: '请输入证书名称',
    },
    {
      label: '证书',
      name: 'certificate',
      type: 'textarea',
      placeholder: '支持上传 .pem 和 .crt 格式的证书文件，上传后会自动解析填充到此处\n也可以直接在此处添加、编辑、删除证书文本内容',
      uploadConfig: {
        accept: '.pem,.crt',
        buttonText: '上传证书文件 (.pem, .crt)'
      }
    },
    {
      label: '证书私钥',
      name: 'private_key',
      type: 'textarea',
      placeholder: '支持上传 .pem 和 .key 格式的私钥文件，上传后会自动解析填充到此处\n也可以直接在此处添加、编辑、删除证书私钥文本内容',
      uploadConfig: {
        accept: '.pem,.key',
        buttonText: '上传私钥文件 (.pem, .key)'
      }
    }
  ]
}

export const formRules = () => {
  return {
    name: [
      {
        required: true,
        message: '证书名称必填',
        type: 'error',
        trigger: 'blur'
      },
      {
        required: true,
        message: '证书名称必填',
        type: 'error',
        trigger: 'change'
      },
      {
        whitespace: true, message: '证书名称不能为空'
      },
      { max: 128, message: '名称长度不能超过128个字符', type: 'error', trigger: 'blur' },
      { max: 128, message: '名称长度不能超过128个字符', type: 'error', trigger: 'change' },
    ],
    certificate: [
      {
        required: true,
        message: '证书必填',
        type: 'error',
        trigger: 'blur'
      },
      {
        required: true,
        message: '证书必填',
        type: 'error',
        trigger: 'change'
      },
    ],
    private_key: [
      {
        required: true,
        message: '证书私钥必填',
        type: 'error',
        trigger: 'blur'
      },
      {
        required: true,
        message: '证书私钥必填',
        type: 'error',
        trigger: 'change'
      },
    ]
  }
}
