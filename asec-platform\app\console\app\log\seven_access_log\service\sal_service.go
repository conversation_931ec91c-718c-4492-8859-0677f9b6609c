package service

import (
	"asdsec.com/asec/platform/app/console/app/log/seven_access_log/dto"
	"asdsec.com/asec/platform/app/console/app/log/seven_access_log/repository"
	"asdsec.com/asec/platform/pkg/aerrors"
	"context"
	"sync"
)

var SalServiceImpl SalService

var SalServiceInit sync.Once

type salService struct {
	db repository.SalRepository
}

func (s salService) GetSensitiveLogList(ctx context.Context, req dto.GetSensitiveLogListReq) (dto.GetSensitiveLogListRsp, error) {
	return s.db.GetSensitiveLogList(ctx, req)
}

func (s salService) GetSensitiveLogDetail(ctx context.Context, uuid string) (dto.SensitiveLogDetail, error) {
	return s.db.GetSensitiveLogDetail(ctx, uuid)
}

func (s salService) GetSevenAccessLogConf(ctx context.Context) ([]dto.SevenAccessLogConf, error) {
	return s.db.GetSevenAccessLogConf(ctx)
}

func (s salService) UpdateSevenAccessLogConf(ctx context.Context, req dto.UpdateSevenAccessLogConfReq) aerrors.AError {

	return s.db.UpdateSevenAccessLogConf(ctx, req)
}

func (s salService) GetSevenAccessLogList(ctx context.Context, req dto.GetSevenAccessLogListReq) (dto.GetSevenAccessLogListRsp, error) {
	return s.db.GetSevenAccessLogList(ctx, req)
}

func (s salService) GetSevenAccessLogDetail(ctx context.Context, uuid string) (dto.SevenAccessLogDetail, error) {
	return s.db.GetSevenAccessLogDetail(ctx, uuid)
}

type SalService interface {
	GetSevenAccessLogList(ctx context.Context, req dto.GetSevenAccessLogListReq) (dto.GetSevenAccessLogListRsp, error)
	GetSevenAccessLogDetail(ctx context.Context, uuid string) (dto.SevenAccessLogDetail, error)
	GetSevenAccessLogConf(ctx context.Context) ([]dto.SevenAccessLogConf, error)
	UpdateSevenAccessLogConf(ctx context.Context, req dto.UpdateSevenAccessLogConfReq) aerrors.AError

	GetSensitiveLogList(ctx context.Context, req dto.GetSensitiveLogListReq) (dto.GetSensitiveLogListRsp, error)
	GetSensitiveLogDetail(ctx context.Context, uuid string) (dto.SensitiveLogDetail, error)
}

func GetSalService() SalService {
	SalServiceInit.Do(func() {
		SalServiceImpl = salService{db: repository.NewSalRepository()}
	})
	return SalServiceImpl
}
