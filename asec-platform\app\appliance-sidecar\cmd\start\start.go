package start

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"syscall"
	"time"

	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/agent_conf"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/cmd_exec"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/events"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/info_collect"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/logbeat"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/module_switch"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/oss"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/scan_task"
	"gopkg.in/ini.v1"

	"go.uber.org/zap"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/access_log"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/enroll"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/heartbeat"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/se"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/virtualip_log"
	"asdsec.com/asec/platform/pkg/initialize"
	"asdsec.com/asec/platform/pkg/utils"
)

const fastRetryInterval = 1 * time.Second
const slowRetryInterval = 30 * time.Second
const fastRetryCount = 3

func InitLog() {
	//TODO 增加grpc zap writer，支持日志写入gRPC流发送到平台
	global.Conf.Zap.Director = filepath.Join(utils.GetConfigDir(), global.Conf.Zap.Director)
	global.Logger = initialize.InitLogger(global.Conf.Zap)

	zap.ReplaceGlobals(global.Logger)
}

// StartSidecar 启动sidecar
func StartSidecar(flagconf string, applianceType int32, installId uint64, wg *sync.WaitGroup) {
	_, ok := v1.ApplianceType_name[applianceType]
	if !ok {
		global.Logger.Sugar().Fatalf("非法的设备类型(ApplianceType: %d)", applianceType)
		return
	}
	global.ApplianceType = v1.ApplianceType(applianceType)

	initialize.InitConfig(flagconf, &global.Conf)
	InitLog()
	initialize.InitSidecar()
	//命令行传入的平台地址优先级高于配置文件地址
	if global.PrivateHost == "" {
		global.PrivateHost = global.Conf.Endpoints.PrivateHost
	}
	if global.InnerHost != "" {
		global.PrivateHost = global.InnerHost
	}
	if global.PublicHost != "" {
		global.Conf.Endpoints.PublicHost = global.PublicHost
	}
	if global.LogServerHost == "" {
		global.LogServerHost = global.Conf.Endpoints.LogCenterHost
	}
	global.Logger.Sugar().Infof("start sidecar with config: path:%s", flagconf)
	host := strings.Split(global.PrivateHost, ":")
	if global.PrivateHost == "" || len(host) <= 1 {
		// 如果没有传入平台地址并且本地没有配置则直接返回
		global.Logger.Sugar().Errorf("please input Correct plat address,current addr: %v", global.PrivateHost)
		return
	}
	global.PrivateIp = host[0]
	// 调用
	global.ApplianceID, _ = global.FromIDFile(global.IDFilePath, global.PrivateIp)

	reqApplianceId := global.ApplianceID
	// 连接主进程rpc通信，在注册之前
	if global.ApplianceType == v1.ApplianceType_AGENT {
		go common.HandSync()
	} else {
		//网关优先使用命令行参数带的ID作为接入请求的ID
		reqApplianceId = installId
		if reqApplianceId <= 0 {
			reqApplianceId = global.ApplianceID
		}
	}
	// 注册接入，分配ApplianceID
	DoGetApplianceId(reqApplianceId, 0)

	//释放tun-server调用的wg
	if global.ApplianceType == v1.ApplianceType_GATEWAY || global.ApplianceType == v1.ApplianceType_SECURITY_EDGE || global.ApplianceType == v1.ApplianceType_CONNECTOR {
		wg.Done()
	}

	bizSidecar()

	if sidecarConf, err := agent_conf.GetSidecarConf(); err != nil {
		global.Conf.ConfigPollInterval = sidecarConf.ConfigPollInterval
	}

	//避免其它地方遗漏,Cancel支持重复调用
	defer global.Cancel()
	if global.ApplianceType == v1.ApplianceType_AGENT {
		defer global.CloseSqlite(global.SqliteClient)
		defer global.CloseSqlite(global.EvidenceSqliteClient)
		defer global.CloseSqlite(global.OffsetSqliteClient)
		defer global.CloseSqlite(global.UserInfoSqliteClient)
		defer global.CloseSqlite(global.FileResultSqliteClient)
		defer global.CloseSqlite(global.TaskSqliteClient)
	}
}

func bizSidecar() {
	wg := sync.WaitGroup{}
	conf := global.Conf
	// 启动应用配置同步
	// windows 的 agent心跳上报移动到服务内
	if conf.Heartbeat.Enable {
		wg.Add(1)
		go heartbeat.StartHeartbeat(global.Context, []int32{int32(os.Getegid())}, &wg)
	}
	if global.ApplianceType == v1.ApplianceType_GATEWAY || global.ApplianceType == v1.ApplianceType_SECURITY_EDGE {
		wg.Add(1)
		go access_log.StartSendAccessLog(global.Context, &wg)
		wg.Add(1)
		go se.AppReaper(global.Context, &wg)
		wg.Add(1)
		go se.GetStrategy(global.Context, &wg)
		wg.Add(1)
		go oss.UploadWebFile(global.Context, &wg)
	}

	if global.ApplianceType == v1.ApplianceType_GATEWAY {
		wg.Add(1)
		go se.SyncPlatformHost(global.Context, &wg)
		time.Sleep(time.Second * 1)
		wg.Add(1)
		go se.SyncApisixApp(global.Context, &wg)
		wg.Add(1)
		go se.SyncApisixCrt(global.Context, &wg)
		wg.Add(1)
		go se.SyncWebWatermark(global.Context, &wg)
		wg.Add(1)
		go se.SyncHosts(global.Context, &wg)
		wg.Add(1)
		go se.SyncVirtualIPPools(global.Context, &wg)
		wg.Add(1)
		go virtualip_log.StartSendVirtualIPEvents(global.Context, &wg)
		wg.Add(1)
		go se.StartGatewayCommandService(global.Context, &wg)
		wg.Add(1)
		go se.StartSSOConfigSync(global.Context, &wg)
	}

	if global.ApplianceType == v1.ApplianceType_AGENT && runtime.GOOS == "windows" {
		// SPA配置监控已在main.go中的StartSPA()中启动，这里不需要重复调用

		// 启动应用配置同步
		//app_config_sync.Start()

		// 定时拉取模块开关并写入ini文件
		module_switch.StartPullModuleSwitchTimer()

		// 自动升级服务 - 暂时注释掉，升级服务暂时不迁移到sidecar
		// go upgrade.StartUpgradeService()
		go common.StartWatchServiceIni()
		go common.PollSwitch()
		// 终端信息采集上报
		wg.Add(1)
		go info_collect.SendCollectedInfo(global.Context, &wg)

		// 终端命令通道 sidecar备份
		wg.Add(1)
		go cmd_exec.PullCmdExec(global.Context, &wg)

		// 配置中心
		wg.Add(1)
		go agent_conf.ConfCenter(global.Context, &wg)

		// 等待配置读取
		time.Sleep(time.Second * 1)
		// 原始事件&告警发送到日志中心
		wg.Add(1)
		go events.SendEvents(global.Context, &wg)
		wg.Add(1)
		go events.SendDlps(global.Context, &wg)

		// 文件上传至oss
		wg.Add(1)
		go oss.UploadFile(global.Context, &wg)

		// 扫描任务最终结果发送到平台
		wg.Add(1)
		go scan_task.SendScanTask(global.Context, &wg)

		// 定期上报扫描任务的进度
		wg.Add(1)
		go scan_task.SendScanTaskAlways(global.Context, &wg)
	}

	if conf.Logbeat.Enable && global.ApplianceType == v1.ApplianceType_AGENT && runtime.GOOS == "windows" {
		//暂时不处理logbeat启动的err
		go func() {
			logbeatProc, _ := logbeat.StartLogBeat(global.Context)
			defer func(logbeatProc *os.Process, sig os.Signal) {
				// 启动报错或者拿不到进程句柄
				if logbeatProc == nil {
					return
				}
				err := logbeatProc.Signal(sig)
				if err != nil {
					global.Logger.Error("failed to send SIGTERM signal to log beat!", zap.Error(err))
				}
			}(logbeatProc, syscall.SIGTERM)
		}()
	}
	// TODO cl 添加优雅退出
	wg.Wait()
}

func GenAgentId(applianceType int32) uint64 {
	InitLog()
	_, ok := v1.ApplianceType_name[applianceType]
	if !ok {
		global.Logger.Sugar().Fatalf("非法的设备类型(ApplianceType: %d)", applianceType)
		return 0
	}
	global.ApplianceType = v1.ApplianceType(applianceType)
	host := strings.Split(global.PrivateHost, ":")
	if len(host) > 1 {
		global.PrivateIp = host[0]
	}
	// 调用
	global.ApplianceID, _ = global.FromIDFile(global.IDFilePath, global.PrivateIp)
	// 注册接入，分配ApplianceID
	DoGetApplianceId(global.ApplianceID, 3)

	// 将agent_id写入配置文件而不是返回到标准输出
	writeAgentIdToConfig(global.ApplianceID)

	return global.ApplianceID
}

// writeAgentIdToConfig 将agent_id写入配置文件
func writeAgentIdToConfig(agentId uint64) {
	if agentId == 0 {
		global.Logger.Sugar().Errorf("agent_id is 0, not writing to config")
		return
	}

	// 写入common_config.ini文件
	configPath := filepath.Join(utils.GetConfigDir(), "config", "common_config.ini")

	// 确保配置目录存在
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		global.Logger.Sugar().Errorf("failed to create config directory: %v", err)
		return
	}

	// 读取或创建配置文件
	cfg, err := ini.Load(configPath)
	if err != nil {
		// 如果文件不存在，创建新的配置
		cfg = ini.Empty()
	}

	// 写入agent_id到[common]节
	cfg.Section("common").Key("agent_id").SetValue(fmt.Sprintf("%d", agentId))

	// 保存配置文件
	if err := cfg.SaveTo(configPath); err != nil {
		global.Logger.Sugar().Errorf("failed to save agent_id to config: %v", err)
	} else {
		global.Logger.Sugar().Infof("agent_id %d written to config file: %s", agentId, configPath)
	}
}

func DoGetApplianceId(originApplianceId uint64, maxRetry uint32) {
	// 注册接入，分配ApplianceID
	retryTimes := 0
	retryInterval := fastRetryInterval
DistributeID:
	var err error
	// update 2024-06-13 每次启动sidecar都调用设备接入，并且将老的ID传入。平台如果存在当前ID则更新，否则创建
	global.ApplianceID, err = enroll.AppEnroll(global.Context, global.ApplianceType, originApplianceId)
	if err != nil || global.ApplianceID == 0 {
		global.Logger.Sugar().Errorf("get ApplianceID err:%v", err.Error())
		if retryTimes >= int(maxRetry) && maxRetry != 0 {
			return
		}
		//如果重试超过3次，并且当前终端ID不为0，则继续跳过注册的过程，优先保障终端功能的正常。否则进入慢重试过程
		if retryTimes >= fastRetryCount {
			retryInterval = slowRetryInterval
		}
		// 如果分配出错，并且本地之前没有ID文件，则无限制重新请求
		time.Sleep(retryInterval)
		retryTimes = retryTimes + 1
		goto DistributeID
	}
}
