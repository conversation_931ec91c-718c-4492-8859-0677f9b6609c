package api

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	"asdsec.com/asec/platform/app/console/app/notification/dto"
	"asdsec.com/asec/platform/app/console/app/notification/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strings"
)

// CreateNotificationTemplate godoc
// @Summary 创建notification template
// @Schemes
// @Description 创建终端弹窗提醒模板
// @Tags        Notification
// @Produce     application/json
// @Param       req body dto.NotificationReq true "创建"
// @Success     200
// @Router      /v1/agent_notification/template [POST]
// @success     200 {object} common.Response{} "ok"
func CreateNotificationTemplate(c *gin.Context) {
	var req dto.NotificationReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err = dto.ValidateType(req.NotificationType)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.FactorNotificationTYpe,
			OperationType:  common.OperateCreate,
			Representation: fmt.Sprintf("%s", req.Name),
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetNotificationService().CreateNotification(c, req)
	if err != nil {
		common.FailWithMessage(c, -1, "新增提醒模板失败")
		return
	}
	common.Ok(c)
}

// UpdateNotificationTemplate godoc
// @Summary 更新notification template
// @Schemes
// @Description 更新终端弹窗提醒模板
// @Tags        Notification
// @Produce     application/json
// @Param       req body dto.NotificationReq true "更新"
// @Success     200
// @Router      /v1/agent_notification/template [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateNotificationTemplate(c *gin.Context) {
	var req dto.NotificationReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err = dto.ValidateType(req.NotificationType)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.FactorNotificationTYpe,
			OperationType:  common.OperateUpdate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetNotificationService().UpdateNotification(c, req)
	if err != nil {
		global.SysLog.Error("update notification template failed", zap.Error(err))
		common.FailWithMessage(c, -1, "更新提醒模板失败")
		return
	}
	common.Ok(c)
}

// DeleteNotificationTemplate godoc
// @Summary 删除notification template
// @Schemes
// @Description 删除终端弹窗提醒模板
// @Tags        Notification
// @Produce     application/json
// @Param       id query string true "删除"
// @Success     200
// @Router      /v1/agent_notification/template [DELETE]
// @success     200 {object} common.Response{} "ok"
func DeleteNotificationTemplate(c *gin.Context) {
	idStr := c.Query("id")
	notification, err := service.GetNotificationService().GetNotificationDetail(c, idStr)
	if err != nil {
		common.FailWithMessage(c, -1, "获取提醒模板列表失败")
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.FactorNotificationTYpe,
			OperationType:  common.OperateDelete,
			Representation: fmt.Sprintf("%s", notification.Name),
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetNotificationService().DeleteNotification(c, idStr)
	if err != nil {
		common.FailWithMessage(c, -1, "获取提醒模板列表失败")
		return
	}
	common.Ok(c)
}

// BatchDeleteNotificationTemplate godoc
// @Summary 批量删除notification template
// @Schemes
// @Description 批量删除终端弹窗提醒模板
// @Tags        Notification
// @Produce     application/json
// @Param       id query string true "批量删除"
// @Success     200
// @Router      /v1/agent_notification/template/batch_del [POST]
// @success     200 {object} common.Response{} "ok"
func BatchDeleteNotificationTemplate(c *gin.Context) {
	req := vo.DelReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	representationVal := strings.Join(req.Names, ",")
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.FactorNotificationTYpe,
			OperationType:  common.OperateDelete,
			Representation: representationVal,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetNotificationService().BatchDeleteNotification(c, req)
	if err != nil {
		common.FailWithMessage(c, -1, "获取提醒模板列表失败")
		return
	}
	common.Ok(c)
}

// GetNotificationList godoc
// @Summary 获取notification列表
// @Schemes
// @Description 创建终端弹窗提醒模板
// @Tags        Notification
// @Produce     application/json
// @Param       req body dto.ListReq true "列表"
// @Success     200
// @Router      /v1/agent_notification/list [POST]
// @success     200 {object} common.Response{data=model.Pagination} "ok"
func GetNotificationList(c *gin.Context) {
	var req dto.ListReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetNotificationService().GetNotificationList(c, req)
	if err != nil {
		common.FailWithMessage(c, -1, "获取提醒模板列表失败")
		return
	}
	common.OkWithData(c, data)
}
