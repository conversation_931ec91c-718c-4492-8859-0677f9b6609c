{"name": "@vue/cli-plugin-babel", "version": "4.5.19", "description": "babel plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-babel"}, "keywords": ["vue", "cli", "babel"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-babel#readme", "dependencies": {"@babel/core": "^7.11.0", "@vue/babel-preset-app": "^4.5.19", "@vue/cli-shared-utils": "^4.5.19", "babel-loader": "^8.1.0", "cache-loader": "^4.1.0", "thread-loader": "^2.1.3", "webpack": "^4.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0"}, "devDependencies": {"jscodeshift": "^0.10.0"}, "publishConfig": {"access": "public"}, "gitHead": "bef7a67566585876d56fa0e41b364675467bba8f"}