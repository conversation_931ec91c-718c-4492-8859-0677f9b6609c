package se

import (
	"asdsec.com/asec/platform/pkg/biz/strategy_engine"
	"asdsec.com/asec/platform/pkg/biz/strategy_engine/custom_rego"
	"context"
	"sync"

	"go.uber.org/zap"

	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/service"
	"google.golang.org/grpc"
)

func GetStrategy(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   GetStrategySe,
		RunType:      common.SimpleSend,
		WaitSecond:   30,
		RandomOffset: 3,
	}
	custom_rego.RegisterBuiltin()
	common.Send(param)
}

func GetStrategySe(conn *grpc.ClientConn, ctx context.Context) error {
	// getV2()
	req := pb.WebAccessInfoReq{}
	resp, err := pb.NewAppClient(conn).WebAccessInfo(ctx, &req)
	global.Logger.Debug("seGetStrategy resp:", zap.Any("resp", resp))
	if err != nil {
		global.Logger.Sugar().Errorf("SeGetStrategy err %v", err)
		return err
	}
	service.AccessInfo = make([]strategy_engine.AccessModel, 0)
	// 置空
	for _, strategyInfo := range resp.StrategyInfo {
		accessModel, err := strategy_engine.TransStrategy(strategyInfo)
		if err != nil {
			global.Logger.Sugar().Errorf("trans accessModel err:%v,id:%v", err, strategyInfo.StrategyId)
			continue
		}
		service.AccessInfo = append(service.AccessInfo, accessModel)
	}
	// uci
	//service.UciInfo = make(map[string]strategy_engine.UciInfo)
	uciReq := pb.UciUserInfoReq{}
	uciResp, err := pb.NewAppClient(conn).UciUserInfo(ctx, &uciReq)
	global.Logger.Debug("seGetStrategy resp:", zap.Any("resp", resp))
	if err != nil {
		global.Logger.Sugar().Errorf("SeGetStrategy err %v", err)
		return err
	}
	for _, v := range uciResp.UciUserInfo {
		service.UciInfo.Store(v.UserId, strategy_engine.UciInfo{
			Score:     v.Score,
			RiskLevel: v.RiskLevel,
			UserId:    v.UserId,
		})
	}
	return nil
}
