package se

import (
	"strings"
	"testing"

	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/pkg/apisix"
	"asdsec.com/asec/platform/pkg/biz/dto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

func init() {
	// 初始化测试用的Logger
	global.Logger, _ = zap.NewDevelopment()
}

func TestGenerateSmartRewriteRules(t *testing.T) {
	tests := []struct {
		name           string
		req            dto.ApplicationGatewayDto
		expectedRules  int
		expectedHeader []dto.UpdateConfig
		expectedFilter []dto.FilterConfig
		description    string
	}{
		{
			name: "服务器地址无端口-HTTPS",
			req: dto.ApplicationGatewayDto{
				ServerSchema:   "https",
				ServerAddress:  "*************",
				PublishSchema:  "https",
				PublishAddress: "example.com:7443",
			},
			expectedRules: 8, // 无端口+默认端口+各自的编码版本+发布域名反向规则4个
			expectedHeader: []dto.UpdateConfig{
				{HeaderKey: "Location", HeaderOrigin: "https://*************", HeaderReplace: "https://example.com:7443"},
				{HeaderKey: "Location", HeaderOrigin: "https://*************:443", HeaderReplace: "https://example.com:7443"},
				// URL编码规则不在这里验证，因为顺序不同
			},
			expectedFilter: []dto.FilterConfig{
				{Options: apisix.ResponseRewriteFilterOption, Scope: apisix.ResponseRewriteFilterScope, Regex: "https://*************", Replace: "https://example.com:7443"},
				{Options: apisix.ResponseRewriteFilterOption, Scope: apisix.ResponseRewriteFilterScope, Regex: "https://*************:443", Replace: "https://example.com:7443"},
			},
			description: "服务器地址无端口时，应该生成无端口和默认端口的匹配规则，以及发布域名的反向规则",
		},
		{
			name: "服务器地址有端口-HTTPS",
			req: dto.ApplicationGatewayDto{
				ServerSchema:   "https",
				ServerAddress:  "*************:8080",
				PublishSchema:  "https",
				PublishAddress: "example.com:7443",
			},
			expectedRules: 6, // 服务器规则2个+编码2个+发布域名反向规则2个（因为7443是非标准端口）
			expectedHeader: []dto.UpdateConfig{
				{HeaderKey: "Location", HeaderOrigin: "https://*************:8080", HeaderReplace: "https://example.com:7443"},
			},
			expectedFilter: []dto.FilterConfig{
				{Options: apisix.ResponseRewriteFilterOption, Scope: apisix.ResponseRewriteFilterScope, Regex: "https://*************:8080", Replace: "https://example.com:7443"},
			},
			description: "服务器地址有端口时，应该只生成精确匹配规则，避免端口叠加",
		},
		{
			name: "服务器地址无端口-HTTP",
			req: dto.ApplicationGatewayDto{
				ServerSchema:   "http",
				ServerAddress:  "*************",
				PublishSchema:  "https",
				PublishAddress: "example.com:7443",
			},
			expectedRules: 8, // 无端口+默认端口+各自的编码版本+发布域名反向规则4个
			expectedHeader: []dto.UpdateConfig{
				{HeaderKey: "Location", HeaderOrigin: "http://*************", HeaderReplace: "https://example.com:7443"},
				{HeaderKey: "Location", HeaderOrigin: "http://*************:80", HeaderReplace: "https://example.com:7443"},
			},
			expectedFilter: []dto.FilterConfig{
				{Options: apisix.ResponseRewriteFilterOption, Scope: apisix.ResponseRewriteFilterScope, Regex: "http://*************", Replace: "https://example.com:7443"},
				{Options: apisix.ResponseRewriteFilterOption, Scope: apisix.ResponseRewriteFilterScope, Regex: "http://*************:80", Replace: "https://example.com:7443"},
			},
			description: "HTTP协议应该使用端口80作为默认端口，并处理发布域名反向规则",
		},
		{
			name: "端口叠加问题场景",
			req: dto.ApplicationGatewayDto{
				ServerSchema:   "https",
				ServerAddress:  "*************", // 无端口
				PublishSchema:  "https",
				PublishAddress: "example.com:7443",
			},
			expectedRules: 8,
			description:   "这种配置下，https://*************:2443 不应该被替换为 https://example.com:7443:2443",
		},
		{
			name: "发布地址带非标准端口的反向规则",
			req: dto.ApplicationGatewayDto{
				ServerSchema:   "https",
				ServerAddress:  "*************",
				PublishSchema:  "https",
				PublishAddress: "example.com:7443",
			},
			expectedRules: 8, // 服务器规则4个+发布域名反向规则4个
			expectedHeader: []dto.UpdateConfig{
				{HeaderKey: "Location", HeaderOrigin: "https://*************", HeaderReplace: "https://example.com:7443"},
				{HeaderKey: "Location", HeaderOrigin: "https://*************:443", HeaderReplace: "https://example.com:7443"},
				// 其他规则（编码和发布域名）不在这里验证
			},
			expectedFilter: []dto.FilterConfig{
				{Options: apisix.ResponseRewriteFilterOption, Scope: apisix.ResponseRewriteFilterScope, Regex: "https://*************", Replace: "https://example.com:7443"},
				{Options: apisix.ResponseRewriteFilterOption, Scope: apisix.ResponseRewriteFilterScope, Regex: "https://*************:443", Replace: "https://example.com:7443"},
				// 其他规则不在这里验证
			},
			description: "发布地址带有非标准端口时，应该生成反向规则处理发布域名的无端口和默认端口情况",
		},
		{
			name: "发布地址带标准端口无需反向规则",
			req: dto.ApplicationGatewayDto{
				ServerSchema:   "https",
				ServerAddress:  "*************",
				PublishSchema:  "https",
				PublishAddress: "example.com:443", // 标准端口
			},
			expectedRules: 4, // 只有服务器规则，无反向规则
			description:   "发布地址带有标准端口时，不需要生成反向规则",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			headerUpdates, filterConfigs := generateSmartRewriteRules(tt.req)

			// 验证规则数量
			assert.Equal(t, tt.expectedRules, len(headerUpdates), "头部更新规则数量不匹配")
			assert.Equal(t, tt.expectedRules, len(filterConfigs), "过滤器规则数量不匹配")

			// 验证关键规则内容
			if len(tt.expectedHeader) > 0 {
				for i, expected := range tt.expectedHeader {
					if i < len(headerUpdates) {
						assert.Equal(t, expected.HeaderKey, headerUpdates[i].HeaderKey, "头部键不匹配")
						assert.Equal(t, expected.HeaderOrigin, headerUpdates[i].HeaderOrigin, "头部原始值不匹配")
						assert.Equal(t, expected.HeaderReplace, headerUpdates[i].HeaderReplace, "头部替换值不匹配")
					}
				}
			}

			if len(tt.expectedFilter) > 0 {
				for i, expected := range tt.expectedFilter {
					if i < len(filterConfigs) {
						assert.Equal(t, expected.Options, filterConfigs[i].Options, "过滤器选项不匹配")
						assert.Equal(t, expected.Scope, filterConfigs[i].Scope, "过滤器作用域不匹配")
						assert.Equal(t, expected.Regex, filterConfigs[i].Regex, "过滤器正则表达式不匹配")
						assert.Equal(t, expected.Replace, filterConfigs[i].Replace, "过滤器替换值不匹配")
					}
				}
			}

			t.Logf("测试通过: %s", tt.description)
		})
	}
}

func TestGenerateSmartRewriteRulesPortLayering(t *testing.T) {
	// 测试端口叠加问题的具体场景
	req := dto.ApplicationGatewayDto{
		ServerSchema:   "https",
		ServerAddress:  "*************", // 无端口
		PublishSchema:  "https",
		PublishAddress: "example.com:7443",
	}

	headerUpdates, filterConfigs := generateSmartRewriteRules(req)

	// 验证生成的规则
	foundExactMatch := false
	foundDefaultPortMatch := false
	foundNonExactMatch := false

	for _, filter := range filterConfigs {
		switch filter.Regex {
		case "https://*************":
			foundExactMatch = true
			assert.Equal(t, "https://example.com:7443", filter.Replace, "无端口URL替换不正确")
		case "https://*************:443":
			foundDefaultPortMatch = true
			assert.Equal(t, "https://example.com:7443", filter.Replace, "默认端口URL替换不正确")
		case "https://*************:2443":
			foundNonExactMatch = true
			t.Errorf("不应该生成非精确匹配的端口规则: %s", filter.Regex)
		}
	}

	assert.True(t, foundExactMatch, "应该包含无端口的精确匹配规则")
	assert.True(t, foundDefaultPortMatch, "应该包含默认端口的匹配规则")
	assert.False(t, foundNonExactMatch, "不应该包含其他端口的匹配规则")

	t.Logf("端口叠加测试通过，生成了 %d 个头部规则和 %d 个过滤规则", len(headerUpdates), len(filterConfigs))
}

func TestGenerateManualRewriteRules(t *testing.T) {
	tests := []struct {
		name            string
		manualRewrites  []apisix.URLManualRewrite
		expectedRules   int
		expectedHeaders []dto.UpdateConfig
		expectedFilters []dto.FilterConfig
		description     string
	}{
		{
			name: "单个手动改写规则",
			manualRewrites: []apisix.URLManualRewrite{
				{Before: "http://old.example.com", After: "https://new.example.com"},
			},
			expectedRules: 2, // 头部规则2个，过滤器规则2个
			expectedHeaders: []dto.UpdateConfig{
				{HeaderKey: "Location", HeaderOrigin: "http://old.example.com", HeaderReplace: "https://new.example.com"},
			},
			expectedFilters: []dto.FilterConfig{
				{Options: apisix.ResponseRewriteFilterOption, Scope: apisix.ResponseRewriteFilterScope, Regex: "http://old.example.com", Replace: "https://new.example.com"},
			},
			description: "单个手动改写规则应该生成原始和编码版本的规则",
		},
		{
			name: "多个手动改写规则",
			manualRewrites: []apisix.URLManualRewrite{
				{Before: "http://old1.example.com", After: "https://new1.example.com"},
				{Before: "http://old2.example.com", After: "https://new2.example.com"},
			},
			expectedRules: 4, // 每个规则2个，共4个
			description:   "多个手动改写规则应该生成相应数量的规则",
		},
		{
			name: "空规则过滤",
			manualRewrites: []apisix.URLManualRewrite{
				{Before: "", After: "https://new.example.com"},
				{Before: "http://old.example.com", After: ""},
				{Before: "http://valid.example.com", After: "https://valid.example.com"},
			},
			expectedRules: 2, // 只有最后一个有效
			description:   "应该过滤掉空的before或after字段",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			headerUpdates, filterConfigs := generateManualRewriteRules(tt.manualRewrites)

			assert.Equal(t, tt.expectedRules, len(headerUpdates), "头部更新规则数量不匹配")
			assert.Equal(t, tt.expectedRules, len(filterConfigs), "过滤器规则数量不匹配")

			if len(tt.expectedHeaders) > 0 {
				for i, expected := range tt.expectedHeaders {
					if i < len(headerUpdates) {
						assert.Equal(t, expected.HeaderKey, headerUpdates[i].HeaderKey)
						assert.Equal(t, expected.HeaderOrigin, headerUpdates[i].HeaderOrigin)
						assert.Equal(t, expected.HeaderReplace, headerUpdates[i].HeaderReplace)
					}
				}
			}

			if len(tt.expectedFilters) > 0 {
				for i, expected := range tt.expectedFilters {
					if i < len(filterConfigs) {
						assert.Equal(t, expected.Options, filterConfigs[i].Options)
						assert.Equal(t, expected.Scope, filterConfigs[i].Scope)
						assert.Equal(t, expected.Regex, filterConfigs[i].Regex)
						assert.Equal(t, expected.Replace, filterConfigs[i].Replace)
					}
				}
			}

			t.Logf("测试通过: %s", tt.description)
		})
	}
}

// 集成测试：模拟真实的apisix插件配置生成
func TestIntegrationSmartRewriteInRouteConfig(t *testing.T) {
	req := dto.ApplicationGatewayDto{
		ServerSchema:   "https",
		ServerAddress:  "*************",
		PublishSchema:  "https",
		PublishAddress: "example.com:7443",
		AppName:        "test-app",
		Uri:            "/test/*",
	}

	// 这里直接测试生成的规则
	headerUpdates, filterConfigs := generateSmartRewriteRules(req)

	// 验证生成的规则能正确处理端口叠加问题
	require.NotEmpty(t, headerUpdates, "应该生成头部更新规则")
	require.NotEmpty(t, filterConfigs, "应该生成过滤器规则")

	// 验证规则的结构
	for _, header := range headerUpdates {
		assert.Equal(t, "Location", header.HeaderKey, "头部键应该是Location")
		assert.NotEmpty(t, header.HeaderOrigin, "头部原始值不应该为空")
		assert.NotEmpty(t, header.HeaderReplace, "头部替换值不应该为空")
		// 验证原始值包含服务器地址或发布地址
		containsServer := strings.Contains(header.HeaderOrigin, "*************")
		containsPublish := strings.Contains(header.HeaderOrigin, "example.com")
		assert.True(t, containsServer || containsPublish, "原始值应该包含服务器地址或发布地址")
		assert.Contains(t, header.HeaderReplace, "example.com:7443", "替换值应该包含发布地址")
	}

	for _, filter := range filterConfigs {
		assert.Equal(t, apisix.ResponseRewriteFilterOption, filter.Options, "过滤器选项应该是jo")
		assert.Equal(t, apisix.ResponseRewriteFilterScope, filter.Scope, "过滤器作用域应该是global")
		assert.NotEmpty(t, filter.Regex, "过滤器正则表达式不应该为空")
		assert.NotEmpty(t, filter.Replace, "过滤器替换值不应该为空")

		// 对于非URL编码的规则，检查包含服务器地址或发布地址
		if !strings.Contains(filter.Regex, "%") {
			containsServer := strings.Contains(filter.Regex, "*************")
			containsPublish := strings.Contains(filter.Regex, "example.com")
			assert.True(t, containsServer || containsPublish, "非编码正则表达式应该包含服务器地址或发布地址")
		}

		// 对于非URL编码的替换值，检查包含发布地址
		if !strings.Contains(filter.Replace, "%") {
			assert.Contains(t, filter.Replace, "example.com:7443", "非编码替换值应该包含发布地址")
		}
	}

	t.Logf("集成测试通过，生成了 %d 个头部规则和 %d 个过滤规则", len(headerUpdates), len(filterConfigs))
}

func TestGenerateSmartRewriteRulesPublishDomainHandling(t *testing.T) {
	// 测试发布域名端口补充的具体场景
	req := dto.ApplicationGatewayDto{
		ServerSchema:   "https",
		ServerAddress:  "*************", // 无端口
		PublishSchema:  "https",
		PublishAddress: "example.com:7443", // 非标准端口
	}

	headerUpdates, filterConfigs := generateSmartRewriteRules(req)

	// 验证生成的规则
	foundPublishNoPort := false
	foundPublishDefaultPort := false
	foundServerNoPort := false
	foundServerDefaultPort := false

	for _, filter := range filterConfigs {
		switch filter.Regex {
		case "https://*************":
			foundServerNoPort = true
			assert.Equal(t, "https://example.com:7443", filter.Replace, "服务器无端口URL替换不正确")
		case "https://*************:443":
			foundServerDefaultPort = true
			assert.Equal(t, "https://example.com:7443", filter.Replace, "服务器默认端口URL替换不正确")
		case "https://example.com":
			foundPublishNoPort = true
			assert.Equal(t, "https://example.com:7443", filter.Replace, "发布域名无端口URL补充不正确")
		case "https://example.com:443":
			foundPublishDefaultPort = true
			assert.Equal(t, "https://example.com:7443", filter.Replace, "发布域名默认端口URL替换不正确")
		}
	}

	assert.True(t, foundServerNoPort, "应该包含服务器无端口的匹配规则")
	assert.True(t, foundServerDefaultPort, "应该包含服务器默认端口的匹配规则")
	assert.True(t, foundPublishNoPort, "应该包含发布域名无端口的补充规则")
	assert.True(t, foundPublishDefaultPort, "应该包含发布域名默认端口的替换规则")

	t.Logf("发布域名处理测试通过，生成了 %d 个头部规则和 %d 个过滤规则", len(headerUpdates), len(filterConfigs))

	// 验证具体规则内容
	publishNoPortFound := false
	publishDefaultPortFound := false

	for _, header := range headerUpdates {
		if header.HeaderOrigin == "https://example.com" {
			publishNoPortFound = true
			assert.Equal(t, "https://example.com:7443", header.HeaderReplace, "发布域名无端口头部规则不正确")
		}
		if header.HeaderOrigin == "https://example.com:443" {
			publishDefaultPortFound = true
			assert.Equal(t, "https://example.com:7443", header.HeaderReplace, "发布域名默认端口头部规则不正确")
		}
	}

	assert.True(t, publishNoPortFound, "应该包含发布域名无端口的头部规则")
	assert.True(t, publishDefaultPortFound, "应该包含发布域名默认端口的头部规则")
}

// TestCalculateAppConfigHash 测试应用配置哈希计算
func TestCalculateAppConfigHash(t *testing.T) {
	// 创建测试数据
	appGateway1 := dto.ApplicationGatewayDto{
		AppName:        "test-app",
		ServerAddress:  "*************",
		ServerSchema:   "https",
		PublishAddress: "example.com",
		PublishSchema:  "https",
		Uri:            "/test/*",
	}

	webConf1 := apisix.CompatibleConfig{
		DefaultRule: []string{"smart_rewrite"},
		HeaderConfig: []apisix.HeaderConfig{
			{Field: "request", Operation: "add", Key: "X-Test", Value: "test"},
		},
	}

	healthConf1 := apisix.HealthConfig{
		Enable: "true",
		Config: &apisix.HealthConfigConfig{
			Path:              "/health",
			Timeout:           5,
			Protocol:          "http",
			HealthCode:        []int{200},
			HealthIntervals:   30,
			UnHealthIntervals: 10,
			SuccessNum:        2,
			FailNums:          3,
		},
	}

	// 计算第一次哈希
	hash1 := CalculateAppConfigHash(appGateway1, webConf1, healthConf1)
	assert.NotEmpty(t, hash1, "哈希值不应为空")

	// 相同配置应该产生相同哈希
	hash2 := CalculateAppConfigHash(appGateway1, webConf1, healthConf1)
	assert.Equal(t, hash1, hash2, "相同配置应该产生相同哈希")

	// 修改Web配置（这会影响哈希值）
	webConf2 := apisix.CompatibleConfig{
		DefaultRule: []string{"smart_rewrite", "cors"}, // 添加 cors 规则
		HeaderConfig: []apisix.HeaderConfig{
			{Field: "request", Operation: "add", Key: "X-Test", Value: "test"},
		},
	}
	hash3 := CalculateAppConfigHash(appGateway1, webConf2, healthConf1)
	assert.NotEqual(t, hash1, hash3, "不同Web配置应该产生不同哈希")

	// 修改Web配置（第二种方式）
	webConf3 := apisix.CompatibleConfig{
		DefaultRule: []string{"smart_rewrite", "cors", "cross_domain"}, // 添加更多规则
		HeaderConfig: []apisix.HeaderConfig{
			{Field: "request", Operation: "add", Key: "X-Test", Value: "test"},
		},
	}
	hash4 := CalculateAppConfigHash(appGateway1, webConf3, healthConf1)
	assert.NotEqual(t, hash1, hash4, "不同Web配置应该产生不同哈希")

	// 修改健康检查配置
	healthConf2 := healthConf1
	healthConf2.Config.Timeout = 10
	hash5 := CalculateAppConfigHash(appGateway1, webConf1, healthConf2)
	assert.NotEqual(t, hash1, hash5, "不同健康检查配置应该产生不同哈希")
}

// TestIsAppConfigChanged 测试配置变更检测
func TestIsAppConfigChanged(t *testing.T) {
	// 清空缓存
	appConfigCacheMutex.Lock()
	appConfigHashCache = make(map[string]string)
	appConfigCacheMutex.Unlock()

	appId := "test-app-123"
	hash1 := "hash1"
	hash2 := "hash2"

	// 第一次检查，应该返回true（缓存中没有）
	changed := IsAppConfigChanged(appId, hash1)
	assert.True(t, changed, "第一次检查应该返回true")

	// 模拟同步成功，更新缓存
	UpdateAppConfigHash(appId, hash1)

	// 相同哈希，应该返回false
	changed = IsAppConfigChanged(appId, hash1)
	assert.False(t, changed, "相同哈希应该返回false")

	// 不同哈希，应该返回true
	changed = IsAppConfigChanged(appId, hash2)
	assert.True(t, changed, "不同哈希应该返回true")

	// 模拟同步成功，更新缓存
	UpdateAppConfigHash(appId, hash2)

	// 再次检查相同哈希，应该返回false
	changed = IsAppConfigChanged(appId, hash2)
	assert.False(t, changed, "再次检查相同哈希应该返回false")
}
