package repository

import (
	v1 "asdsec.com/asec/platform/api/conf/v1"
	eventConstants "asdsec.com/asec/platform/app/console/app/events/constants"
	"asdsec.com/asec/platform/app/console/app/read_event/constants"
	"asdsec.com/asec/platform/app/console/app/read_event/dto"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"asdsec.com/asec/platform/pkg/model"
	readEvent "asdsec.com/asec/platform/pkg/model/read_event"
	"asdsec.com/asec/platform/pkg/utils"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/lib/pq"
	"gorm.io/gorm"
	"sort"
	"strings"
)

// EventFilterRepository 接口定义
type EventFilterRepository interface {
	GetReadEventCondition(ctx context.Context, req dto.GetReadEventCommonReq) (dto.GetReadEventConditionRsp, error)
	GetReadEventList(ctx context.Context, req dto.GetReadEventReq) (dto.GetReadEventListRsp, error)
	UpdateReadEventFilter(ctx context.Context, req dto.UpdateEventFilterReq) error
	GetReadEventFilter(ctx context.Context) (dto.EventFilterRsp, error)
	GetReadEventDetail(ctx context.Context, uuid string) (readEvent.FileEvents, error)
	GetReadEventProcessList(ctx context.Context, req dto.GetReadEventReq) (dto.GetProcessListRsp, error)
}

// NewEventFilterRepository 创建接口实现接口实现
func NewEventFilterRepository() EventFilterRepository {
	return &eventFilterRepository{}
}

type eventFilterRepository struct {
}

func (e eventFilterRepository) GetReadEventCondition(ctx context.Context, req dto.GetReadEventCommonReq) (dto.GetReadEventConditionRsp, error) {
	ckDb, err := getCommonCkDbByReq(ctx, req)
	if err != nil {
		return dto.GetReadEventConditionRsp{}, err
	}
	// sensitive info
	var sensitiveInfo []dto.SensitiveInfo
	err = ckDb.Model(readEvent.FileEvents{}).Select("sensitive_rule_name,sensitive_rule_id,sensitive_level,count(uuid) as count").WithContext(ctx).
		Where("empty(sensitive_rule_id) = 0").Group("sensitive_rule_id,sensitive_rule_name,sensitive_level").
		Order("sensitive_level desc").Find(&sensitiveInfo).
		Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get sensitive_rule_id condition failed. err=%v", err)
		return dto.GetReadEventConditionRsp{}, err
	}
	var nonStgInfo []dto.SensitiveInfo
	err = ckDb.Model(readEvent.FileEvents{}).Select("sensitive_rule_id,count(uuid) as count,'非敏感数据' AS sensitive_rule_name").WithContext(ctx).
		Where("empty(sensitive_rule_id) != 0").Group("sensitive_rule_id").Find(&nonStgInfo).
		Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get sensitive_rule_id condition failed. err=%v", err)
		return dto.GetReadEventConditionRsp{}, err
	}
	sensitiveInfo = append(sensitiveInfo, nonStgInfo...)
	// file_type
	var fileType []dto.FileType
	err = ckDb.Model(readEvent.FileEvents{}).
		Select("SUBSTRING(CAST(file_category_id AS String),1,4) as code,file_category as name,count(uuid) as count").WithContext(ctx).
		Group("SUBSTRING(CAST(file_category_id AS String),1,4),file_category").
		Find(&fileType).
		Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get sensitive_rule_id condition failed. err=%v", err)
		return dto.GetReadEventConditionRsp{}, err
	}
	return dto.GetReadEventConditionRsp{SensitiveInfo: sensitiveInfo, FileType: fileType}, nil
}

func (e eventFilterRepository) GetReadEventProcessList(ctx context.Context, req dto.GetReadEventReq) (dto.GetProcessListRsp, error) {
	ckDb, err := getCommonCkDbByReq(ctx, req.GetReadEventCommonReq)
	if err != nil {
		return dto.GetProcessListRsp{}, err
	}
	rsDb, err := getCommonCkDbByReq(ctx, req.GetReadEventCommonReq)
	if err != nil {
		return dto.GetProcessListRsp{}, err
	}
	pageReq := model.Pagination{
		Limit:  req.Limit,
		Offset: req.Offset,
	}
	var processCountList []dto.ProcessItem
	ckDb = ckDb.Model(readEvent.FileEvents{}).
		Select("replaceOne(channel,'udef_','') as name,channel as process,count(uuid) as count,visitParamExtractString(process_info,'software_name') as software," +
			"channel || '_' || visitParamExtractString(process_info,'software_name') as key").
		Where("empty(channel) = 0").Group("channel,visitParamExtractString(process_info,'software_name')").Order("channel")
	pageReq, err = model.Paginate(&processCountList, &pageReq, ckDb)
	if err != nil {
		return dto.GetProcessListRsp{}, err
	}
	res := make([]dto.ProcessItem, 0)
	var processRs []dto.ProcessRs
	err = rsDb.Model(readEvent.FileEvents{}).Select("groupUniqArray(channel) as process_list,visitParamExtractString(process_info,'software_name') as software," +
		"count(DISTINCT uuid) as count").
		Where("empty(visitParamExtractString(process_info,'software_name')) = 0 AND empty(channel) = 0").
		Group("visitParamExtractString(process_info,'software_name')").Limit(req.Limit).Offset(req.Offset).Find(&processRs).Error
	if err != nil {
		return dto.GetProcessListRsp{}, err
	}

	processMap := make(map[string]dto.ProcessItem)
	for _, v := range processCountList {
		processMap[v.Key] = v
		if v.Software == "" {
			res = append(res, v)
		}
	}
	for _, v := range processRs {
		processTmp := dto.ProcessItem{
			Name:    v.Software,
			Process: v.Software,
			Count:   v.Count,
		}
		children := make([]dto.ProcessItem, 0)
		for _, process := range v.ProcessList {
			children = append(children, processMap[fmt.Sprintf("%s_%s", process, v.Software)])
		}
		sort.Slice(children, func(i, j int) bool {
			return children[i].Count > children[j].Count
		})
		processTmp.Children = children
		processTmp.ProcessCount = len(children)
		res = append(res, processTmp)
	}

	sort.Slice(res, func(i, j int) bool {
		return res[i].Count > res[j].Count
	})
	eventCount := 0
	for _, v := range res {
		eventCount = eventCount + v.Count
	}
	return dto.GetProcessListRsp{
		CommonPage: model.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page},
		Process:    res,
		EventCount: eventCount,
	}, nil
}

func (e eventFilterRepository) GetReadEventList(ctx context.Context, req dto.GetReadEventReq) (dto.GetReadEventListRsp, error) {
	ckDb, err := getCommonCkDbByReq(ctx, req.GetReadEventCommonReq)
	if err != nil {
		return dto.GetReadEventListRsp{}, err
	}
	var readEvents []dto.ReadEvents
	ckDb = ckDb.Model(readEvent.FileEvents{}).
		Select("file_name,file_path,toDateTime(occur_time, 'Asia/Shanghai') as occur_time,file_size,activity,user_name,"+
			"sensitive_rule_id,sensitive_level,CASE WHEN sensitive_level = 0  THEN '非敏感数据' ELSE sensitive_rule_name END as sensitive_rule_name,"+
			"uuid,channel,channel_type,file_category_id,file_category,file_type_name").
		Where("empty(channel) = 0").Where("visitParamExtractString(process_info,'software_name') in ?", req.SoftwareName).
		Order("occur_time desc")
	pageReq := model.Pagination{
		Limit:  req.Limit,
		Offset: req.Offset,
	}
	pageReq, err = model.Paginate(&readEvents, &pageReq, ckDb)
	if err != nil {
		return dto.GetReadEventListRsp{}, err
	}

	activityList, err := commonApi.GetSpecialConfigByType(ctx, eventConstants.ActivityType)
	if err != nil {
		global.SysLog.Sugar().Errorf("get activity list failed. err=%v", err)
		return dto.GetReadEventListRsp{}, err
	}
	activityMap := make(map[string]string)
	for _, v := range activityList {
		activityMap[v.Key] = v.Value
	}

	for index, event := range readEvents {
		readEvents[index].ActivityDesc = activityMap[event.Activity]
		readEvents[index].FileSizeDesc = utils.FormatBytes(float64(event.FileSize))
	}

	return dto.GetReadEventListRsp{
		CommonPage: model.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page},
		Events:     readEvents,
		EventCount: int(pageReq.TotalRows),
	}, nil
}

func (e eventFilterRepository) GetReadEventDetail(ctx context.Context, uuid string) (readEvent.FileEvents, error) {
	ckDb, err := global.GetCkClient(ctx)
	if err != nil {
		return readEvent.FileEvents{}, err
	}
	var res readEvent.FileEvents
	err = ckDb.Model(readEvent.FileEvents{}).Select("uuid,corp_id,event_type,event_sub_type,user_name,file_name,toDateTime(last_change_time, 'Asia/Shanghai') AS last_change_time,"+
		"file_type,activity,toDateTime(occur_time, 'Asia/Shanghai') AS occur_time,channel,score,data_category,user_tags,event_source,agent_name,file_path,"+
		"lower(severity) as severity,severity_id,file_size,owner,toDateTime(file_create_time, 'Asia/Shanghai') AS file_create_time,md5,sha256,channel,channel_type,file_category_id,"+
		"data_category,sensitive_rule_name,sensitive_level,plat_type,dst_path,src_path,source_name,source_type,original_file_name,original_file_path,"+
		"visitParamExtractString(process_info,'software_name') as software,visitParamExtractString(process_info,'process_name') as process_name,"+
		"visitParamExtractString(process_info,'full_process_image') as process_path,file_category,file_type_name,visitParamExtractString(process_info,'company_name') as company_name").
		Where("uuid = ?", uuid).First(&res).Error
	if err != nil {
		return readEvent.FileEvents{}, err
	}
	activityList, err := commonApi.GetSpecialConfigByType(ctx, eventConstants.ActivityType)
	if err != nil {
		global.SysLog.Sugar().Errorf("get activity list failed. err=%v", err)
		return readEvent.FileEvents{}, err
	}
	activityMap := make(map[string]string)
	for _, v := range activityList {
		activityMap[v.Key] = v.Value
	}
	if strings.HasPrefix(res.Channel, constants.UdefChannel) {
		res.Channel = strings.ReplaceAll(res.Channel, constants.UdefChannel, "")
	}
	res.ActivityDesc = activityMap[res.Activity]
	return res, nil
}

func (e eventFilterRepository) UpdateReadEventFilter(ctx context.Context, req dto.UpdateEventFilterReq) error {
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return pgDb.Transaction(func(tx *gorm.DB) error {
		err = tx.Table(readEvent.EventFilter{}.TableName()).Where("filter_type = ?", req.FilterType).
			Select("process", "file_type_code", "enable_all_user", "user_ids", "user_group_ids", "reserve_day", "scan_content").
			Updates(&readEvent.EventFilter{
				FileTypeCode:  req.FileTypeCode,
				Process:       req.Process,
				EnableAllUser: req.EnableAllUser,
				UserIds:       req.UserIds,
				UserGroupIds:  req.UserGroupIds,
				ReserveDay:    req.ReserveDay,
				ScanContent:   req.ScanContent,
			}).Error
		if err != nil {
			return err
		}
		var suffix pq.StringArray
		err = pgDb.Raw(" select unnest(suffix) as suffix from tb_file_type WHERE code in ?  AND suffix IS NOT null and suffix != '{}'", req.FileTypeCode).
			Find(&suffix).Error
		if err != nil {
			return err
		}

		globalFilters := make([]*v1.FilterItem, 1)
		for _, suf := range suffix {
			fileType := &v1.FilterItem{
				ExpressionValue:  suf,
				ExpressionSymbol: constants.EndWith,
				FieldName:        constants.FilePath,
				Action:           constants.Include,
			}
			globalFilters = append(globalFilters, fileType)
		}

		for _, suf := range req.Process {
			process := &v1.FilterItem{
				ExpressionValue:  suf,
				ExpressionSymbol: constants.Is,
				FieldName:        constants.ProcessName,
				Action:           constants.Exclude,
			}
			globalFilters = append(globalFilters, process)
		}

		marshal, err := proto.Marshal(&v1.EventFilters{
			GlobalFilterItems: globalFilters,
			ScanContent:       uint32(req.ScanContent),
		})
		if err != nil {
			return err
		}
		changeReq := conf_center.ConfChangeReq{
			ConfBizId:       req.Id,
			ConfType:        constants.EvenFilter,
			ConfData:        marshal,
			ConfGranularity: conf_center.GlobalConf,
			Tx:              tx,
			RedisCli:        global.SysRedisClient,
			ChangeType:      conf_center.UpdateConf,
		}
		if req.EnableAllUser != constants.EnableAllUser {
			changeReq.ConfGranularity = conf_center.NonGlobalConf
			changeReq.ConfRelation = &conf_center.ConfRelation{
				UserId:      req.UserIds,
				UserGroupId: req.UserGroupIds,
			}
		}
		err = conf_center.ConfChange(changeReq)
		if err != nil {
			return err
		}
		return nil
	})
}

func (e eventFilterRepository) GetReadEventFilter(ctx context.Context) (dto.EventFilterRsp, error) {
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.EventFilterRsp{}, err
	}
	var eventFilter readEvent.EventFilter
	err = pgDb.Table(readEvent.EventFilter{}.TableName()).Find(&eventFilter).Error
	if err != nil {
		return dto.EventFilterRsp{}, err
	}

	return dto.EventFilterRsp{
		Id:            eventFilter.Id,
		FileTypeCode:  eventFilter.FileTypeCode,
		Process:       eventFilter.Process,
		UserIds:       eventFilter.UserIds,
		UserGroupIds:  eventFilter.UserGroupIds,
		EnableAllUser: eventFilter.EnableAllUser,
		ReserveDay:    eventFilter.ReserveDay,
		ScanContent:   eventFilter.ScanContent,
	}, nil
}

func getCommonCkDbByReq(ctx context.Context, req dto.GetReadEventCommonReq) (*gorm.DB, error) {
	ckDb, err := global.GetCkClient(ctx)
	if err != nil {
		return ckDb, err
	}
	if len(req.FileCategoryIds) > 0 {
		ckDb = ckDb.Where("has(array(?),SUBSTRING(CAST(file_category_id AS String),1,4))", req.FileCategoryIds)
	}
	if len(req.SensitiveStrategy) > 0 {
		ckDb = ckDb.Where("sensitive_rule_id IN ?", req.SensitiveStrategy)
	}
	if req.Keyword != "" {
		keyword := "%" + dbutil.EscapeForLike(req.Keyword) + "%"
		ckDb = ckDb.Where("channel ILIKE ? or file_name ILIKE ? or visitParamExtractString(process_info,'software_name') ILIKE ?", keyword, keyword, keyword)
	}
	if len(req.Channel) > 0 {
		ckDb = ckDb.Where("channel IN ?", req.Channel)
	}
	if len(req.ExcludeChannel) > 0 {
		ckDb = ckDb.Where("channel NOT IN ? AND visitParamExtractString(process_info,'software_name') NOT IN ?", req.ExcludeChannel, req.ExcludeChannel)
	}
	if req.EnableAllUser != constants.EnableAllUser {
		userIds := req.UserIds
		if len(req.UserGroupIds) > 0 {
			groupUserIds, err := commonApi.GetUserIdByGroups(ctx, req.UserGroupIds)
			if err != nil {
				return ckDb, err
			}
			userIds = append(userIds, groupUserIds...)
		}
		ckDb = ckDb.Where("user_id in ?", userIds)
	}

	ckDb = ckDb.Where("file_category_id IN ?", req.IncludeFileCategoryIds).
		Where("toDateTime(occur_time, 'Asia/Shanghai') >= ? AND toDateTime(occur_time, 'Asia/Shanghai') <= ?", req.StartT, req.EndT)
	return ckDb, nil
}
