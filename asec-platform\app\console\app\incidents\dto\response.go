package dto

import (
	"asdsec.com/asec/platform/app/console/common/utils"
)

type BaseData struct {
	AgentIp        []string `gorm:"column:agent_ip;type:[]varchar;comment:客户端IP数组" json:"agent_ip,omitempty"`
	AgentName      string   `gorm:"column:agent_name;type:varchar;comment:客户端名称" json:"agent_name"`
	Channel        string   `gorm:"column:channel;type:varchar;comment:外发通道名称" json:"channel"`
	AppName        string   `gorm:"column:app_name;type:varchar;comment:应用名称" json:"app_name"`
	SrcCountry     string   `gorm:"column:src_country;type:varchar;comment:国家代码CN" json:"src_country"`
	SrcCountryName string   `gorm:"column:src_country_name;type:varchar;comment:国家名" json:"src_country_name"`
	SrcProvince    string   `gorm:"column:src_province;type:varchar;comment:省份" json:"src_province"`
	SrcCity        string   `gorm:"column:src_city;type:varchar;comment:城市" json:"src_city"`
}

type ContextDbData struct {
	BaseData
	Value             int64  `gorm:"column:value;type:int;comment:城市" json:"value"`
	PublicIP          string `gorm:"column:public_ip;type:varchar;comment:公网ip" json:"public_ip"`
	FileCategoryId    int64  `gorm:"column:file_category_id;type:int64;comment:文件类型id" json:"file_category_id"`
	SensitiveRuleName string `gorm:"column:sensitive_rule_name;type:varchar;comment:城市" json:"sensitive_rule_name"`
	SensitiveLevel    int64  `gorm:"column:sensitive_level;type:int;comment:等级" json:"sensitive_level"`
}

type ContextData struct {
	BaseData
	FileType     string `gorm:"-" json:"file_type"`
	DataCategory string `gorm:"-" json:"data_category"`
	Value        int64  `gorm:"column:value;type:int;comment:城市" json:"value"`
}

type IncidentResp struct {
	BaseData
	UserName          string `gorm:"column:user_name;type:varchar;comment:用户名称" json:"user_name"`
	FileName          string `gorm:"column:file_name;type:varchar;comment:文件名称" json:"file_name"`
	FilePath          string `gorm:"column:file_path;type:varchar;comment:文件路径" json:"file_path"`
	FileSize          int64  `gorm:"column:file_size;type:int64;comment:文件大小" json:"file_size"`
	Activity          string `gorm:"column:activity;type:varchar;comment:行为类型" json:"activity"`
	OccurTime         string `gorm:"column:occur_time;type:timestamptz;comment:行为发生时间" json:"occur_time"`
	ChannelType       string `gorm:"column:channel_type;type:varchar;comment:IM通信" json:"channel_type"` //code
	DstPath           string `gorm:"column:dst_path;type:varchar;comment:外发/上传/拷贝路径,地址" json:"dst_path"`
	Severity          string `gorm:"column:severity;type:varchar;comment:严重等级" json:"severity"`
	SeverityId        int8   `gorm:"column:severity_id;type:int8;comment:事件等级ID" json:"severity_id"`
	AccessStatus      string `gorm:"column:access_status;type:int;comment:访问状态" json:"access_status"`
	AccessMethod      string `gorm:"column:access_method;type:varchar;comment:访问方式" json:"access_method"`
	DenyReason        string `gorm:"column:deny_reason;type:varchar;comment:拒绝原因" json:"deny_reason"`
	AuthError         string `gorm:"column:auth_error;type:varchar;comment:是否登录/登出失败" json:"auth_error"`
	AuthErrorDetail   string `gorm:"column:auth_error_detail;type:varchar;comment:登录登出失败描述" json:"auth_error_detail"`
	OriginalFileName  string `gorm:"column:original_file_name;type:varchar;comment:原文件名称(文件重命名事件)" json:"original_file_name"`
	OriginalFilePath  string `gorm:"column:original_file_path;type:varchar;comment:原文件路径（文件移动事件）" json:"original_file_path"`
	FileCategoryId    int64  `gorm:"column:file_category_id;type:int64;comment:文件类型id" json:"file_category_id"`
	SensitiveRuleName string `gorm:"column:sensitive_rule_name;type:int;comment:城市" json:"sensitive_rule_name"`
	SensitiveLevel    int    `gorm:"column:sensitive_level;type:varchar;comment:等级" json:"sensitive_level"`
	SrcIp             string `gorm:"column:src_ip;type:varchar;comment:源IP" json:"src_ip"`
	FileType          string `gorm:"-" json:"file_type"`
	IncidentType      string `gorm:"-" json:"incident_type"`
	TypeValue         string `gorm:"-" json:"type_value"`
	SensitiveRuleId   string `gorm:"column:sensitive_rule_id;type:varchar;comment:命中敏感数据id" json:"sensitive_rule_id"`
	FileSource
	SrcPath      string `gorm:"column:src_path;type:varchar;comment:src源路径" json:"src_path"`
	FileCategory string `gorm:"-" json:"file_category"`
}

type FileSource struct {
	SourceId   string `gorm:"source_id" json:"source_id"`
	SourceType string `gorm:"source_type" json:"source_type"`
	SourceName string `gorm:"source_name" json:"source_name"`
}

type IncidentNameInfo struct {
	IncidentName string          `gorm:"column:incident_name;type:varchar(255);comment:事件名" json:"incident_name"`
	RiskLevel    int             `gorm:"column:risk_level;type:int4;comment:严重等级" json:"risk_level"`
	CreatedAt    utils.FrontTime `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"created_at"`
}

type TraceData struct {
	MatchCondition []TraceCondition `json:"match_condition"`
}

type TraceCondition struct {
	Name       string `json:"name"`        // 事件
	WindowSize string `json:"window_size"` // 窗口大小 36 秒数
	Value      string `json:"value"`       // 阈值  50000 bytes
}
