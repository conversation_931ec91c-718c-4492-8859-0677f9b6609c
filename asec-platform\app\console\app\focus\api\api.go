package api

import (
	"asdsec.com/asec/platform/app/console/app/focus/model"
	"asdsec.com/asec/platform/app/console/app/focus/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// FocusList godoc
// @Summary 获取关注列表
// @Schemes
// @Description 获取关注列表
// @Produce     application/json
// @Success     200
// @Router      /v1/focus/list [GET]
// @success     200 {object} common.Response{} "ok"
func FocusList(c *gin.Context) {
	var req model.FocusOnReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetFocusService().GetFocusList(c, req)
	if err != nil {
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// FocusOn godoc
// @Summary 关注用户/事件
// @Schemes
// @Description 关注用户/事件
// @Produce     application/json
// @Success     200
// @Router      /v1/focus [PUT]
// @success     200 {object} common.Response{} "ok"
func FocusOn(c *gin.Context) {
	var req model.FocusOnCommonReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	aeErr := service.GetFocusService().FocusOn(c, req)
	if aeErr != nil {
		common.FailAError(c, aeErr)
		return
	}
	common.Ok(c)
}

// FocusUserList godoc
// @Summary 关注用户列表
// @Schemes
// @Description 关注用户列表
// @Produce     application/json
// @Success     200
// @Router      /v1/focus/user [GET]
// @success     200 {object} common.Response{} "ok"
func FocusUserList(c *gin.Context) {
	var req model.GetFocusUserListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetFocusService().GetFocusUserList(c, req)
	if err != nil {
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}
