package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	v1 "asdsec.com/asec/platform/api/auth/v1"
	pb "asdsec.com/asec/platform/api/auth/v1/admin"
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/idp/oauth2"
	"asdsec.com/asec/platform/app/auth/internal/idp/webauth"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/structpb"
)

type AdminService struct {
	pb.UnimplementedAdminServer
	corp       *biz.CorpUsecase
	authPolicy *biz.AuthPolicyUsecase
	idp        *biz.IdpUsecase
	role       *biz.RoleUsecase
	user       *biz.UserUsecase
	userGroup  *biz.UserGroupUsecase
	userSource *biz.UserSourceUsecase
	auth       *biz.AuthUsecase
	log        *log.Helper
}

func NewAdminService(corp *biz.CorpUsecase, authPolicy *biz.AuthPolicyUsecase, idp *biz.IdpUsecase, user *biz.UserUsecase, userGroup *biz.UserGroupUsecase, userSource *biz.UserSourceUsecase, role *biz.RoleUsecase, auth *biz.AuthUsecase, logger log.Logger) *AdminService {
	return &AdminService{
		corp:       corp,
		authPolicy: authPolicy,
		idp:        idp,
		user:       user,
		userGroup:  userGroup,
		userSource: userSource,
		role:       role,
		auth:       auth,
		log:        log.NewHelper(logger),
	}
}

func (s *AdminService) GetCorpId(ctx context.Context, corpName string) (string, error) {
	corp, err := s.corp.GetCorpByName(ctx, corpName)
	if err != nil {
		return "", err
	}
	return corp.ID, nil
}

// OAuth2Test 实现测试OAuth2配置的接口
func (s *AdminService) OAuth2Test(ctx context.Context, req *pb.OAuth2TestRequest) (*pb.OAuth2TestReply, error) {
	log.Infof("收到OAuth2配置测试请求: %v", req)

	// 生成唯一测试ID
	testId := uuid.New().String()
	logger := log.With(log.GetLogger())

	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 获取IDP基本信息
	idpBasic, err := s.idp.GetIDPById(ctx, corpId, req.IdpId)
	if err != nil {
		log.Errorf("获取OAuth2提供商失败: %v", err)
		return &pb.OAuth2TestReply{
			Success: false,
			Message: fmt.Sprintf("获取OAuth2提供商失败: %v", err),
		}, err
	}

	// 检查是否是OAuth2类型
	if idpBasic.Type != "oauth2" && idpBasic.TemplateType != "oauth2" {
		log.Errorf("身份提供商类型不是OAuth2: %s，%s", idpBasic.Type, idpBasic.TemplateType)
		return &pb.OAuth2TestReply{
			Success: false,
			Message: fmt.Sprintf("身份提供商类型不是OAuth2: %s", idpBasic.Type),
		}, fmt.Errorf("身份提供商类型不是OAuth2")
	}

	attrs, err := s.idp.GetIDPAttrs(ctx, corpId, req.IdpId)
	if err != nil {
		log.Errorf("获取IDP属性失败: %v", err)
		return &pb.OAuth2TestReply{
			Success: false,
			Message: fmt.Sprintf("获取IDP属性失败: %v", err),
		}, err
	}

	// 创建OAuth2提供商实例
	attrMap := attrs

	// 从属性中获取必要的配置
	globalData := attrMap["oauth2_global_data"]
	codeData := attrMap["oauth2_code_data"]
	userData := attrMap["oauth2_user_data"]
	logoutOpen := attrMap["oauth2_logout_open"]
	logoutData := attrMap["oauth2_logout_data"]
	callbackURL := attrMap["callback_url"]

	// 创建OAuth2提供商
	provider, err := oauth2.NewOAuth2Provider(
		idpBasic.ID,
		idpBasic.Name,
		globalData,
		codeData,
		userData,
		logoutData,
		logoutOpen == "true",
		callbackURL,
		logger,
	)
	if err != nil {
		log.Errorf("创建OAuth2提供商失败: %v", err)
		return &pb.OAuth2TestReply{
			Success: false,
			Message: fmt.Sprintf("创建OAuth2提供商失败: %v", err),
		}, err
	}

	// 设置测试模式环境变量
	ctx = context.WithValue(ctx, common.TestIDKey, testId)
	ctx = context.WithValue(ctx, common.IsTestKey, true)
	ctx = context.WithValue(ctx, common.IdpIDKey, req.IdpId)

	// 执行测试
	testResult, err := provider.TestAuth(ctx)
	if err != nil {
		log.Errorf("OAuth2测试失败: %v", err)

		// 根据错误内容进行分类处理
		errorMessage := fmt.Sprintf("OAuth2测试失败: %v", err)

		// 检查是否为脚本错误
		if strings.Contains(err.Error(), "编译表达式错误") ||
			strings.Contains(err.Error(), "脚本编译错误") {
			errorMessage = fmt.Sprintf("OAuth2脚本编译错误: %v", err)
		} else if strings.Contains(err.Error(), "执行表达式错误") ||
			strings.Contains(err.Error(), "脚本执行错误") {
			errorMessage = fmt.Sprintf("OAuth2脚本执行错误: %v", err)
		}

		// 返回详细错误信息，但不向上传递原始错误
		return &pb.OAuth2TestReply{
			Success: false,
			Message: errorMessage,
			TestId:  testId, // 保留测试ID以便前端引用
		}, nil
	}

	// 添加测试ID到结果
	testResult["test_id"] = testId

	// 缓存测试结果，设置有效期为10分钟
	resultJSON, _ := json.Marshal(testResult)
	log.Infof("testResult测试结果: %s", string(resultJSON))
	if err := s.auth.CacheTest(ctx, testId, string(resultJSON), 10*60); err != nil {
		log.Warnf("缓存OAuth2测试结果失败: %v", err)
	}

	var resultStruct *structpb.Struct
	resultStruct, err = structpb.NewStruct(testResult)
	if err != nil {
		// 如果直接转换失败，尝试通过JSON作为中介进行转换
		log.Warnf("直接转换Struct失败: %v，尝试通过JSON转换", err)

		// 先解析JSON回到新的map
		var jsonMap map[string]interface{}
		if err = json.Unmarshal(resultJSON, &jsonMap); err != nil {
			log.Errorf("JSON解析失败: %v", err)
			// 即使转换失败，也返回基本响应
			return &pb.OAuth2TestReply{
				Success: true,
				Message: "OAuth2配置测试启动成功，但结果格式转换失败",
				TestId:  testId,
			}, nil
		}

		// 使用解析后的map创建Struct
		resultStruct, err = structpb.NewStruct(jsonMap)
		if err != nil {
			log.Errorf("JSON转换Struct失败: %v", err)
			// 即使转换失败，也返回基本响应
			return &pb.OAuth2TestReply{
				Success: true,
				Message: "OAuth2配置测试启动成功，但结果格式转换失败",
				TestId:  testId,
			}, nil
		}
	}

	log.Infof("resultStruct响应数据结构: %+v", resultStruct)
	return &pb.OAuth2TestReply{
		Success: true,
		Message: "OAuth2配置测试启动成功",
		Data:    resultStruct,
		TestId:  testId,
	}, nil
}

// ValidateWebAuthScript 验证WebAuth脚本语法
func (s *AdminService) ValidateWebAuthScript(ctx context.Context, req *pb.ValidateWebAuthScriptRequest) (*pb.ValidateWebAuthScriptReply, error) {
	s.log.Infof("收到脚本验证请求: 类型=%s, 长度=%d", req.ScriptType, len(req.Script))

	if req.Script == "" {
		return &pb.ValidateWebAuthScriptReply{
			Valid: true,
			Error: "",
		}, nil
	}

	// 创建基础环境变量
	mockEnv := make(map[string]interface{})

	// 根据脚本类型添加特定环境变量
	switch req.ScriptType {
	case "input":
		// 为输入脚本添加常用变量
		mockEnv["step"] = map[string]interface{}{
			"Method":  "GET",
			"URL":     "https://example.com/api",
			"Headers": []interface{}{},
			"Gets":    []interface{}{},
			"Cookies": []interface{}{},
		}
	case "output":
		// 为输出脚本添加常用变量
		mockEnv["response"] = map[string]interface{}{
			"status": 200,
			"body":   "{}",
		}
	}

	// 加入常用变量
	mockEnv["UserName"] = "test_user"
	mockEnv["Password"] = "test_pass"

	// 使用webauth包中的ValidateScript函数验证脚本
	err := webauth.ValidateScript(req.Script, mockEnv)

	if err != nil {
		s.log.Warnf("脚本验证失败: %v", err)
		return &pb.ValidateWebAuthScriptReply{
			Valid: false,
			Error: err.Error(),
		}, nil
	}

	return &pb.ValidateWebAuthScriptReply{
		Valid: true,
		Error: "",
	}, nil
}
