package api

import (
	"time"

	"asdsec.com/asec/platform/app/console/app/log/seven_access_log/dto"
	"asdsec.com/asec/platform/app/console/app/log/seven_access_log/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/web"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetSevenAccessLogList godoc
// @Summary 七层日志列表
// @Schemes
// @Description 七层日志列表
// @Tags        DdrSource
// @Produce     application/json
// @Param       req body dto.GetSevenAccessLogListReq true "七层日志列表"
// @Success     200
// @Router      /v1/seven_access_log/list [POST]
// @success     200 {object} common.Response{data=dto.GetSevenAccessLogListRsp} "ok"
func GetSevenAccessLogList(c *gin.Context) {
	var req dto.GetSevenAccessLogListReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.StartTime != "" || req.EndTime != "" {
		startT, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.StartTimeStamp = startT.Unix()

		endT, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.EndTimeStamp = endT.Unix()

		if req.StartTimeStamp > req.EndTimeStamp {
			common.Fail(c, common.ParamInvalidError)
			return
		}
	}

	data, err := service.GetSalService().GetSevenAccessLogList(c, req)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetSevenAccessLogList err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}

	common.OkWithData(c, data)
}

// GetSevenAccessLogDetail godoc
// @Summary 获取日志详情
// @Schemes
// @Description 获取证书详情
// @Tags        DdrSource
// @Produce     application/json
// @Success     200
// @Router      /v1/domain_ssl/detail [GET]
// @success     200 {object} common.Response{data=dto.SevenAccessLogDetail} "ok"
func GetSevenAccessLogDetail(c *gin.Context) {
	uuid := c.Query("uuid")
	data, err := service.GetSalService().GetSevenAccessLogDetail(c, uuid)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetSevenAccessLogDetail err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetSevenAccessLogConf godoc
// @Summary 获取日志字段配置
// @Schemes
// @Description 获取日志字段配置
// @Tags        SevenAccessLogConf
// @Produce     application/json
// @Success     200
// @Router      /v1/seven_access_log/conf [GET]
// @success     200 {object} common.Response{data=dto.SevenAccessLogDetail} "ok"
func GetSevenAccessLogConf(c *gin.Context) {
	data, err := service.GetSalService().GetSevenAccessLogConf(c)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetSevenAccessLogConf err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// UpdateSevenAccessLogConf godoc
// @Summary 修改日志字段配置
// @Schemes
// @Description 修改日志字段配置
// @Tags        SevenAccessLogConf
// @Produce     application/json
// @Success     200
// @Router      /v1/seven_access_log/conf [GET]
// @success     200 {object} common.Response{} "ok"
func UpdateSevenAccessLogConf(c *gin.Context) {
	var req dto.UpdateSevenAccessLogConfReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	serverHost := web.GetServerHost(c)
	req.ServerHost = serverHost
	aeErr := service.GetSalService().UpdateSevenAccessLogConf(c, req)
	if aeErr != nil {
		global.SysLog.Sugar().Errorf("UpdateSevenAccessLogConf err:%v", aeErr.Error())
		common.FailAError(c, aeErr)
		return
	}
	common.Ok(c)
}

// GetSensitiveLogList godoc
// @Summary 敏感数据日志列表
// @Schemes
// @Description 敏感数据日志列表
// @Tags        DdrSource
// @Produce     application/json
// @Param       req body dto.GetSensitiveLogListReq true "敏感数据日志列表"
// @Success     200
// @Router      /v1/sensitive_log/list [POST]
// @success     200 {object} common.Response{data=dto.GetSensitiveLogListRsp} "ok"
func GetSensitiveLogList(c *gin.Context) {
	var req dto.GetSensitiveLogListReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.StartTime != "" || req.EndTime != "" {
		startT, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.StartTimeStamp = startT.Unix()

		endT, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.EndTimeStamp = endT.Unix()

		if req.StartTimeStamp > req.EndTimeStamp {
			common.Fail(c, common.ParamInvalidError)
			return
		}
	}

	data, err := service.GetSalService().GetSensitiveLogList(c, req)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetSensitiveLogList err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}

	common.OkWithData(c, data)
}

// GetSensitiveLogDetail godoc
// @Summary 获取日志详情
// @Schemes
// @Description 获取证书详情
// @Tags        DdrSource
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_log/detail [GET]
// @success     200 {object} common.Response{data=dto.SevenAccessLogDetail} "ok"
func GetSensitiveLogDetail(c *gin.Context) {
	uuid := c.Query("uuid")
	data, err := service.GetSalService().GetSensitiveLogDetail(c, uuid)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetSensitiveLogDetail err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}
