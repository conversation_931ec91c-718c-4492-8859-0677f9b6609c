package api

import (
	subcommon "asdsec.com/asec/platform/app/console/app/incidents/common"
	"asdsec.com/asec/platform/app/console/app/incidents/dto"
	"asdsec.com/asec/platform/app/console/app/incidents/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/excel"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
)

// GetProcess godoc
// @Summary 获取事件时间链
// @Schemes
// @Description 获取事件时间链
// @Tags        incidents
// @Produce     application/json
// @Param       req body dto.ProcessReq true "查询参数"
// @Success     200
// @Router      /v1/incidents/process [GET]
// @success     200 {object} common.Response{data=[]model.FileEvents} "response"
func GetProcess(c *gin.Context) {
	var req dto.ProcessReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	if err != nil {
		global.SysLog.Error(err.Error())
		common.Fail(c, common.ParamInvalidError)
		return
	}
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	if err != nil {
		global.SysLog.Error(err.Error())
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if endTime.Before(startTime) {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetIncidentService().GetIncidentLogs(c, req)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	common.OkWithData(c, data)
}

// GetContext godoc
// @Summary 获取上下文信息
// @Schemes
// @Description 获取上下文信息
// @Tags        incidents
// @Produce     application/json
// @Param       req body dto.ContextReq true "查询参数"
// @Success     200
// @Router      /v1/incidents/context [GET]
// @success     200 {object} common.Response{data=[]dto.ContextData} "response"
func GetContext(c *gin.Context) {
	var req dto.ContextReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	if err != nil {
		global.SysLog.Error(err.Error())
		common.Fail(c, common.ParamInvalidError)
		return
	}
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	if err != nil {
		global.SysLog.Error(err.Error())
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if endTime.Before(startTime) {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetIncidentService().GetContextInfo(c, req)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	common.OkWithData(c, data)
}

// QueryIncidentList godoc
// @Summary 查询事件列表
// @Schemes
// @Description 查询事件列表
// @Tags        incidents
// @Produce     application/json
// @Param       req body subcommon.QueryIncidentListRequest true "查询事件列表"
// @Success     200
// @Router      /v1/incidents/ [GET]
// @success     200 {object} common.Response{data=subcommon.QueryIncidentListRespone} "ok"
func QueryIncidentList(ctx *gin.Context) {
	var req subcommon.QueryIncidentListRequest
	if err := ctx.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetIncidentService().QueryIncidentList(ctx, req)
	if err != nil {
		global.SysLog.Error("query incidents list error", zap.Error(err))
		common.Fail(ctx, common.QueryIncidentListErr)
		return
	}

	common.OkWithData(ctx, resp)
}

// QueryUEBAStrategySum godoc
// @Summary 查询事件类型
// @Schemes
// @Description 查询事件类型
// @Tags        incidents
// @Produce     application/json
// @Success     200
// @Router      /v1/incidents/event_type [GET]
// @success     200 {object} common.Response{data=[]subcommon.UEABStrategySum} "ok"
func QueryUEBAStrategySum(ctx *gin.Context) {
	var req subcommon.QueryTimeParam
	if err := ctx.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetIncidentService().GetUEBAStrategySum(ctx, req)
	if err != nil {
		global.SysLog.Error("query ueba strategy summary error", zap.Error(err))
		common.Fail(ctx, common.QueryUEBAStrategySumErr)
		return
	}
	common.OkWithData(ctx, resp)
}

// QueryEventStateSum godoc
// @Summary 查询事件状态计数
// @Schemes
// @Description 查询事件状态计数
// @Tags        incidents
// @Produce     application/json
// @Param       req body subcommon.QueryTimeParam true "查询事件状态计数"
// @Success     200
// @Router      /v1/incidents/state_sum [GET]
// @success     200 {object} common.Response{data=[]subcommon.EventStateSum} "ok"
func QueryEventStateSum(ctx *gin.Context) {
	var req subcommon.QueryTimeParam
	if err := ctx.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetIncidentService().GetEventStateSum(ctx, req)
	if err != nil {
		global.SysLog.Error("query event state summary error", zap.Error(err))
		common.Fail(ctx, common.QueryEventStateSumErr)
		return
	}
	common.OkWithData(ctx, resp)
}

// QueryUserTop godoc
// @Summary 查询top用户
// @Schemes
// @Description 查询top用户
// @Tags        incidents
// @Produce     application/json
// @Param       req body subcommon.QueryTimeParam true "查询top用户"
// @Success     200
// @Router      /v1/incidents/user_top [GET]
// @success     200 {object} common.Response{data=[]subcommon.UserTopItem} "ok"
func QueryUserTop(ctx *gin.Context) {
	var req subcommon.QueryTimeParam
	if err := ctx.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetIncidentService().QueryUserTop(ctx, req)
	if err != nil {
		global.SysLog.Error("query user top error", zap.Error(err))
		common.Fail(ctx, common.QueryUserTopErr)
		return
	}
	common.OkWithData(ctx, resp)
}

// QueryIncidentPartialSum godoc
// @Summary 事件摘要上部分
// @Schemes
// @Description 事件摘要上部分
// @Tags        incidents
// @Produce     application/json
// @Param       req body subcommon.IncidentID true "事件摘要上部分"
// @Success     200
// @Router      /v1/incidents/partial_sum [GET]
// @success     200 {object} common.Response{data=subcommon.IncidentPartialSum} "ok"
func QueryIncidentPartialSum(ctx *gin.Context) {
	var req subcommon.IncidentID
	if err := ctx.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}

	resp, err := service.GetIncidentService().GetIncidentPartialSum(ctx, req.ID)
	if err != nil {
		global.SysLog.Error("query Incident Partial summary error", zap.Error(err))
		common.Fail(ctx, common.QueryIncidentPartialSumErr)
		return
	}
	common.OkWithData(ctx, resp)

}

// ChangeIncidentState godoc
// @Summary 更改事件状态
// @Schemes
// @Description 更改事件状态
// @Tags        incidents
// @Produce     application/json
// @Param       req body subcommon.ChangeEventStateRequest true "更改事件状态"
// @Success     200
// @Router      /v1/incidents/state [PUT]
// @success     200 {object} common.Response{} "ok"
func ChangeIncidentState(ctx *gin.Context) {
	var req subcommon.ChangeEventStateRequest
	if err := ctx.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	err := service.GetIncidentService().ChangeIncidentState(ctx, req)
	if err != nil {
		global.SysLog.Error("change Incident state error", zap.Error(err))
		common.Fail(ctx, common.ChangeIncidentStateErr)
		return
	}
	common.Ok(ctx)
}

// QueryIncidentSummary godoc
// @Summary 事件摘要
// @Schemes
// @Description 事件摘要
// @Tags        incidents
// @Produce     application/json
// @Param       req body subcommon.IncidentID true "事件摘要"
// @Success     200
// @Router      /v1/incidents/state [GET]
// @success     200 {object} common.Response{data=subcommon.UEBAEventSummaryInfo} "ok"
func QueryIncidentSummary(ctx *gin.Context) {
	var req subcommon.IncidentID
	if err := ctx.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetIncidentService().GetIncidentSummary(ctx, req.ID)
	if err != nil {
		global.SysLog.Error("query Incident summary error", zap.Error(err))
		common.Fail(ctx, common.QueryIncidentSummaryErr)
		return
	}
	common.OkWithData(ctx, resp)
}

func Export(ctx *gin.Context) {
	var req subcommon.QueryIncidentListRequest
	if err := ctx.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetIncidentService().QueryCompIncidentList(ctx, req)
	if err != nil {
		global.SysLog.Error("export Incident list error", zap.Error(err))
		common.Fail(ctx, common.IncidentExportErr)
		return
	}
	data := subcommon.IncidentExportArrayCast(ctx, resp)
	searchCondition := make(map[string]string, 0)
	excel.ExportExcel(subcommon.ExportName, searchCondition, data, subcommon.ExportName, ctx, "", true)
	return
}

// GetIncidentName godoc
// @Summary 事件名称以及等级
// @Schemes
// @Description 事件名称以及等级
// @Tags        incidents
// @Produce     application/json
// @Param       req body dto.NameReq true "查询参数"
// @Success     200
// @Router      /v1/incidents/incident_name [GET]
// @success     200 {object} common.Response{data=[]dto.IncidentNameInfo} "ok"
func GetIncidentName(c *gin.Context) {
	var req dto.NameReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	if err != nil {
		global.SysLog.Error(err.Error())
		common.Fail(c, common.ParamInvalidError)
		return
	}
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	if err != nil {
		global.SysLog.Error(err.Error())
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if endTime.Before(startTime) {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetIncidentService().GetIncidentName(c, req)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	common.OkWithData(c, data)
}
