package events

import (
	"asdsec.com/asec/platform/app/console/app/events/api"
	"github.com/gin-gonic/gin"
)

func EventsApi(r *gin.RouterGroup) {
	v := r.Group("/v1/events")
	{
		v.POST("/list", api.GetEventsList)
		v.GET("/details", api.GetEventsDetails)
		v.POST("/export", api.Export)
		v.GET("/file_type", api.GetFileType)
		v.GET("/activity_type", api.GetActivityType)
		v.GET("/file_types", api.GetFileTypes)
		v.POST("/delete", api.DeleteEvents)

		// 调查分析历史记录
		v.DELETE("/history", api.DeleteHistory)    //删除
		v.GET("/condition", api.Condition)         //过滤条件
		v.GET("/history/list", api.GetHistoryList) //获取调查历史列表
		v.POST("/users", api.GetEventsUserList)    //用户视角

		v.GET("/activity/list", api.GetActivityList) // activity 列表
		v.POST("/factor", api.GetEventsCondition)
		v.GET("/activity/category", api.GetActivityCategory) // activity 分类
	}
}
