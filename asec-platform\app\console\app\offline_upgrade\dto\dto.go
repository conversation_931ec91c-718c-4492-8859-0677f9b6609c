package dto

import "mime/multipart"

type PkgUploadReq struct {
	//ChunkFile   multipart.File `form:"-" json:"chunk_file"`
	//ChunkIndex  int            `form:"chunk_index" json:"chunk_index"`
	//ChunkCounts int            `form:"chunk_counts" json:"chunk_counts"`

	//FileSize    int64          `form:"file_size" json:"file_size"`
	//FileMd5     string         `form:"file_md5" json:"file_md5"`
	ChunkFile   *multipart.FileHeader `form:"-" json:"chunk_file"`
	FilePath    string                `form:"file_path" json:"file_path"`
	HasCompress bool                  `form:"has_compress" json:"has_compress"`
	FileName    string                `form:"file_name" json:"file_name"`
}
