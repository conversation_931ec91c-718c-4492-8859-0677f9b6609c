package model

type UserTagDB struct {
	ID     int    `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	UserID string `gorm:"column:user_id;type:varchar(255);comment:用户ID" json:"user_id"`
	TagID  int    `gorm:"column:tag_id;type:bigint;comment:标签ID" json:"tag_id"`
}

func (UserTagDB) TableName() string {
	return "tb_user_tag"
}

type UserTagConfigDB struct {
	ID   int    `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	Name string `gorm:"column:name;type:varchar(255);comment:标签名" json:"name"`
}

func (UserTagConfigDB) TableName() string {
	return "tb_user_tag_config"
}
