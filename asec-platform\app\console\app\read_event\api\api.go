package api

import (
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/app/read_event/dto"
	"asdsec.com/asec/platform/app/console/app/read_event/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
)

// GetReadEventCondition godoc
// @Summary 获取read事件的筛选条件
// @Schemes
// @Description 获取read事件的筛选条件
// @Tags        event_filter
// @Produce     application/json
// @Param       req body dto.GetReadEventCommonReq true "获取read事件的筛选条件"
// @Success     200
// @Router      /v1/read/condition [POST]
// @success     200 {object} common.Response{} "ok"
func GetReadEventCondition(c *gin.Context) {
	req := dto.GetReadEventCommonReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	checkP := true
	req.StartT, req.EndT, checkP = checkParam(req)
	if !checkP {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetEventFilterService().GetReadEventCondition(c, req)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetReadEventCondition err:%v,req:%v", err, req)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetReadEventList godoc
// @Summary 获取read事件
// @Schemes
// @Description 获取read事件
// @Tags        event_filter
// @Produce     application/json
// @Param       req body dto.GetReadEventReq true "获取read事件"
// @Success     200
// @Router      /v1/read/list [POST]
// @success     200 {object} common.Response{} "ok"
func GetReadEventList(c *gin.Context) {
	req := dto.GetReadEventReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	checkP := true
	req.StartT, req.EndT, checkP = checkParam(req.GetReadEventCommonReq)
	if !checkP {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetEventFilterService().GetReadEventList(c, req)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetReadEventList err:%v,req:%v", err, req)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// UpdateReadEventFilter godoc
// @Summary 修改读取事件过滤条件并且下发至终端
// @Schemes
// @Description 修改读取事件过滤条件并且下发至终端
// @Tags        event_filter
// @Produce     application/json
// @Param       req body dto.UpdateEventFilterReq true "修改读取事件过滤条件并且下发至终端"
// @Success     200
// @Router      /v1/read/filter [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateReadEventFilter(c *gin.Context) {
	req := dto.UpdateEventFilterReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.ReserveDay <= 0 || req.ReserveDay > 30 {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err = service.GetEventFilterService().UpdateReadEventFilter(c, req)
	if err != nil {
		global.SysLog.Sugar().Errorf("UpdateEventFilter err:%v,req:%v", err, req)
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.UnknownChannelAnalysisTYpe,
			OperationType:  common.OperateUpdate,
			Representation: "规则设置",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
}

// GetReadEventFilter godoc
// @Summary 获取读取事件过滤条件
// @Schemes
// @Description 获取读取事件过滤条件
// @Tags        event_filter
// @Produce     application/json
// @Success     200
// @Router      /v1/read/filter [GET]
// @success     200 {object} common.Response{} "ok"
func GetReadEventFilter(c *gin.Context) {
	data, err := service.GetEventFilterService().GetReadEventFilter(c)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetReadEventFilter err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// ExcludeProcess godoc
// @Summary 排除进程
// @Schemes
// @Description 排除进程
// @Tags        event_filter
// @Produce     application/json
// @Success     200
// @Router      /v1/read/process [POST]
// @success     200 {object} common.Response{} "ok"
func ExcludeProcess(c *gin.Context) {
	req := dto.ExcludeProcessReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err = service.GetEventFilterService().ExcludeProcess(c, req.Process)
	if err != nil {
		global.SysLog.Sugar().Errorf("ExcludeProcess err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// GetReadEventDetail godoc
// @Summary 获取读取事件详情
// @Schemes
// @Description 获取读取事件详情
// @Tags        event_filter
// @Produce     application/json
// @Success     200
// @Router      /v1/read/event/detail [GET]
// @success     200 {object} common.Response{} "ok"
func GetReadEventDetail(c *gin.Context) {
	uuid := c.Query("uuid")
	data, err := service.GetEventFilterService().GetReadEventDetail(c, uuid)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetReadEventDetail err:%v,uuid:%v", err, uuid)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetProcessList godoc
// @Summary 获取读取事件进程列表
// @Schemes
// @Description 获取读取事件进程列表
// @Tags        event_filter
// @Produce     application/json
// @Success     200
// @Router      /v1/read/event/process_list [POST]
// @success     200 {object} common.Response{} "ok"
func GetProcessList(c *gin.Context) {
	req := dto.GetReadEventReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	checkP := true
	req.StartT, req.EndT, checkP = checkParam(req.GetReadEventCommonReq)
	if !checkP {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetEventFilterService().GetReadEventProcessList(c, req)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetReadEventProcessList err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// CleanReadEvent godoc
// @Summary 清除历史数据
// @Schemes
// @Description 清除历史数据
// @Tags        event_filter
// @Produce     application/json
// @Success     200
// @Router      /v1/read/event/clean [POST]
// @success     200 {object} common.Response{} "ok"
func CleanReadEvent(c *gin.Context) {
	err := service.GetEventFilterService().CleanReadEvent(c)
	if err != nil {
		global.SysLog.Sugar().Errorf("CleanReadEvent err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

func checkParam(req dto.GetReadEventCommonReq) (time.Time, time.Time, bool) {
	startT, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	if err != nil {
		global.SysLog.Sugar().Errorf("parse req start time err:%v", err)
		return time.Time{}, time.Time{}, false
	}
	endT, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	if err != nil {
		global.SysLog.Sugar().Errorf("parse req end time err:%v", err)
		return time.Time{}, time.Time{}, false
	}
	if startT.After(endT) {
		global.SysLog.Sugar().Errorf("start time after end time err:%v", err)
		return time.Time{}, time.Time{}, false
	}
	return startT, endT, true
}
