package dto

import (
	"github.com/lib/pq"
)

type CreateAuthPolicyParam struct {
	Name            string
	RootGroupId     string
	GroupIds        []string
	UserIds         []string
	CorpId          string
	Description     string
	IdpList         []string
	EnableAllUser   bool
	Enable          bool
	AuthEnhancement []byte
	TimeIds         []string
}

type UpdateAuthPolicyParam struct {
	PolicyId        string
	Name            string
	RootGroupId     string
	GroupIds        []string
	UserIds         []string
	CorpId          string
	Description     string
	IdpList         []string
	EnableAllUser   bool
	Enable          bool
	AuthEnhancement []byte
	TimeIds         []string
}

type CreateAuthPolicyDaoParam struct {
	CorpId          string
	Id              string
	Name            string
	Description     string
	GroupIds        []string
	UserIds         []string
	IdpList         []string
	RootGroupId     string
	EnableAllUser   bool
	Enable          bool
	IsDefault       bool
	AuthEnhancement []byte
	TimeIds         []string
}

type AuthPolicyInfo struct {
	ID              string
	Name            string
	Description     string
	GroupInfoList   []BindGroupInfo
	UserInfoList    []BindUserInfo
	IdpInfoMap      BindIDPInfoMap
	EnableAllUser   bool
	IsDefault       bool
	Enable          bool
	AuthEnhancement AuthEnhancement `json:"auth_enhancement"`
	TimeIds         pq.StringArray  `json:"time_ids"`
}

type ListAuthPolicyResp struct {
	AuthPolicies []AuthPolicyInfo
	Count        int64
}
