package common

import (
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"time"
)

const (
	ExportName                = "Incident.ExcelName.IncidentExcel"
	SensitiveSendIncidentType = "sensitive_send"
)

type RiskLevelMap struct {
	Level int
	Desc  string
}

var (
	RiskLevelOfcritical = RiskLevelMap{5, "严重"}
	RiskLevelOfHigh     = RiskLevelMap{4, "高危"}
	RiskLevelOfMedium   = RiskLevelMap{3, "中危"}
	RiskLevelOfLow      = RiskLevelMap{2, "低危"}
	RiskLevelOfInfo     = RiskLevelMap{1, "信息"}
)

type IncidentStatusMap struct {
	Status int
	Desc   string
}

var (
	UndeterminedIncidentStatus = IncidentStatusMap{1, "未确定"}
	ConfirmedIncidentStatus    = IncidentStatusMap{2, "已确定"}
	AllowIncidentStatus        = IncidentStatusMap{3, "标记为允许"}
)

type UEBAEventSummaryInfo struct {
	UserName       string      `json:"username"`
	Tags           []string    `json:"tags"`
	StrategyName   string      `json:"strategy_name"`
	SeverityLevel  int         `json:"severity_level"`
	Time           string      `json:"time"`
	AgentName      string      `json:"agent_name"`
	AgentID        string      `json:"agent_id"`
	PlatType       string      `json:"plat_type"`
	IPAddr         string      `json:"ip_addr"`
	IncidentType   string      `json:"incident_type"`
	SummaryData    interface{} `json:"summary_data"`
	EventStartTime string      `json:"event_start_time"`
	EventEndTime   string      `json:"event_end_time"`
	FileType       string      `json:"file_type"`
	EventId        string      `json:"event_id"`
}

type SummaryData struct {
	Channel        string     `json:"channel"`
	FileCategoryId int64      `json:"file_category_id"`
	FileName       string     `json:"file_name"`
	FilePath       string     `json:"file_path"`
	FileSize       int        `json:"file_size"`
	Md5            string     `json:"md5"`
	Owner          string     `json:"owner"`
	SensitiveLevel int        `json:"sensitive_level"`
	Sha256         string     `json:"sha256"`
	SourceInfo     SourceInfo `json:"sourceInfo"`
}

type SourceInfo struct {
	SourceId   string `json:"source_id"`
	SourceName string `json:"source_name"`
	SourceType string `json:"source_type"`
	SrcPath    string `json:"src_path"`
}

type QueryBaseParam struct {
	Offset int `form:"offset" json:"offset"`
	Limit  int `form:"limit" json:"limit"`
}

type QueryTimeParam struct {
	StartTime string `form:"start_time" json:"start_time"`
	EndTime   string `form:"end_time" json:"end_time"`
}

type QueryIncidentListRequest struct {
	QueryBaseParam
	StrategyID string `form:"strategy_id" json:"strategy_id"`
	Search     string `form:"search" json:"search"`
	EventState int    `form:"event_state" json:"event_state"`
	RiskLevel  int    `form:"risk_level" json:"risk_level"`
	QueryTimeParam
}

type QueryIncidentListRespone struct {
	TotalNum int               `json:"total_num"`
	Data     []IncidentPartial `json:"data"`
}

type IncidentPartial struct {
	//事件ID
	ID string `json:"id"`
	//事件名
	Name string `json:"name"`
	//风险等级
	SeverityLevel int `json:"severity_level"`
	//外发通道
	Channel string `json:"channel"`
	//访问应用
	Application string `json:"application"`
	//命中的策略名
	StrategyName string `json:"strategy_name"`
	//用户名
	UserName string `json:"user_name"`
	//事件状态
	EventState int `json:"event_state"`
	//事件结束时间
	EndTime string `json:"end_time"`
}

type UEABStrategySum struct {
	ID    string `gorm:"column:strategy_id;type:varchar(255);comment:命中策略ID" json:"id"`
	Name  string `gorm:"column:strategy_name;type:varchar(255);comment:命中策略名 " json:"name"`
	Count int    `gorm:"column:count;type:bigint;comment:个数" json:"count"`
}

type EventStateSum struct {
	EventState string `gorm:"column:incident_status;type:int4;comment:事件状态(1:未确定;2:已确定;3:标记允许;-1:正在生成)" json:"event_state"`
	Count      int    `gorm:"column:count;type:bigint;comment:个数" json:"count"`
}

type UserTopItem struct {
	ID    string `gorm:"column:user_id;type:varchar(255);comment:用户ID" json:"user_id" json:"id"`
	Name  string `gorm:"column:user_name;type:varchar(255);comment:用户名" json:"user_name" json:"name"`
	Count int    `gorm:"column:count;type:bigint;comment:个数" json:"count"`
}

type ChangeEventStateRequest struct {
	ID         string `json:"id"`
	EventState int    `json:"event_state" binding:"required,min=1,max=3"`
}

type IncidentID struct {
	ID string `form:"id" json:"id"`
}

type AgentInfo struct {
	AgentID   int64    `json:"agent_id"`
	AgentName string   `json:"agent_name"`
	AgentIP   []string `json:"agent_ip"`
	PlatType  string   `json:"plat_type"`
}

type IncidentPartialSum struct {
	ID         string   `json:"id"`
	Name       string   `json:"name"`
	UserName   string   `json:"user_name"`
	UserID     string   `json:"user_id"`
	AgentID    string   `json:"agent_id"`
	EventCount int      `json:"event_count"`
	RiskScore  int      `json:"risk_score"`
	EventState int      `json:"event_state"`
	Tags       []string `json:"tags"`
}

type IncidentTypeTopItem struct {
	ID        string `gorm:"column:strategy_id;type:varchar(255);comment:命中策略ID" json:"strategy_id" json:"id"`
	EventType string `gorm:"column:strategy_name;type:varchar(255);comment:命中策略名 " json:"strategy_name" json:"event_type"`
	Count     int    `gorm:"column:count;type:bigint;comment:个数" json:"count"`
}

type IncidentExport struct {
	IncidentName    string `excelColumn:"A" excelDesc:"Incident.IncidentData.IncidentName" excelWidth:"50"`
	RiskLevel       string `excelColumn:"B" excelDesc:"Incident.IncidentData.RiskLevel" excelWidth:"10"`
	IncidentStatus  string `excelColumn:"C" excelDesc:"Incident.IncidentData.IncidentStatus" excelWidth:"10"`
	ApplicationName string `excelColumn:"D" excelDesc:"Incident.IncidentData.ApplicationName" excelWidth:"10"`
	Channel         string `excelColumn:"E" excelDesc:"Incident.IncidentData.Channel" excelWidth:"10"`
	StrategyName    string `excelColumn:"F" excelDesc:"Incident.IncidentData.StrategyName" excelWidth:"15"`
	UserName        string `excelColumn:"G" excelDesc:"Incident.IncidentData.UserName" excelWidth:"10"`
	EventStartTime  string `excelColumn:"H" excelDesc:"Incident.IncidentData.EventStartTime" excelWidth:"22"`
}

func IncidentExportCast(dbData model.UEBAIncidentDB, channelMap map[string]string) IncidentExport {
	var result IncidentExport
	copier.Copy(&result, &dbData)
	switch dbData.RiskLevel {
	case RiskLevelOfcritical.Level:
		result.RiskLevel = RiskLevelOfcritical.Desc
	case RiskLevelOfHigh.Level:
		result.RiskLevel = RiskLevelOfHigh.Desc
	case RiskLevelOfMedium.Level:
		result.RiskLevel = RiskLevelOfMedium.Desc
	case RiskLevelOfLow.Level:
		result.RiskLevel = RiskLevelOfLow.Desc
	case RiskLevelOfInfo.Level:
		result.RiskLevel = RiskLevelOfInfo.Desc
	}

	switch dbData.IncidentStatus {
	case UndeterminedIncidentStatus.Status:
		result.IncidentStatus = UndeterminedIncidentStatus.Desc
	case ConfirmedIncidentStatus.Status:
		result.IncidentStatus = ConfirmedIncidentStatus.Desc
	case AllowIncidentStatus.Status:
		result.IncidentStatus = AllowIncidentStatus.Desc
	}
	result.Channel = channelMap[dbData.Channel]
	result.EventStartTime = time.Unix(dbData.EventStartTime, 0).Format("2006-01-02 15:04:05")
	return result
}

func IncidentExportArrayCast(ctx context.Context, dbData []model.UEBAIncidentDB) []IncidentExport {
	var result []IncidentExport
	channelMap, err := commonApi.GetAllChannel(ctx)
	if err != nil {
		global.SysLog.Error("get channel failed", zap.Error(err))
		return nil
	}
	for _, it := range dbData {
		result = append(result, IncidentExportCast(it, channelMap))
	}
	return result
}
