package dto

type AsecKafkaLogPlugin struct {
	IncludeReqBody     bool               `json:"include_req_body"`
	IncludeRespBody    bool               `json:"include_resp_body"`
	KafkaTopic         string             `json:"kafka_topic"`
	LogFormat          map[string]string  `json:"log_format"`
	Brokers            []Broker           `json:"brokers"`
	StaticResourceConf StaticResourceConf `json:"static_resource_conf"`
}
type StaticResourceConf struct {
	Enable    bool     `json:"enable"`
	FileTypes []string `json:"file_types"`
}
type Broker struct {
	Host string `json:"host"`
	Port int    `json:"port"`
}
