package service

import (
	"asdsec.com/asec/platform/app/console/app/auth/admin/model"
	"asdsec.com/asec/platform/app/console/app/auth/admin/repository"
	configMode "asdsec.com/asec/platform/app/console/app/special_api/model"
	confRepo "asdsec.com/asec/platform/app/console/app/special_api/repository"
	global "asdsec.com/asec/platform/app/console/global"
	globalModel "asdsec.com/asec/platform/pkg/model"
	"context"
	"strconv"
	"sync"
)

var AdminServiceImpl AdminService

// AdminServiceInit 单例对象
var AdminServiceInit sync.Once

type AdminService interface {
	CreateAdmin(ctx context.Context, req model.CreateAdminReq) error
	DeleteAdmin(ctx context.Context, userId string, corpId string) error
	GetAdminList(ctx context.Context, req model.GetAdminListReq) (globalModel.Pagination, error)
	GetAdminById(ctx context.Context, userId string, corpId string) (model.GetAdminRsp, error)
	UpdateAdmin(ctx context.Context, req model.UpdateAdminReq) error
	UpdateAdminPassword(ctx context.Context, req model.UpdateSelfAdminReq) error
	GetAdminByCorpAndName(ctx context.Context, corpId, userName string) (model.Admin, error)
	UpdateAdminAuthConfig(ctx context.Context, req model.AdminAuthConfig) error
	GetAdminAuthConfig(ctx context.Context) (model.AdminAuthConfig, error)
}

type adminService struct {
	db repository.AdminRepository
}

func (a adminService) GetAdminAuthConfig(ctx context.Context) (model.AdminAuthConfig, error) {
	resp := model.AdminAuthConfig{}
	timeoutConf := configMode.ConfReq{Type: AdminAuthConfigType, Key: AdminAuthKey}
	err, timeoutConfModel := confRepo.NewSpecialRepository().GetSpecialConf(ctx, timeoutConf)
	if err != nil {
		return resp, err
	}
	resp.SessionTimeout, _ = strconv.ParseUint(timeoutConfModel.Value, 10, 64)
	//convert to minutes
	resp.SessionTimeout = resp.SessionTimeout / 1000000000 / 60
	failCountConf := configMode.ConfReq{Type: AdminAuthConfigType, Key: MaxFailedCountKey}
	err, failCountConfModel := confRepo.NewSpecialRepository().GetSpecialConf(ctx, failCountConf)
	if err != nil {
		return resp, err
	}
	resp.MaxFailedTimes, _ = strconv.ParseUint(failCountConfModel.Value, 10, 64)

	lockDurationConf := configMode.ConfReq{Type: AdminAuthConfigType, Key: LockedDuration}
	err, LockedDurationConfModel := confRepo.NewSpecialRepository().GetSpecialConf(ctx, lockDurationConf)
	if err != nil {
		return resp, err
	}
	resp.LockedDuration, _ = strconv.ParseUint(LockedDurationConfModel.Value, 10, 64)

	return resp, nil
}

const AdminAuthConfigType = "ADMIN"
const AdminAuthKey = "access_token_duration"

const MaxFailedCountKey = "admin_login_max_failed_count"
const LockedDuration = "admin_login_lock_duration"

func (a adminService) UpdateAdminAuthConfig(ctx context.Context, req model.AdminAuthConfig) error {
	timeoutConf := configMode.ConfReq{Type: AdminAuthConfigType, Key: AdminAuthKey, Value: strconv.FormatUint(req.SessionTimeout*1000000000*60, 10)}
	err := confRepo.NewSpecialRepository().UpsertSpecialConf(ctx, timeoutConf)
	if err != nil {
		return err
	}
	failCountConf := configMode.ConfReq{Type: AdminAuthConfigType, Key: MaxFailedCountKey, Value: strconv.FormatUint(req.MaxFailedTimes, 10)}
	err = confRepo.NewSpecialRepository().UpsertSpecialConf(ctx, failCountConf)
	if err != nil {
		return err
	}
	lockDurationConf := configMode.ConfReq{Type: AdminAuthConfigType, Key: LockedDuration, Value: strconv.FormatUint(req.LockedDuration, 10)}
	err = confRepo.NewSpecialRepository().UpsertSpecialConf(ctx, lockDurationConf)
	if err != nil {
		return err
	}
	//重置缓存
	global.InitTokenConfig()
	return nil
}

func (a adminService) GetAdminByCorpAndName(ctx context.Context, corpId, userName string) (model.Admin, error) {
	return a.db.GetAdminByCorpAndName(ctx, corpId, userName)
}

func (a adminService) UpdateAdminPassword(ctx context.Context, req model.UpdateSelfAdminReq) error {
	return a.db.UpdateAdminPassword(ctx, req)
}

func (a adminService) UpdateAdmin(ctx context.Context, req model.UpdateAdminReq) error {
	return a.db.UpdateAdmin(ctx, req)
}

func (a adminService) GetAdminById(ctx context.Context, userId string, corpId string) (model.GetAdminRsp, error) {
	return a.db.GetAdminById(ctx, userId, corpId)
}

func (a adminService) GetAdminList(ctx context.Context, req model.GetAdminListReq) (globalModel.Pagination, error) {
	return a.db.GetAdminList(ctx, req)
}

func (a adminService) DeleteAdmin(ctx context.Context, userId string, corpId string) error {
	return a.db.DeleteAdmin(ctx, userId, corpId)
}

func (a adminService) CreateAdmin(ctx context.Context, req model.CreateAdminReq) error {
	return a.db.CreateAdmin(ctx, req)
}

func GetAdminService() AdminService {
	AdminServiceInit.Do(func() {
		AdminServiceImpl = &adminService{db: repository.NewAppRepository()}
	})
	return AdminServiceImpl
}
