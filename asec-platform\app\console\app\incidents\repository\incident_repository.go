package repository

import (
	comm "asdsec.com/asec/platform/app/console/app/appliancemgt/common"
	"asdsec.com/asec/platform/app/console/app/incidents/common"
	"asdsec.com/asec/platform/app/console/app/incidents/dto"
	modelpriv "asdsec.com/asec/platform/app/console/app/incidents/model"
	userRiskService "asdsec.com/asec/platform/app/console/app/user_risk/service"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	normErrors "errors"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type TimeParam struct {
	StartTime time.Time
	EndTime   time.Time
}

type QueryParam struct {
	Offset     int
	Limit      int
	Search     string
	StrategyID string
	EventState int
	RiskLevel  int
	TimeParam
}

type QueryResult struct {
	Count int64
	Data  []model.UEBAIncidentDB
}

type FileEventsRepositoryImpl interface {
	GetIncidentLogs(ctx context.Context, req dto.ProcessReq, ids []string) ([]dto.IncidentResp, error)
	GetContextInfo(ctx context.Context, req dto.ContextReq) ([]dto.ContextData, error)
	GetIncidentName(ctx context.Context, req dto.NameReq) ([]dto.IncidentNameInfo, error)
	GetAgentLoginInfo(ctx context.Context, req dto.ProcessReq) ([]dto.IncidentResp, error)
	GetIncidentById(ctx context.Context, incidentId string) (model.IncidentsTb, error)

	Query(ctx context.Context, param QueryParam) (QueryResult, error)
	Find(ctx context.Context, id string) (model.UEBAIncidentDB, error)
	GetEventStateSum(ctx context.Context, param TimeParam) ([]common.EventStateSum, error)
	GetUEABStrategySum(ctx context.Context, timeParam common.QueryTimeParam) ([]common.UEABStrategySum, error)
	QueryUserTop(ctx context.Context, param TimeParam) ([]common.UserTopItem, error)
	ChangeIncidentState(ctx context.Context, id string, state int) error
	QueryIncidentTypeTop(ctx context.Context, param TimeParam) ([]common.IncidentTypeTopItem, error)

	GetUserTags(ctx context.Context, userID string) (modelpriv.UserTagConfigDB, error)
}

func NewIncidentRepository() FileEventsRepositoryImpl {
	return &incidentReposPriv{}
}

// private
type incidentReposPriv struct {
}

func (i *incidentReposPriv) GetIncidentName(ctx context.Context, req dto.NameReq) ([]dto.IncidentNameInfo, error) {
	var names []dto.IncidentNameInfo
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(&model.UEBAIncidentDB{}).Distinct("incident_name,risk_level").Select("incident_name,"+
		"risk_level,created_at").Where("user_id = ? and created_at >= ? and created_at <= ? and id != ?", req.UserId,
		req.StartTime, req.EndTime, req.IncidentsId).Find(&names).Error
	if err != nil {
		return nil, err
	}
	return names, nil
}

func (self *incidentReposPriv) Query(ctx context.Context, param QueryParam) (QueryResult, error) {
	var result QueryResult
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	db = db.Model(&model.UEBAIncidentDB{})
	if len(param.Search) > 0 {
		tmp := fmt.Sprintf("%%%s%%", strings.ToLower(param.Search))
		db = db.Where("LOWER(user_name) LIKE ? OR LOWER(incident_name) LIKE ?", tmp, tmp)
	}
	if param.RiskLevel != 0 {
		db = db.Where("risk_level = ?", param.RiskLevel)
	}

	if len(param.StrategyID) > 0 {
		db = db.Where("strategy_id = ?", param.StrategyID)
	}

	if param.EventState != 0 {
		db = db.Where("incident_status = ?", param.EventState)
	}

	if !param.StartTime.IsZero() || !param.EndTime.IsZero() {
		db = db.Where("created_at > ? and created_at < ?", param.StartTime, param.EndTime)
	}
	err = db.Offset(param.Offset).Limit(param.Limit).Order("created_at desc").Find(&result.Data).Limit(-1).Offset(-1).Count(&result.Count).Error
	return result, err
}

func (self *incidentReposPriv) GetUEABStrategySum(ctx context.Context, timeParam common.QueryTimeParam) ([]common.UEABStrategySum, error) {
	var result []common.UEABStrategySum
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(&model.UEBAIncidentDB{}).
		Select("strategy_id,strategy_name,count(*)").
		Where("created_at > ? and created_at < ?", timeParam.StartTime, timeParam.EndTime).
		Group("strategy_id,strategy_name").
		Find(&result).Error

	return result, err
}

func (self *incidentReposPriv) GetEventStateSum(ctx context.Context, param TimeParam) ([]common.EventStateSum, error) {
	var result []common.EventStateSum
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(&model.UEBAIncidentDB{}).
		Where("created_at > ? and created_at < ?", param.StartTime, param.EndTime).
		Select("incident_status,count(*)").Group("incident_status").Find(&result).Error
	return result, err
}

func (self *incidentReposPriv) QueryUserTop(ctx context.Context, param TimeParam) ([]common.UserTopItem, error) {
	var result []common.UserTopItem
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(&model.UEBAIncidentDB{}).
		Where("created_at > ? and created_at < ?", param.StartTime, param.EndTime).
		Select("user_id,user_name,count(*)").Group("user_id,user_name").Order("count desc").Find(&result).Error
	return result, err
}

func (self *incidentReposPriv) Find(ctx context.Context, id string) (model.UEBAIncidentDB, error) {
	var result model.UEBAIncidentDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&result).Where("id = ?", id).Find(&result).Error
	return result, err
}

func (self *incidentReposPriv) ChangeIncidentState(ctx context.Context, id string, state int) (err error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		var incident model.UEBAIncidentDB
		err = tx.Model(&model.UEBAIncidentDB{}).Where("id = ?", id).Find(&incident).Error
		if err != nil {
			return err
		}
		originStatus := incident.IncidentStatus
		userRisk, err := userRiskService.GetUserRiskService().GetUserScoreByUserId(ctx, incident.UserID)
		if err != nil {
			return err
		}

		var incidentStatus int
		err = tx.Model(&model.UEBAIncidentDB{}).Select("incident_status").Where("id = ?", id).Find(&incidentStatus).Error
		if err != nil {
			return err
		}

		err = tx.Model(&model.UEBAIncidentDB{}).Where("id = ?", id).Update("incident_status", state).Error
		if err != nil {
			return err
		}
		// 避免多次调用接口，多次扣分，只有当前事件状态和req中的状态不一致才进行重新算分
		if incidentStatus != state {
			return userRiskService.GetUserRiskService().ReCountScoreForEventType(ctx, tx, incident.IncidentScore, userRisk.Score, userRisk.UserId, state, originStatus)
		}
		return nil
	})
}

func (i *incidentReposPriv) GetContextInfo(ctx context.Context, req dto.ContextReq) ([]dto.ContextData, error) {
	var result []dto.ContextData
	var events []dto.ContextDbData
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return result, err
	}
	startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	err = db.Model(&model.FileEvents{}).Select("count(*) as value,agent_ip,agent_name,channel,sensitive_rule_name,"+
		"file_category_id,sensitive_level,src_country,src_country_name,src_province,src_city,app_name,public_ip").Where("user_id = ? "+
		"and occur_time >= ? and occur_time <= ? and activity = ?", req.UserId, startTime.Unix(), endTime.Unix(),
		req.Activity).Group("agent_ip,agent_name,channel,sensitive_rule_name,file_category_id,sensitive_level,src_country," +
		"src_country_name,src_province,src_city,app_name,public_ip").Find(&events).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return result, err
	}
	fileType := i.GetFileType(ctx)
	for _, v := range events {
		var tmp dto.ContextData
		tmp.BaseData = v.BaseData
		tmp.Value = v.Value
		if v.FileCategoryId > 0 {
			tmp.FileType = fileType[v.FileCategoryId]
		}
		if v.SensitiveRuleName != "" {
			level := strconv.FormatInt(v.SensitiveLevel, 10)
			tmp.DataCategory = "L" + level + v.SensitiveRuleName
		}
		if v.PublicIP != "" {
			tmp.AgentIp = []string{v.PublicIP}
		}
		result = append(result, tmp)
	}
	return result, nil
}

func (i *incidentReposPriv) GetIncidentLogs(ctx context.Context, req dto.ProcessReq, ids []string) ([]dto.IncidentResp, error) {
	var events []dto.IncidentResp
	db, err := global.GetCkClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return events, err
	}
	startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	err = db.Model(&model.FileEvents{}).Select("user_name,file_name,access_status,access_method,auth_error_detail,"+
		"file_category_id,activity,occur_time,channel,data_category,agent_name,file_path,deny_reason,auth_error,"+
		"lower(severity) as severity,severity_id,file_size,owner,channel,channel_type,auth_error_detail,src_ip,"+
		"data_category,src_country,src_country_name,src_province,app_name,original_file_name,original_file_path,"+
		"src_city, dst_path,sensitive_rule_name,sensitive_level,sensitive_rule_id,source_id,source_type,source_name,src_path").
		Where("uuid in (?) and occur_time >= ? and occur_time <= ?", ids,
			startTime.Unix(), endTime.Unix()).Order("occur_time").Find(&events).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return nil, err
	}
	fileType := i.GetFileType(ctx)
	for k, v := range events {
		fileCategory, err := commonApi.GetFileTypeAndCode(ctx, strconv.FormatInt(events[k].FileCategoryId, 10))
		// 这里查询失败不返回空，这里存在识别不出来的文件类型
		if err != nil && !normErrors.Is(err, comm.ErrUnSupportFileType) {
			global.SysLog.Error(err.Error())
		}
		events[k].FileCategory = fileCategory
		events[k].FileType = fileType[v.FileCategoryId]
	}
	return events, nil
}

func (i *incidentReposPriv) GetFileType(ctx context.Context) map[int64]string {
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return nil
	}
	var data []model.FileType
	allFileType := make(map[int64]string, 0)
	err = pgDb.Model(model.FileType{}).Find(&data).Error
	if err != nil {
		return nil
	}
	for _, v := range data {
		allFileType[v.Code] = v.Name
	}
	return allFileType
}

func (i *incidentReposPriv) GetIncidentById(ctx context.Context, incidentId string) (model.IncidentsTb, error) {
	var incidents model.IncidentsTb
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return incidents, err
	}
	err = pgDb.Model(&model.IncidentsTb{}).Select("id,incident_name,raw_event_ids,trace_data").Where("id = ?",
		incidentId).First(&incidents).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return incidents, err
	}
	return incidents, nil
}

func (i *incidentReposPriv) GetAgentLoginInfo(ctx context.Context, req dto.ProcessReq) ([]dto.IncidentResp, error) {
	var events []dto.IncidentResp
	db, err := global.GetCkClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return events, err
	}
	startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	err = db.Model(&model.FileEvents{}).Select("user_name,file_name,access_status,access_method,auth_error_detail,"+
		"file_type,activity,occur_time,channel,data_category,agent_name,file_path,deny_reason,auth_error,src_ip,"+
		"lower(severity) as severity,severity_id,file_size,owner,channel,channel_type,auth_error_detail,"+
		"data_category,src_country,src_country_name,src_province,app_name,original_file_name,original_file_path,"+
		"src_city, dst_path").Where("agent_id = ? and activity='login' and occur_time >= ? and occur_time <= ?",
		req.AgentId, startTime.Unix(), endTime.Unix()).Order("occur_time").Find(&events).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return nil, err
	}
	return events, nil
}

func (self *incidentReposPriv) GetUserTags(ctx context.Context, userID string) (modelpriv.UserTagConfigDB, error) {
	var result modelpriv.UserTagConfigDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return result, err
	}
	var tmpUserTag modelpriv.UserTagDB
	err = db.Model(&tmpUserTag).Where("user_id = ?", userID).Find(&tmpUserTag).Error
	if err != nil {
		return result, err
	}
	err = db.Model(&result).Where("id = ?", tmpUserTag.ID).Find(&result).Error
	return result, err
}

func (self *incidentReposPriv) QueryIncidentTypeTop(ctx context.Context, param TimeParam) ([]common.IncidentTypeTopItem, error) {
	var result []common.IncidentTypeTopItem
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return nil, err
	}
	err = db.Model(&model.UEBAIncidentDB{}).
		Where("created_at > ? and created_at < ?", param.StartTime, param.EndTime).
		Select("strategy_id,strategy_name,count(*)").
		Group("strategy_id,strategy_name").
		Order("count desc").
		Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, err
}
