package model

import (
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/utils/jwt_util"
)

type AdminLoginReq struct {
	Name      string `form:"name" json:"name" binding:"required"`
	Password  string `form:"password" json:"password" binding:"required"`
	CorpId    string `json:"corp_id"`
	IsEncrypt bool   `json:"is_encrypt"`
}

type CreateAdminReq struct {
	Name        string       `json:"name" binding:"required"`
	Password    string       `json:"password" binding:"required"`
	Phone       string       `json:"phone"`
	Email       string       `json:"email"`
	ExpireTime  string       `json:"expire_time"`
	CorpId      string       `json:"corp_id"`
	RoleId      string       `json:"role_id" binding:"required"`
	Status      int          `json:"status" binding:"required"`
	Desc        string       `json:"desc"`
	Permissions []Permission `json:"permissions"`
	ExpireType  int          `json:"expire_type" binding:"required"`
}

type UpdateAdminReq struct {
	UserId      string       `json:"user_id" binding:"required"`
	Name        string       `json:"name" binding:"required"`
	Password    string       `json:"password"`
	Desc        string       `json:"desc"`
	Phone       string       `json:"phone"`
	Email       string       `json:"email"`
	ExpireTime  string       `json:"expire_time"`
	RoleId      string       `json:"role_id"`
	CorpId      string       `json:"corp_id"`
	Permissions []Permission `json:"permissions"`
	Status      int          `gorm:"status" json:"status" binding:"required,oneof=1 2"`
	ExpireType  int          `gorm:"expire_type" json:"expire_type"`
}

type Permission struct {
	Name       string `json:"name"`
	Permission string `json:"permission"`
}

type GetAdminListReq struct {
	model.Pagination
	CorpId string `json:"corpId"`
	UserId string `json:"user_id"`
}

type CreateCredentialReq struct {
	UserId   string `json:"user_id"`
	Password string `json:"password"`
	CorpId   string `json:"corp_id"`
}

type RefreshTokenReq struct {
	UserId       string `json:"user_id"`
	CorpId       string `json:"corp_id"`
	RefreshToken string
	Claim        jwt_util.TokenClaims
}

type UpdateSelfAdminReq struct {
	UserId      string `json:"user_id"`
	CorpId      string `json:"corp_id"`
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required"`
}

type AdminAuthConfig struct {
	SessionTimeout uint64 `json:"session_timeout"`
	MaxFailedTimes uint64 `json:"max_failed_times"`
	LockedDuration uint64 `json:"locked_duration"`
}
