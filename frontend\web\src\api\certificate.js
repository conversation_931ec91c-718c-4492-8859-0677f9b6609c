import service from '@/utils/request'

export const getDomainSSL = (params) => {
  return service({
    url: '/console/v1/domain_ssl/list',
    method: 'get',
    params
  })
}

export const delDomainSSL = (data) => {
  return service({
    url: '/console/v1/domain_ssl',
    method: 'delete',
    data
  })
}

export const getDomainSSLDetail = (params) => {
  return service({
    url: '/console/v1/domain_ssl/detail',
    method: 'get',
    params
  })
}

export const addDomainSSL = (data) => {
  return service({
    url: '/console/v1/domain_ssl',
    method: 'post',
    data
  })
}

export const upDomainSSL = (data) => {
  return service({
    url: '/console/v1/domain_ssl',
    method: 'put',
    data
  })
}

// 获取证书列表用于应用创建时选择
export const getCertificateList = (params) => {
  return service({
    url: '/console/v1/application/certificates',
    method: 'get',
    params
  })
}
