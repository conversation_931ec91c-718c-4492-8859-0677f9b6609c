package api

import (
	"asdsec.com/asec/platform/app/console/utils/excel"
	"errors"
	"fmt"
	"net"
	"regexp"
	"strconv"
	"strings"

	"asdsec.com/asec/platform/app/console/app/application/constants"
	"asdsec.com/asec/platform/app/console/app/application/dto"
	"asdsec.com/asec/platform/app/console/app/application/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/app/console/common/utils"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/web"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	utils2 "asdsec.com/asec/platform/pkg/utils"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/miekg/dns"
	"go.uber.org/zap"
)

// AddApplicationByType godoc
// @Summary 添加应用根据类型
// @Schemes
// @Description 添加应用根据类型
// @Tags        Application
// @accept      application/json
// @Param       req body dto.AddApplicationByTypeReq true "application data"
// @Produce     application/json
// @Security    ApiKeyAuth
// @Success     200
// @Router      /v1/application [Post]
func AddApplicationByType(c *gin.Context) {
	var req dto.AddApplicationByTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppResourceType,
			OperationType:  common.OperateCreate,
			Representation: req.AppName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	// 查询应用名称是否存在
	count, err := service.GetApplicationService().AppCountByName(c, req.AppName, 0)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("find strategy by name err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if count > 0 {
		common.Fail(c, common.AppNameRepeat)
		return
	}
	// 未选中标签时，指定为默认分类标签
	if len(req.GroupIds) == 0 {
		req.GroupIds = append(req.GroupIds, strconv.Itoa(constants.DefaultGroupId))
	}
	var id uint64
	if req.AppType == constants.WebAppType {
		var webAppReq dto.AddWebAppReq
		serverHost := web.GetServerHost(c)
		webAppReq, err = structWebAppReq(req, serverHost)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if !checkIpOrDomainAndPort(req.ServerAddress) || !checkIpOrDomainAndPort(req.PublishAddress) {
			common.FailAError(c, aerrors.New("ip or domain invalid", constants.AppDomainInvalidErr))
			return
		}
		var aeErr aerrors.AError
		id, aeErr = service.GetApplicationService().AddWebApplication(c, webAppReq)
		if aeErr != nil {
			errorLog = aeErr.Error()
			common.FailAError(c, aeErr)
			return
		}
	} else if req.AppType == constants.PortalAppType {
		var res dto.PortalAppReq
		err := copier.Copy(&res, req)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		var groupIds []int64
		for _, groupId := range req.GroupIds {
			idStr, err := strconv.ParseInt(groupId, 10, 64)
			if err != nil {
				common.Fail(c, common.ParamInvalidError)
				return
			}
			groupIds = append(groupIds, idStr)
		}
		res.GroupIds = groupIds

		var aeErr aerrors.AError
		id, aeErr = service.GetApplicationService().AddPortalApplication(c, res)
		if aeErr != nil {
			errorLog = aeErr.Error()
			common.FailAError(c, aeErr)
			return
		}
	} else {
		var tunAppReq dto.ApplicationReq
		tunAppReq, err = structTunAppReq(req)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if !CheckTunReq(tunAppReq) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		//判断设置的IP是否与域名IP冲突
		var sites []string
		for _, v := range tunAppReq.AppSites {
			if !utils2.CheckTunAppSite(v.Address, v.Port) {
				common.FailAError(c, aerrors.New("ip or domain invalid", constants.AppDomainInvalidErr))
				return
			}
			// todo  地址判断一下ip还是域名
			ip := net.ParseIP(v.Address)
			if ip == nil {
				_, _, err := net.ParseCIDR(v.Address)
				if err != nil {
					_, IsDomain := dns.IsDomainName(v.Address)
					if !IsDomain {
						common.Fail(c, common.ParamInvalidError)
						return
					}
				}
			}
			tmp := v.Protocol + "," + v.Address + "," + v.Port
			if utils.InArray(tmp, sites) {
				common.Fail(c, common.AppAddrRepeat)
				return
			}
			sites = append(sites, tmp)
		}
		sites = nil
		id, err = service.GetApplicationService().AddApplication(c, &tunAppReq)
		if err != nil {
			errorLog = err.Error()
			common.Fail(c, common.AddAppErr)
			return
		}
	}

	common.OkWithData(c, strconv.FormatUint(id, 10))
}

// ImportApp godoc
// @Summary excel导入应用
// @Schemes
// @Description excel导入应用
// @Tags        Application
// @accept      application/json
// @Produce     application/json
// @Security    ApiKeyAuth
// @Success     200
// @Router      /v1/application/import [Post]
func ImportApp(c *gin.Context) {
	var req dto.ImportAppReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppResourceType,
			OperationType:  common.OperateCreate,
			Representation: "importApp",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	/********参数校验******************/
	for _, v := range req.AppData {
		// 查询应用名称是否存在
		count, err := service.GetApplicationService().AppCountByName(c, v[common.ImportAppName], 0)
		if err != nil {
			errorLog = err.Error()
			global.SysLog.Error("find strategy by name err", zap.Error(err))
			common.Fail(c, common.OperateError)
			return
		}
		if count > 0 {
			errorLog = "应用名重复"
			common.FailFormat(c, common.AppNameRepeatDetail, "app_name", v[common.ImportAppName])
			return
		}
		if len(v) != common.ImportAppExcelLen {
			errorLog = "数据模板错误"
			common.Fail(c, common.TemplateError)
			return
		}
		if v[common.ImportAppType] != "WEB应用" && v[common.ImportAppType] != "隧道应用" && v[common.ImportAppType] != "门户应用" {
			errorLog = "参数错误"
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if v[common.ImportAppType] == "" || v[common.ImportAppName] == "" || v[common.ImportAppShowStatus] == "" || v[common.ImportAppStatus] == "" ||
			((v[common.ImportAppGateway] == "" || v[common.ImportAppServerAddress] == "") && v[common.ImportAppType] != "门户应用") ||
			(v[common.ImportAppStatus] != "启用" && v[common.ImportAppStatus] != "停用" && v[common.ImportAppStatus] != "维护") {
			errorLog = "参数错误"
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if (v[common.ImportAppShowStatus] != "是" && v[common.ImportAppShowStatus] != "否") ||
			(v[common.ImportAppSmartRewrite] != "是" && v[common.ImportAppSmartRewrite] != "否" && v[common.ImportAppSmartRewrite] != "") {
			errorLog = "参数错误"
			common.Fail(c, common.ParamInvalidError)
			return
		}
	}

	id, aeErr := service.GetApplicationService().ImportApp(c, req.AppData)
	if aeErr != nil {
		errorLog = aeErr.Error()
		common.FailAError(c, aeErr)
		return
	}

	common.OkWithData(c, strconv.FormatUint(id, 10))
}

// ApplicationExport godoc
// @Summary 导出应用
// @Schemes
// @Description 导出应用
// @Tags        Application
// @accept      application/json
// @Param       req body dto.AddApplicationByTypeReq true "application data"
// @Produce     application/json
// @Security    ApiKeyAuth
// @Success     200
// @Router      /v1/application/export [Post]
func ApplicationExport(c *gin.Context) {
	var req dto.GetApplicationListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetApplicationService().GetApplicationList(c, req)
	if err != nil {
		global.SysLog.Error("get application err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	// 标题
	title := constants.AppExcel
	var res []dto.ApplicationGroupItem
	res = data.Data
	download, err := service.GetApplicationService().AppDownload(c, res)
	if err != nil {
		return
	}
	var templateMap = map[string][]string{}
	excel.ExportExcelTemplate(title, download, c, templateMap)
	return
}

// ApplicationTemplate godoc
// @Summary 应用导入模板下载
// @Schemes
// @Description 应用导入模板下载
// @Tags        Application
// @accept      application/json
// @Param       req body dto.AddApplicationByTypeReq true "application data"
// @Produce     application/json
// @Security    ApiKeyAuth
// @Success     200
// @Router      /v1/application/template [Post]
func ApplicationTemplate(c *gin.Context) {
	appliances, err := service.GetApplicationService().Appliances(c)
	if err != nil {
		return
	}
	if len(appliances) == 0 {
		common.Fail(c, common.GateWayEmpty)
		return
	}

	title := constants.WEBExcelTemplate
	var template []dto.AppDownload
	var templateMap = map[string][]string{
		"A2:A2000": {"WEB应用", "隧道应用", "门户应用"},
		"C2:C2000": {"启用", "停用", "维护"},
		"D2:D2000": appliances,
		"J2:J2000": {"是", "否"},
		"H2:H2000": {"是", "否"},
	}

	template = append(template, dto.AppDownload{
		AppType:        "WEB应用",
		Name:           "web应用名称",
		AppStatus:      "启用",
		SdpList:        appliances[0],
		ServerAddress:  "https://127.0.0.1:80",
		PublishAddress: "https://127.0.0.1:8080",
		Uri:            "/*",
		SmartRewrite:   "是",
		DependSite:     "",
		ShowStatus:     "是",
		PortalShowName: "展示名",
		PortalDesc:     "这是一个web应用",
	})

	template = append(template, dto.AppDownload{
		AppType:        "隧道应用",
		Name:           "隧道应用名称",
		AppStatus:      "启用",
		SdpList:        appliances[0],
		ServerAddress:  "tcp:oa.domain.com:80,udp:*************:8080",
		ShowStatus:     "是",
		PortalShowName: "展示名",
		PortalDesc:     "这是一个隧道应用",
	})

	template = append(template, dto.AppDownload{
		AppType:        "门户应用",
		Name:           "门户应用名称",
		AppStatus:      "启用",
		ShowStatus:     "是",
		PortalShowName: "展示名",
		PortalDesc:     "这是一个门户应用",
	})

	excel.ExportExcelTemplate(title, template, c, templateMap)
	return
}

// GetApplicationList godoc
// @Summary 应用列表
// @Schemes
// @Description 修改应用
// @Tags        Application
// @accept      application/json
// @Param       req body dto.UpdateApplicationByTypeReq true "application data"
// @Produce     application/json
// @Security    ApiKeyAuth
// @Success     200
// @Router      /v1/application [Post]
func GetApplicationList(c *gin.Context) {
	var req dto.GetApplicationListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetApplicationService().GetApplicationList(c, req)
	if err != nil {
		global.SysLog.Error("get application err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetUserAppList godoc
func GetUserAppList(c *gin.Context) {
	var req dto.UserAppReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetApplicationService().GetMyApp(c, req.UserId)
	if err != nil {
		common.FailWithMessage(c, -1, "获取用户应用失败")
		return
	}
	common.OkWithData(c, data)
}

func structTunAppReq(req dto.AddApplicationByTypeReq) (dto.ApplicationReq, error) {
	var res dto.ApplicationReq
	err := copier.Copy(&res, req)
	if err != nil {
		return dto.ApplicationReq{}, err
	}
	res.IconURL = req.IconURL // 确保图标URL被复制

	var seAppList []dto.SeApps
	for _, v := range req.SdpList {
		seAppList = append(seAppList, dto.SeApps{SeId: v, AppType: constants.GatewayAppType})
	}
	var groupIds []int64
	for _, groupId := range req.GroupIds {
		idStr, err := strconv.ParseInt(groupId, 10, 64)
		if err != nil {
			return dto.ApplicationReq{}, err
		}
		groupIds = append(groupIds, idStr)
	}
	res.GroupIds = groupIds
	res.SeApp = seAppList
	return res, nil
}

func structWebAppReq(req dto.AddApplicationByTypeReq, serverHost string) (dto.AddWebAppReq, error) {
	var res dto.AddWebAppReq
	err := copier.Copy(&res, req)
	if err != nil {
		return dto.AddWebAppReq{}, err
	}
	var seAppList []dto.SeApps
	for _, v := range req.SdpList {
		seAppList = append(seAppList, dto.SeApps{SeId: v, AppType: constants.GatewayAppType})
	}
	if req.Uri == "" {
		res.Uri = constants.DefaultUri
	}
	var groupIds []int64
	for _, groupId := range req.GroupIds {
		idStr, err := strconv.ParseInt(groupId, 10, 64)
		if err != nil {
			return dto.AddWebAppReq{}, err
		}
		groupIds = append(groupIds, idStr)
	}

	webHeaderConfig := make([]dto.HeaderConfig, 0)
	defaultRule := make([]string, 0)
	for _, v := range req.WebCompatibleConfig.HeaderConfig {
		if v.Key != "" {
			webHeaderConfig = append(webHeaderConfig, v)
		}
	}
	if len(req.WebCompatibleConfig.DefaultRule) > 0 {
		defaultRule = req.WebCompatibleConfig.DefaultRule
	}
	res.WebCompatibleConfig.DefaultRule = defaultRule
	res.WebCompatibleConfig.HeaderConfig = webHeaderConfig
	res.GroupIds = groupIds
	res.ServerHost = serverHost
	res.SeApp = seAppList
	//门户配置 应用中心首页地址
	res.WebUrl = fmt.Sprintf("%s://%s", req.PublishSchema, req.PublishAddress)
	if req.WebUrl != "" {
		res.WebUrl = req.WebUrl
	}
	res.IconURL = req.IconURL
	if len(req.SdpList) > 1 {
		return dto.AddWebAppReq{}, errors.New("the number of sdp currently supports one")
	}
	return res, err
}

func checkIpOrDomainAndPort(addr string) bool {
	host, port, err := net.SplitHostPort(addr)

	if err != nil {
		if strings.Contains(err.Error(), constants.MissingPort) {
			host = addr
		} else {
			// 无法解析主机和端口,格式不正确
			return false
		}
	}
	//  检查端口是否在范围内:
	if port != "" {
		portNum, err := strconv.Atoi(port)
		if err != nil || portNum < 1 || portNum > 65535 {
			return false
		}
	}
	// 检查主机是否是域名或者IP:
	if ip := net.ParseIP(host); ip != nil {
		// 是IP格式
		return true
	}

	domainRegex := regexp.MustCompile(`^[a-z0-9]+([-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}$`)
	if domainRegex.MatchString(host) {
		// 是域名格式
		return true
	}
	return false
}

func CheckTunReq(req dto.ApplicationReq) bool {
	if len(req.SeApp) == 0 || len(req.AppSites) == 0 {
		return false
	}
	return true
}

// GetCertificateList godoc
// @Summary 获取证书列表用于应用创建时选择
// @Schemes
// @Description 获取证书列表用于应用创建时选择
// @Tags        Application
// @accept      application/json
// @Param       search query string false "搜索关键字"
// @Param       limit query int false "限制数量" default(50)
// @Param       offset query int false "偏移量" default(0)
// @Produce     application/json
// @Security    ApiKeyAuth
// @Success     200 {object} dto.CertificateListResponse
// @Router      /v1/application/certificates [GET]
func GetCertificateList(c *gin.Context) {
	search := c.DefaultQuery("search", "")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	// 构建查询条件
	db, err := global.GetDBClient(c.Request.Context())
	if err != nil {
		global.SysLog.Error("get db client failed", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	query := db.Table("tb_certificate").Select("id::text as id, name, domain, created_at, updated_at")
	
	// 搜索过滤
	if search != "" {
		query = query.Where("name ILIKE ?", "%"+search+"%")
	}

	// 计算总数
	var total int64
	countQuery := *query
	if err := countQuery.Count(&total).Error; err != nil {
		global.SysLog.Error("count certificates failed", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	// 分页查询
	var certificates []dto.CertificateInfo
	if err := query.Limit(limit).Offset(offset).Order("created_at desc").Scan(&certificates).Error; err != nil {
		global.SysLog.Error("get certificates failed", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	response := dto.CertificateListResponse{
		Data: certificates,
		Total: total,
	}

	common.OkWithData(c, response)
}
