package incidents

import (
	"asdsec.com/asec/platform/app/console/app/incidents/api"
	"github.com/gin-gonic/gin"
)

func IncidentsApi(r *gin.RouterGroup) {
	v := r.Group("/v1/incidents")
	{
		v.GET("/process", api.GetProcess)
		v.GET("/context", api.GetContext)
		v.GET("/incident_name", api.GetIncidentName)

		v.GET("", api.QueryIncidentList)
		v.GET("/event_type", api.QueryUEBAStrategySum)
		v.GET("/state_sum", api.QueryEventStateSum)
		v.GET("/user_top", api.QueryUserTop)
		v.GET("/partial_sum", api.QueryIncidentPartialSum)
		v.PUT("/state", api.ChangeIncidentState)
		v.GET("/summary", api.QueryIncidentSummary)
		v.GET("/export", api.Export)
	}
}
