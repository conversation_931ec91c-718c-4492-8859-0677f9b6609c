package apisix

const (
	PostMethod         = "POST"
	GetMethod          = "GET"
	DeleteMethod       = "DELETE"
	PutMethod          = "PUT"
	XApiSixHeader      = "X-API-KEY"
	ApisixAdminApiPort = 9180

	ReserveRouteName = "_KEEP_IT"
)
const (
	ApisixRoute               = "/apisix/admin/routes"
	HealthCheckResult         = "/v1/healthcheck"
	ApisixCheckRoute          = "http://apisix:9094"
	ApisixCrtRoute            = "/apisix/admin/ssls"
	ApisixEndpoint            = "http://apisix:9180"
	ApisixDefaultTimeOut      = 6
	ApisixDefaultPriority     = 1
	ApisixDefaultUpstreamType = "roundrobin"

	ApisixCorsPlugin            = "cors"
	ApisixRealIPPlugin          = "real-ip"
	ApisixForwardPlugin         = "asec-forward-auth"
	ApisixVirtualIPPlugin       = "asec-virtual-ip"
	ApisixUrlControlPlugin      = "asec-url-control"
	ApisixResponseRewritePlugin = "asec-response-rewrite"
	ApisixResponseProcessPlugin = "asec-response-process"
	ApisixFormFillPlugin        = "asec-form-fill"
	ApisixProxyRewritePlugin    = "proxy-rewrite"
	ApisixSourceIPInsertPlugin  = "asec-source-ip-insert"
	ApisixExtPluginPostReq      = "ext-plugin-post-req"
	StrategyPluginName          = "strategy_plugin"
	ApisixUrlSmartRewriteRule   = "url_smart_rewrite"
	ApisixUrlManualRewriteRule  = "url_manual_rewrite"

	ApisixDependSiteRule  = "depend_site" // 依赖站点规则
	DependSiteTypeAssign  = "assign"      // 指定站点方式
	DependSiteTypeAll     = "all"         // 泛域名方式
	ApisixUrlControlRule  = "url_control"
	ApisixUriPath         = "uri"          //应用路径
	ApisixCrossDomainRule = "cross_domain" // 跨域处理规则

	ApisixSourceIPInsertRule  = "source_ip_insert"  // 自定义源IP插入规则
	ApisixSourceIPGetRule     = "source_ip_get"     // 源IP获取规则
	ApisixSingleSignOnRule    = "single_sign_on"    // 单点登录规则
	ApisixHeaderConfigRule    = "header_config"     // 请求头配置规则
	ApisixHostsRule           = "hosts"             // 自定义Host规则
	ApisixWebSocketRule       = "websocket"         // WebSocket支持规则
	ApisixBypassRule          = "bypass"            // 降级模式规则
	ApisixHttp2HttpsRule      = "http2https"        // HTTP到HTTPS重定向规则
	ApisixErrorResponseRule   = "error_response"    // 异步请求响应处理
	ApisixFrontUrlContorlRule = "front_url_control" //前端路由增强控制

	// 单点登录类型
	SingleSignOnTypeMicroApp  = "micro_application" // 微应用单点登录
	SingleSignOnTypeOAuth2    = "oauth2"            // OAuth2协议对接
	SingleSignOnTypeCAS       = "cas"               // CAS协议对接
	SingleSignOnTypeFillForms = "fill_forms"        // 表单代填

	// 办公应用类型及对应User-Agent关键词
	OfficeAppDingtalk     = "dingtalk"    // 钉钉
	OfficeAppQiYeWx       = "qiyewx"      // 企业微信
	OfficeAppFeishu       = "feishu"      // 飞书
	OfficeAppZhezhengding = "zhezhending" // 浙政钉

	// User-Agent检测关键词映射
	UserAgentKeywordDingtalk     = "dingtalk"
	UserAgentKeywordQiYeWx       = "qiyewx"
	UserAgentKeywordFeishu       = "feishu"
	UserAgentKeywordZhezhengding = "zhezhending"

	// 支持的源IP头部名称
	SourceIPHeaderXRealIP       = "X-Real-IP"
	SourceIPHeaderXForwardedFor = "X-Forwarded-For"
	SourceIPHeaderCFConnecting  = "CF-Connecting-IP"
	SourceIPHeaderXClientIP     = "X-Client-IP"
	SourceIPHeaderXOriginalIP   = "X-Original-IP"

	ResponseRewriteDisable      = false
	ResponseRewriteBodyBase64   = false
	ResponseRewriteFilterOption = "jo"
	ResponseRewriteFilterScope  = "global"

	RequestHeaderField   = "request_header"
	ResponseHeaderField  = "response_header"
	HeaderOptAdd         = "add"
	HeaderOptSet         = "set"
	HeaderOptUpdate      = "update"
	HeaderOptRemove      = "remove"
	HeaderOrigin         = "Origin"
	HeaderAcceptEncoding = "Accept-Encoding"

	HttpSchema      = "http"
	HttpSchemaPort  = 80
	HttpsSchema     = "https"
	HttpsSchemaPort = 443

	XApisixKey = "8aOWcEqGY4gHwPh2deX6zFSvoLuQKBJk"
)

var CorsPlugin = map[string]any{
	"allow_credential": false,
	"allow_headers":    "*",
	"allow_methods":    "*",
	"allow_origins":    "*",
	"expose_headers":   "*",
	"max_age":          5,
}

type ApisixRouteReq struct {
	Name            string         `json:"name"`
	Status          int            `json:"status"`
	Methods         []string       `json:"methods"`
	Plugins         map[string]any `json:"plugins"`
	Uri             string         `json:"uri"`
	Host            string         `json:"host"`
	Upstream        Upstream       `json:"upstream"`
	EnableWebsocket bool           `json:"enable_websocket,omitempty"`
}

// 依赖站点配置结构
type DependSiteConfig struct {
	Type string `json:"type"` // assign 或 all
	Text string `json:"text"` // 站点列表，换行分隔
}

// 跨域配置结构 (已废弃，仅为向后兼容保留)
// 新的跨域处理逻辑只依赖 default_rule 中是否包含 "cross_domain"
type CrossDomainConfig struct {
	Method string `json:"method,omitempty"`
}

// URL控制配置结构
type URLControlConfig struct {
	Type string `json:"type"` // "free_auth" 或 "forbid_access"
	Text string `json:"text"` // URL路径列表，换行分隔
}

// 手动URL改写配置结构
type URLManualRewrite struct {
	Before string `json:"before"` // 改写前的URL
	After  string `json:"after"`  // 改写后的URL
}

// 源IP插入配置结构
type SourceIPInsertConfig struct {
	Header    string `json:"header"`    // 插入的请求头字段，如 "X-Real-IP"
	Direction string `json:"direction"` // 插入方向：left从左到右，right从右到左
	Separator string `json:"separator"` // 分隔符，默认为逗号
	Position  int    `json:"position"`  // 插入位置（第几个）
}

// 自定义源IP插入插件配置结构
type SourceIPInsertPlugin struct {
	Header    string `json:"header"`    // 插入的请求头字段，如 "X-Real-IP"
	Direction string `json:"direction"` // 插入方向：left从左到右，right从右到左
	Separator string `json:"separator"` // 分隔符，默认为逗号
	Position  int    `json:"position"`  // 插入位置（第几个）
}

// 源IP获取配置结构
type SourceIPGetConfig struct {
	TrustIPs  string `json:"trust_ips"` // 可信任的IP列表，逗号分隔
	Source    string `json:"source"`    // 源IP头部名称
	Recursive string `json:"recursive"` // 是否递归解析
}

type WebSocketConfig struct {
	Enable bool `json:"enable"` // 是否启用WebSocket支持
}

type Upstream struct {
	Type          string        `json:"type"`
	PassHost      string        `json:"pass_host"`
	UpstreamHost  string        `json:"upstream_host,omitempty"`
	Scheme        string        `json:"scheme"`
	Timeout       Timeout       `json:"timeout"`
	KeepalivePool KeepalivePool `json:"keepalive_pool"`
	Nodes         []Node        `json:"nodes"`
	Checks        *Checks       `json:"checks,omitempty"`
}

type Checks struct {
	Active Active `json:"active,omitempty"`
}

type Active struct {
	Timeout           int       `json:"timeout,omitempty"`
	HttpPath          string    `json:"http_path,omitempty"`
	Host              string    `json:"host,omitempty"`
	Healthy           Healthy   `json:"healthy,omitempty"`
	Unhealthy         Unhealthy `json:"unhealthy,omitempty"`
	Type              string    `json:"type,omitempty"`
	Port              int       `json:"port"`
	VerifyCertificate bool      `json:"https_verify_certificate"`
}
type Healthy struct {
	Interval     int   `json:"interval"`
	Success      int   `json:"successes"`
	HttpStatuses []int `json:"http_statuses"`
}
type Unhealthy struct {
	Interval     int `json:"interval"` //探测间隔
	HttpFailures int `json:"http_failures"`
	TcpFailures  int `json:"tcp_failures"`
	Timeouts     int `json:"timeouts"`
}

type Node struct {
	Host   string `json:"host"`
	Port   int    `json:"port"`
	Weight int    `json:"weight"`
}
type Timeout struct {
	Connect int `json:"connect"`
	Send    int `json:"send"`
	Read    int `json:"read"`
}

type KeepalivePool struct {
	Size        int `json:"size"`
	IdleTimeout int `json:"idle_timeout"`
	Requests    int `json:"requests"`
}

type ApisixRsp struct {
	Key   string         `json:"key"`
	Value ApisixRspValue `json:"value"`
}
type ApisixRspValue struct {
	Id      string         `json:"id"`
	Plugins map[string]any `json:"plugins"`
}

type GatewayRelationship struct {
	SdpId    string `json:"sdp_id"`
	ApisixId string `json:"apisix_id"`
}
type GatewayRelationshipValue struct {
	Value []GatewayRelationship `json:"value"`
}
type SdpItem struct {
	SdpId    string
	Endpoint string
}

type CompatibleConfig struct {
	DefaultRule      []string              `json:"default_rule"`
	HeaderConfig     []HeaderConfig        `json:"header_config"`
	DependSite       *DependSiteConfig     `json:"depend_site,omitempty"`
	CrossDomain      *CrossDomainConfig    `json:"cross_domain,omitempty"`
	SourceIPInsert   *SourceIPInsertConfig `json:"source_ip_insert,omitempty"`
	SourceIPGet      *SourceIPGetConfig    `json:"source_ip_get,omitempty"`
	URLControl       *URLControlConfig     `json:"url_control,omitempty"`
	URLManualRewrite []URLManualRewrite    `json:"url_manual_rewrite,omitempty"`
	SingleSignOn     *SingleSignOnConfig   `json:"single_sign_on,omitempty"`
	WebSocket        *WebSocketConfig      `json:"websocket,omitempty"`
	Hosts            string                `json:"hosts,omitempty"`
}

type HealthConfig struct {
	Enable string              `json:"enable"`
	Config *HealthConfigConfig `json:"config"`
}

type HealthConfigConfig struct {
	Path              string `json:"path"`
	Timeout           int    `json:"timeout"`
	Protocol          string `json:"protocol"`
	HealthCode        []int  `json:"health_code"`
	HealthIntervals   int    `json:"health_intervals"`
	UnHealthIntervals int    `json:"un_health_intervals"`
	SuccessNum        int    `json:"success_num"`
	FailNums          int    `json:"fail_nums"`
}

type HeaderConfig struct {
	Field     string `json:"field"`
	Operation string `json:"operation"`
	Key       string `json:"key"`
	Value     string `json:"value"`
}

type ApisixCrtReq struct {
	Cert string   `json:"cert"`
	Key  string   `json:"key"`
	Snis []string `json:"snis"`
}

type ApisixListResp struct {
	List []ApisixListItem `json:"list"`
}

type ApisixListItem struct {
	Key   string      `json:"key"`
	Value ApisixValue `json:"value"`
}
type ApisixValue struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type WebWatermarkPlugin struct {
	WatermarkPluginConf `json:"watermark_conf"`
	Enable              bool `json:"enable"`
}

type WatermarkPluginConf struct {
	Alpha          int8     `json:"alpha"`
	Color          string   `json:"color"`
	ColumnsSpacing int32    `json:"columns_spacing"`
	Content        []string `json:"content"`
	EffectiveApps  `json:"effective_apps"`
	EffectiveUsers UserInfo `json:"effective_users"`
	ExcludeUsers   UserInfo `json:"exclude_users"`
	LineSpacing    int32    `json:"line_spacing"`
	Rotate         int8     `json:"rotate"`
	Secret         bool     `json:"secret"`
	Size           int8     `json:"size"`
}
type EffectiveApps struct {
	AppIds    []string `json:"app_ids"`
	EnableAll bool     `json:"enable_all"`
}

type UserInfo struct {
	UserIds   []string `json:"user_ids"`
	GroupIds  []string `json:"group_ids"`
	EnableAll bool     `json:"enable_all"`
}

// 依赖站点过滤规则
type DependSiteFilter struct {
	Regex   string `json:"regex"`
	Replace string `json:"replace"`
	Options string `json:"options"`
	Scope   string `json:"scope"`
}

// 依赖站点路由配置
type DependSiteRoute struct {
	Id       string             `json:"id"`
	Uri      string             `json:"uri"`
	Hosts    []string           `json:"hosts"`
	Upstream DependSiteUpstream `json:"upstream"`
	Methods  []string           `json:"methods"`
	Status   int                `json:"status"`
	Name     string             `json:"name"`
}

type DependSiteUpstream struct {
	Type   string         `json:"type"`
	Scheme string         `json:"scheme,omitempty"`
	Nodes  map[string]int `json:"nodes"`
}

// 单点登录配置
type SingleSignOnConfig struct {
	Type   string               `json:"type"`   // 单点登录类型：micro_application、oauth2、cas、fill_forms
	Config SingleSignOnConfData `json:"config"` // 单点登录具体配置
}

// 单点登录配置数据
type SingleSignOnConfData struct {
	IdpType             string `json:"idp_type"`              // 身份提供商类型
	IdpId               string `json:"idp_id"`                // 身份提供商ID
	Appid               string `json:"app_id"`                // 应用ID（微应用、OAuth2等）
	AppSecret           string `json:"app_secret"`            // 应用密钥
	Callback            string `json:"callback"`              // 回调地址
	RecognizePatterns   string `json:"recognize_patterns"`    // User-Agent识别模式
	LoginUrl            string `json:"login_url"`             // 登录URL（表单代填等）
	LoginType           string `json:"login_type"`            // 登录类型
	UsernameInput       string `json:"username_input"`        // 用户名输入框选择器
	UsernameValue       string `json:"username_value"`        // 用户名取值方式
	CustomUsernameValue string `json:"custom_username_value"` // 自定义用户名值
	PasswordInput       string `json:"password_input"`        // 密码输入框选择器
	PasswordValue       string `json:"password_value"`        // 密码取值方式
	CustomPasswordValue string `json:"custom_password_value"` // 自定义密码值
	LoginButton         string `json:"login_button"`          // 登录按钮选择器
	AutoLogin           string `json:"auto_login"`            // 自动登录开关：1开启，0关闭
	AutoLoginPed        string `json:"auto_login_ped"`        // 自动登录周期（秒）
	OfficeAppType       string `json:"office_app_type"`       // 办公应用类型：dingtalk、qiyewx、feishu、zhezhending
	AuthServiceUrl      string `json:"auth_service_url"`      // 认证服务URL（用于办公应用SSO跳转）
	CallbackUri         string `json:"callback_uri"`          // 回调URI路径（默认：/auth/login/v1/callback）
	CorpId              string `json:"corp_id"`               // 企业ID（钉钉等需要）
}
