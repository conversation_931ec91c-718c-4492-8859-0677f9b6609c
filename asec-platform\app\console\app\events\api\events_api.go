package api

import (
	"asdsec.com/asec/platform/app/console/app/events/dto"
	"asdsec.com/asec/platform/app/console/app/events/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/excel"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
)

// GetActivityList godoc
// @Summary 获取activity列表
// @Schemes
// @Description 获取activity列表
// @Tags        Events
// @Produce     application/json
// @Success     200
// @Router      /v1/activity/list [POST]
// @success     200 {object} common.Response{data=[]dto.GetActivityResp} "response"
func GetActivityList(c *gin.Context) {
	list, err := service.GetAnalysisService().GetActivityList(c)
	if err != nil {
		global.SysLog.Error("get activity list err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, list)
}

// GetActivityCategory godoc
// @Summary 获取activity分类列表
// @Schemes
// @Description 获取activity分类列表
// @Tags        Events
// @Produce     application/json
// @Success     200
// @Router      /v1/activity/category [GET]
// @success     200 {object} common.Response{data=[]dto.GetActivityCategoryResp} "response"
func GetActivityCategory(c *gin.Context) {
	list, err := service.GetAnalysisService().GetActivityCategoryList(c)
	if err != nil {
		global.SysLog.Error("get activity category list err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, list)
}

// GetEventsList godoc
// @Summary 获取调查分析列表
// @Schemes
// @Description 获取调查分析事件列表
// @Tags        Events
// @Produce     application/json
// @Param       req body dto.ListEventsReq true "查询参数"
// @Success     200
// @Router      /v1/events/list [POST]
// @success     200 {object} common.Response{data=[]model.FileEvents} "response"
func GetEventsList(c *gin.Context) {
	var req dto.GetEventListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.StartTime != "" && req.EndTime != "" {
		startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
		if err != nil {
			global.SysLog.Error(err.Error())
			common.Fail(c, common.ParamInvalidError)
			return
		}
		endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
		if err != nil {
			global.SysLog.Error(err.Error())
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if endTime.Before(startTime) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.StartT = startTime
		req.EndT = endTime
	}
	data, err := service.GetAnalysisService().GetEventsList(c, req)
	if err != nil {
		global.SysLog.Error("query err", zap.Error(err))
		common.Fail(c, common.GetEventsError)
		return
	}
	common.OkWithData(c, data)
}

// GetEventsUserList godoc
// @Summary 用户视角
// @Schemes
// @Description 用户视角
// @Tags        Events
// @Produce     application/json
// @Param       req body dto.ListEventsReq true "查询参数"
// @Success     200
// @Router      /v1/events/users [POST]
// @success     200 {object} common.Response{data=[]model.GetUsersRsp} "response"
func GetEventsUserList(c *gin.Context) {
	var req dto.GetEventListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.StartTime != "" && req.EndTime != "" {
		startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
		if err != nil {
			global.SysLog.Error(err.Error())
			common.Fail(c, common.ParamInvalidError)
			return
		}
		endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
		if err != nil {
			global.SysLog.Error(err.Error())
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if endTime.Before(startTime) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.StartT = startTime
		req.EndT = endTime
	}
	data, err := service.GetAnalysisService().GetEventsUserList(c, req)
	if err != nil {
		global.SysLog.Error("query err", zap.Error(err))
		common.Fail(c, common.GetEventsError)
		return
	}
	common.OkWithData(c, data)
}

type UuidReq struct {
	Id string `form:"id" json:"id" binding:"required,min=1"`
}

// GetEventsDetails godoc
// @Summary 获取调查分析事件详情
// @Schemes
// @Description 获取调查分析事件详情
// @Tags        Events
// @Produce     application/json
// @Param       req body UuidReq true "查询参数"
// @Success     200
// @Router      /v1/events/details [GET]
// @success     200 {object} common.Response{data=model.FileEvents} "response"
func GetEventsDetails(c *gin.Context) {
	var req UuidReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetAnalysisService().GetEventsDetail(c, req.Id)
	if err != nil {
		global.SysLog.Error("delete err", zap.Error(err))
		common.Fail(c, common.GetEventsError)
		return
	}
	common.OkWithData(c, data)
}

// DeleteEvents godoc
// @Summary 删除事件列表
// @Schemes
// @Description 删除事件列表
// @Tags        Events
// @Produce     application/json
// @Param       req body dto.Ids true "ID列表"
// @Success     200
// @Router      /v1/events/delete [POST]
// @success     200 {object} common.Response{} "response"
func DeleteEvents(c *gin.Context) {
	var req dto.Ids
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetAnalysisService().DeleteEvents(c, req)
	if err != nil {
		global.SysLog.Error("delete err", zap.Error(err))
		common.FailWithMessage(c, -1, "删除事件列表失败")
		return
	}
	common.Ok(c)
}

// Export godoc
// @Summary 导出调查记录
// @Schemes
// @Description 导出调查记录
// @Tags        Events
// @Produce     application/json
// @Param       req body ExportReq true "保存参数"
// @Success     200
// @Router      /v1/events/export [POST]
// @success     200 {object} common.Response{} "response"
func Export(c *gin.Context) {
	var req dto.GetEventListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.StartTime != "" && req.EndTime != "" {
		startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
		if err != nil {
			global.SysLog.Error(err.Error())
			common.Fail(c, common.ParamInvalidError)
			return
		}
		endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
		if err != nil {
			global.SysLog.Error(err.Error())
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if endTime.Before(startTime) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.StartT = startTime
		req.EndT = endTime
	}

	title, searchCondition, events, err := service.GetAnalysisService().EventsExcel(c, req)
	if err != nil {
		global.SysLog.Error(err.Error())
		common.Fail(c, common.UnknownError)
		return
	}

	excel.ExportExcel(title, searchCondition, events, title, c, "", true)
	return
}

type IdsReq struct {
	Ids []string `form:"ids" json:"ids" binding:"required,min=1"`
}

// DeleteHistory godoc
// @Summary 删除历史调查记录
// @Schemes
// @Description 删除历史调查记录
// @Tags        Events
// @Produce     application/json
// @Param       req body IdsReq true "历史查询删除参数"
// @Success     200
// @Router      /v1/events/history [DELETE]
// @success     200 {object} common.Response{} "ok"
func DeleteHistory(c *gin.Context) {
	var req IdsReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetAnalysisService().DeleteHistory(c, req.Ids)
	if err != nil {
		global.SysLog.Error("delete err", zap.Error(err))
		common.Fail(c, common.DeleteHistoryError)
		return
	}
	common.Ok(c)
}

type IdReq struct {
	Id uint64 `form:"id" json:"id" binding:"required,min=1"`
}

// Condition godoc
// @Summary 历史调查条件
// @Schemes
// @Description 条件
// @Tags        Events
// @Produce     application/json
// @Success     200
// @Router      /v1/events/condition [GET]
// @success     200 {object} common.Response{data=model.FilterCondition} "application list"
func Condition(c *gin.Context) {
	data, err := service.GetAnalysisService().FilterCondition(c)
	if err != nil {
		global.SysLog.Error("delete err", zap.Error(err))
		common.Fail(c, common.GetConditionError)
		return
	}
	common.OkWithData(c, data)
}

// GetFileType godoc
// @Summary 文件类型
// @Schemes
// @Description 列表
// @Tags        Events
// @Produce     application/json
// @Success     200
// @Router      /v1/events/file_type [GET]
// @success     200 {object} common.Response{data=[]model.FileType} "application list"
func GetFileType(c *gin.Context) {
	data, err := service.GetAnalysisService().GetFileType(c)
	if err != nil {
		global.SysLog.Error("delete err", zap.Error(err))
		common.Fail(c, common.GetConditionError)
		return
	}
	common.OkWithData(c, data)
}

// GetActivityType godoc
// @Summary 外发通道类型
// @Schemes
// @Description 列表
// @Tags        Events
// @Produce     application/json
// @Success     200
// @Router      /v1/events/activity_type [GET]
// @success     200 {object} common.Response{data=[]model.ChannelTypeDB} "application list"
func GetActivityType(c *gin.Context) {
	builtIn := c.Query("built_in")
	data, err := service.GetAnalysisService().GetActivityType(c, builtIn)
	if err != nil {
		global.SysLog.Error("delete err", zap.Error(err))
		common.Fail(c, common.GetConditionError)
		return
	}
	common.OkWithData(c, data)
}

// GetFileTypes godoc
// @Summary 文件类型
// @Schemes
// @Description 列表
// @Tags        Events
// @Produce     application/json
// @Success     200
// @Router      /v1/events/file_types [GET]
// @success     200 {object} common.Response{data=[]model.FileType} "application list"
func GetFileTypes(c *gin.Context) {
	data, err := service.GetAnalysisService().GetFileTypes(c)
	if err != nil {
		global.SysLog.Error("delete err", zap.Error(err))
		common.Fail(c, common.GetConditionError)
		return
	}
	common.OkWithData(c, data)
}

// GetEventsCondition godoc
// @Summary 获取调查分析筛选条件
// @Schemes
// @Description 获取调查分析筛选条件
// @Tags        Events
// @Produce     application/json
// @Param       req body true "查询参数"
// @Success     200
// @Router      /v1/events/list [POST]
// @success     200 {object} common.Response{data=[]model.FileEvents} "response"
func GetEventsCondition(c *gin.Context) {
	var req dto.GetEventConditionReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.StartTime != "" && req.EndTime != "" {
		startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
		if err != nil {
			global.SysLog.Error(err.Error())
			common.Fail(c, common.ParamInvalidError)
			return
		}
		endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
		if err != nil {
			global.SysLog.Error(err.Error())
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if endTime.Before(startTime) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		req.StartT = startTime
		req.EndT = endTime
	}
	data, err := service.GetAnalysisService().GetEventCondition(c, req)
	if err != nil {
		global.SysLog.Error("query err", zap.Error(err))
		common.Fail(c, common.GetEventsError)
		return
	}
	common.OkWithData(c, data)
}

// GetHistoryList godoc
// @Summary 获取历史调查列表
// @Schemes
// @Description 获取历史调查列表
// @Tags        Events
// @Produce     application/json
// @Success     200
// @Router      /v1/events/history/list [GET]
// @success     200 {object} common.Response{} "history analysis list"
func GetHistoryList(c *gin.Context) {
	var req dto.GetHistoryListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetAnalysisService().GetHistoryList(c, req)
	if err != nil {
		global.SysLog.Error("delete err", zap.Error(err))
		common.Fail(c, common.GetConditionError)
		return
	}
	common.OkWithData(c, data)
}
