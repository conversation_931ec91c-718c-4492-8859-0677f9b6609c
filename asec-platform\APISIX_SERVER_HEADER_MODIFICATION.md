# APISIX Server 头部修改

## 功能概述

修改 APISIX 代理请求返回的 `Server` 头部，将其从默认的 `APISIX/3.6.0` 改为自定义的 `AsecHttpAgent/1.0`，以隐藏真实的服务器信息并提供统一的服务标识。

## 实现方案

### 1. 全局配置方式（推荐）

在 APISIX 的 `config.yaml` 中添加全局 nginx 配置，使用 `more_set_headers` 指令修改所有响应的 `Server` 头部。

### 2. 配置修改

**文件位置**：`deps/gateway/apisix/apisix_conf/config.yaml`

**修改内容**：
```yaml
nginx_config:
  error_log_level: "warn"
  envs:
    - LANG
    - LD_LIBRARY_PATH
  http_configuration_snippet: |
    # 全局修改 Server 头部
    more_set_headers "Server: AsecHttpAgent/1.0";
  http_server_configuration_snippet: |
    # 其他现有配置...
```

## 技术原理

### 1. more_set_headers 指令

`more_set_headers` 是 nginx 的 `headers-more-nginx-module` 模块提供的指令，用于设置或修改 HTTP 响应头部。

**语法**：
```nginx
more_set_headers "Header-Name: Header-Value";
```

**特点**：
- 可以设置、添加或删除响应头部
- 支持条件设置
- 优先级高于其他头部设置方式

### 2. 配置层级

- `http_configuration_snippet`：在 nginx 的 `http` 块中添加配置
- 影响范围：所有虚拟主机和位置
- 执行时机：在响应发送前修改头部

## 配置效果

### 修改前
```http
HTTP/1.1 200 OK
Server: APISIX/3.6.0
Content-Type: application/json
Content-Length: 123
...
```

### 修改后
```http
HTTP/1.1 200 OK
Server: AsecHttpAgent/1.0
Content-Type: application/json
Content-Length: 123
...
```

## 安全优势

### 1. 信息隐藏
- ✅ 隐藏真实的服务器软件信息
- ✅ 防止基于版本的攻击
- ✅ 减少信息泄露风险

### 2. 统一标识
- ✅ 提供统一的服务标识
- ✅ 便于服务识别和管理
- ✅ 符合企业品牌要求

### 3. 合规要求
- ✅ 满足安全合规要求
- ✅ 符合信息安全标准
- ✅ 降低安全风险评级

## 替代方案

### 1. 插件方式

如果需要更细粒度的控制，可以使用 `response-rewrite` 插件：

```json
{
  "response-rewrite": {
    "headers": {
      "set": {
        "Server": "AsecHttpAgent/1.0"
      }
    }
  }
}
```

**优点**：
- 可以针对特定路由配置
- 支持条件设置
- 更灵活的控制

**缺点**：
- 需要在每个路由中配置
- 配置复杂度较高
- 可能遗漏某些路由

### 2. 自定义插件方式

使用现有的 `asec-response-rewrite` 插件：

```json
{
  "asec-response-rewrite": {
    "headers": {
      "Server": "AsecHttpAgent/1.0"
    }
  }
}
```

### 3. Lua 脚本方式

在 nginx 配置中使用 Lua 脚本：

```nginx
header_filter_by_lua_block {
    ngx.header["Server"] = "AsecHttpAgent/1.0"
}
```

## 部署步骤

### 1. 修改配置文件
```bash
# 编辑 APISIX 配置文件
vim deps/gateway/apisix/apisix_conf/config.yaml

# 添加 http_configuration_snippet 配置
```

### 2. 重启 APISIX 服务
```bash
# 重启 APISIX 容器或服务
docker-compose restart apisix

# 或者重新加载配置
apisix reload
```

### 3. 验证配置
```bash
# 测试请求，检查 Server 头部
curl -I http://your-apisix-host/

# 预期输出包含：
# Server: AsecHttpAgent/1.0
```

## 验证方法

### 1. 命令行验证
```bash
# 使用 curl 检查响应头
curl -I http://localhost:80/

# 使用 wget 检查响应头
wget --server-response --spider http://localhost:80/

# 使用 httpie 检查响应头
http HEAD http://localhost:80/
```

### 2. 浏览器验证
1. 打开浏览器开发者工具
2. 访问任意通过 APISIX 代理的页面
3. 查看 Network 标签页中的响应头
4. 确认 `Server` 头部为 `AsecHttpAgent/1.0`

### 3. 自动化测试
```bash
#!/bin/bash
# 自动化验证脚本

EXPECTED_SERVER="AsecHttpAgent/1.0"
ACTUAL_SERVER=$(curl -s -I http://localhost:80/ | grep -i "^server:" | cut -d' ' -f2- | tr -d '\r\n')

if [ "$ACTUAL_SERVER" = "$EXPECTED_SERVER" ]; then
    echo "✅ Server 头部配置正确: $ACTUAL_SERVER"
else
    echo "❌ Server 头部配置错误: 期望 '$EXPECTED_SERVER', 实际 '$ACTUAL_SERVER'"
fi
```

## 注意事项

### 1. 模块依赖
- 确保 nginx 编译时包含 `headers-more-nginx-module` 模块
- APISIX 默认包含此模块，无需额外安装

### 2. 配置语法
- 注意 YAML 格式的缩进
- `more_set_headers` 指令需要分号结尾
- 头部值需要用引号包围

### 3. 性能影响
- 全局头部修改对性能影响极小
- 比插件方式性能更好
- 不会增加明显的延迟

### 4. 兼容性
- 适用于所有 APISIX 版本
- 兼容所有 HTTP 协议版本
- 不影响现有功能

## 故障排除

### 1. 配置不生效
**问题**：修改后 Server 头部仍然是 APISIX/3.6.0

**解决方案**：
1. 检查 YAML 格式是否正确
2. 确认配置文件路径正确
3. 重启 APISIX 服务
4. 检查 nginx 错误日志

### 2. 语法错误
**问题**：APISIX 启动失败

**解决方案**：
1. 检查 YAML 缩进
2. 确认指令语法正确
3. 查看 APISIX 启动日志
4. 验证配置文件格式

### 3. 模块缺失
**问题**：提示 `more_set_headers` 指令未知

**解决方案**：
1. 确认 APISIX 版本支持
2. 检查 nginx 模块列表
3. 使用替代方案（插件方式）

## 总结

通过在 APISIX 配置中添加全局的 `more_set_headers` 指令，成功实现了：

- ✅ **统一修改**：所有响应的 Server 头部都被修改为 `AsecHttpAgent/1.0`
- ✅ **安全提升**：隐藏了真实的服务器软件信息
- ✅ **配置简单**：只需修改一处配置，影响全局
- ✅ **性能优秀**：nginx 原生指令，性能影响极小
- ✅ **维护方便**：集中配置，易于管理

这个修改提高了系统的安全性，符合信息安全最佳实践，同时提供了统一的服务标识。
