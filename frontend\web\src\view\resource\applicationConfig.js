export const detailscolumnsconfig = (type, rewrite) => {
  let web = [
    {
      label: '应用名称：',
      value: 'app_name',
    }, {
      label: '应用图标：',
      value: 'icon_url',
      type: 'image'
    }, {
      label: '应用类型：',
      value: 'app_type',
      type: 'status'
    },
    {
      label: '应用状态:',
      value: 'app_status',
      type: 'status'
    },
    {
      label: '发布网关：',
      value: 'bind_se',
    }, {
      label: '应用标签：',
      value: 'group_relationship',
    },
    {
      label: '服务器地址：',
      value: 'server_address',
      type: ''
    }, {
      label: '发布地址：',
      value: 'publish_address',
      type: ''
    }, {
      label: '应用路径：',
      value: 'uri',
      type: ''
    }, {
      label: 'url智能重写：',
      value: 'default_rule',
    },
    {
      label: 'url手动重写：',
      value: 'url_manual_rewrite',
    },
    {
      label: '依赖站点：',
      value: 'depend_site',
    },
    {
      label: '请求/响应头改写：',
      value: 'header_config',
    },
    {
      label: '应用中心首页地址：',
      value: 'web_url',
    },{
      label: '门户显示名称：',
      value: 'portal_show_name',
    }, {
      label: '应用描述：',
      value: 'portal_desc',
    },
    {
      label: '单点登录：',
      value: 'single_sign_on',
    },
    {
      label: '健康检测：',
      value: 'health_config',
    },
    {
      label: 'URL路径白名单：',
      value: 'url_control',
    },
  ]

  if (!rewrite) {
    const cw = JSON.parse(JSON.stringify(web))
    web = cw.filter(item => item.value !== 'header_config')
  }

  const app = [
    {
      label: '应用名称：',
      value: 'app_name',
    }, {
      label: '应用图标：',
      value: 'icon_url',
      type: 'image'
    }, {
      label: '应用类型：',
      value: 'app_type',
      type: 'status'
    },
    {
      label: '应用状态:',
      value: 'app_status',
      type: 'status'
    },
    {
      label: '发布网关：',
      value: 'bind_se',
    }, {
      label: '应用标签：',
      value: 'group_relationship',
    },
    {
      label: '应用地址：',
      value: 'app_addresses',
      type: ''
    },
    {
      label: '应用中心首页地址：',
      value: 'web_url',
    },{
      label: '门户显示名称：',
      value: 'portal_show_name',
    }, {
      label: '应用描述：',
      value: 'portal_desc',
    },
    {
      label: '单点登录：',
      value: 'single_sign_on',
    },
    {
      label: '健康检测：',
      value: 'health_config',
    },
  ]

  const portal = [
    {
      label: '应用名称：',
      value: 'app_name',
    }, {
      label: '应用图标：',
      value: 'icon_url',
      type: 'image'
    }, {
      label: '应用类型：',
      value: 'app_type',
      type: 'status'
    },
    {
      label: '应用状态:',
      value: 'app_status',
      type: 'status'
    },
   {
      label: '应用标签：',
      value: 'group_relationship',
    },
    {
      label: '应用中心首页地址：',
      value: 'web_url',
    },{
      label: '门户显示名称：',
      value: 'portal_show_name',
    }, {
      label: '应用描述：',
      value: 'portal_desc',
    }
  ]

  return type === 'web' ? web : (type === 'tun'? app : portal )
}

export const tableColumnsConfig = () => {
  return [
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 46,
    },
    {
      colKey: 'name',
      title: '应用',
      sortType: 'all',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
        fontSize: '12px'
      },
    },
    {
      colKey: 'application_address',
      title: '应用地址',
    },
    {
      colKey: 'publish_endpoint',
      title: '发布地址',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      colKey: 'sdp_list',
      title: '发布网关',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
      cell: (h, { row }) => {
        return row.sdp_list?.join(',') || '未发布'
      },
    },
    {
      title: '应用状态',
      colKey: 'app_status',
      width: 100,
      cell: (h, { row }) => {

        const app_status = Number(row.app_status ?? 1)
        return h(
          't-tag',
          {
            style: {
              background: app_status === 1 ? '#E8F3FF' : '#FFF0ED',
              color: app_status === 1 ? '#0052D9' : '#E34D59'
            }
          },
          app_status === 1 ? '启用' : (app_status === 2 ? '维护中' :"禁用")
        )
      }
    },
    {
      colKey: 'access_strategy',
      title: '关联策略',
      width: 100,
      // cell: (h, { row }) => {
      //   return h('t-tag', { id: 'cy', class: 't-pagination__total', innerHTML: `${row.access_strategy?.length || '-'}` }, [])
      // },
    },
    {
      colKey: 'operation',
      title: '操作',
      width: '150',
      foot: '-',
      fixed: 'right'
    },
  ]
}

export const formColumnsConfig = (sdpOptions, groupOptions) => {
  return [
    {
      label: '',
      name: '',
      type: 'info',
      placeholder: '自定义应用添加后默认阻止所有访问，配置策略后才允许访问',
    },
    {
      label: '应用名称',
      name: 'app_name',
      type: 'input',
      len: 128,
      placeholder: '请输入应用名称',
    },
    {
      label: '应用图标（支持png、jpg格式，建议图片大小48*48像素）',
      name: 'icon_url',
      type: 'upload',
      uploadProps: {
        accept: 'image/*',
        format: ['jpg', 'jpeg', 'png'],
        maxSize: 2048,
        multiple: false,
        defaultIcon: '/assets/default-app-icon.png'
      },
      placeholder: '请上传应用图标'
    },
    {
      label: '访问方式',
      name: 'web_access',
      type: 'switch',
      trueText: '开启',
      falseText: '关闭',
      switchName: '无客户端访问',
      switchDesc: '(Web类型应用，开启该选项后，通过浏览器即可访问)'
    },
    {
      label: '应用地址',
      name: 'app_sites',
      type: 'table',
      title: '主机',
      len: 128,
      options: [{
        label: 'TCP',
        value: 'tcp',
      }, {
        label: 'UDP',
        value: 'udp',
      }, {
        label: 'ALL',
        value: 'all',
      }],
      address: [
        {
          required: true,
          message: '服务器地址不能为空',
          trigger: 'blur',
        }
      ],
      placeholder: {
        tcp_port: '请输入端口或者端口范围（例如443,443-445）',
        udp_port: '请输入端口或者端口范围（例如443,443-445）',
      },
      pre: {
        tcp_port: 'TCP:',
        udp_port: 'UDP:',
      },
    },
    // {
    //   label: '',
    //   name: 'app_address',
    //   type: 'asdList',
    //   title: '主机',
    //   placeholder: '域名/泛域名/IP/IP范围/网段',
    // },
    // {
    //   label: '协议和端口',
    //   name: 'port',
    //   type: 'moreInpu',
    //   title: '主机',
    //   len: 128,
    //   placeholder: {
    //     tcp_port: '请输入端口或者端口范围（例如443,443-445）',
    //     udp_port: '请输入端口或者端口范围（例如443,443-445）',
    //   },
    //   pre: {
    //     tcp_port: 'TCP:',
    //     udp_port: 'UDP:',
    //   },
    // },
    {
      label: '',
      name: 'web_url',
      type: 'radio_input',
      title: '门户中心首页地址',
      placeholder: '如/home.html，在用户门户中心点击应用图标时打开的首地址',
    },
    {
      label: '发布网关',
      name: 'sdp_list',
      type: 'select',
      options: sdpOptions,
      optionsconfig: {
        key: 'appliance_id',
        value: 'appliance_id',
        label: 'app_name'
      },
      placeholder: '请选择',
    },
    {
      label: '应用标签',
      name: 'group_ids',
      type: 'asd_add_select',
      options: groupOptions,
      optionsconfig: {
        key: 'id',
        value: 'id',
        label: 'name'
      },
      placeholder: '请选择',
    },
    {
      type: 'radio-group',
      label: '应用状态',
      field: 'app_status',
      defaultValue: 1,
      options: [
        { label: '启用', value: 1 },
        { label: '维护', value: 2 }
      ]
    },
  ]
}

export const webformColumnsConfig = (options, groupOptions, accessingOptions) => {
  return [
    {
      label: '',
      name: '',
      type: 'info',
      placeholder: '自定义应用添加后默认阻止所有访问，配置策略后才允许访问',
    },
    {
      label: '应用名称',
      name: 'app_name',
      type: 'input',
      len: 128,
      placeholder: '请输入应用名称',
    },
    {
      label: '应用图标（建议上传正方形图片48*48，大小不超过2MB）',
      name: 'icon_url',
      type: 'upload',
      uploadProps: {
        accept: 'image/*',
        format: ['jpg', 'jpeg', 'png', 'gif'],
        maxSize: 2048,
        multiple: false,
        defaultIcon: '/assets/default-app-icon.png'
      }
    },
    {
      label: '访问方式',
      name: 'web_access',
      type: 'switch',
      trueText: '开启',
      falseText: '关闭',
      switchName: '无客户端访问',
      switchDesc: '(Web类型应用，开启该选项后，通过浏览器即可访问)'
    },
    {
      label: '',
      name: 'first_accessing',
      type: 'checkbox-select',
      placeholder: '首次访问该应用时需进行认证',
      options: accessingOptions,
    },
    {
      label: '服务器地址',
      name: 'server_address',
      selectName: 'server_schema',
      type: 'select_input',
      len: 128,
      placeholder: 'www.domain.com:443或*************',
    },
    {
      label: '发布地址',
      name: 'publish_address',
      ifCopy: true,
      selectName: 'publish_schema',
      type: 'select_input',
      len: 128,
      placeholder: '请输入发布地址',
    },
    {
      label: '',
      name: '',
      type: 'shrinkinfo',
      title: '自定义发布地址',
      placeholder: `用户自定义的应用发布地址，请按以下步骤操作： 步骤1：请前往域名管理平台，将应用发布地址解析到应用发布网关地址 步骤2：配置应用https证书`,
    },
    {
      label: '',
      name: '',
      type: 'shrinkcustom',
      title: '浏览器访问兼容配置',
      placeholder: `用户自定义的应用发布地址，请按以下步骤操作： 步骤1：请前往域名管理平台，将应用发布地址解析到应用发布网关地址 步骤2：配置应用https证书`,
    },
    {
      label: '发布网关',
      name: 'sdp_list',
      type: 'select',
      options: options,
      optionsconfig: {
        key: 'appliance_id',
        value: 'appliance_id',
        label: 'app_name'
      },
      placeholder: '请选择网关',
    },
    {
      label: '应用标签',
      name: 'group_ids',
      type: 'asd_add_select',
      options: groupOptions,
      optionsconfig: {
        key: 'id',
        value: 'id',
        label: 'name'
      },
      placeholder: '请选择',
    },
    {
      type: 'radio-group',
      label: '应用状态',
      field: 'app_status',
      defaultValue: 1,
      options: [
        { label: '启用', value: 1 },
        { label: '维护', value: 2 }
      ]
    },
  ]
}

export const formRules = () => {
  return {
    app_name: [
      {
        required: true,
        message: '应用名称必填',
        type: 'error',
        trigger: 'blur'
      },
      {
        required: true,
        message: '应用名称必填',
        type: 'error',
        trigger: 'blur'
      },
      {
        whitespace: true, message: '应用名称不能为空'
      },
      { max: 128, message: '应用长度不能超过128个字符', type: 'error', trigger: 'blur' },
      { max: 128, message: '应用长度不能超过128个字符', type: 'error', trigger: 'blur' },
    ],
    server_address: [
      {
        required: true,
        message: '服务器地址必填',
        type: 'error',
        trigger: 'blur'
      },
      {
        required: true,
        message: '服务器地址必填',
        type: 'error',
        trigger: 'blur'
      },
    ],
    publish_address: [
      {
        required: true,
        message: '发布地址必填',
        type: 'error',
        trigger: 'blur'
      },
      {
        required: true,
        message: '发布地址必填',
        type: 'error',
        trigger: 'blur'
      },
    ],
    sdp_list: [
      {
        required: true,
        message: '发布网关必选',
        type: 'error',
        trigger: 'change'
      },
    ],
    app_address: [
      { validator: app_address_validator,  trigger: 'blur' }
    ],
    app_app_sites: [
      { validator: app_address_validator,  trigger: 'blur' }
    ],
    port: [
      {
        required: true,
        message: '端口必填',
        type: 'error',
        trigger: 'blur'
      },
      {
        required: true,
        message: '端口必填',
        type: 'error',
        trigger: 'blur'
      },
      { validator: port_validator, trigger: 'blur' }
    ],
    icon_url: [
      {
        required: false,
        trigger: 'change'
      }
    ],
  }
}

const app_address_validator = (val) => {
  if (val.length > 0 && val[0] !== '') {
    return { result: true, type: 'success' }
  }
  return { result: false, message: '应用地址至少添加一条！', type: 'error' }
}

const port_validator = (val) => {
  if (val.tcp_port || val.udp_port) {
    return { result: true, type: 'success' }
  }
  return { result: false, message: 'TCP或UDP端口不能为空！', type: 'error' }
}