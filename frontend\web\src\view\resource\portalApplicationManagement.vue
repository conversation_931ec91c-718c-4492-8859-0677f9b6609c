<template>
  <div class="application">
    <t-tabs v-model="application_tabs" class="one-tabs">
      <t-tab-panel :value="1" label="门户应用管理" :destroy-on-hide="false" style="background: #EEEEEE">
        <div :class="isAction?'top-title-d':'top-title'"/>
        <div class="content" :style="isAction?'margin-top: -1px;height: calc(100vh - 112px)': ''">
          <div
              class="shrink"
              :style="isAction?'top: -12px':''"
              style="display: none"
              @click="upshrink"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
          >
            <svg class="icon" style="font-size: 12px" aria-hidden="true">
              <use :xlink:href="shrink_icon"/>
            </svg>
          </div>
          <t-layout class="frame" style="height: 100%">
            <t-aside v-if="!showAside"
                     style="border-right: 1px solid #EBEBEB;border-radius: 4px 0 0 4px;width: 33px;height: 100%;margin: 0px"
                     class="zk">
              <t-popup content="展开" :show-arrow="true" placement="bottom">
                <div class="fold-aside-content" @click="showAside = true">
                  <i class="iconfont icon-fenyefuyou" style="font-size: 12px"/>
                </div>
              </t-popup>
            </t-aside>
            <t-aside v-else style="border-right: 1px solid #EBEBEB;border-radius: 4px 0 0 4px;width: 250px">
              <AppAsideMenu
                  :data="groupOptions"
                  :policy-group-data="policyGroupData"
                  :add="addGroup"
                  :edit="editGroup"
                  :del="deleGroup"
                  :move="handleMoveGroup"
                  :update-group-sort="updateGroupSort"
                  :get-data="getGroupList"
                  :tree-item-click="selectGroup"
                  :keys="{ value: 'id', label: 'name', children: 'child_group' }"
                  :fold="flodClick"
              />
            </t-aside>
            <t-layout>
              <t-header class="asdc-header">

                <t-button variant="outline" class="asdc-default-but" @click="getDataList">
                  <template #icon>
                    <i class="iconfont icon-shuaxin"/>
                  </template>
                  刷新
                </t-button>
                <el-input
                    v-model="search"
                    clearable
                    placeholder="请输入关键字搜索"
                    class="asdc-search"
                    style="margin-top: 17px;width: 289px"
                    @enter="getDataList"
                    @clear="getDataList"
                >
                  <template #prepend>
                    <el-select v-model="search_select" placeholder="全部" style="width: 95px;background: #ffffff">
                      <el-option label="全部" value="all"/>
                      <el-option label="应用名称" value="APP_NAME"/>
                      <el-option label="应用地址" value="SERVER_ADDRESS"/>
                      <el-option label="发布地址" value="PUBLISH_ADDRESS"/>
                      <el-option label="应用网关" value="SDP"/>
                    </el-select>
                  </template>
                  <template #append>
                    <el-button type="primary" @click="getDataList"
                               style="
                               background: #0052D9;
                               border-top-right-radius: 4px;
                               border-bottom-right-radius: 4px;
                               border-bottom-left-radius: 0px;
                               border-top-left-radius: 0px;
                                color: #FFFFFF"
                               :icon="Search" />
                  </template>
                </el-input>
                <el-select
                    v-model="application"
                    class="m-2"
                    placeholder="全部应用"
                    size="small"
                    style="width: 100px;margin-right: 10px;float: right;margin-top: 17px"
                    @change="getDataList"
                >
                  <el-option label="全部应用" value="all"/>
                  <el-option label="Web应用" value="web"/>
                  <el-option label="隧道应用" value="tun"/>
                </el-select>
              </t-header>
              <t-content
                  style="background: #ffffff;padding: 0px 18px 0px 18px;display: inline-flex; "
                  :style="isAction?'height: calc(100vh - 200px);width:100%':'height: calc(100vh - 400px)'"
              >
                <t-table
                    height="calc(100% - 69px)"
                    vertical-align="top"
                    row-key="id"
                    :sort="sort"
                    resizable
                    hover
                    :loading="loading"
                    disable-data-page="true"
                    :columns="columns"
                    :data="data"
                    :selected-row-keys="selectedRowKeys"
                    :pagination="pagination"
                    :pagination-affixed-bottom="true"
                    :reserve-selected-row-on-paginate="true"
                    row-class-name="table-row"
                    @page-change="onPageChange"
                    @select-change="onSelectChange"
                >
                  <template #empty>
                    <div class="t-table-em" style="text-align: center;">
                      <img class="empty-img" src="@/assets/empty.png">
                      <div>暂无数据</div>
                    </div>
                  </template>

                  <template #app_name="{ row }">
                    <svg class="icon" style="font-size: 12px;margin-right: 5px" aria-hidden="true">
                      <use :xlink:href="row.app_type === 'web'?'#icon-webyingyong':'#icon-yingyong1'"/>
                    </svg>
                    {{ row.name || row.portal_show_name || row.app_name || '-' }}
                  </template>

                  <template #app_type="{ row }">
                    <span>{{ row.app_type === 'web' ? 'Web应用' : ( row.app_type === 'tun' ? '隧道应用' :"门户应用") }}</span>
                  </template>

                  <template #application_address="{ row }">
                    <span>{{ formatApplicationAddress(row) }}</span>
                  </template>

                  <template #group_name="{ row }">
                    <span>{{ row.group_name || '默认分类' }}</span>
                  </template>

                  <template #app_status="{ row }">
                    <t-tag :style="{
                      background: row.app_status === 1 ? '#E8F3FF' : '#FFF0ED',
                      color: row.app_status === 1 ? '#0052D9' : '#E34D59'
                    }">
                      {{ row.app_status === 1 ? '启用' : (row.app_status === 2 ? '维护中' : '禁用') }}
                    </t-tag>
                  </template>

                  <template #portal_visible="{ row }">
                    <t-tag :style="{
                      background: row.show_status ? '#E8F3FF' : '#F3F3F3',
                      color: row.show_status ? '#0052D9' : '#999999'
                    }">
                      {{ row.show_status ? '可见' : '隐藏' }}
                    </t-tag>
                  </template>

                  <template #operation="{ row }">
                    <t-link
                        v-if="isEditVal(route.meta.title)"
                        theme="primary"
                        hover="color"
                        class="font-class operation"
                        style="padding: 0px 5px;margin-right: 8px;border-radius: 4px;font-size: 12px"
                        @click="upData(row)"
                    >
                      编辑
                    </t-link>
                  </template>

                </t-table>
              </t-content>
            </t-layout>
          </t-layout>
        </div>
      </t-tab-panel>
    </t-tabs>
 

    <!--    新增编辑抽屉-->
    <t-drawer v-model:visible="opvisible" size="600" destroy-on-close :close-btn="true">
      <template #header>
        <div style="font-size: 14px;font-width: 500;">{{ formData.id === '' ? '新增应用' : '编辑应用' }}</div>
      </template>
      <t-space direction="vertical" style="width: calc(100% - 16px);margin-left: 9px">

        <AppDrawer
            @refreshGroup="getGroupList"
            :key="componentKey"
            :sdp-options="sdpOptions"
            :api-data="formData" ref="appDraw"
            :group-options="groupOptions[0]"
            :add-app-type="addAppType"
        ></AppDrawer>
      </t-space>
      <template #footer>
        <t-button class="asdc-primary-but"  :disabled="submitting" style="float: right;width: 80px" @click="onSubmit">确定</t-button>
        <t-button variant="outline" style="width: 80px;" @click.stop="opvisible = false"> 取消</t-button>
      </template>
    </t-drawer>
  </div>
</template>

<script>
export default {
  name: 'ApplicationManagement'
}
</script>
<script setup>
import {h, reactive, ref, watch, computed, onMounted} from 'vue'
import { Search } from '@element-plus/icons-vue'
import {useRouter, useRoute} from 'vue-router'
import {getIdpList} from "@/api/auth";
import AppDrawer from "./appDrawer.vue";

import {
  formColumnsConfig,
  webformColumnsConfig
} from './applicationConfig'
import AppAsideMenu from '@/components/asideMenu/appAsideMenu.vue'
import {
  addApp,
  getApplicationList,
  getResourceGroup,
  upApp,
  getApp,
  createGroup,
  updateGroup,
  delGroup,
  MoveApplicationGroup,
  UpdateGroupSort
} from '@/api/resource'
import {ElMessage} from 'element-plus'
import {getAgentsList} from '@/api/agents'
import {MessagePlugin} from 'tdesign-vue-next'
import { isEditVal } from '@/utils/limite'
const route = useRoute()

const router = useRouter()
const application_tabs = ref(1)
const opvisible = ref(false)
const search = ref('')
const search_select = ref('all')
const application = ref('all')
let formData = reactive({
})
const componentKey = ref(0)
const sort = ref({
  sortBy: 'status',
  descending: true,
})
// 格式化应用地址显示
const formatApplicationAddress = (row) => {
  // 如果是Web应用，优先显示web_url
  if (row.app_type === 'web' && row.web_url) {
    return row.web_url
  }

  // 如果application_address是数组，取第一个元素
  if (Array.isArray(row.application_address)) {
    return row.application_address[0] || '-'
  }

  // 如果application_address是字符串
  if (row.application_address) {
    // 尝试解析JSON数组格式的字符串
    try {
      const parsed = JSON.parse(row.application_address)
      if (Array.isArray(parsed)) {
        return parsed[0] || '-'
      }
      return row.application_address
    } catch {
      return row.application_address
    }
  }

  // 最后尝试其他字段
  return row.server_address || row.web_url || '-'
}



const isAction = ref(true)
const shrink_icon = ref('#icon-heiseshang')
const upshrink = () => {
  isAction.value = !isAction.value
  shrink_icon.value = isAction.value ? '#icon-heisexia' : '#icon-heiseshang'
}

const handleMouseEnter = () => {
  if (isAction.value) {
    shrink_icon.value = '#icon-jiantouxia'
  } else {
    shrink_icon.value = '#icon-jiantoushang'
  }
}

const handleMouseLeave = () => {
  if (isAction.value) {
    shrink_icon.value = '#icon-heisexia'
  } else {
    shrink_icon.value = '#icon-heiseshang'
  }
}

const loading = ref(false)
const data = ref([])
const selectedRowKeys = ref([])
const columns = [
  {
    title: '应用名称',
    colKey: 'app_name',
    width: 120,
    ellipsis: true
  },
  {
    title: '应用类型',
    colKey: 'app_type',
    width: 100
  },
  {
    title: '应用地址',
    colKey: 'application_address',
    width: 200,
    ellipsis: true
  },
  {
    title: '应用标签',
    colKey: 'group_name',
    width: 200,
    ellipsis: true
  },
  {
    title: '应用状态',
    colKey: 'app_status',
    width: 80
  },
  {
    title: '门户可见',
    colKey: 'portal_visible',
    width: 80
  },
  {
    title: '操作',
    colKey: 'operation',
    width: 100,
    fixed: 'right'
  }
]

const details_columns = ref([])
const form_config = ref([])

const total = ref(0)
const pagination = reactive({
  current: 1,
  pageSize: 50,
  total: total,
  pageSizeOptions: [50, 100, 150, 200],
  paginationAffixedBottom: true,
  showJumper: true,
  totalContent: () => {
    return [
      h('div', {id: 'foo', class: 't-pagination__total', innerHTML: `共${total.value}条`}, []),
    ]
  }
})

const onPageChange = (pageInfo, context) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
  getDataList('next')
}

const onSelectChange = (value) => {
  selectedRowKeys.value = value
}

const selectGroupId = ref(0)
const getDataList = async (type) => {
  if (type !== 'next') {
    pagination.current = 1
    pagination.pageSize = 50
  }
  loading.value = true
  const query = {
    limit: pagination.pageSize,
    offset: (pagination.current - 1) * pagination.pageSize,
    search: search.value.trim(),
    group_id: Number(selectGroupId.value)
  }
  if (application.value !== 'all') {
    query.app_type = application.value
  }
  if (search_select.value !== 'all') {
    query.search_columns = [search_select.value]
  }
  const res = await getApplicationList(query)
  if (res.status === 200 && res.data.code !== -1) {
    data.value = res.data.data.data || []
    total.value = res.data.data.total_num || 0
    loading.value = false
  } else {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
    loading.value = false
  }
}

getDataList()

const setInitialFormData = () => {
  formData= {}
  formData.id = ''
  addAppType.value = ''
}

// 获取标签列表
const groupOptions = ref([])
const policyGroupData = computed(() => {
  return groupOptions.value?.[0]?.child_group?.filter(item =>
      item.name !== '全部标签' && item.name !== '默认分类'
  ) || []
})

const getGroupList = async (name) => {
  const res = await getResourceGroup({need_default: true, name: name})
  if (res.status === 200) {
    groupOptions.value = res.data.data
    console.log('分组数据已更新:', groupOptions.value)
  } else {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
  }
}

// 监听 groupOptions 的变化
watch(groupOptions, (newVal) => {
  console.log('groupOptions updated:', newVal)
}, { deep: true })

// 组件挂载时获取数据
onMounted(() => {
  getGroupList()
})

const addGroup = async (value) => {
  if (!value) {
    ElMessage({
      customClass: 'hint',
      type: 'error',
      message: '标签名称不能为空',
    })
    return true
  }
  const res = await createGroup({
    group_name: value.replace(/(^\s*)|(\s*$)/g, ''),
  })
  if (res.status === 200 && res.data.code !== -1) {
    await getGroupList()
    return false
  } else {
    ElMessage({
      customClass: 'hint',
      type: 'error',
      message: res.data.msg,
    })
    return true
  }
}

const editGroup = async (value, row) => {
  const res = await updateGroup({
    id: row.id,
    group_name: value.replace(/(^\s*)|(\s*$)/g, ''),
  })
  if (res.status === 200 && res.data.code !== -1) {
    await getGroupList()
    return false
  } else {
    ElMessage({
      type: 'error',
      message: res.data.msg,
    })
    return true
  }
}

const a = ref(1)
const deleGroup = async (value, row) => {
  a.value++
  const res = await delGroup({
    id: row.id,
  })
  if (res.status === 200 && res.data.code !== -1) {
    await getGroupList()
    return false
  } else {
    await MessagePlugin.error(res.data.msg)
    return true
  }
}

// applicationManagement.vue
const handleMoveGroup = async ( position, groupId, targetId ) => {
  try {
    // 参数转换映射
    const params = {
      group_id: Number(groupId),          // 接口要求的参数名
      target_id: Number(targetId),  // 接口要求的参数名
      position: position // 根据接口文档映射值
    }

    const res = await MoveApplicationGroup(params)

    if (res.data.code === 0) {
      await MessagePlugin.success('移动成功')
      await getGroupList()
      await getDataList()
    } else {
      throw new Error(res.data.msg)
    }
  } catch (err) {
    await MessagePlugin.error(err.message || '移动失败')
    throw err
  }
}

const updateGroupSort = async (groups) => {
  try {
    const res = await UpdateGroupSort({ groups })
    if (res.data.code === 0) {
      await getGroupList()
    } else {
      throw new Error(res.data.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存分组排序失败:', error)
    await MessagePlugin.error('保存分组排序失败')
    throw error
  }
}
// 在打开移动对话框时处理参数

const selectGroup = (value) => {
  pagination.current = 1
  pagination.pageSize = 50
  selectGroupId.value = value.node.value
  getDataList()
}

// 获取网关列表
const getSDPList = async () => {
  const query = {
    limit: 500,
    offset: 0,
  }
  const res = await getAgentsList(query)
  if (res.data.code === 0) {
    return res.data?.data?.rows
  }
  return []
}
const sdpOptions = ref([])
const addAppType = ref('')

const appDraw = ref()
const submitting = ref(false)
const onSubmit = async (type) => {
  if (submitting.value) return // 防止重复点击
  try {
    submitting.value = true
    // 调用子组件的 validate 方法
    const isSubValid = await appDraw.value?.validate()
    if (isSubValid !== true) {
      console.log('数据校验失败')
      return false
    }
    // 获取子组件中的 formData
    const childFormData = appDraw.value.getDrawerData()
    console.log(childFormData)

    childFormData.sdp_list = [childFormData.sdp]

    let res = ''
    res = formData.id ? await upApp(childFormData) : await addApp(childFormData)
    if (res.data.code !== 0) {
      await MessagePlugin.error(res.data.msg)
      return
    }
    await MessagePlugin.info(formData.id ? '修改成功!' : '新增成功')
    if (type === 'skip') {
      await router.push({ name: 'realTimeRules' })
      return
    }
    opvisible.value = false
    await getGroupList()
    await getDataList()
  } catch (error) {
    console.error(error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

const idp_list = ref([])
const get_idp_list = async(id) => {
  const res = await getIdpList({ rootGroupId: id })
  if (res?.status === 200) {
    idp_list.value = res.data
  }
}

const upData = async (row) => {
  opvisible.value  = false
  componentKey.value += 1
  setInitialFormData()
  await get_idp_list()
  sdpOptions.value = await getSDPList()

  const res = await getApp({id: row.id})
  if (res.data.code !== 0) {
    await MessagePlugin.error(res.data.msg)
    return
  }
  formData = res.data.data
  addAppType.value = 'portal'
  formData.id = row.id.toString()
  if (formData.app_type === 'portal'){
    formData.sdp_list = []//网关回显
  }else{
    formData.sdp_list = formData.bind_se.map(obj => obj.appliance_id) //网关回显
  }

  formData.group_ids = formData.group_relationship?.map(obj => obj.id)
  formData.isPath = formData.uri !== ''
  if (formData.app_type === 'web'){
    formData.default_rule = formData.web_compatible_config.default_rule.includes('url_smart_rewrite')
    formData.manual_rule = formData.web_compatible_config.default_rule.includes('url_manual_rewrite')
    formData.depend_site = formData.web_compatible_config.default_rule.includes('depend_site')
    formData.isPath = formData.web_compatible_config.default_rule.includes('uri')
    if (!formData.isPath){
      formData.uri = '/*'
    }
    const weburl = formData.publish_schema + '://' + formData.publish_address
    formData.web_url_web = formData.web_url.replace(weburl, '')
    // 处理表单代填启用标志回显
    if (formData.web_compatible_config.default_rule.includes('single_sign_on') &&
        formData.web_compatible_config.single_sign_on.type === 'fill_forms') {
      formData.form_fill_enabled = 1
    } else {
      formData.form_fill_enabled = 0
    }
  }
  opvisible.value = true
}

const visible = ref(false)
const appDetail = ref({})




const showAside = ref(true)
// 侧边栏折叠
const flodClick = () => {
  showAside.value = false
}

watch(() => formData.web_access, () => {
  get_idp_list()
  form_config.value = formData.web_access ? webformColumnsConfig(sdpOptions, groupOptions.value[0]?.child_group, idp_list?.value?.assistIdpList) : formColumnsConfig(sdpOptions, groupOptions.value[0]?.child_group)
}, {immediate: true})
</script>
<style lang="scss" scoped>
.application {
  padding: 0px 0px !important;

  .top-title {
    height: 222px;
    width: 100%;
    margin-bottom: 10px;
    background: #ffffff;
  }
  .el-form-item{
    margin-bottom: 9px;
  }
  .top-title-d {
    margin-bottom: 12px;
  }

  .t-tabs {
    .t-tabs__content {
      overflow: visible;
    }
  }

  .content {
    margin: 0px 12px;
    border-radius: 4px;
    border: 1px solid #EBEBEB;
    overflow: initial;
    height: calc(100vh - 330px);

    .shrink {
      position: absolute;
      font-size: 10px;
      z-index: 999;
      width: 28px;
      height: 28px;
      border-radius: 100px;
      border: 1px solid #EBEBEB;
      left: 49%;
      line-height: 24px;
      background: #ffffff;
      top: 202px;
      text-align: center;

      .icon {
        vertical-align: -0.2em
      }
    }

    .shrink:hover {
      color: var(--main-color-theme);
      z-index: 9;
      border: 1px solid var(--main-color-theme);
      background-color: var(--main-color-theme);
    }

    .icon-zhengshuguanli:hover {
      color: #0052D9;
    }
  }

  .operation {
    display: block;
    text-align: center;
    float: left;
    line-height: 32px;
  }

  .operation:hover {
    background: #EBEBEB;
  }

  .more {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    width: 20px;
    height: 32px;
    border-radius: 4px;
  }

  .more::after {
    content: "...";
    position: absolute;
    right: 20%;
    transform: translateY(-10%);
  }

  .t-popup__content {
    font-size: 12px;
  }
}

.icon-xinzeng {
  font-size: 10px;
  margin-right: 4px;
}

.hint {
  z-index: 9999 !important;
}

.copy {
  svg {
    display: none;
  }
}

.copy:hover svg {
  top: -5px;
  display: block;
}

.t-form-item__web_access {
  margin-bottom: 5px !important;
}

/* 新增应用统一下拉按钮样式 - 修复垂直对齐 */
.add-app-unified {
  display: inline-block;
  position: relative;
  margin-right: 12px; /* 添加右边距，与其他按钮保持间距 */
  vertical-align: middle; /* 确保垂直对齐 */
  height: 32px; /* 固定高度与其他按钮一致 */
  line-height: 32px; /* 行高对齐 */
}

/* 修复TDesign dropdown的垂直对齐 */
.add-app-unified .t-dropdown {
  display: inline-block !important;
  vertical-align: middle !important;
  height: 32px !important;
  line-height: 32px !important;
}

.unified-dropdown {
  display: inline-block;
}

.unified-button {
  display: flex;
  align-items: center;
  background: #0052d9;
  border: 1px solid #0052d9;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  height: 32px; /* 与TDesign按钮高度保持一致 */
  overflow: hidden;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
  box-sizing: border-box; /* 确保边框计算正确 */
}

.unified-button:hover {
  background: #1a5cd9;
  border-color: #1a5cd9;
  box-shadow: 0 2px 8px rgba(0, 82, 217, 0.3);
}

.main-section {
  display: flex;
  align-items: center;
  padding: 0 4px 0 8px;
  flex: 1;
  height: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.main-section:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.add-icon {
  font-size: 14px;
  margin-right: 6px;
}

.button-text {
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
}

/* 完全移除分隔线 */
.divider-line {
  display: none;
}

.dropdown-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  height: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-width: 24px;
  width: 24px;
}

.dropdown-section:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.dropdown-icon {
  font-size: 10px;
  color: white;
}

/* 确保下拉菜单正确显示 */
.unified-dropdown .t-dropdown__trigger {
  display: inline-block;
  width: 100%;
}


</style>
