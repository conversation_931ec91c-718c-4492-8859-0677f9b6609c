package service

import (
	riskConstants "asdsec.com/asec/platform/app/console/app/risk_setting/constants"
	riskService "asdsec.com/asec/platform/app/console/app/risk_setting/service"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/common"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/repository"
	commonErr "asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	model2 "asdsec.com/asec/platform/pkg/model"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"sort"
	"sync"
)

// public
type SensitiveStrategyServiceImpl interface {
	Query(ctx context.Context, data common.QuerySensitiveStrategyReqData) (model2.Pagination, error)
	QueryAssocStrategy(ctx context.Context, id int) ([]model.SensitiveStrategyDB, error)
	Find(ctx context.Context, id string) (model.SensitiveStrategyDB, error)
	Add(ctx context.Context, data common.SensitiveStrategyBaseData) error
	Delete(ctx context.Context, id string) aerrors.AError
	Change(ctx context.Context, data common.ChangeSensitiveStrategyReqData) error
	StrategySummary(ctx context.Context) ([]common.StrategySummaryResp, error)
	CheckAddStrategyReq(ctx context.Context, req common.SensitiveStrategyBaseData) error
	DelSensitiveStgList(ctx context.Context, ids []string) aerrors.AError
	UpdateEnableStgBatch(ctx context.Context, req common.UpdateStgBatchReq) error
}

func GetSensitiveStrategyService() SensitiveStrategyServiceImpl {
	sensitiveStrategyServInitPriv.Do(func() {
		sensitiveStrategyInstance = &sensitiveStrategyServicePriv{repository.NewSensitiveStrategyRepository()}
	})
	return sensitiveStrategyInstance
}

// private
var sensitiveStrategyServInitPriv sync.Once
var sensitiveStrategyInstance SensitiveStrategyServiceImpl

type sensitiveStrategyServicePriv struct {
	db repository.SensitiveStrategyRepositoryImpl
}

func (p *sensitiveStrategyServicePriv) DelSensitiveStgList(ctx context.Context, ids []string) aerrors.AError {
	data, _ := p.db.QueryAssocRule(ctx, ids)
	if len(data) > 0 {
		return aerrors.New("assoated sensitive strategy", commonErr.DeleteSensitiveStrategyErr)
	}
	return p.db.DelSensitiveStgList(ctx, ids)
}

func (p *sensitiveStrategyServicePriv) CheckAddStrategyReq(ctx context.Context, req common.SensitiveStrategyBaseData) error {
	identifyWay := req.IdentifyWay
	for _, i := range identifyWay {
		switch i {
		case 1:
			if len(req.SourceId) == 0 {
				return errors.New("required sourceId condition")
			}
			break
		case 2:
			if len(req.FileNameRule) == 0 || req.FileNameOperator == "" {
				return errors.New("required fileNameRule condition")
			}
			break
		case 3:
			if len(req.FileTypeCode) == 0 && req.CheckFileSuffix == 2 {
				return errors.New("required fileType condition")
			}
			break
		case 4:
			if req.MaxFileSize == 0 && req.MinFileSize == 0 {
				return errors.New("required fileSize condition")
			}
			break
		case 5:
			if len(req.ContentRule) == 0 || req.ContentOperator == "" {
				return errors.New("required fileNameRule condition")
			}
			break
		default:
			return errors.New("wrong identifyWay")
		}
	}
	return nil
}

func (self *sensitiveStrategyServicePriv) StrategySummary(ctx context.Context) ([]common.StrategySummaryResp, error) {
	data, err := self.db.StrategySummary(ctx)
	sort.Slice(data, func(i, j int) bool {
		return data[i].Count > data[j].Count
	})

	if err != nil {
		return data, err
	}
	if len(data) > 5 {
		data = data[:5]
	}
	return data, nil
}

func (self *sensitiveStrategyServicePriv) Query(ctx context.Context, inData common.QuerySensitiveStrategyReqData) (model2.Pagination, error) {
	return self.db.GetSgList(ctx, inData)
}

func (self *sensitiveStrategyServicePriv) Find(ctx context.Context, id string) (model.SensitiveStrategyDB, error) {
	return self.db.Find(ctx, id)
}

func (self *sensitiveStrategyServicePriv) Add(ctx context.Context, data common.SensitiveStrategyBaseData) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) (err error) {
		ddrScore, err := self.db.Add(ctx, data, tx)
		if err != nil {
			log.Errorf("add sensitive strategy failed. err=%v", err)
			return err
		}
		err = riskService.GetRiskSettingService().CascadeCreatRiskSetting(ctx, ddrScore.Id, riskConstants.DataScoreIndicator, ddrScore.SensitiveLevel, data.CorpId, tx)
		return err
	})

}

func (self *sensitiveStrategyServicePriv) Delete(ctx context.Context, id string) aerrors.AError {
	data, _ := self.db.QueryAssocRule(ctx, []string{id})
	if len(data) > 0 {
		return aerrors.New("accrc rule", commonErr.DeleteSensitiveStrategyErr)
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, commonErr.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) (err error) {
		err = self.db.Delete(ctx, id, tx)
		if err != nil {
			log.Errorf("delete sensitive strategy failed. err=%v", err)
			return err
		}
		err = riskService.GetRiskSettingService().CascadeDeleteRiskSetting(ctx, riskConstants.DataScoreIndicator, id, tx)
		return err
	})
	if err != nil {
		return aerrors.NewWithError(err, commonErr.OperateError)
	}
	return nil
}

func (self *sensitiveStrategyServicePriv) Change(ctx context.Context, data common.ChangeSensitiveStrategyReqData) error {
	return self.db.Change(ctx, data)
}

func (self *sensitiveStrategyServicePriv) QueryAssocStrategy(ctx context.Context, id int) ([]model.SensitiveStrategyDB, error) {
	return self.db.QueryAssocStrategy(ctx, id)
}

func (p *sensitiveStrategyServicePriv) UpdateEnableStgBatch(ctx context.Context, req common.UpdateStgBatchReq) error {
	return p.db.UpdateEnableStgBatch(ctx, req.Ids, req.Enable)
}
