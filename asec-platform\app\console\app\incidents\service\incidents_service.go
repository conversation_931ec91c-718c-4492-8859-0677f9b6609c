package service

import (
	"asdsec.com/asec/platform/app/console/app/incidents/common"
	"asdsec.com/asec/platform/app/console/app/incidents/dto"
	"asdsec.com/asec/platform/app/console/app/incidents/repository"
	"asdsec.com/asec/platform/app/console/app/user_risk/constants"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"sync"
	"time"
)

type IncidentServiceImpl interface {
	QueryIncidentList(ctx context.Context, request common.QueryIncidentListRequest) (common.QueryIncidentListRespone, error)
	QueryCompIncidentList(ctx context.Context, request common.QueryIncidentListRequest) ([]model.UEBAIncidentDB, error)
	GetUEBAStrategySum(ctx context.Context, timeParam common.QueryTimeParam) ([]common.UEABStrategySum, error)
	GetEventStateSum(ctx context.Context, param common.QueryTimeParam) ([]common.EventStateSum, error)
	QueryUserTop(ctx context.Context, param common.QueryTimeParam) ([]common.UserTopItem, error)
	GetIncidentPartialSum(ctx context.Context, id string) (common.IncidentPartialSum, error)
	GetIncidentSummary(ctx context.Context, id string) (common.UEBAEventSummaryInfo, error)
	ChangeIncidentState(ctx context.Context, request common.ChangeEventStateRequest) error
	QueryIncidentTypeTop(ctx context.Context, param common.QueryTimeParam) ([]common.IncidentTypeTopItem, error)

	GetIncidentLogs(ctx context.Context, req dto.ProcessReq) ([]dto.IncidentResp, error)
	GetContextInfo(ctx context.Context, req dto.ContextReq) ([]dto.ContextData, error)
	GetIncidentName(ctx context.Context, req dto.NameReq) ([]dto.IncidentNameInfo, error)
}

func GetIncidentService() IncidentServiceImpl {
	incidentServInitPriv.Do(func() {
		incidentInstance = &incidentServicePriv{repository.NewIncidentRepository()}
	})
	return incidentInstance
}

// private
var incidentServInitPriv sync.Once
var incidentInstance IncidentServiceImpl

type incidentServicePriv struct {
	db repository.FileEventsRepositoryImpl
}

func (i *incidentServicePriv) GetIncidentName(ctx context.Context, req dto.NameReq) ([]dto.IncidentNameInfo, error) {
	return i.db.GetIncidentName(ctx, req)
}

func (self *incidentServicePriv) QueryIncidentList(ctx context.Context, request common.QueryIncidentListRequest) (common.QueryIncidentListRespone, error) {
	var (
		dbParam repository.QueryParam
		result  common.QueryIncidentListRespone
	)
	dbParam.Limit = request.Limit
	dbParam.Offset = request.Offset
	dbParam.StrategyID = request.StrategyID
	dbParam.Search = request.Search
	dbParam.EndTime, _ = time.Parse("2006-01-02 15:04:05", request.EndTime)
	dbParam.StartTime, _ = time.Parse("2006-01-02 15:04:05", request.StartTime)
	dbParam.EventState = request.EventState
	dbParam.RiskLevel = request.RiskLevel
	dbResult, err := self.db.Query(ctx, dbParam)
	if err != nil {
		return result, err
	}

	result.TotalNum = int(dbResult.Count)

	for _, it := range dbResult.Data {
		result.Data = append(result.Data, common.IncidentPartial{
			ID:            it.ID,
			Name:          it.IncidentName,
			SeverityLevel: it.RiskLevel,
			Channel:       it.Channel,
			Application:   it.ApplicationName,
			StrategyName:  it.StrategyName,
			UserName:      it.UserName,
			EventState:    it.IncidentStatus,
			EndTime:       time.Unix(it.EventEndTime, 0).Format("2006-01-02 15:04:05"),
		})
	}

	return result, nil
}

func (self *incidentServicePriv) GetUEBAStrategySum(ctx context.Context, timeParam common.QueryTimeParam) ([]common.UEABStrategySum, error) {
	return self.db.GetUEABStrategySum(ctx, timeParam)
}

func (self *incidentServicePriv) GetEventStateSum(ctx context.Context, param common.QueryTimeParam) ([]common.EventStateSum, error) {
	var dbParam repository.TimeParam
	dbParam.EndTime, _ = time.Parse("2006-01-02 15:04:05", param.EndTime)
	dbParam.StartTime, _ = time.Parse("2006-01-02 15:04:05", param.StartTime)
	return self.db.GetEventStateSum(ctx, dbParam)
}

func (self *incidentServicePriv) QueryUserTop(ctx context.Context, param common.QueryTimeParam) ([]common.UserTopItem, error) {
	var dbParam repository.TimeParam
	dbParam.EndTime, _ = time.Parse("2006-01-02 15:04:05", param.EndTime)
	dbParam.StartTime, _ = time.Parse("2006-01-02 15:04:05", param.StartTime)
	return self.db.QueryUserTop(ctx, dbParam)
}

func (self *incidentServicePriv) GetIncidentPartialSum(ctx context.Context, id string) (common.IncidentPartialSum, error) {
	var result common.IncidentPartialSum
	dbData, err := self.db.Find(ctx, id)
	if err != nil {
		return result, err
	}
	var tmpAgentInfo common.AgentInfo
	err = json.Unmarshal(dbData.AgentInfo, &tmpAgentInfo)
	if err != nil {
		return result, err
	}
	result.ID = dbData.ID
	result.Name = dbData.IncidentName
	result.UserID = dbData.UserID
	result.AgentID = strconv.FormatInt(tmpAgentInfo.AgentID, 10)
	result.EventCount = len(dbData.RawEventIds)
	result.RiskScore = dbData.IncidentScore
	result.EventState = dbData.IncidentStatus
	result.UserName = dbData.UserName
	tagData, err := self.db.GetUserTags(ctx, result.UserID)
	if err != nil {
		return result, err
	}
	if tagData.ID != 0 {
		result.Tags = append(result.Tags, tagData.Name)
	}
	return result, err
}

func (self *incidentServicePriv) ChangeIncidentState(ctx context.Context, request common.ChangeEventStateRequest) error {
	return self.db.ChangeIncidentState(ctx, request.ID, request.EventState)
}

func (i *incidentServicePriv) GetContextInfo(ctx context.Context, req dto.ContextReq) ([]dto.ContextData, error) {
	return i.db.GetContextInfo(ctx, req)
}

func (i *incidentServicePriv) GetIncidentLogs(ctx context.Context, req dto.ProcessReq) ([]dto.IncidentResp, error) {
	incident, err := i.db.GetIncidentById(ctx, req.IncidentId)
	if err != nil {
		return nil, err
	}
	var ids []string
	for _, v := range incident.RawEventIds {
		ids = append(ids, v)
	}
	if len(ids) == 0 {
		return nil, nil
	}
	var tData dto.TraceData
	if incident.TraceData != "" {
		err = json.Unmarshal([]byte(incident.TraceData), &tData)
		if err != nil {
			global.SysLog.Error(err.Error())
		}
	}
	var data []dto.IncidentResp
	if req.AgentId != "" {
		// 对登录的单独处理,根据agent_id和active
		data, err = i.db.GetAgentLoginInfo(ctx, req)
	} else {
		data, err = i.db.GetIncidentLogs(ctx, req, ids)
	}
	if err != nil {
		global.SysLog.Error(err.Error())
		return nil, err
	}
	if len(tData.MatchCondition) > 0 && len(data) > 0 {
		data[len(data)-1].IncidentType = tData.MatchCondition[0].Name
		data[len(data)-1].TypeValue = tData.MatchCondition[0].Value
	}
	return data, nil
}

func (self *incidentServicePriv) GetIncidentSummary(ctx context.Context, id string) (common.UEBAEventSummaryInfo, error) {
	var result common.UEBAEventSummaryInfo
	dbData, err := self.db.Find(ctx, id)
	if err != nil {
		return result, err
	}
	var tmpAgentInfo common.AgentInfo
	err = json.Unmarshal(dbData.AgentInfo, &tmpAgentInfo)
	if err != nil {
		return result, err
	}
	err = json.Unmarshal(dbData.SummaryData, &result.SummaryData)
	if err != nil {
		return result, err
	}

	var eventId string
	if strings.Contains(dbData.IncidentType, constants.SendActivityType) && len(dbData.RawEventIds) > 0 {
		ckDb, err := global.GetCkClient(ctx)
		if err != nil {
			return result, err
		}
		err = ckDb.Model(model.FileEvents{}).Select("uuid").
			Where("uuid in ? AND activity IN ('send','clipboard_paste_text')  ", []string(dbData.RawEventIds)).Order("occur_time desc").
			Find(&eventId).Limit(1).Error
		if err != nil {
			return result, err
		}
	}
	result.EventId = eventId
	result.UserName = dbData.UserName
	result.StrategyName = dbData.StrategyName
	result.SeverityLevel = dbData.RiskLevel
	result.PlatType = tmpAgentInfo.PlatType
	result.Time = time.Unix(dbData.EventEndTime, 0).Format("2006-01-02 15:04:05")

	result.AgentName = tmpAgentInfo.AgentName
	result.AgentID = strconv.FormatInt(tmpAgentInfo.AgentID, 10)
	result.EventStartTime = time.Unix(dbData.EventStartTime, 0).Format("2006-01-02 15:04:05")
	result.EventEndTime = time.Unix(dbData.EventEndTime, 0).Format("2006-01-02 15:04:05")
	for _, it := range tmpAgentInfo.AgentIP {
		result.IPAddr = it
	}
	result.IncidentType = dbData.IncidentType

	tagData, err := self.db.GetUserTags(ctx, dbData.UserID)
	if err != nil {
		return common.UEBAEventSummaryInfo{}, err
	}
	if tagData.ID != 0 {
		result.Tags = append(result.Tags, tagData.Name)
	}
	if result.IncidentType == common.SensitiveSendIncidentType {
		var summaryData common.SummaryData
		err = json.Unmarshal(dbData.SummaryData, &summaryData)
		if err != nil {
			global.SysLog.Sugar().Warnf("unmarshal summary data failed. summary_data=%s,err=%v", dbData.SummaryData, err)
			return result, nil
		}
		fileCategoryId := strconv.FormatInt(summaryData.FileCategoryId, 10)
		result.FileType, err = commonApi.GetFileTypeAndCode(ctx, fileCategoryId)
		if err != nil {
			global.SysLog.Sugar().Warnf("get file type failed. file_category_id=%d,err=%v", summaryData.FileCategoryId, err)
		}
	}
	return result, nil
}

func (self *incidentServicePriv) QueryIncidentTypeTop(ctx context.Context, param common.QueryTimeParam) ([]common.IncidentTypeTopItem, error) {
	var dbParam repository.TimeParam
	dbParam.EndTime, _ = time.Parse("2006-01-02 15:04:05", param.EndTime)
	dbParam.StartTime, _ = time.Parse("2006-01-02 15:04:05", param.StartTime)
	return self.db.QueryIncidentTypeTop(ctx, dbParam)
}

func (self *incidentServicePriv) QueryCompIncidentList(ctx context.Context, request common.QueryIncidentListRequest) ([]model.UEBAIncidentDB, error) {
	var (
		dbParam repository.QueryParam
	)
	dbParam.Limit = request.Limit
	dbParam.Offset = request.Offset
	dbParam.StrategyID = request.StrategyID
	dbParam.Search = request.Search
	dbParam.EndTime, _ = time.Parse("2006-01-02 15:04:05", request.EndTime)
	dbParam.StartTime, _ = time.Parse("2006-01-02 15:04:05", request.StartTime)
	dbParam.EventState = request.EventState
	result, err := self.db.Query(ctx, dbParam)
	return result.Data, err
}
