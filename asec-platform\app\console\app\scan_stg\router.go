package risk_setting

import (
	"asdsec.com/asec/platform/app/console/app/scan_stg/api"
	"github.com/gin-gonic/gin"
)

func ScanStgApi(r *gin.RouterGroup) {
	v := r.Group("/v1/scan_strategy")
	{
		v.POST("", api.CreateScanStg)
		v.GET("/list", api.GetScanStgList)
		v.PUT("", api.UpdateScanStg)
		v.GET("/detail", api.GetScanStgDetail)
		v.DELETE("", api.DeleteScanStg)
	}
	{
		v.POST("/result", api.GetTaskResult)
		v.GET("/result_detail", api.GetTaskResultDetail)
		v.POST("/result_user", api.GetTaskResultUser)
		v.PUT("/result_stop", api.StopScan)
	}
}
