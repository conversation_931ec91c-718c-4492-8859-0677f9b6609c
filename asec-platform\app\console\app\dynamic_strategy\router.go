package dynamic_strategy

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/api"
	"github.com/gin-gonic/gin"
)

func DynamicStrategyApi(r *gin.RouterGroup) {
	v := r.Group("/v1/dynamic_strategy")
	// 策略组
	{
		v.POST("/group", api.CreateGroup)
		v.DELETE("/group", api.DeleteGroup)
		v.PUT("/group", api.UpdateGroup)
		v.GET("/group_list", api.GroupList)
		v.POST("/group_move", api.GroupMove)
	}
	// 策略
	{
		v.POST("/strategy", api.AddStrategy)
		v.PUT("/strategy", api.UpdateStrategy)
		v.DELETE("/strategy", api.DelStrategy)
		v.POST("/enable", api.EnableStrategy)
		v.GET("/strategy", api.StrategyDetail)
		v.POST("/clear_match", api.ClearStrategyMatch)
		v.POST("/strategy_move", api.StrategyMove)
		v.GET("/strategy_simple_list", api.StrategySimpleList)
		v.POST("/strategy_list", api.StrategyList)
		v.GET("/factor_list", api.FactorList)
	}
	// 策略因子 -- 时间
	{
		v.POST("/factor_time", api.AddFactorTime)
		v.GET("/factor_time_list", api.FactorTimeList)
		v.POST("/factor_time_list_page", api.FactorTimeListPage)
		v.PUT("/factor_time", api.UpdateFactorTime)
		v.DELETE("/factor_time", api.DelFactorTime)
	}
	// 策略因子--ip
	{
		v.POST("/factor_ip", api.CreateFactorIp)
		v.POST("/factor_ip_list", api.FactorIpList)
		v.PUT("/factor_ip", api.UpdateFactorIp)
		v.DELETE("/factor_ip", api.DelFactorIp)
	}
	// 策略因子--网络位置
	{
		v.POST("/factor_net_location", api.CreateNetLocation)
		v.POST("/factor_net_location_list", api.NetLocationList)
		v.PUT("/factor_net_location", api.UpdateNetLocation)
		v.DELETE("/factor_net_location", api.DelNetLocation)
	}
	// 策略因子--进程
	{
		v.POST("/factor_process", api.CreateFactorProcess)
		v.POST("/factor_process_list", api.FactorProcessList)
		v.PUT("/factor_process", api.UpdateFactorProcess)
		v.DELETE("/factor_process", api.DelFactorProcess)
	}
	// 策略因子获取接口
	{
		v.GET("/factor/ip", api.FactorIp)
		v.GET("/factor/process", api.FactorProcess)
		v.GET("/factor/net_location", api.FactorNetLocation)
	}

}
