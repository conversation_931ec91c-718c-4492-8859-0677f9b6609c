package dto

type UpdateFeatureSwitchReq struct {
	ModuleKey       string                `json:"module_key,omitempty"`
	FeatureSwitches []FeatureSwitchUpdate `json:"feature_switches"`
	Flag            int                   `json:"flag"`
}

type FeatureSwitchDto struct {
	EntityType  string `gorm:"column:entity_type;type:varchar;comment:实体类型" json:"type"`
	EntityID    string `gorm:"column:entity_id;type:varchar;comment:实体ID" json:"id"`
	ModuleKey   string `gorm:"unique;column:module_key;type:varchar;comment:模块键" json:"module_key"`
	Flag        int    `gorm:"column:flag;type:integer;comment:标志" json:"-"`
	IsEnabled   int    `gorm:"column:is_enabled;type:integer;comment:是否启用" json:"-"`
	AgentType   string `gorm:"column:agent_type;type:varchar;comment:终端类型,比如windows,darwin" json:"icon,omitempty"`
	Path        string `json:"path,omitempty"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name,omitempty"`
}

type FeatureSwitchUpdate struct {
	EntityType string `json:"type"`
	EntityID   string `json:"id"`
	ModuleKey  string `json:"module_key"`
	AgentType  string `json:"agent_type"`
}
