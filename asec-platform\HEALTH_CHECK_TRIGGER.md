# 健康检查触发功能

## 功能概述

在 route 同步到 APISIX 成功后，增加访问资源的逻辑来触发健康检查。这样可以让健康检查立即开始工作，而不是等待 APISIX 的定时健康检查周期。

## 实现原理

### 1. 主应用健康检查触发

当主应用的 route 同步成功后，系统会：
1. 检查健康检查是否启用（`healthConf.Enable == "1"`）
2. 构建健康检查 URL
3. 异步发送 HTTP 请求到健康检查端点
4. 验证响应状态码是否符合预期

### 2. 依赖站点健康检查触发

当依赖站点路由创建成功后，系统会：
1. 检查是否继承了主应用的健康检查配置
2. 解析依赖站点的目标地址
3. 构建健康检查 URL
4. 异步发送 HTTP 请求到依赖站点的健康检查端点

## 技术实现

### 主应用健康检查触发

```go
// triggerHealthCheck 触发健康检查，通过访问应用资源来激活健康检查
func triggerHealthCheck(appGateway dto.ApplicationGatewayDto, healthConf apisix.HealthConfig) {
    // 只有在启用健康检查时才触发
    if healthConf.Enable != "1" || healthConf.Config == nil {
        global.Logger.Sugar().Debugf("应用 %s 健康检查未启用，跳过触发", appGateway.AppName)
        return
    }

    // 构建健康检查URL
    healthCheckURL := fmt.Sprintf("%s://%s%s", 
        appGateway.ServerSchema, 
        appGateway.ServerAddress, 
        healthConf.Config.Path)

    // 在后台异步执行健康检查触发，避免阻塞主流程
    go func() {
        defer func() {
            if r := recover(); r != nil {
                global.Logger.Sugar().Errorf("触发健康检查时发生panic: %v", r)
            }
        }()

        // 创建HTTP客户端，设置超时
        client := &http.Client{
            Timeout: time.Duration(healthConf.Config.Timeout) * time.Second,
            Transport: &http.Transport{
                TLSClientConfig: &tls.Config{
                    InsecureSkipVerify: true, // 跳过SSL验证，适用于内网环境
                },
            },
        }

        // 发送健康检查请求
        resp, err := client.Get(healthCheckURL)
        if err != nil {
            global.Logger.Sugar().Warnf("触发应用 %s 健康检查失败: %v", appGateway.AppName, err)
            return
        }
        defer resp.Body.Close()

        // 检查响应状态码
        isHealthy := false
        for _, code := range healthConf.Config.HealthCode {
            if resp.StatusCode == code {
                isHealthy = true
                break
            }
        }

        if isHealthy {
            global.Logger.Sugar().Infof("应用 %s 健康检查触发成功: HTTP %d", appGateway.AppName, resp.StatusCode)
        } else {
            global.Logger.Sugar().Warnf("应用 %s 健康检查触发失败: HTTP %d (期望: %v)", 
                appGateway.AppName, resp.StatusCode, healthConf.Config.HealthCode)
        }
    }()
}
```

### 依赖站点健康检查触发

```go
// triggerDependSiteHealthCheck 触发依赖站点的健康检查
func triggerDependSiteHealthCheck(routeId string, dependRoute apisix.DependSiteRoute, upstreamScheme string, inheritedChecks apisix.Checks) {
    // 只有在继承了健康检查配置时才触发
    if inheritedChecks.Active.Healthy.Interval <= 0 || inheritedChecks.Active.Unhealthy.Interval <= 0 {
        global.Logger.Sugar().Debugf("依赖站点路由 %s 未继承健康检查配置，跳过触发", routeId)
        return
    }

    // 解析依赖站点的地址
    var targetHost string
    var targetPort int
    for nodeAddr := range dependRoute.Upstream.Nodes {
        // nodeAddr 格式可能是 "host:port" 或 "host"
        parts := strings.Split(nodeAddr, ":")
        targetHost = parts[0]
        if len(parts) > 1 {
            if port, err := strconv.Atoi(parts[1]); err == nil {
                targetPort = port
            }
        }
        break // 使用第一个节点进行健康检查
    }

    // 构建健康检查URL
    var healthCheckURL string
    if targetPort > 0 && targetPort != 80 && targetPort != 443 {
        healthCheckURL = fmt.Sprintf("%s://%s:%d%s", upstreamScheme, targetHost, targetPort, inheritedChecks.Active.HttpPath)
    } else {
        healthCheckURL = fmt.Sprintf("%s://%s%s", upstreamScheme, targetHost, inheritedChecks.Active.HttpPath)
    }

    // 异步执行健康检查触发...
}
```

### 调用时机

#### 主应用
```go
// route 同步成功后
global.Logger.Sugar().Infof("应用 %s (%s) route 同步到 apisix 成功", appGateway.AppName, upstreamId)

// 触发健康检查，让健康检查立即开始工作
triggerHealthCheck(appGateway, healthConf)

// 更新哈希缓存
UpdateAppConfigHash(v.Id, configHash)
```

#### 依赖站点路由
```go
// 依赖站点路由创建成功后
global.Logger.Sugar().Debugf("依赖站点路由 %s 创建成功", routeId)

// 触发依赖站点的健康检查
triggerDependSiteHealthCheck(routeId, dependRoute, upstreamScheme, inheritedChecks)
```

## 功能特性

### 1. 异步执行
- 使用 goroutine 异步执行健康检查触发
- 不阻塞主同步流程
- 包含 panic 恢复机制

### 2. 智能判断
- 只有在健康检查启用时才触发
- 依赖站点只有在继承了健康检查配置时才触发
- 自动跳过无效配置

### 3. 安全配置
- 跳过 SSL 证书验证（适用于内网环境）
- 设置合理的超时时间
- 完整的错误处理

### 4. 详细日志
- 记录触发过程的详细日志
- 区分成功和失败的情况
- 提供调试信息

## 优势

### 1. 立即生效
- 健康检查不再需要等待 APISIX 的定时检查周期
- 配置同步后立即验证应用健康状态
- 更快的故障发现和恢复

### 2. 主动验证
- 主动验证应用和依赖站点的可用性
- 确保配置同步后服务真正可用
- 提前发现配置或服务问题

### 3. 完整覆盖
- 主应用和依赖站点都支持健康检查触发
- 继承主应用的健康检查配置
- 统一的健康检查逻辑

### 4. 非侵入性
- 异步执行，不影响主流程性能
- 失败不会影响配置同步
- 可选功能，可以通过配置控制

## 日志示例

### 成功触发
```
INFO  应用 demo-app upstream 同步到 apisix 成功
INFO  应用 demo-app route 同步到 apisix 成功
DEBUG 触发应用 demo-app 健康检查: https://192.168.1.100:8080/health
INFO  应用 demo-app 健康检查触发成功: HTTP 200
```

### 失败触发
```
DEBUG 触发依赖站点 depend-site-123 健康检查: https://example.com/health
WARN  依赖站点 depend-site-123 健康检查触发失败: HTTP 500 (期望: [200])
```

### 跳过触发
```
DEBUG 应用 demo-app 健康检查未启用，跳过触发
DEBUG 依赖站点路由 depend-site-456 未继承健康检查配置，跳过触发
```

## 总结

健康检查触发功能通过在配置同步成功后主动访问健康检查端点，实现了：
- ✅ 健康检查的立即生效
- ✅ 主动的服务可用性验证
- ✅ 更快的问题发现和诊断
- ✅ 完整的主应用和依赖站点覆盖

这大大提高了系统的响应性和可靠性，确保配置同步后服务能够立即正常工作。
