package dto

import (
	"asdsec.com/asec/platform/pkg/model/notification_model"
	"errors"
)

const NotificationConfType = "notification"

type NotificationType string

const (
	//Block 阻断
	Block NotificationType = "block"
	//Audit 审计
	Audit    NotificationType = "audit"
	Approval NotificationType = "approval"
)

func ValidateType(ntype NotificationType) error {
	switch ntype {
	case Block, Audit, Approval:
		return nil
	default:
		return errors.New("invalid status")
	}
}

type NotificationReq struct {
	Id               string           `json:"id"`
	Name             string           `json:"name"`
	NotificationType NotificationType `json:"notification_type"`
	Title            string           `json:"title"`
	Content          string           `json:"content"`
}
type ListReq struct {
	Limit  int    `json:"limit" binding:"required,min=1,max=1000"`
	Offset int    `json:"offset"`
	Search string `json:"search" required:"false" binding:"omitempty"`
}

type NotificationListResp struct {
	TotalNum         int64                             `json:"total_num"`
	NotificationList []notification_model.Notification `json:"notification_list"`
}

type DelReq struct {
	Ids []string `json:"ids"`
}
