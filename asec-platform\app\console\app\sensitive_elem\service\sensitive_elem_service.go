package service

import (
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/common"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/constants"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/repository"
	global "asdsec.com/asec/platform/app/console/global"
	"context"
	"encoding/json"
	"errors"
	"github.com/jackc/pgtype"
	"github.com/jinzhu/copier"
	"sync"
	"time"
)

// public
type SensitiveElemServiceImpl interface {
	Query(ctx context.Context, req common.QuerySensitiveElemReqData) (common.QuerySensitiveElemListRsp, error)
	QueryTotal(ctx context.Context) (common.QuerySensitiveElemTotal, error)
	QueryItem(ctx context.Context, id int) (model.SensitiveElemDB, error)
	QueryItemByNameAndCategory(ctx context.Context, name string, category int, builtIn int) (model.SensitiveElemDB, error)
	Add(ctx context.Context, data common.SensitiveElemReqData) error
	Delete(ctx context.Context, id int) error
	Change(ctx context.Context, data common.SensitiveElemReqData) error
	Summary(ctx context.Context) (map[string]int64, error)
	QueryAlgorithmList(ctx context.Context) ([]common.SensitiveElem, error)
}

func GetSensitiveElemService() SensitiveElemServiceImpl {
	sensitiveElemServInitPriv.Do(func() {
		sensitiveElemInstance = &sensitiveElemServicePriv{repository.NewSensitiveElemRepository()}
	})
	return sensitiveElemInstance
}

// private
var sensitiveElemServInitPriv sync.Once
var sensitiveElemInstance SensitiveElemServiceImpl

type sensitiveElemServicePriv struct {
	db repository.SensitiveElemRepositoryImpl
}

func (self *sensitiveElemServicePriv) QueryAlgorithmList(ctx context.Context) ([]common.SensitiveElem, error) {
	return self.db.QueryAlgorithmList(ctx)
}

func (self *sensitiveElemServicePriv) QueryItemByNameAndCategory(ctx context.Context, name string, category int, builtIn int) (model.SensitiveElemDB, error) {
	return self.db.QueryItemByNameAndCategory(ctx, name, category, builtIn)
}

func (self *sensitiveElemServicePriv) Summary(ctx context.Context) (map[string]int64, error) {
	return self.db.Summary(ctx)
}

func (self *sensitiveElemServicePriv) Query(ctx context.Context, req common.QuerySensitiveElemReqData) (common.QuerySensitiveElemListRsp, error) {
	return self.db.Query(ctx, req)
}

func (self *sensitiveElemServicePriv) QueryItem(ctx context.Context, id int) (model.SensitiveElemDB, error) {
	return self.db.QueryItem(ctx, id)
}

func (self *sensitiveElemServicePriv) Add(ctx context.Context, data common.SensitiveElemReqData) error {
	now := time.Now()
	//查询标签是否存在
	if len(data.Tags) != 0 {
		for _, tag := range data.Tags {
			ret, err := GetSensitiveElemTagService().Find(ctx, tag)
			if err != nil || ret.Id == 0 {
				return errors.New("tag does not exist")
			}
		}
	}
	value := data.Value
	tmp := model.SensitiveElemDB{
		Category:     int8(data.Type),
		Description:  data.Desc,
		ElementName:  data.ElementName,
		RelatedCount: 0,
		Value:        value,
		BuiltIn:      2,
		CreateTime:   now,
		UpdateTime:   now,
	}
	if int8(data.Type) == constants.AlgorithmElemType {
		var err error
		tmp.Value, err = self.transAlgorithmValue(ctx, data)
		if err != nil {
			return err
		}
	}
	copier.Copy(&tmp.Tags, &data.Tags)
	return self.db.Add(ctx, tmp)
}

func (self *sensitiveElemServicePriv) Delete(ctx context.Context, id int) error {
	return self.db.Delete(ctx, id)
}

func (self *sensitiveElemServicePriv) Change(ctx context.Context, data common.SensitiveElemReqData) error {
	//查询标签是否存在
	if len(data.Tags) != 0 {
		for _, tag := range data.Tags {
			ret, err := GetSensitiveElemTagService().Find(ctx, tag)
			if err != nil || ret.Id == 0 {
				return errors.New("tag does not exist")
			}
		}
	}
	value := data.Value
	tmp := model.SensitiveElemDB{
		Id:           data.Id,
		Category:     int8(data.Type),
		Description:  data.Desc,
		ElementName:  data.ElementName,
		RelatedCount: 0,
		BuiltIn:      int8(data.BuiltIn),
		Value:        value,
		UpdateTime:   time.Now(),
	}
	if int8(data.Type) == constants.AlgorithmElemType {
		var err error
		tmp.Value, err = self.transAlgorithmValue(ctx, data)
		if err != nil {
			return err
		}
	}
	copier.Copy(&tmp.Tags, &data.Tags)
	return self.db.Change(ctx, tmp)
}

func (self *sensitiveElemServicePriv) QueryTotal(ctx context.Context) (common.QuerySensitiveElemTotal, error) {
	return self.db.QueryTotal(ctx)
}

func (self *sensitiveElemServicePriv) transAlgorithmValue(ctx context.Context, data common.SensitiveElemReqData) (pgtype.JSONB, error) {
	value := data.Value
	var algorithmReq common.AlgorithmReqData
	err := json.Unmarshal(data.Value.Bytes, &algorithmReq)
	if err != nil {
		global.SysLog.Sugar().Errorf("unmarshal algorithm req failed. err=%v", err)
		return pgtype.JSONB{}, err
	}
	var itemValue common.AlgorithmValue
	//查询算法模型
	item, err := self.QueryItem(ctx, algorithmReq.ElemId)
	if item.Category == constants.RegexpElemType {
		err = json.Unmarshal(item.Value.Bytes, &itemValue.Value)
		if err != nil {
			global.SysLog.Sugar().Errorf("unmarshal algorithm value failed. err=%v", err)
			return pgtype.JSONB{}, err
		}
	} else {
		err = json.Unmarshal(item.Value.Bytes, &itemValue)
		if err != nil {
			global.SysLog.Sugar().Errorf("unmarshal algorithm value failed. err=%v", err)
			return pgtype.JSONB{}, err
		}
	}

	//将前端传过来的count赋值
	itemValue.Threshold = algorithmReq.ElemCount
	itemValue.ElemId = item.Id
	//统计类的算法模型默认认为是正则
	itemValue.ValueType = 2
	itemByte, err := json.Marshal(itemValue)
	if err != nil {
		global.SysLog.Sugar().Errorf("unmarshal algorithm value failed. err=%v", err)
		return pgtype.JSONB{}, err
	}
	value.Set(itemByte)
	return value, err
}
