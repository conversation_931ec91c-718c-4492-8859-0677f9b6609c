package seven_access_log

import (
	"asdsec.com/asec/platform/app/console/app/log/seven_access_log/api"
	"github.com/gin-gonic/gin"
)

func SevenAccessLog(r *gin.RouterGroup) {
	v := r.Group("/v1/seven_access_log")
	{
		//列表接口
		v.POST("/list", api.GetSevenAccessLogList)
		//详情接口
		v.GET("/detail", api.GetSevenAccessLogDetail)

		//日志字段配置
		v.GET("/conf", api.GetSevenAccessLogConf)
		//修改字段配置
		v.PUT("/conf", api.UpdateSevenAccessLogConf)
	}
	// sensitive log
	s := r.Group("/v1/sensitive_log")
	{
		s.POST("/list", api.GetSensitiveLogList)
		s.GET("/detail", api.GetSensitiveLogDetail)
	}
}
