package repository

import (
	"asdsec.com/asec/platform/pkg/model"
	"fmt"
	"gorm.io/driver/clickhouse"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"os"
	"testing"
	"time"
)

var DB *gorm.DB
var PgDb *gorm.DB

func init() {
	var err error
	dbDSN := "clickhouse://default:123456@*************:9000/test?read_timeout=20s"

	if DB, err = gorm.Open(clickhouse.Open(dbDSN), &gorm.Config{}); err != nil {
		fmt.Printf("failed to connect database, got error %v", err)
		os.Exit(1)
	}
	if os.Getenv("DEBUG") == "true" {
		DB = DB.Debug()
	}
	dsn := "host=*********** user=asec password=pg@asd@1234! dbname=asec_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai"
	PgDb, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		fmt.Printf("failed to connect database, got postgres error %v", err)
		os.Exit(1)
	}
}

type User struct {
	ID        uint64 `gorm:"primaryKey"`
	Name      string
	FirstName string
	LastName  string
	Age       int64
	Active    bool
	Salary    float32
	CreatedAt time.Time
	UpdatedAt time.Time
}

func Test_analysisRepository_GetEventsList(t *testing.T) {
	var tmp []model.FileEvents
	err := DB.Model(model.FileEvents{}).Limit(2).Offset(0).Find(&tmp).Error
	if err != nil {
		t.Log(err.Error())
	}
	t.Log(tmp)
}

func TestAddHistory(t *testing.T) {
	var tmps []model.SubCondition
	tmp := model.SubCondition{Type: []string{"file_info", "file_name"}, Relation: "eq", Value: nil}
	tmp1 := model.SubCondition{Type: []string{"file_info", "md5"}, Relation: "eq", Value: nil}
	tmps = append(tmps, tmp)
	tmps = append(tmps, tmp1)
	history := model.EventsHistoryQuery{
		Name:      "test",
		StartTime: "1",
		EndTime:   "2",
		Condition: tmps,
	}
	err := PgDb.Model(&model.EventsHistoryQuery{}).Create(&history).Error
	if err != nil {
		t.Log(err.Error())
	}
}

func TestUser(t *testing.T) {
	var user = User{ID: 3, Name: "update", FirstName: "zhang", LastName: "jinzhu", Age: 18, Active: true, Salary: 8.8888}
	if err := DB.Create(&user).Error; err != nil {
		t.Fatalf("failed to create user, got error %v", err)
	}

	var result User
	if err := DB.Find(&result, user.ID).Error; err != nil {
		t.Fatalf("failed to query user, got error %v", err)
	}
	t.Log(result)
}
func TestAutoMigrate(t *testing.T) {
	type UserMigrateColumn struct {
		ID           uint64
		Name         string
		IsAdmin      bool
		Birthday     time.Time `gorm:"precision:4"`
		Debit        float64   `gorm:"precision:4"`
		Note         string    `gorm:"size:10;comment:my note"`
		DefaultValue string    `gorm:"default:hello world"`
	}

	if DB.Migrator().HasColumn("users", "is_admin") {
		t.Fatalf("users's is_admin column should not exists")
	}

	if err := DB.Table("users").AutoMigrate(&UserMigrateColumn{}); err != nil {
		t.Fatalf("no error should happen when auto migrate, but got %v", err)
	}

	if !DB.Migrator().HasTable("users") {
		t.Fatalf("users should exists")
	}

	if !DB.Migrator().HasColumn("users", "is_admin") {
		t.Fatalf("users's is_admin column should exists after auto migrate")
	}

	columnTypes, err := DB.Migrator().ColumnTypes("users")
	if err != nil {
		t.Fatalf("failed to get column types, got error %v", err)
	}

	for _, columnType := range columnTypes {
		switch columnType.Name() {
		case "id":
			if columnType.DatabaseTypeName() != "UInt64" {
				t.Fatalf("column id primary key should be correct, name: %v, column: %#v", columnType.Name(), columnType)
			}
		case "note":
			if length, ok := columnType.Length(); !ok || length != 10 {
				t.Fatalf("column name length should be correct, name: %v, column: %#v", columnType.Name(), columnType)
			}

			if comment, ok := columnType.Comment(); !ok || comment != "my note" {
				t.Fatalf("column name length should be correct, name: %v, column: %#v", columnType.Name(), columnType)
			}
		case "default_value":
			if defaultValue, ok := columnType.DefaultValue(); !ok || defaultValue != "hello world" {
				t.Fatalf("column name default_value should be correct, name: %v, column: %#v", columnType.Name(), columnType)
			}
		case "debit":
			if decimal, scale, ok := columnType.DecimalSize(); !ok || (scale != 0 || decimal != 4) {
				t.Fatalf("column name debit should be correct, name: %v, column: %#v", columnType.Name(), columnType)
			}
		case "birthday":
			if decimal, scale, ok := columnType.DecimalSize(); !ok || (scale != 0 || decimal != 4) {
				t.Fatalf("column name birthday should be correct, name: %v, column: %#v", columnType.Name(), columnType)
			}
		}
	}
}

type PartFileEvents struct {
	Uuid         string `gorm:"column:uuid;type:varchar" json:"uuid"`
	CorpId       string `gorm:"column:corp_id;type:varchar;comment:租户id" json:"corp_id"`
	EventType    string `gorm:"column:event_type;type:varchar;comment:事件类型" json:"event_type"`
	EventSubType string `gorm:"column:event_sub_type;type:varchar;comment:事件子类型" json:"event_sub_type"`
	EventSource  string `gorm:"column:event_source;type:varchar;comment:事件来源" json:"event_source"`
	UserId       string `gorm:"column:user_id;type:varchar;comment:用户ID" json:"user_id"`
	UserName     string `gorm:"column:user_name;type:varchar;comment:用户名称" json:"user_name" excelColumn:"E" excelDesc:"Events.EventData.UserInfo"`
}

func (PartFileEvents) TableName() string {
	return "tb_file_events"
}
func TestEvents(t *testing.T) {
	var tmp []PartFileEvents
	err := DB.Model(&PartFileEvents{}).Select("uuid,user_id").Limit(5).Find(&tmp).Error
	if err != nil {
		t.Log(err.Error())
		return
	}
	t.Log(tmp)
}
