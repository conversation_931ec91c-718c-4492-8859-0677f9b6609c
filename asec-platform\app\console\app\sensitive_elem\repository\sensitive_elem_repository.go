package repository

import (
	v1 "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/common"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/constants"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/model"
	global "asdsec.com/asec/platform/app/console/global"
	model2 "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
	"strconv"
	"strings"
)

// public

type SensitiveElemRepositoryImpl interface {
	Query(ctx context.Context, req common.QuerySensitiveElemReqData) (common.QuerySensitiveElemListRsp, error)
	QueryTotal(ctx context.Context) (common.QuerySensitiveElemTotal, error)
	QueryItem(ctx context.Context, id int) (model.SensitiveElemDB, error)
	QueryItemByNameAndCategory(ctx context.Context, name string, category int, builtIn int) (model.SensitiveElemDB, error)
	Add(ctx context.Context, elem model.SensitiveElemDB) error
	Change(ctx context.Context, elem model.SensitiveElemDB) error
	Delete(ctx context.Context, id int) error
	Summary(ctx context.Context) (map[string]int64, error)
	QueryAlgorithmList(ctx context.Context) ([]common.SensitiveElem, error)
}

const (
	SensitiveElemConfType      = "sensitive_element_custom"
	SensitiveElemBuiltConfType = "sensitive_element_built"
)

func NewSensitiveElemRepository() SensitiveElemRepositoryImpl {
	return &sensitiveElemReposPriv{}
}

// private
type sensitiveElemReposPriv struct {
}

func (self *sensitiveElemReposPriv) QueryAlgorithmList(ctx context.Context) ([]common.SensitiveElem, error) {
	var ret []common.SensitiveElem
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return []common.SensitiveElem{}, err
	}
	err = db.Model(model.SensitiveElemDB{}).
		Where("is_algorithm_model or category = ?", constants.RegexpElemType).
		Find(&ret).Order("created_at asc").Error
	if err != nil {
		return []common.SensitiveElem{}, err
	}
	return ret, nil
}

func (self *sensitiveElemReposPriv) QueryItemByNameAndCategory(ctx context.Context, name string, category int, builtIn int) (model.SensitiveElemDB, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model.SensitiveElemDB{}, err
	}
	var ret model.SensitiveElemDB
	err = db.Model(model.SensitiveElemDB{}).Where("sensitive_element_name = ? and category = ? and built_in = ? and not is_algorithm_model", name, category, builtIn).Find(&ret).Error
	if err != nil {
		return model.SensitiveElemDB{}, err
	}
	return ret, nil
}

const CustomBuiltValue = 2

func (self *sensitiveElemReposPriv) Summary(ctx context.Context) (map[string]int64, error) {
	result := map[string]int64{"customize": 0, "built_in": 0}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	var builtIn int64
	var customize int64
	err = db.Model(&model.SensitiveStrategyDB{}).Where("built_in=1").Count(&builtIn).Error
	if err != nil {
		return result, err
	}
	result["built_in"] = builtIn
	err = db.Model(&model.SensitiveStrategyDB{}).Where("built_in=2").Count(&customize).Error
	if err != nil {
		return result, err
	}
	result["customize"] = customize
	return result, nil
}

func (self *sensitiveElemReposPriv) Query(ctx context.Context, req common.QuerySensitiveElemReqData) (common.QuerySensitiveElemListRsp, error) {
	var result []common.SensitiveElem
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return common.QuerySensitiveElemListRsp{}, err
	}
	pageReq := model2.Pagination{
		Limit:  req.Limit,
		Offset: req.Offset,
	}
	elemTagT := model.SensitiveElemTagDB{}.TableName()
	elemT := model.SensitiveElemDB{}.TableName()
	db = db.Model(model.SensitiveElemDB{}).Where("built_in=?", req.BuiltIn).Where("not is_algorithm_model").
		Group(fmt.Sprintf("%s.id", elemT)).Order("category desc").Order(fmt.Sprintf("%s.created_at desc", elemT))
	if req.BuiltIn == CustomBuiltValue {
		db = db.Select(fmt.Sprintf("%s.*,", elemT) +
			fmt.Sprintf("array (select unnest (array_agg(%s.id) filter(where %s.id is not null) )) AS tags", elemTagT, elemTagT)).
			Joins(fmt.Sprintf("Left join %s on %s.id  = any(%s.tags)", elemTagT, elemTagT, elemT))
	} else {
		db = db.Select("*")
	}
	if req.Search != "" {
		search := "%" + strings.ToLower(req.Search) + "%"
		db = db.Where("LOWER(sensitive_element_name) LIKE ?", search)
	}
	if req.Category != 0 {
		db = db.Where("category = ?", req.Category)
	}
	page, err := model2.Paginate(&result, &pageReq, db)
	if err != nil {
		return common.QuerySensitiveElemListRsp{}, err
	}
	return common.QuerySensitiveElemListRsp{
		CommonPage: model2.CommonPage{CurrentPage: page.Page, PageSize: page.Limit, TotalNum: int(page.TotalRows)},
		Data:       result,
	}, nil
}

func (self *sensitiveElemReposPriv) QueryItem(ctx context.Context, id int) (model.SensitiveElemDB, error) {
	var result model.SensitiveElemDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&result).Where("id=?", id).Find(&result).Error
	return result, err
}

func (self *sensitiveElemReposPriv) Add(ctx context.Context, elem model.SensitiveElemDB) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		dbErr := tx.Create(&elem).Error
		if dbErr != nil {
			return dbErr
		}
		// 更新配置
		changeReq, confErr := elemToChangeReq(conf_center.AddConf, elem, tx)
		if confErr != nil {
			global.SysLog.Sugar().Errorf("elemToChangeReq err:%v", confErr)
			return confErr
		}
		confErr = conf_center.ConfChange(changeReq)
		if confErr != nil {
			global.SysLog.Sugar().Errorf("confChange err:%v", confErr)
			return confErr
		}
		return nil
	})
	return err
}

func elemToChangeReq(changeType conf_center.ConfChangeType, elem model.SensitiveElemDB, tx *gorm.DB) (conf_center.ConfChangeReq, error) {
	element := v1.SensitiveElement{
		Id:   uint32(elem.Id),
		Type: uint32(elem.Category),
	}
	switch elem.Category {
	//算法类型
	case constants.AlgorithmElemType:
		var itemValue common.AlgorithmValue
		err := json.Unmarshal(elem.Value.Bytes, &itemValue)
		if err != nil {
			global.SysLog.Sugar().Errorf("unmarshal algorithm value failed. err=%v", err)
			return conf_center.ConfChangeReq{}, err
		}
		algorithmValue := v1.AlgorithmValue{
			Threshold: uint32(itemValue.Threshold),
			Value:     itemValue.Value,
			ValueType: uint32(itemValue.ValueType),
		}
		element.AlgorithmValue = &algorithmValue
		break
	//关键字、正则类型
	default:
		var strValue []string
		err := jsoniter.Unmarshal(elem.Value.Bytes, &strValue)
		if err != nil {
			return conf_center.ConfChangeReq{}, err
		}
		value := v1.Value{Value: strValue}
		element.KrValue = &value
	}
	data, err := proto.Marshal(&element)
	if err != nil {
		return conf_center.ConfChangeReq{}, err
	}
	confType := SensitiveElemConfType
	if elem.BuiltIn == constants.SystemBuiltInType {
		confType = SensitiveElemBuiltConfType
	}
	return conf_center.ConfChangeReq{
		ConfBizId:       strconv.Itoa(elem.Id),
		ConfType:        confType,
		ConfData:        data,
		ConfGranularity: 1,
		Tx:              tx,
		RedisCli:        global.SysRedisClient,
		ChangeType:      changeType,
	}, nil
}

func (self *sensitiveElemReposPriv) Delete(ctx context.Context, id int) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		dbErr := db.Model(&model.SensitiveElemDB{}).Where("id=? and built_in != 1", id).Delete(id).Error
		if dbErr != nil {
			return dbErr
		}
		confChangeErr := conf_center.ConfChange(conf_center.ConfChangeReq{
			ConfBizId:  strconv.Itoa(id),
			ConfType:   SensitiveElemConfType,
			Tx:         tx,
			RedisCli:   global.SysRedisClient,
			ChangeType: conf_center.DelConf,
		})
		if confChangeErr != nil {
			global.SysLog.Sugar().Errorf("confChangeErr :%v", confChangeErr)
			return confChangeErr
		}
		return nil
	})
	return err
}

func (self *sensitiveElemReposPriv) Change(ctx context.Context, elem model.SensitiveElemDB) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		dbErr := db.Model(&model.SensitiveElemDB{}).Where("id = ?", elem.Id).Updates(&elem).Error
		if dbErr != nil {
			return dbErr
		}
		dbErr = db.Model(&model.SensitiveElemDB{}).Select("description").Where("id = ?", elem.Id).Updates(&elem).Error
		if dbErr != nil {
			return dbErr
		}
		// 配置更新
		changeReq, confErr := elemToChangeReq(conf_center.UpdateConf, elem, tx)
		if confErr != nil {
			global.SysLog.Sugar().Errorf("elemToChangeReq err:%v", confErr)
			return confErr
		}
		confErr = conf_center.ConfChange(changeReq)
		if confErr != nil {
			global.SysLog.Sugar().Errorf("confChange err:%v", confErr)
			return confErr
		}
		return nil
	})
	return err
}

func (self *sensitiveElemReposPriv) QueryTotal(ctx context.Context) (common.QuerySensitiveElemTotal, error) {
	var result common.QuerySensitiveElemTotal
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&model.SensitiveElemDB{}).Where("built_in != 1").Count(&result.CustomSum).Error
	if err != nil {
		return result, err
	}
	err = db.Model(&model.SensitiveElemDB{}).Where("built_in = 1").Count(&result.InternalSum).Error
	return result, err
}
