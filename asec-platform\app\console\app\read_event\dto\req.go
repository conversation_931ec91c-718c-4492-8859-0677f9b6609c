package dto

import "time"

type GetReadEventCommonReq struct {
	StartTime              string    `json:"start_time" binding:"required"`
	EndTime                string    `json:"end_time" binding:"required"`
	FileCategoryIds        []string  `json:"file_category_ids"`
	SensitiveStrategy      []string  `json:"sensitive_strategy"`
	Keyword                string    `json:"keyword"`
	Channel                []string  `json:"channel"`
	ExcludeChannel         []string  `json:"exclude_channel"`
	IncludeFileCategoryIds []int64   `json:"include_file_category_ids"`
	UserIds                []string  `json:"user_ids"`
	UserGroupIds           []string  `json:"user_group_ids"`
	EnableAllUser          int       `json:"enable_all_user"`
	StartT                 time.Time `json:"-"`
	EndT                   time.Time `json:"-"`
}

type GetReadEventReq struct {
	GetReadEventCommonReq
	Limit        int      `json:"limit"`
	Offset       int      `json:"offset"`
	SoftwareName []string `json:"software_name"`
}

type UpdateEventFilterReq struct {
	Id            string   `json:"id" binding:"required"`
	FilterType    string   `json:"filter_type" binding:"required"`
	Process       []string `json:"process"`
	FileTypeCode  []int64  `json:"file_type_code"`
	EnableAllUser int      `json:"enable_all_user"`
	UserIds       []string `json:"user_ids"`
	UserGroupIds  []string `json:"user_group_ids"`
	ReserveDay    int      `json:"reserve_day"`
	ScanContent   int      `json:"scan_content"`
}

type ExcludeProcessReq struct {
	Process string `json:"process" binding:"required"`
}
