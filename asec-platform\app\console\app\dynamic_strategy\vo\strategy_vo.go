package vo

import (
	"asdsec.com/asec/platform/app/console/app/user/dto"
	model "asdsec.com/asec/platform/pkg/model/strategy_model"
	"encoding/json"
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"time"
)

type AddStrategyReq struct {
	StrategyName        string       `gorm:"column:strategy_name;comment:'策略名称'" json:"strategy_name" binding:"required"`
	UserIds             []string     `gorm:"column:user_ids;comment:'用户id'" json:"user_ids"`
	UserGroupIds        []string     `gorm:"column:user_group_ids;comment:'用户分组id'" json:"user_group_ids"`
	RoleIds             []string     `gorm:"column:user_role_ids;comment:'角色id'" json:"user_role_ids"`
	EnableAllUser       int          `gorm:"column:enable_all_user;comment:'全部用户'" json:"enable_all_user"`
	AppIds              []string     `gorm:"column:app_ids;comment:'应用id'" json:"app_ids"`
	EnableAllApp        int          `gorm:"column:enable_all_app;comment:'全部应用'" json:"enable_all_app"`
	AccessWay           []string     `gorm:"column:access_way;comment:'访问方式'" json:"access_way"`
	AccessConstraint    pgtype.JSONB `gorm:"column:access_constraint;comment:'访问约束'" json:"access_constraint"`
	Action              string       `gorm:"column:action;comment:'执行动作类型'" json:"action" binding:"required"`
	StrategyGroupId     string       `gorm:"column:strategy_group_id;comment:'策略分组id'" json:"strategy_group_id" binding:"required"`
	StrategyStatus      int          `gorm:"column:enable;comment:'是否启用'" json:"enable"  binding:"required"`
	ActionConfig        pgtype.JSONB `gorm:"column:action_config;comment:'动作所属配置文件'" json:"action_config"`
	NoticeId            string       `gorm:"column:notice_id;comment:'通知模板ID'" json:"notice_id"`
	DynamicRule         pgtype.JSONB `gorm:"column:dynamic_rule;comment:'动态策略规则'" json:"dynamic_rule"`
	TimeId              string       `gorm:"column:time_id;comment:'时间选择模板id'" json:"time_id"`
	SubAction           string       `gorm:"column:sub_action;comment:'执行动作子类型'" json:"sub_action"`
	DenyInfo            pgtype.JSONB `gorm:"column:deny_info;comment:'拒绝策略'" json:"deny_info"`
	AppGroupIds         []string     `gorm:"column:app_group_ids;comment:'应用标签id'" json:"app_tag_ids"`
	StrategyDetail      string       `gorm:"column:strategy_desc;comment:'策略描述'" json:"strategy_desc"`
	ExcludeUserIds      []string     `gorm:"column:exclude_user_ids;comment:'排除用户'" json:"exclude_user_ids"`
	ExcludeUserGroupIds []string     `gorm:"column:exclude_user_group_ids" json:"exclude_user_group_ids"`
	ExcludeUserRoleIds  []string     `gorm:"column:exclude_user_role_ids" json:"exclude_user_role_ids"`
	StartTime           string       `gorm:"column:start_time;comment:'开始时间'" json:"start_time"`
	EndTime             string       `gorm:"column:end_time;comment:'结束时间'" json:"end_time"`
	UserRiskRule        UserRiskRule `gorm:"-" json:"user_risk_rule"`
}

type UpdateStrategyReq struct {
	AddStrategyReq
	Id uint64 `json:"id,string" binding:"required"`
}

type StrategyDetailResp struct {
	Id                  uint64         `gorm:"column:id" json:"id,string" binding:"required"`
	StrategyName        string         `gorm:"column:strategy_name;comment:'策略名称'" json:"strategy_name" binding:"required"`
	UserIds             pq.StringArray `gorm:"column:user_ids;type:string;comment:'用户id'" json:"user_ids"`
	UserGroupIds        pq.StringArray `gorm:"column:user_group_ids;type:string;comment:'用户分组id'" json:"user_group_ids"`
	RoleIds             pq.StringArray `gorm:"column:role_ids;type:string;comment:'角色id'" json:"user_role_ids"`
	EnableAllUser       int            `gorm:"column:enable_all_user;comment:'全部用户'" json:"enable_all_user"`
	AppIds              pq.StringArray `gorm:"column:app_ids;type:string;comment:'应用id'" json:"app_ids"`
	EnableAllApp        int            `gorm:"column:enable_all_app;comment:'全部应用'" json:"enable_all_app"`
	AccessWay           pq.StringArray `gorm:"column:access_way;type:string;comment:'访问方式'" json:"access_way"`
	AccessConstraint    pgtype.JSONB   `gorm:"column:access_constraint;comment:'访问约束'" json:"access_constraint"`
	Action              string         `gorm:"column:action;comment:'执行动作类型'" json:"action" binding:"required"`
	StrategyGroupId     string         `gorm:"column:strategy_group_id;comment:'策略分组id'" json:"strategy_group_id" binding:"required"`
	StrategyStatus      int            `gorm:"column:strategy_status;comment:'是否启用'" json:"enable"  binding:"required"`
	ActionConfig        pgtype.JSONB   `gorm:"column:action_config;comment:'动作所属配置文件'" json:"action_config"`
	NoticeId            string         `gorm:"column:notice_id;comment:'通知模板ID'" json:"notice_id"`
	DynamicRule         pgtype.JSONB   `gorm:"column:dynamic_rule;comment:'动态策略规则'" json:"dynamic_rule"`
	TimeId              string         `gorm:"column:time_id;comment:'时间选择模板id'" json:"time_id"`
	SubAction           string         `gorm:"column:sub_action;comment:'执行动作子类型'" json:"sub_action"`
	DenyInfo            pgtype.JSONB   `gorm:"column:deny_info;comment:'拒绝策略'" json:"deny_info"`
	AppGroupIds         pq.StringArray `gorm:"column:app_group_ids;type:string;comment:'应用标签id'" json:"app_group_ids"`
	StrategyDetail      string         `gorm:"column:strategy_detail;comment:'策略描述'" json:"strategy_desc"`
	ExcludeUserIds      pq.StringArray `gorm:"column:exclude_user_ids;type:string;comment:'排除用户'" json:"exclude_user_ids"`
	ExcludeUserGroupIds pq.StringArray `gorm:"column:exclude_user_group_ids;type:string" json:"exclude_user_group_ids"`
	ExcludeUserRoleIds  pq.StringArray `gorm:"column:exclude_user_role_ids;type:string" json:"exclude_user_role_ids"`
	StartTime           string         `gorm:"column:start_time;comment:'开始时间'" json:"start_time"`
	EndTime             string         `gorm:"column:end_time;comment:'结束时间'" json:"end_time"`
	AppNames            pq.StringArray `gorm:"column:app_names;type:string" json:"app_names"`
	AppTagNames         pq.StringArray `gorm:"column:app_tag_names;type:string" json:"app_tag_names"`
	StrategyGroupName   string         `gorm:"strategy_group_name" json:"strategy_group_name"`
	MatchCount          int            `gorm:"match_count'" json:"match_count"`

	UserInfo        dto.UserComponentEchoRsp `gorm:"-" json:"user_info"`
	ExcludeUserInfo dto.UserComponentEchoRsp `gorm:"-" json:"exclude_user_info"`
	TimeDetail      *model.FactorTime        `gorm:"-" json:"time_detail,omitempty"`

	App              pgtype.JSONB `gorm:"column:app;comment:'动态策略规则';default:'{}'::jsonb" json:"app"`
	UserRiskRuleInfo pgtype.JSONB `gorm:"column:user_risk_rule;comment:'用户评分约束';default:'{}'::jsonb" json:"-"`
	UserRiskRule     UserRiskRule `gorm:"-" json:"user_risk_rule,omitempty"`
}

type UserRiskRule struct {
	Type      string `json:"type,omitempty"`
	Operator  string `json:"operator,omitempty"`
	Score     int    `json:"score,omitempty"`
	RiskLevel int    `json:"risk_level,omitempty"`
}

type DelStrategyReq struct {
	Ids  []string `json:"ids" binding:"required"`
	Name []string `json:"names"`
}

type EnableStrategyReq struct {
	DelStrategyReq
	// 1启用/2停止
	Enable int `gorm:"column:enable;comment:'是否启用'" json:"enable"  binding:"required,oneof=1 2"`
}

type CopyStrategyReq struct {
	Id string `json:"id" binding:"required"`
}

type StrategyMoveReq struct {
	StrategyId uint64 `json:"strategy_id,string" binding:"required"`
	GroupId    string `json:"group_id" binding:"required"`
	// 移动目标所属的优先级 (0代表置顶 -1代表置底)
	DestPriority int `json:"dest_priority"`
	// 移动位置 before xx之前/after xx之后
	Where string `json:"where" binding:"oneof=before after ''"`
}

type StrategySimpleListResp struct {
	Id           uint64 `json:"id,string"`
	Priority     int    `json:"priority"`
	StrategyName string `json:"strategy_name"`
}

type StrategyListReq struct {
	Limit       int    `json:"limit" binding:"required,min=1,max=1000"`
	Offset      int    `json:"offset" binding:"min=0"`
	Search      string `json:"search"`
	OrderColumn string `json:"order_column"`
	Order       string `json:"order"`
	GroupId     string `json:"group_id"`
	Id          uint64 `json:"id,string,omitempty"`
}

type StrategyListResp struct {
	TotalNum int                `json:"total_num"`
	ListData []StrategyListData `json:"list_data"`
}

type StrategyListData struct {
	GroupName         string              `json:"group_name"`
	GroupId           string              `json:"id"`
	GroupCount        int                 `json:"group_count"`
	GroupStrategyData []GroupStrategyData `json:"group_strategy_data"`
}

type GroupStrategyData struct {
	Id                  string            `gorm:"column:id;NOT NULL" json:"id"`
	StrategyName        string            `gorm:"column:strategy_name;comment:'策略名称'" json:"strategy_name"`
	StrategyDetail      string            `gorm:"column:strategy_desc;comment:'策略描述'" json:"strategy_desc"`
	GroupName           string            `gorm:"column:group_name" json:"group_name"`
	StrategyGroupId     string            `gorm:"column:strategy_group_id;comment:'策略分组id'" json:"strategy_group_id"`
	StrategySource      StrategySource    `gorm:"-" json:"strategy_source"`
	StrategyDest        StrategyDest      `json:"strategy_dest"`
	Action              string            `gorm:"column:action;comment:'执行动作类型'" json:"action"`
	ActionConfig        pgtype.JSONB      `gorm:"column:action_config;comment:'动作所属配置文件'" json:"action_config"`
	StrategyStatus      int               `gorm:"column:enable;comment:'是否启用'" json:"enable"`
	Priority            int               `gorm:"column:priority;comment:'优先级'" json:"priority"`
	TimeId              string            `gorm:"column:time_id;comment:'时间选择模板id'" json:"time_id"`
	CreatedAt           time.Time         `gorm:"column:created_at" json:"created_at"`
	UpdatedAt           time.Time         `gorm:"column:updated_at" json:"updated_at"`
	StartTime           string            `gorm:"column:start_time;comment:'开始时间'" json:"start_time"`
	EndTime             string            `gorm:"column:end_time;comment:'结束时间'" json:"end_time"`
	MatchCount          int               `gorm:"column:match_count'" json:"match_count"`
	MaxCountData        *time.Time        `gorm:"column:max_count_data'" json:"max_count_data"`
	HealthTip           HealthTip         `gorm:"-" json:"health_tip"`
	StrategyTimeExpired bool              `gorm:"-" json:"strategy_time_expired"`
	TimeDetail          *model.FactorTime `gorm:"-" json:"time_detail,omitempty"`
}

func (s HealthTip) MarshalJSON() ([]byte, error) {
	type alias HealthTip
	if s == (HealthTip{}) {
		return []byte("{}"), nil
	}
	return json.Marshal((alias)(s))
}

//func (s *GroupStrategyData) MarshalJSON() ([]byte, error) {
//	type alias GroupStrategyData
//	if s.HealthTip == nil {
//		s.HealthTip = []HealthTip{}
//	}
//	return json.Marshal((*alias)(s))
//}

type HealthTip struct {
	TipLevel int    `json:"tip_level"`
	Tip      string `json:"tip"`
}

type StrategySource struct {
	UserIds               pq.StringArray `gorm:"column:user_ids;comment:'用户id';type:string" json:"user_ids"`
	UserGroupIds          pq.StringArray `gorm:"column:user_group_ids;comment:'用户分组id';type:string" json:"user_group_ids"`
	RoleIds               pq.StringArray `gorm:"column:user_role_ids;comment:'角色id';type:string" json:"user_role_ids"`
	UserNames             pq.StringArray `json:"user_names"`
	UserGroupNames        pq.StringArray `json:"user_group_names"`
	UserRoleNames         pq.StringArray `json:"user_role_names"`
	EnableAllUser         int            `gorm:"column:enable_all_user;comment:'全部用户'" json:"enable_all_user"`
	DynamicRule           pgtype.JSONB   `gorm:"column:dynamic_rule;comment:'动态策略规则';default:'{}'::jsonb" json:"dynamic_rule"`
	ExcludeUserIds        pq.StringArray `gorm:"column:exclude_user_ids;comment:'排除用户';type:string" json:"exclude_user_ids"`
	ExcludeUserGroupIds   pq.StringArray `gorm:"column:exclude_user_group_ids;type:string" json:"exclude_user_group_ids"`
	ExcludeUserRoleIds    pq.StringArray `gorm:"column:exclude_user_role_ids;type:string" json:"exclude_user_role_ids"`
	ExcludeUserNames      pq.StringArray `json:"exclude_user_names"`
	ExcludeUserGroupNames pq.StringArray `json:"exclude_user_group_names"`
	ExcludeUserRoleNames  pq.StringArray `json:"exclude_user_role_names"`
	UserRiskRule          pgtype.JSONB   `gorm:"-" json:"user_risk_rule,omitempty"`
}

type StrategyDest struct {
	AppIds       pq.StringArray `gorm:"column:app_ids;comment:'应用id';type:string" json:"app_ids"`
	AppNames     pq.StringArray `json:"app_names"`
	EnableAllApp int            `gorm:"column:enable_all_app;comment:'全部应用'" json:"enable_all_app"`
	AppGroupIds  pq.StringArray `gorm:"column:app_tag_ids;comment:'应用标签id';type:bigint" json:"app_group_ids"`
	AppTagNames  pq.StringArray `json:"app_tag_names"`
	App          pgtype.JSONB   `gorm:"column:app;comment:'动态策略规则';default:'{}'::jsonb" json:"app"`
}
