package model

import (
	pkgmodel "asdsec.com/asec/platform/pkg/model"
	"github.com/lib/pq"
)

type SpaConfig struct {
	pkgmodel.BaseModel
	// 是否启用SPA功能
	EnableSPA bool `json:"enable_spa" gorm:"type:boolean;default:false"`
	// 白名单ip地址
	IpWhiteLists []IPWhiteList `json:"ip_white_lists" gorm:"serializer:json;type:jsonb"`
	// 允许的端口列表
	AllowPorts pq.StringArray `json:"allow_ports" gorm:"type:text[]"`
	// 是否启用TCP隐身
	EnableTCPInvisible bool `json:"enable_tcp_invisible" gorm:"type:boolean;default:false"`
	// 敲门周期(秒) 客户端每隔多少秒执行一次敲门
	KnockDoorInterval int64 `json:"knock_door_interval" gorm:"type:bigint"`
	// 服务端允许访问的持续时间，必须大于敲门周期
	ServerAllowAccessTime int64 `json:"server_allow_access_time" gorm:"type:bigint"`
	// 敲门时间误差（秒） 允许客户端与服务端的时间偏差范围
	KnockDoorTimeError int64 `json:"knock_door_time_error" gorm:"type:bigint"`
	// 激活码有效期 分钟
	ActivationCodeValidity int64 `json:"activation_code_validity" gorm:"type:bigint"`
}

func (s *SpaConfig) TableName() string {
	return "tb_spa_config"
}

type IPWhiteList struct {
	StartIP string `json:"start_ip"` // 起始IP
	EndIP   string `json:"end_ip"`   // 结束IP
	Mask    int    `json:"mask"`     // CIDR掩码位
}
