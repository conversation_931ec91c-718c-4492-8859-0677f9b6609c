package model

import "time"

// ApplicationAccount 应用账户模型（根据实际PostgreSQL表结构）
type ApplicationAccount struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	CorpID      int64     `gorm:"column:corp_id;not null" json:"corp_id"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updated_at"`
	LocalUserID string    `gorm:"column:local_user_id;size:50;not null" json:"local_user_id"`
	AppID       int64     `gorm:"column:app_id" json:"app_id"`
	Username    string    `gorm:"column:username;size:255;not null" json:"username"`
	Password    string    `gorm:"column:password;size:255;not null" json:"password"`
}

// TableName 指定表名
func (ApplicationAccount) TableName() string {
	return "tb_application_account"
}

// FormFillResponse 表单代填响应结构
type FormFillResponse struct {
	ErrCode string       `json:"errcode"`
	ErrMsg  string       `json:"errmsg"`
	Data    FormFillData `json:"data"`
}

// FormFillData 表单代填数据
type FormFillData struct {
	AccountDataValue string `json:"AccountDataValue"` // 账户名
	PwdDataValue     string `json:"PwdDataValue"`     // 密码
	FormType         string `json:"FormType"`         // 表单类型
	AutoLogin        string `json:"AutoLogin"`        // 是否自动登录
	AutoLoginPed     string `json:"AutoLoginPed"`     // 自动登录周期
}

// FormFillConfig 表单代填配置
type FormFillConfig struct {
	FormType     string             `json:"form_type"`      // 表单类型：auto_fill | custom_fill
	LoginURL     string             `json:"login_url"`      // 登录页面URL
	Data         FormFillDataConfig `json:"data"`           // 配置数据
	AutoLogin    string             `json:"auto_login"`     // 是否自动登录
	AutoLoginPed string             `json:"auto_login_ped"` // 自动登录周期
}

// FormFillDataConfig 表单代填数据配置
type FormFillDataConfig struct {
	// 智能代填配置
	FillAuthType string `json:"fill_auth_type"` // 认证类型：phone_pwd, mail_pwd, system_set, custom_set, mail_pre_pwd
	AutoLogin    string `json:"auto_login"`     // 是否自动登录
	AutoLoginPed string `json:"auto_login_ped"` // 自动登录周期

	// 自定义代填配置
	AccountInputType  string `json:"account_input_type"`  // 账号输入类型：fixed | system_try
	AccountInputValue string `json:"account_input_value"` // 账号输入选择器
	PwdInputType      string `json:"pwd_input_type"`      // 密码输入类型：fixed | system_try
	PwdInputValue     string `json:"pwd_input_value"`     // 密码输入选择器
	SubmitInputType   string `json:"submit_input_type"`   // 提交按钮类型：fixed | system_try
	SubmitInputValue  string `json:"submit_input_value"`  // 提交按钮选择器

	// 系统设置的账户密码
	CustomUsernameValue string `json:"custom_username_value"` // 自定义用户名
	CustomPasswordValue string `json:"custom_password_value"` // 自定义密码
}

// UserInfo 用户信息
type UserInfo struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	Password string `json:"password"` // 注意：实际生产环境中密码应该是加密的
}

// UserCredentials 用户凭证
type UserCredentials struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// ApplicationConfig 应用配置
type ApplicationConfig struct {
	ID             string      `json:"id"`
	Name           string      `json:"name"`
	PublishAddress string      `json:"publish_address"`
	FormFillConfig interface{} `json:"form_fill_config"` // 表单代填配置
}

// SingleSignOnConfig 单点登录配置
type SingleSignOnConfig struct {
	Type   string      `json:"type"`   // 单点登录类型
	Config interface{} `json:"config"` // 配置数据
}

// UpdateAccountRequest 更新账户请求
type UpdateAccountRequest struct {
	AppID    string `json:"app_id" binding:"required"`
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UserAppCredentials 用户应用凭证映射
type UserAppCredentials struct {
	ID         string `json:"id"`
	UserID     string `json:"user_id"`
	AppID      string `json:"app_id"`
	Username   string `json:"username"`
	Password   string `json:"password"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
}

// FormFillRequest 表单代填请求结构
type FormFillRequest struct {
	AppID  string `json:"app_id" binding:"required"`
	UserID string `json:"user_id" binding:"required"`
	Action string `json:"action" binding:"required"`
}
