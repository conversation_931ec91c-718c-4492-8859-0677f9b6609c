package repository

import (
	comm "asdsec.com/asec/platform/app/console/app/appliancemgt/common"
	"asdsec.com/asec/platform/app/console/app/events/constants"
	"asdsec.com/asec/platform/app/console/app/events/dto"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	"encoding/json"
	normErrors "errors"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/lib/pq"
	"gorm.io/gorm"
	"sort"
	"strconv"
	"strings"
	"time"
)

// AnalysisRepository 接口定义
type AnalysisRepository interface {
	DeleteHistory(ctx context.Context, ids []string) error
	FilterCondition(ctx context.Context) (dto.ConditionResp, error)
	GetEventsList(ctx context.Context, req dto.GetEventListReq, downloadQuery bool) (dto.GetEventListRsp, error)
	GetEventsDetail(ctx context.Context, id string) (model.FileEvents, error)
	GetFileType(ctx context.Context) ([]model.FileType, error)
	GetActivityType(ctx context.Context, buildIn string) ([]model.ChannelTypeDB, error)
	GetFileTypes(ctx context.Context) ([]model.FileType, error)
	GetActivityList(ctx context.Context) ([]model.SpecialConfig, error)
	GetEventCondition(ctx context.Context, req dto.GetEventConditionReq) (dto.Condition, error)
	GetHistoryList(ctx context.Context, req dto.GetHistoryListReq) ([]dto.GetHistoryListRsp, error)
	GetEventsUserList(ctx context.Context, req dto.GetEventListReq) (dto.GetUsersRsp, error)
	DeleteEvents(ctx context.Context, req dto.Ids) error
}

// NewAnalysisRepository 创建接口实现接口实现
func NewAnalysisRepository() AnalysisRepository {
	return &analysisRepository{}
}

type analysisRepository struct {
}

func (a *analysisRepository) DeleteEvents(ctx context.Context, req dto.Ids) error {
	ckDb, err := global.GetCkClient(ctx)
	if err != nil {
		return err
	}
	alertTb := model.AlertEvent{}.TableName()
	//删除对应的原始事件
	err = ckDb.Debug().Exec(fmt.Sprintf("DELETE FROM %s where file_event_id in ?", alertTb), req.Ids).Error
	if err != nil {
		return err
	}
	//删除对应的UEBA事件
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	//判断incident事件表和id数组的交集，存在交集则删除
	err = db.Where("raw_event_ids && ?", pq.Array(req.Ids)).Delete(&model.UEBAIncidentDB{}).Error
	if err != nil {
		return err
	}
	eventsTb := model.FileEvents{}.TableName()
	//删除对应的原始事件
	err = ckDb.Debug().Exec(fmt.Sprintf("DELETE FROM %s where uuid in ?", eventsTb), req.Ids).Debug().Error
	if err != nil {
		return err
	}
	return nil
}

func (a *analysisRepository) GetEventsUserList(ctx context.Context, req dto.GetEventListReq) (dto.GetUsersRsp, error) {
	ckDb, err := global.GetCkClient(ctx)
	if err != nil {
		return dto.GetUsersRsp{}, err
	}

	ckDb, err = getEventsDbByCod(ckDb, ctx, req)
	if err != nil {
		return dto.GetUsersRsp{}, err
	}
	// 查数据
	if req.FileKeyword != "" {
		search := "%" + dbutil.EscapeForLike(req.FileKeyword) + "%"
		ckDb = ckDb.Where("(md5 ILIKE ? OR file_name ILIKE ?)", search, search)
	}
	// 查人
	if req.NameKeyword != "" {
		search := "%" + dbutil.EscapeForLike(req.NameKeyword) + "%"
		ckDb = ckDb.Where("user_name ILIKE ?", search)
	}
	var eventCount int
	err = ckDb.Model(model.FileEvents{}).Select("count(uuid) as event_count").Find(&eventCount).Error
	if err != nil {
		return dto.GetUsersRsp{}, err
	}
	ckDb = ckDb.Model(model.FileEvents{}).
		Select("user_id,count(`uuid`) as event_count,arrayDistinct(flatten(groupUniqArray(user_name))) as user_names").
		Where("empty(user_id) = 0 ").Group("user_id").Order("count(`uuid`) desc")
	// 分页
	pageReq := model.Pagination{Limit: req.Limit, Offset: req.Offset}
	var userList []dto.GetUserItem
	pageReq, err = model.Paginate(&userList, &pageReq, ckDb)
	if err != nil {
		return dto.GetUsersRsp{}, err
	}
	focusMap, err := commonApi.GetFocusMap(ctx)
	if err != nil {
		return dto.GetUsersRsp{}, err
	}
	userEchoMap, err := commonApi.UserEchoMap(ctx, nil)
	if err != nil {
		return dto.GetUsersRsp{}, err
	}
	userIds := make([]string, 0)
	for index, user := range userList {
		userList[index].UserFocus = focusMap[user.UserId]
		if userEchoMap[user.UserId].UserPath == "" {
			userList[index].UserPath = "-"
		} else {
			userList[index].UserPath = userEchoMap[user.UserId].UserPath
		}
		if userEchoMap[user.UserId].UserName == "" {
			if len(user.UserNames) == 0 {
				userList[index].UserName = "-"
			} else {
				userList[index].UserName = user.UserNames[0]
			}
		} else {
			userList[index].UserName = userEchoMap[user.UserId].UserName
		}
		userIds = append(userIds, user.UserId)
	}
	userTagMap, err := commonApi.GetUserTagByUserId(ctx, userIds)
	if err != nil {
		return dto.GetUsersRsp{}, err
	}
	for index, user := range userList {
		if userTagMap[user.UserId] == nil {
			userList[index].TotalTags = make([]string, 0)
		} else {
			userList[index].TotalTags = userTagMap[user.UserId]
		}
	}
	return dto.GetUsersRsp{
		UserList:   userList,
		EventCount: eventCount,
		CommonPage: model.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page}}, nil
}

func (a *analysisRepository) GetHistoryList(ctx context.Context, req dto.GetHistoryListReq) ([]dto.GetHistoryListRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	startT := time.Now().Add(-30 * 24 * time.Hour)
	db = db.Model(model.EventsHistoryQuery{}).
		Select("CAST(id AS text) as id,name,type,condition,create_time").
		Where("create_time >= ?", startT).
		Order("create_time desc")
	var res []dto.GetHistoryListRsp
	if req.Type != "" {
		db = db.Where("type = ?", req.Type)
	}
	if req.Search != "" {
		search := dbutil.EscapeForLike(req.Search)
		db = db.Where("name like ?", search)
	}

	err = db.Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (a *analysisRepository) GetEventCondition(ctx context.Context, req dto.GetEventConditionReq) (dto.Condition, error) {
	conditionDb, err := global.GetCkClient(ctx)
	if err != nil {
		return dto.Condition{}, err
	}
	conditionDb = conditionDb.Model(model.FileEvents{})
	var eventReq dto.GetEventListReq
	err = copier.Copy(&eventReq, &req)
	if err != nil {
		return dto.Condition{}, err
	}
	conditionDb, err = getEventsDbByCod(conditionDb, ctx, eventReq)
	if err != nil {
		return dto.Condition{}, err
	}
	// 查数据
	if req.FileKeyword != "" {
		search := "%" + dbutil.EscapeForLike(req.FileKeyword) + "%"
		conditionDb = conditionDb.Where("(md5 ILIKE ? OR file_name ILIKE ?)", search, search)
	}
	// 查人
	if req.NameKeyword != "" {
		search := "%" + dbutil.EscapeForLike(req.NameKeyword) + "%"
		conditionDb = conditionDb.Where("user_name ILIKE ?", search)
	}
	// 筛选条件返回
	condition, err := GetConditionByEvents(conditionDb, ctx)
	if err != nil {
		return dto.Condition{}, err
	}
	return condition, nil
}

func getEventsDbByCod(ckDb *gorm.DB, ctx context.Context, req dto.GetEventListReq) (*gorm.DB, error) {
	ckDb = ckDb.Where("activity != 'access' AND empty(user_id) = 0")
	// 时间
	if !req.StartT.IsZero() && !req.EndT.IsZero() {
		ckDb = ckDb.Where("toDateTime(occur_time, 'Asia/Shanghai') >= ? AND toDateTime(occur_time, 'Asia/Shanghai') <= ?", req.StartT, req.EndT)
	}
	// 身份标签
	if len(req.UserTags) > 0 {
		ckDb = ckDb.Where("hasAny(user_tags,array(?))", req.UserTags)
	}
	// 敏感等级
	if len(req.DataCondition.SensitiveLevel) > 0 {
		ckDb = ckDb.Where("sensitive_level IN ?", req.DataCondition.SensitiveLevel)
	}
	// 敏感数据
	if len(req.DataCondition.SensitiveIds) > 0 {
		ckDb = ckDb.Where("sensitive_rule_id IN ?", req.DataCondition.SensitiveIds)
	}
	// 文件类型
	if len(req.DataCondition.FileCategoryIds) > 0 {
		ckDb = ckDb.Where("has(array(?),SUBSTRING(CAST(file_category_id AS String),1,4)) ", req.DataCondition.FileCategoryIds)
	}
	// 行为 - 数据获取/使用
	if len(req.BehaviorCondition.Activity) > 0 {
		ckDb = ckDb.Where("activity IN ? ", req.BehaviorCondition.Activity)
	}
	// 通道
	if len(req.BehaviorCondition.ChannelList) > 0 {
		ckDb = ckDb.Where("channel IN ? ", req.BehaviorCondition.ChannelList)
	}
	userIds := make([]string, 0)
	// 根据所属组织获取user_id
	userIds, err := commonApi.GetUserIdByGroups(ctx, req.GroupIds)
	if err != nil {
		return nil, err
	}
	// 关注用户ids
	userIds = append(userIds, req.UserIds...)
	// 用户查询
	if len(userIds) > 0 || len(req.GroupIds) > 0 {
		ckDb = ckDb.Where("user_id IN ? ", userIds)
	}
	// 风险等级
	if len(req.Severity) > 0 {
		ckDb = ckDb.Where("LOWER(severity) IN ? ", req.Severity)
	}
	// 关注事件ids
	if len(req.EventIds) > 0 {
		ckDb = ckDb.Where("uuid IN ? ", req.EventIds)
	}
	if len(req.SourceIds) > 0 {
		ckDb = ckDb.Where("source_id IN ? ", req.SourceIds)
	}
	return ckDb, nil
}

var (
	querySelect = "user_id,uuid,corp_id,event_type,event_sub_type,user_name,file_name,file_type,file_size,activity," +
		"toDateTime(occur_time, 'Asia/Shanghai') AS occur_time,channel,score,data_category,user_tags,file_category_id," +
		"LOWER(severity) as severity,severity_id,dst_path,channel_type,file_category,file_type_name," +
		"CASE WHEN sensitive_level = 0  THEN '非敏感数据' ELSE sensitive_rule_name END as sensitive_rule_name,sensitive_level,sensitive_rule_id," +
		"visitParamExtractInt(score_reason,'tag_score') as tag_score,visitParamExtractString(score_reason,'tag_name') as tag_name," +
		"visitParamExtractInt(score_reason,'channel_score') as channel_score,visitParamExtractInt(score_reason,'sensitive_data_score') as sensitive_data_score," +
		"visitParamExtractInt(score_reason,'hide_suffix_score') as hide_suffix_score,visitParamExtractInt(score_reason,'file_compression_score') as file_compression_score," +
		"visitParamExtractInt(score_reason,'rename_score') as rename_score,visitParamExtractInt(score_reason,'copy_score') as copy_score"
	downloadSelect = "agent_name,source_name,src_path,file_path,md5,sha256,user_id,uuid,corp_id,event_type,event_sub_type,user_name,file_name,file_type,file_size,activity," +
		"toDateTime(occur_time, 'Asia/Shanghai') AS occur_time,channel,score,data_category,user_tags,file_category_id," +
		"LOWER(severity) as severity,severity_id,dst_path,channel_type,file_category,file_type_name," +
		"CASE WHEN sensitive_level = 0  THEN '非敏感数据' ELSE sensitive_rule_name END as sensitive_rule_name,sensitive_level,sensitive_rule_id," +
		"visitParamExtractInt(score_reason,'tag_score') as tag_score,visitParamExtractString(score_reason,'tag_name') as tag_name," +
		"visitParamExtractInt(score_reason,'channel_score') as channel_score,visitParamExtractInt(score_reason,'sensitive_data_score') as sensitive_data_score," +
		"visitParamExtractInt(score_reason,'hide_suffix_score') as hide_suffix_score,visitParamExtractInt(score_reason,'file_compression_score') as file_compression_score," +
		"visitParamExtractInt(score_reason,'rename_score') as rename_score,visitParamExtractInt(score_reason,'copy_score') as copy_score"
)

func (a *analysisRepository) GetEventsList(ctx context.Context, req dto.GetEventListReq, downloadQuery bool) (dto.GetEventListRsp, error) {
	ckDb, err := global.GetCkClient(ctx)
	if err != nil {
		return dto.GetEventListRsp{}, err
	}
	if downloadQuery {
		ckDb = ckDb.Model(model.FileEvents{}).Select(downloadSelect)
	} else {
		ckDb = ckDb.Model(model.FileEvents{}).Select(querySelect)
	}

	ckDb, err = getEventsDbByCod(ckDb, ctx, req)
	if err != nil {
		return dto.GetEventListRsp{}, err
	}
	// 用户视角user_id
	if req.UserId != "" {
		ckDb = ckDb.Where("user_id = ?", req.UserId)
	}
	// 分页
	pageReq := model.Pagination{Limit: req.Limit, Offset: req.Offset}
	needSaveHistory := false
	saveName := ""
	saveType := ""
	// 查人
	if req.NameKeyword != "" {
		pageReq.SearchColumns = []string{"user_name"}
		pageReq.Search = req.NameKeyword
		needSaveHistory = true
		saveName = req.NameKeyword
		saveType = constants.HistoryUserType
	}
	// 查数据
	if req.FileKeyword != "" {
		pageReq.SearchColumns = []string{"md5", "file_name"}
		pageReq.Search = req.FileKeyword
		needSaveHistory = true
		saveName = req.FileKeyword
		saveType = constants.HistoryDataType
	}
	// 排序
	ckDb = ckDb.Order(req.Order)
	// 事件查询分页
	var fileEventList []model.FileEvents
	pageReq, err = model.Paginate(&fileEventList, &pageReq, ckDb)
	if err != nil {
		return dto.GetEventListRsp{}, err
	}
	focusMap, err := commonApi.GetFocusMap(ctx)
	if err != nil {
		return dto.GetEventListRsp{}, err
	}
	channelMap, err := commonApi.GetAllChannel(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return dto.GetEventListRsp{}, err
	}
	activityList, err := commonApi.GetSpecialConfigByType(ctx, constants.ActivityType)
	if err != nil {
		global.SysLog.Sugar().Errorf("get activity list failed. err=%v", err)
		return dto.GetEventListRsp{}, err
	}
	activityMap := make(map[string]string)
	for _, v := range activityList {
		activityMap[v.Key] = v.Value
	}
	userMap := make(map[string]bool)
	for k, v := range fileEventList {
		userMap[v.UserId] = true
		fileEventList[k].UserFocus, fileEventList[k].EventFocus = focusMap[v.UserId], focusMap[v.Uuid]
		fileEventList[k].ActivityDesc = channelMap[v.Channel] + activityMap[v.Activity]
		if channelMap[v.Channel] == "" {
			fileEventList[k].ActivityDesc = strings.ReplaceAll(v.Channel, constants.UdefChannel, "") + activityMap[v.Activity]
		}
		fileEventList[k].FileNameDesc = fmt.Sprintf("%s(%s %s)", v.FileName, fileEventList[k].FileCategory, utils.FormatBytes(float64(v.FileSize)))
		if v.SensitiveRuleId != "" {
			fileEventList[k].SensitiveDesc = fmt.Sprintf("L%d %s", v.SensitiveLevel, v.SensitiveRuleName)
		} else {
			fileEventList[k].SensitiveDesc = "-"
		}
		fileEventList[k].UserNameDesc = v.UserName
		if len(v.UserTags) > 0 {
			fileEventList[k].UserNameDesc = v.UserName + "(" + strings.Join(v.UserTags, ",") + ")"
		}
	}
	// 保存历史查询
	if needSaveHistory {
		err = saveHistory(ctx, req, saveType, saveName)
		if err != nil {
			global.SysLog.Sugar().Errorf("save history failed. err=%v", err)
			return dto.GetEventListRsp{}, err
		}
	}
	return dto.GetEventListRsp{
		EvenList:   fileEventList,
		UserCount:  len(userMap),
		CommonPage: model.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page}}, nil
}

func saveHistory(ctx context.Context, req dto.GetEventListReq, historyType, name string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	condition, err := json.Marshal(req)
	if err != nil {
		return err
	}
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return err
	}
	return db.Create(&model.EventsHistoryQuery{
		Condition:  string(condition),
		Type:       historyType,
		CreateTime: time.Now(),
		Name:       name,
		Id:         strconv.FormatUint(id, 10),
	}).Error
}

func GetConditionByEvents(db *gorm.DB, ctx context.Context) (dto.Condition, error) {
	userTags := make([]dto.CommonCondition, 0)
	sensitiveLevelCod := make([]dto.CommonCondition, 0)
	sensitiveStrategyCod := make([]dto.CommonCondition, 0)
	fileCategoryCod := make([]dto.CommonCondition, 0)
	getActivityCod := make([]dto.CommonCondition, 0)
	useActivityCod := make([]dto.CommonCondition, 0)
	channelCod := make([]dto.CommonCondition, 0)
	sourceCod := make([]dto.CommonCondition, 0)

	// user_tags
	err := db.Select("arrayJoin(user_tags  AS tag) AS name,COUNT(tag) AS count,arrayJoin(user_tags  AS tag) AS id").WithContext(ctx).
		Group("arrayJoin(user_tags  AS tag)").Order("arrayJoin(user_tags  AS tag) ASC").Find(&userTags).
		Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get user_tags condition failed. err=%v", err)
		return dto.Condition{}, err
	}

	// sensitive_level
	err = db.Select("CASE WHEN CAST(sensitive_level AS String) ='0' then '非敏感数据' ELSE 'L' || CAST(sensitive_level AS String) END  AS name," +
		"COUNT(uuid) AS count,sensitive_level AS id").WithContext(ctx).Order("sensitive_level desc").
		Group("sensitive_level").Find(&sensitiveLevelCod).
		Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get sensitive_level condition failed. err=%v", err)
		return dto.Condition{}, err
	}
	// sensitive_rule_id
	err = db.Select("sensitive_rule_name AS name,COUNT(uuid) AS count,sensitive_rule_id AS id").WithContext(ctx).
		Where("empty(sensitive_rule_id) = 0").Group("sensitive_rule_id,sensitive_rule_name,sensitive_rule_id").
		Order("sensitive_rule_name desc").Find(&sensitiveStrategyCod).
		Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get sensitive_rule_id condition failed. err=%v", err)
		return dto.Condition{}, err
	}
	// file_category_id
	err = db.Select("COUNT(uuid) AS count,SUBSTRING(CAST(file_category_id AS String),1,4) AS id,file_category as name").WithContext(ctx).
		Group("SUBSTRING(CAST(file_category_id AS String),1,4),file_category").Find(&fileCategoryCod).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get file_category_id condition failed. err=%v", err)
		return dto.Condition{}, err
	}

	// data_source get
	// todo 未来还有ftp下载等需要区分开
	err = db.Select("COUNT(uuid) AS count,activity AS id").WithContext(ctx).Where("activity IN ?", constants.GetActivityList).
		Group("activity").Find(&getActivityCod).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get activity source condition failed. err=%v", err)
		return dto.Condition{}, err
	}
	// git
	var gitCod dto.CommonCondition
	err = db.Select("COUNT(uuid) AS count,(channel || ' ' || activity) AS name,activity as id").WithContext(ctx).Where("activity IN ?", constants.GitActivityList).Group("channel,activity").
		Find(&gitCod).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get git activity source condition failed. err=%v", err)
		return dto.Condition{}, err
	}
	// data_source use
	err = db.Select("COUNT(uuid) AS count,activity AS id").WithContext(ctx).Where("activity IN ?", constants.UseActivityList).
		Group("activity").Find(&useActivityCod).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("use activity source condition failed. err=%v", err)
		return dto.Condition{}, err
	}
	activityList, err := commonApi.GetSpecialConfigByType(ctx, constants.ActivityType)
	if err != nil {
		global.SysLog.Sugar().Errorf("get activity list failed. err=%v", err)
		return dto.Condition{}, err
	}
	activityMap := make(map[string]string)
	for _, v := range activityList {
		activityMap[v.Key] = v.Value
	}
	activityMap[constants.DownloadActivity] = constants.DownloadActivityName
	activityMap[constants.GitPushActivity] = constants.GitPushActivityName
	activityMap[constants.SvnCommitActivity] = constants.SvnCommitNameActivity

	for k, v := range getActivityCod {
		getActivityCod[k].Name = activityMap[v.Id]
	}
	for k, v := range useActivityCod {
		useActivityCod[k].Name = activityMap[v.Id]
	}
	if gitCod.Count > 0 {
		getActivityCod = append(getActivityCod, gitCod)
	}

	// channel
	err = db.Select("COUNT(uuid) AS count,channel AS id").WithContext(ctx).Where("empty(channel) = 0 AND empty(channel_type) = 0 AND activity IN ('send','clipboard_paste_text')").
		Group("channel").Find(&channelCod).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get channel condition failed. err=%v", err)
		return dto.Condition{}, err
	}
	channelMap, err := commonApi.GetAllChannel(ctx)
	if err != nil {
		global.SysLog.Sugar().Errorf("get channel map failed. err=%v", err)
		return dto.Condition{}, err
	}
	for k, v := range channelCod {
		if channelMap[v.Id] == "" {
			if strings.HasPrefix(v.Id, constants.UdefChannel) {
				channelCod[k].Name = strings.ReplaceAll(v.Id, constants.UdefChannel, "")
			}
		} else {
			channelCod[k].Name = channelMap[v.Id]
		}
	}
	// source
	err = db.Select("COUNT(uuid) AS count,source_id as id,source_name as name").WithContext(ctx).Where("empty(source_id) = 0").
		Group("source_id,source_name,occur_time").Order("occur_time asc").Find(&sourceCod).Error
	if err != nil {
		global.SysLog.Sugar().Errorf("get source condition failed. err=%v", err)
		return dto.Condition{}, err
	}
	sourceMap := make(map[string]dto.CommonCondition)
	for _, v := range sourceCod {
		tmp := sourceMap[v.Id]
		tmp.Id = v.Id
		tmp.Name = v.Name
		tmp.Count = tmp.Count + v.Count
		sourceMap[v.Id] = tmp
	}
	sourceCod = sourceCod[:0]
	for _, v := range sourceMap {
		sourceCod = append(sourceCod, v)
	}
	// 排序
	sort.Slice(fileCategoryCod, func(i, j int) bool {
		if fileCategoryCod[j].Id == constants.OtherCondition {
			return true
		}
		return strings.Compare(fileCategoryCod[i].Name, fileCategoryCod[j].Name) > 0
	})
	sort.Slice(getActivityCod, func(i, j int) bool {
		return strings.Compare(getActivityCod[i].Name, getActivityCod[j].Name) > 0
	})
	sort.Slice(useActivityCod, func(i, j int) bool {
		return strings.Compare(useActivityCod[i].Name, useActivityCod[j].Name) > 0
	})
	sort.Slice(channelCod, func(i, j int) bool {
		return strings.Compare(channelCod[i].Name, channelCod[j].Name) > 0
	})
	sort.Slice(sourceCod, func(i, j int) bool {
		return strings.Compare(sourceCod[i].Name, sourceCod[j].Name) > 0
	})
	return dto.Condition{
		UserTags:          userTags,
		SensitiveLevel:    sensitiveLevelCod,
		SensitiveStrategy: sensitiveStrategyCod,
		FileCategoryIds:   fileCategoryCod,
		GetActivity:       getActivityCod,
		UseActivity:       useActivityCod,
		ChannelList:       channelCod,
		SourceList:        sourceCod,
	}, nil
}

func (a *analysisRepository) GetActivityList(ctx context.Context) ([]model.SpecialConfig, error) {
	client, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	var resList []model.SpecialConfig
	err = client.Model(&model.SpecialConfig{}).
		Select("id,key,value").
		Where("type=?", "activity").Order("id").
		Find(&resList).Error
	if err != nil {
		return nil, err
	}
	return resList, nil
}

func (a *analysisRepository) GetFileTypes(ctx context.Context) ([]model.FileType, error) {
	var data []model.FileType
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return data, err
	}
	err = db.Model(&model.FileType{}).Where("parent_id = 0").Find(&data).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return data, err
	}
	for k, v := range data {
		data[k].Children = getSubFileType(ctx, v.Id)
	}
	return data, nil
}

func (a *analysisRepository) GetActivityType(ctx context.Context, builtIn string) ([]model.ChannelTypeDB, error) {
	var data []model.ChannelTypeDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return data, err
	}
	if builtIn != "" {
		db = db.Where("built_in = ? AND channel != 'unknow'", builtIn)
	}
	err = db.Model(&model.ChannelTypeDB{}).Where("pid = '0'").Find(&data).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return data, err
	}
	for k, v := range data {
		data[k].Children = getSubActivityType(ctx, v.Id, builtIn)
	}
	return data, nil
}

func (a *analysisRepository) GetFileType(ctx context.Context) ([]model.FileType, error) {
	var data []model.FileType
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return data, err
	}
	err = db.Model(&model.FileType{}).Where("parent_id = 0").Find(&data).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return data, err
	}
	return data, nil
}

func getSubFileType(ctx context.Context, pid int64) []model.FileType {
	var tmp []model.FileType
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return tmp
	}
	err = db.Model(&model.FileType{}).Where("parent_id = ?", pid).Find(&tmp).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return tmp
	}
	if len(tmp) > 0 {
		for k, v := range tmp {
			tmp[k].Children = getSubFileType(ctx, v.Id)
		}
	}
	return tmp
}

func getSubActivityType(ctx context.Context, pid uint64, builtIn string) []model.ChannelTypeDB {
	var tmp []model.ChannelTypeDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return tmp
	}
	if builtIn != "" {
		db = db.Where("built_in = ?", builtIn)
	}
	pidStr := strconv.FormatUint(pid, 10)
	err = db.Model(&model.ChannelTypeDB{}).Where("pid = ?", pidStr).Find(&tmp).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return tmp
	}
	if len(tmp) > 0 {
		for k, v := range tmp {
			tmp[k].Children = getSubActivityType(ctx, v.Id, builtIn)
		}
	}
	return tmp
}

func (a *analysisRepository) GetEventsDetail(ctx context.Context, id string) (model.FileEvents, error) {
	var data model.FileEvents
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return data, err
	}
	err = db.Model(model.FileEvents{}).Select("uuid,corp_id,event_type,event_sub_type,user_name,file_name,toDateTime(file_create_time, 'Asia/Shanghai') AS file_create_time,"+
		"file_type,activity,toDateTime(occur_time, 'Asia/Shanghai') AS occur_time,channel,score,data_category,user_tags,event_source,agent_name,file_path,"+
		"lower(severity) as severity,severity_id,file_size,owner,file_create_time,md5,sha256,channel,channel_type,file_category_id,toDateTime(last_change_time, 'Asia/Shanghai') AS last_change_time,"+
		"data_category,sensitive_rule_name,sensitive_level,plat_type,dst_path,src_path,source_name,source_type,original_file_name,original_file_path,"+
		"visitParamExtractString(process_info,'software_name') as software,visitParamExtractString(process_info,'process_name') as process_name,"+
		"visitParamExtractString(process_info,'full_process_image') as process_path,file_category,file_type_name").
		Where("uuid = ?", id).First(&data).Error
	if err != nil {
		global.SysLog.Error(err.Error())
		return model.FileEvents{}, err
	}

	if strings.HasPrefix(data.Channel, constants.UdefChannel) {
		data.Channel = strings.ReplaceAll(data.Channel, constants.UdefChannel, "")
	}
	// 这里查询失败不返回空，这里存在识别不出来的文件类型
	if err != nil && !normErrors.Is(err, comm.ErrUnSupportFileType) {
		global.SysLog.Error(err.Error())
	}
	return data, nil
}

func (a *analysisRepository) FilterCondition(ctx context.Context) (dto.ConditionResp, error) {
	var data model.FilterCondition
	var res dto.ConditionResp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return res, err
	}
	err = db.Model(&model.FilterCondition{}).First(&data).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return res, err
	}
	if err == gorm.ErrRecordNotFound {
		return res, nil
	}
	err = json.Unmarshal([]byte(data.Condition), &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

func (a *analysisRepository) DeleteHistory(ctx context.Context, ids []string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return err
	}
	return db.Model(&model.EventsHistoryQuery{}).Where("id in ?", ids).Delete(&model.EventsHistoryQuery{}).Error
}

func getFileTypeByCode(ctx context.Context, code []interface{}) []interface{} {
	var res []interface{}
	var data []model.FileType
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return res
	}
	err = db.Model(&model.FileType{}).Where("code in (?)", code).Find(&data).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return res
	}
	for _, v := range data {
		res = append(res, v.Name)
	}
	return res
}
