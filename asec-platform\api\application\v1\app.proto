syntax = "proto3";
package asdsec.core.api.app;

//import "asdsec/core/api/common/appliance_info.proto";
option go_package = "asdsec.com/asec/platform/api/app/v1;v1";


message SeGetAppReq{
  uint64 appliance_id = 2;
}

message GetAppResp{
  repeated AppInfo apps = 1;
}

message AppInfo{
  uint64 app_id = 1;
  string app_name = 2;
  string port = 3;
  string address = 4;
  string protocol = 5;
}
message SERouteReq{
  uint64 corp_id = 1;
  uint64 appliance_id = 2;
}

message SERoute{
  repeated uint64 connectors = 1;
  repeated uint64 app_ids = 2;
  repeated string address = 3;
}

message SERoutes{
  repeated SERoute se_route = 1;
  //connector id数组，去重，用于配置反向代理
  repeated uint64 connectors = 2;
}

message SeGetStrategyResp{
  repeated SeStrategy se_strategy = 1;
}

message SeStrategy{
  uint64 strategy_id = 1;
  repeated uint64 app_ids = 2;
  repeated string user_ids = 3;
  string start_time = 4;
  string end_time = 5;
  uint32 enable_all_user = 6;
  uint32 enable_all_app = 7;
  uint32 enable_log = 8;
  string strategy_name = 9;
}

message WebAccessInfoReq{}

message WebAccessInfoResp{
  repeated StrategyInfo StrategyInfo = 1;
}

message WebAppInfo{
  uint64 app_id = 1;
  string app_name = 2;
  int32 port = 3;
  string address = 4;
  string protocol = 5;
  string uri = 6;
  string server_address = 7;
  string idp_id = 8;
  int32 app_status = 9; // 应用状态：1=启用，2=维护中，3=禁用
}

message StrategyInfo{
  uint64 strategy_id = 1;
  repeated uint64 app_ids = 2;
  repeated string user_ids = 3;
  string start_time = 4;
  string end_time = 5;
  uint32 enable_all_user = 6;
  uint32 enable_all_app = 7;
  uint32 enable_log = 8;
  string strategy_name = 9;
  string action = 10;
  int32 priority = 11;
  bytes rego_file = 12;
  TimeGap time_gap = 13;
  string user_risk_rule = 14;
}

message TimeGap{
  string time_id = 1;
  string gap_name = 2;
  int32 interval_type = 3;
  bool sunday = 4;
  bool monday = 5;
  bool tuesday = 6;
  bool wednesday = 7;
  bool thursday = 8;
  bool friday = 9;
  bool saturday = 10;
  string daily_start_time = 11;
  string daily_end_time = 12;
  bool all_day_enable = 13;
}

message WebAppInfoReq{
}

message WebAppInfoResp{
  repeated WebAppInfo web_app_info = 1;
}

message UciUserInfoReq{

}

message UciUserInfoResp{
  repeated UciUserInfo uci_user_info = 1;
}

message UciUserInfo{
  string user_id = 1;
  uint32 score = 2;
  uint32 risk_level = 3;
}
message WebGatewayRsAppReq{
  string appliance_id = 1;
}
message WebGatewayRsAppResp{
  repeated ApplicationGatewayInfo apps = 1;
}
message WebGatewayRsCrtReq{
}
message WebGatewayRsCrtResp{
  repeated Crt crts = 1;
}
message Crt{
  string cert = 1;
  string key = 2;
  string id = 3;
  repeated string domain = 4;
}

message WebGatewayWatermarkReq{
}
message WebGatewayWatermarkResp{
  Watermark WatermarkConf = 1;
}
message Watermark{
  bool enable = 1;
  repeated string app_ids = 2;
  bool enable_all_app = 3;
  bool enable_all_app_group = 4;
  bool user_all = 5;
  repeated string user_ids = 6;
  repeated string group_ids = 7;
  bool exclude_user_all = 8;
  repeated string exclude_user_ids = 9;
  repeated string exclude_group_ids = 10;
  repeated string content_config = 11;
  FontStyle font_style = 12;
  bool secret = 13;
}
message FontStyle{
  string color = 1;
  uint32 alpha = 2;
  uint32 size = 3;
  uint32 rotate = 4;
  uint32 line_spacing = 5;
  uint32 columns_spacing =6;
}
message ApplicationGatewayInfo{
  string id = 1;
  string app_name = 2;
  string server_address = 3;
  string server_schema = 4;
  string publish_address = 5;
  string publish_schema = 6;
  bytes web_compatible_config = 7;
  bytes health_config = 9;
  string uri = 8;
}

message WebGatewayHostsReq{
}
message WebGatewayHostsResp{
  repeated WebGatewayHosts hosts = 1;
}

message WebGatewayHosts{
  string host = 1;
  repeated string address = 2;
  string remark = 3;
}

message WebGatewayVirtualIPPoolsReq {
  string appliance_id = 1;
}

message WebGatewayVirtualIPPoolsResp {
    repeated VirtualIPPoolConfig pools = 1;
    VirtualIPGlobalSettings global_settings = 2;  // 添加全局设置
}

message VirtualIPGlobalSettings {
    bool enabled = 1;                    // 全局功能开关
    int32 global_max_duration = 2;       // 全局最大分配时长(小时)
    string description = 3;              // 功能描述
}

message VirtualIPPoolConfig {
  string id = 1;
  string name = 2;
  string ip_range = 3;
  string pool_type = 4; // "shared" 或 "dedicated"
  int32 ip_expiry_duration = 5;
  int32 cleanup_interval = 6;
  AllocationPolicy allocation_policy = 7;
  ReleasePolicy release_policy = 8;
  repeated DirectoryConfig directory_configs = 9;
  repeated DedicatedIPConfig dedicated_configs = 10; // 独享IP配置
}

message AllocationPolicy {
  string strategy = 1; // "round_robin", "random", "least_used"
  int32 max_ips_per_user = 2;
}

message ReleasePolicy {
  bool release_on_logout = 1;
  int32 idle_timeout_hours = 2;
  bool force_release_on_limit = 3;
}

message DirectoryConfig {
  string directory_id = 1;
  string directory_name = 2;
  repeated string user_groups = 3;
  string config = 4; 
}

message DedicatedIPConfig {
  string user_id = 1;
  string user_name = 2;
  repeated string virtual_ips = 3;
  int32 priority = 4;
}

// 网关上报IP分配状态
message ReportVirtualIPAllocationsReq {
  string appliance_id = 1;
  repeated IPAllocation allocations = 2;
  map<string, PoolStats> pool_stats = 3;
  int64 report_time = 4; // Unix时间戳
}

message ReportVirtualIPAllocationsResp {
  bool success = 1;
  string message = 2;
  int32 error_code = 3;      
  repeated string failed_ips = 4;  
}

message IPAllocation {
  string ip_address = 1;
  string user_id = 2;
  string device_id = 3;
  string pool_name = 4;
  int64 allocated_at = 5;
  int64 expires_at = 6;
  int64 last_used_at = 7;
  string status = 8;
  
  // 当前报告周期内的流量增量
  int64 period_upstream_bytes = 9;   
  int64 period_downstream_bytes = 10;
  
  // 总累计流量（整个分配期间的总量）
  int64 total_upstream_bytes = 11;     
  int64 total_downstream_bytes = 12;
  
  // 统计信息
  int32 session_count = 13;
  int64 last_traffic_time = 14;        // 最后有流量的时间
}

message PoolStats {
  int32 total_ips = 1;
  int32 allocated_ips = 2;
  int32 available_ips = 3;
  int64 last_updated = 4;     // Unix时间戳
}

message TrafficStatsReq {
  string appliance_id = 1;
  repeated TrafficBucket traffic_buckets = 2;
  int64 report_time = 3;
  int32 processed_buckets = 4;    
  int32 failed_buckets = 5;      
}


message TrafficBucket {
  string ip_address = 1;
  string user_id = 2;
  int64 bucket_start_time = 3;         // 时间桶开始时间
  int64 bucket_end_time = 4;           // 时间桶结束时间
  int64 upstream_bytes = 5;            // 该时间段内的上行流量
  int64 downstream_bytes = 6;          // 该时间段内的下行流量
  int32 session_count = 7;             // 该时间段内的会话数
}

message TrafficStatsResp {
  bool success = 1;
  string message = 2;
}

// 网关命令管理
message GatewayCommandReq {
  string appliance_id = 1;
}

message GatewayCommandResp {
  repeated GatewayCommand commands = 1;
}

message GatewayCommand {
  string command_id = 1;           // 命令ID
  string command_type = 2;         // 命令类型: "virtual_ip", "network", "security"等
  string action = 3;               // 命令动作: "manual_release", "sync_release", "update_config"等
  string target_resource = 4;      // 目标资源标识符(如IP地址、用户ID等)
  map<string, string> parameters = 5; // 命令参数(key-value格式，支持扩展)
  string reason = 6;               // 执行原因
  int64 created_at = 7;            // 命令创建时间
  int64 expires_at = 8;            // 命令过期时间
  string status = 9;               // 命令状态: "pending", "processing", "completed", "failed"
  string created_by = 10;          // 创建者
}

message GatewayCommandResultReq {
  string appliance_id = 1;
  repeated GatewayCommandResult results = 2;
}

message GatewayCommandResultResp {
  bool success = 1;
  string message = 2;
}

message GatewayCommandResult {
  string command_id = 1;           // 命令ID
  string status = 2;               // 执行状态: "completed", "failed"
  string message = 3;              // 执行结果消息
  int64 executed_at = 4;           // 执行时间
  map<string, string> result_data = 5; // 执行结果数据
}

// 网关拉取SSO IDP配置请求
message WebGatewayRsSSOIDPReq{
  string corp_id = 1;   // 企业ID
  string type = 2;      // IDP类型: dingtalk/feishu/qiyewx
}

// 网关拉取SSO IDP配置响应
message WebGatewayRsSSOIDPResp{
  repeated SSOIDPInfo idps = 1;
}

// SSO IDP配置信息
message SSOIDPInfo{
  string id = 1;                    // IDP ID
  string name = 2;                  // IDP名称
  string type = 3;                  // IDP类型
  string corp_id = 4;               // 企业ID
  string app_id = 5;                // 应用ID
  string app_key = 6;               // 应用Key
  string app_secret = 7;            // 应用Secret (加密)
  string redirect_uri = 8;          // 回调地址
  bool enable = 9;                  // 是否启用
  int64 created_at = 10;            // 创建时间
  int64 updated_at = 11;            // 更新时间
}

// 网关获取平台域名请求
message WebGatewayPlatformDomainReq{
  uint64 appliance_id = 1;   // 网关ID
}

// 网关获取平台域名响应  
message WebGatewayPlatformDomainResp{
  string platform_domain = 1;   // 平台域名
  bool success = 2;             // 是否成功获取
  string message = 3;           // 错误信息
}

service App{
  // 网关获取应用信息
  rpc SeGetApp(SeGetAppReq) returns (GetAppResp);
  // connector模式下网关拉取路由信息
  rpc SERoute(SERouteReq) returns(SERoutes);
  // 网关获取策略信息
  rpc SeGetStrategy(SeGetAppReq) returns (SeGetStrategyResp);
  // web网关获取应用
  rpc WebAppInfo(WebAppInfoReq) returns (WebAppInfoResp);
  // web网关获取策略
  rpc WebAccessInfo(WebAccessInfoReq) returns (WebAccessInfoResp);
  // 网关获取用户评分
  rpc UciUserInfo(UciUserInfoReq) returns (UciUserInfoResp);
  // 网关拉取关联应用
  rpc WebGatewayRsApp(WebGatewayRsAppReq) returns (WebGatewayRsAppResp);
  // 网关拉取关联证书
  rpc WebGatewayRsCrt(WebGatewayRsCrtReq) returns (WebGatewayRsCrtResp);
  // 网关拉取web水印
  rpc WebGatewayWatermark(WebGatewayWatermarkReq) returns (WebGatewayWatermarkResp);
  // 网关拉取Hosts
  rpc WebGatewayHosts(WebGatewayHostsReq) returns (WebGatewayHostsResp);
  // 网关拉取虚拟IP池配置
  rpc WebGatewayVirtualIPPools(WebGatewayVirtualIPPoolsReq) returns (WebGatewayVirtualIPPoolsResp);
  // 网关上报IP分配状态
  rpc ReportVirtualIPAllocations(ReportVirtualIPAllocationsReq) returns (ReportVirtualIPAllocationsResp);
  // 网关上报流量统计
  rpc ReportTrafficStats(TrafficStatsReq) returns (TrafficStatsResp);
  // 网关命令拉取
  rpc GatewayCommand(GatewayCommandReq) returns (GatewayCommandResp);
  // 网关命令结果报告
  rpc ReportGatewayCommandResult(GatewayCommandResultReq) returns (GatewayCommandResultResp);
  // 网关拉取SSO配置
  rpc WebGatewayRsSSOIDP(WebGatewayRsSSOIDPReq) returns (WebGatewayRsSSOIDPResp);
  // 网关获取连接的平台域名
  rpc WebGatewayPlatformDomain(WebGatewayPlatformDomainReq) returns (WebGatewayPlatformDomainResp);
}

