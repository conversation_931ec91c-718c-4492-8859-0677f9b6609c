package api

import (
	subcommon "asdsec.com/asec/platform/app/console/app/sensitive_elem/common"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/constants"
	"asdsec.com/asec/platform/app/console/app/sensitive_elem/service"
	"asdsec.com/asec/platform/app/console/utils/web"
	"errors"
	"strings"

	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

// QuerySensitiveElemTag godoc
// @Summary 查询敏感数据标签
// @Schemes
// @Description 查询敏感数据标签
// @Tags        sensitive strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_element/tags [GET]
// @success     200 {object} common.Response{data=[]model.SensitiveElemTagDB} "ok"
func QuerySensitiveElemTag(ctx *gin.Context) {
	respData, err := service.GetSensitiveElemTagService().All(ctx)
	if err != nil {
		global.SysLog.Error("query SensitiveElemTag list error", zap.Error(err))
		common.Fail(ctx, common.QuerySensitiveElemTagErr)
		return
	}
	common.OkWithData(ctx, respData)
}

// AddSensitiveElemTag godoc
// @Summary 增加敏感数据标签
// @Schemes
// @Description 增加敏感数据标签
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.AddSensitiveElemTagReqData true "增加敏感数据标签"
// @Success     200
// @Router      /v1/sensitive_element/tags [POST]
// @success     200 {object} common.Response{} "ok"
func AddSensitiveElemTag(ctx *gin.Context) {
	var req subcommon.AddSensitiveElemTagReqData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	var errstr string
	err := service.GetSensitiveElemTagService().Add(ctx, req.Name)
	if err != nil {
		global.SysLog.Error("add SensitiveElemTag list error", zap.Error(err))
		common.Fail(ctx, common.AddSensitiveElemTagErr)
		errstr = err.Error()
		return
	}
	defer subcommon.RecordLog(ctx, common.SensitiveStrategyElemTagType, common.OperateCreate, req.Name, errstr)
	common.Ok(ctx)
}

// DeleteSensitiveElemTag godoc
// @Summary 删除敏感数据标签
// @Schemes
// @Description 删除敏感数据标签
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.IDReqData true "删除敏感数据标签"
// @Success     200
// @Router      /v1/sensitive_element/tags [DELETE]
// @success     200 {object} common.Response{} "ok"
func DeleteSensitiveElemTag(ctx *gin.Context) {
	var req subcommon.IDReqData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	var errstr string
	err := service.GetSensitiveElemTagService().Delete(ctx, req.Id)
	if err != nil {
		global.SysLog.Error("delete SensitiveElemTag list error", zap.Error(err))
		common.Fail(ctx, common.DeleteSensitiveElemTagErr)
		errstr = err.Error()
		return
	}
	defer subcommon.RecordLog(ctx, common.SensitiveStrategyElemTagType, common.OperateDelete, strconv.Itoa(req.Id), errstr)
	common.Ok(ctx)
}

// QuerySensitiveElem godoc
// @Summary 查询敏感数据
// @Schemes
// @Description 查询敏感数据
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.QuerySensitiveElemReqData true "查询敏感数据"
// @Success     200
// @Router      /v1/sensitive_elem [GET]
// @success     200 {object} common.Response{data=common.QuerySensitiveElemRespData} "ok"
func QuerySensitiveElem(ctx *gin.Context) {
	var req subcommon.QuerySensitiveElemReqData
	if err := ctx.ShouldBindQuery(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	data, err := service.GetSensitiveElemService().Query(ctx, req)
	if err != nil {
		global.SysLog.Error("query SensitiveElemTag list error", zap.Error(err))
		common.Fail(ctx, common.QuerySensitiveElemErr)
		return
	}
	common.OkWithData(ctx, data)
}

// QuerySensitiveAlgorithmElem godoc
// @Summary 查询敏感元素算法模型
// @Schemes
// @Description 查询敏感元素算法模型
// @Tags        sensitive strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_elem/algorithm [GET]
// @success     200 {object} common.Response{data=[]common.SensitiveElem} "ok"
func QuerySensitiveAlgorithmElem(ctx *gin.Context) {
	data, err := service.GetSensitiveElemService().QueryAlgorithmList(ctx)
	if err != nil {
		global.SysLog.Error("query SensitiveElemAlgorithm list error", zap.Error(err))
		common.Fail(ctx, common.QuerySensitiveElemErr)
		return
	}
	common.OkWithData(ctx, data)
}

// AddSensitiveElem godoc
// @Summary 添加敏感数据
// @Schemes
// @Description 添加敏感数据
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.SensitiveElemReqData true "添加敏感数据"
// @Success     200
// @Router      /v1/sensitive_elem [POST]
// @success     200 {object} common.Response{} "ok"
func AddSensitiveElem(ctx *gin.Context) {
	var req subcommon.SensitiveElemReqData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	var errstr string
	data, err := service.GetSensitiveElemService().QueryItemByNameAndCategory(ctx, req.ElementName, req.Type, constants.CustomBuiltInType)
	if err != nil {
		global.SysLog.Error("add SensitiveElem list error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		errstr = err.Error()
		return
	}
	if data.Id > 0 {
		common.Fail(ctx, common.AddSensitiveElemErr)
		return
	}
	err = service.GetSensitiveElemService().Add(ctx, req)
	if err != nil {
		global.SysLog.Error("add SensitiveElem list error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		errstr = err.Error()
		return
	}
	defer subcommon.RecordLog(ctx, common.SensitiveStrategyElemType, common.OperateCreate, req.ElementName, errstr)
	common.Ok(ctx)
}

// DeleteSensitiveElem godoc
// @Summary 删除敏感数据
// @Schemes
// @Description 删除敏感数据
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.IDReqData true "删除敏感数据"
// @Success     200
// @Router      /v1/sensitive_elem [DELETE]
// @success     200 {object} common.Response{} "ok"
func DeleteSensitiveElem(ctx *gin.Context) {
	var req subcommon.IDReqData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	var errstr string
	strategyDB, err := service.GetSensitiveStrategyService().QueryAssocStrategy(ctx, req.Id)
	if err != nil {
		global.SysLog.Error("delete SensitiveElem list error", zap.Error(err))
		common.Fail(ctx, common.DeleteSensitiveElemErr)
		errstr = err.Error()
		return
	}
	if len(strategyDB) > 0 {
		global.SysLog.Error("associated strategy", zap.Error(err))
		common.Fail(ctx, common.DeleteSensitiveElemAssocErr)
		errstr = "associated strategy "
		return
	}
	err = service.GetSensitiveElemService().Delete(ctx, req.Id)
	if err != nil {
		global.SysLog.Error("delete SensitiveElem list error", zap.Error(err))
		common.Fail(ctx, common.DeleteSensitiveElemErr)
		errstr = err.Error()
		return
	}
	defer subcommon.RecordLog(ctx, common.SensitiveStrategyElemType, common.OperateDelete, req.Name, errstr)
	common.Ok(ctx)
}

// ChangeSensitiveElem godoc
// @Summary 更新敏感数据
// @Schemes
// @Description 更新敏感数据
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.SensitiveElemReqData true "更新敏感数据"
// @Success     200
// @Router      /v1/sensitive_elem [PUT]
// @success     200 {object} common.Response{} "ok"
func ChangeSensitiveElem(ctx *gin.Context) {
	var req subcommon.SensitiveElemReqData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	var errstr string
	data, err := service.GetSensitiveElemService().QueryItemByNameAndCategory(ctx, req.ElementName, req.Type, req.BuiltIn)
	if err != nil {
		global.SysLog.Error("add SensitiveElem list error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		errstr = err.Error()
		return
	}
	if data.Id > 0 && data.Id != req.Id {
		common.Fail(ctx, common.ChangeSensitiveElemErr)
		return
	}
	err = service.GetSensitiveElemService().Change(ctx, req)
	if err != nil {
		global.SysLog.Error("change SensitiveElem list error", zap.Error(err))
		common.Fail(ctx, common.ChangeSensitiveElemErr)
		errstr = err.Error()
		return
	}
	defer subcommon.RecordLog(ctx, common.SensitiveStrategyElemType, common.OperateUpdate, req.ElementName, errstr)
	common.Ok(ctx)
}

// SensitiveElemTotal godoc
// @Summary 查询敏感数据总数
// @Schemes
// @Description 查询敏感数据总数
// @Tags        sensitive strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_elem/total [GET]
// @success     200 {object} common.Response{subcommon.QuerySensitiveElemTotal} "ok"
func SensitiveElemTotal(ctx *gin.Context) {
	data, err := service.GetSensitiveElemService().QueryTotal(ctx)
	if err != nil {
		global.SysLog.Error("change SensitiveElem list error", zap.Error(err))
		common.Fail(ctx, common.SensitiveElemTotalErr)
		return
	}
	common.OkWithData(ctx, data)
}

// QuerySensitiveStrategy godoc
// @Summary 查询敏感策略
// @Schemes
// @Description 查询敏感策略
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.QuerySensitiveStrategyReqData true "查询敏感策略"
// @Success     200
// @Router      /v1/sensitive_strategy/query [POST]
// @success     200 {object} common.Response{data=common.QuerySensitiveStrategyRespData} "ok"
func QuerySensitiveStrategy(ctx *gin.Context) {
	var req subcommon.QuerySensitiveStrategyReqData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}

	respData, err := service.GetSensitiveStrategyService().Query(ctx, req)

	//service
	if err != nil {
		global.SysLog.Error("query SensitiveStrategy list error", zap.Error(err))
		common.Fail(ctx, common.QuerySensitiveStrategyErr)
		return
	}
	common.OkWithData(ctx, respData)
}

// AddSensitiveStrategy godoc
// @Summary 添加敏感策略
// @Schemes
// @Description 添加敏感策略
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.SensitiveStrategyBaseData true "添加敏感策略"
// @Success     200
// @Router      /v1/sensitive_strategy [POST]
// @success     200 {object} common.Response{} "ok"
func AddSensitiveStrategy(ctx *gin.Context) {
	var req subcommon.SensitiveStrategyBaseData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	if len(req.IdentifyWay) == 0 {
		global.SysLog.Error("add sensitive strategy,no identify way")
		common.Fail(ctx, common.CheckAddStrategyReqRequiredError)
		return
	}

	if err := service.GetSensitiveStrategyService().CheckAddStrategyReq(ctx, req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.CheckAddStrategyReqError)
		return
	}

	if err := checkFileNameRule(req); err != nil {
		global.SysLog.Error("checkFileNameRule err", zap.Error(err))
		common.Fail(ctx, common.CheckFileNameRuleErr)
		return
	}

	req.CorpId = web.GetAdminCorpId(ctx)
	var errstr string
	err := service.GetSensitiveStrategyService().Add(ctx, req)
	if err != nil {
		global.SysLog.Error("add SensitiveStrategy list error", zap.Error(err))
		common.Fail(ctx, common.AddSensitiveStrategyErr)
		errstr = err.Error()
		return
	}

	defer subcommon.RecordLog(ctx, common.SensitiveStrategyType, common.OperateCreate, req.RuleName, errstr)
	common.Ok(ctx)
}

// checkFileNameRule 检查敏感元素文件名 规则参数
func checkFileNameRule(req subcommon.SensitiveStrategyBaseData) error {
	err := checkFileRule(req.FileNameRule)
	if err != nil {
		return err
	}
	err = checkFileRule(req.ContentRule)
	if err != nil {
		return err
	}
	return nil
}

func checkFileRule(rule []subcommon.SensitiveStrategyFileNameRule) error {
	if len(rule) == 0 {
		return nil
	}
	for _, r := range rule {
		if r.Operator == "count" && r.Count <= 0 {
			return errors.New("count operator need count exceeds 0")
		}
	}
	return nil
}

// DeleteSensitiveStrategy godoc
// @Summary 删除敏感策略
// @Schemes
// @Description 删除敏感策略
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.SnowflakeIDReqData true "删除敏感策略"
// @Success     200
// @Router      /v1/sensitive_strategy [DELETE]
// @success     200 {object} common.Response{} "ok"
func DeleteSensitiveStrategy(ctx *gin.Context) {
	var req subcommon.SnowflakeIDReqData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	var errstr string
	err := service.GetSensitiveStrategyService().Delete(ctx, req.Id)
	if err != nil {
		global.SysLog.Error("delete SensitiveStrategy list error", zap.Error(err))
		common.Fail(ctx, common.DeleteSensitiveStrategyErr)
		errstr = err.Error()
		return
	}
	defer subcommon.RecordLog(ctx, common.SensitiveStrategyType, common.OperateDelete, req.Name, errstr)
	common.Ok(ctx)
}

// ChangeSensitiveStrategy godoc
// @Summary 更新敏感策略
// @Schemes
// @Description 更新敏感策略
// @Tags        sensitive strategy
// @Produce     application/json
// @Param       req body subcommon.ChangeSensitiveStrategyReqData true "更新敏感策略"
// @Success     200
// @Router      /v1/sensitive_strategy [PUT]
// @success     200 {object} common.Response{} "ok"
func ChangeSensitiveStrategy(ctx *gin.Context) {
	var req subcommon.ChangeSensitiveStrategyReqData
	if err := ctx.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	if len(req.IdentifyWay) == 0 {
		global.SysLog.Error("change sensitive strategy,no identify way")
		common.Fail(ctx, common.CheckAddStrategyReqRequiredError)
		return
	}
	if err := service.GetSensitiveStrategyService().CheckAddStrategyReq(ctx, req.SensitiveStrategyBaseData); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.CheckAddStrategyReqError)
		return
	}
	if err := checkFileNameRule(req.SensitiveStrategyBaseData); err != nil {
		global.SysLog.Error("checkFileNameRule err", zap.Error(err))
		common.Fail(ctx, common.CheckFileNameRuleErr)
		return
	}
	var errstr string
	err := service.GetSensitiveStrategyService().Change(ctx, req)
	if err != nil {
		global.SysLog.Error("change SensitiveStrategy list error", zap.Error(err))
		common.Fail(ctx, common.ChangeSensitiveStrategyErr)
		errstr = err.Error()
		return
	}
	defer subcommon.RecordLog(ctx, common.SensitiveStrategyType, common.OperateUpdate, req.RuleName, errstr)
	common.Ok(ctx)
}

// DefaultStrategySummary godoc
// @Summary 规则推荐统计
// @Schemes
// @Description 规则推荐统计
// @Tags        sensitive strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_strategy/summary [GET]
// @success     200 {object} common.Response{} "ok"
func DefaultStrategySummary(ctx *gin.Context) {
	data, err := service.GetSensitiveStrategyService().StrategySummary(ctx)
	if err != nil {
		global.SysLog.Error("Query DefaultSensitiveStrategy list error", zap.Error(err))
		common.Fail(ctx, common.QueryDefaultSensitiveStrategyErr)
		return
	}
	common.OkWithData(ctx, data)
}

// ElemSummary godoc
// @Summary 敏感元素统计
// @Schemes
// @Description 敏感元素统计
// @Tags        sensitive strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_elem/summary [GET]
// @success     200 {object} common.Response{} "ok"
func ElemSummary(c *gin.Context) {
	data, err := service.GetSensitiveElemService().Summary(c)
	if err != nil {
		global.SysLog.Error("ElemSummary error:", zap.Error(err))
		common.Fail(c, common.QuerySensitiveElemErr)
		return
	}
	common.OkWithData(c, data)
}

// DelSensitiveStgList godoc
// @Summary 批量删除敏感数据
// @Schemes
// @Description 批量删除敏感数据
// @Tags        sensitive strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_strategy/batch [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelSensitiveStgList(c *gin.Context) {
	var req subcommon.DelSensitiveStgListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	err := service.GetSensitiveStrategyService().DelSensitiveStgList(c, req.Ids)
	if err != nil {
		global.SysLog.Error("DelSensitiveStrategy error:", zap.Error(err))
		common.FailAError(c, err)
		return
	}
	errStr := ""
	if err != nil {
		errStr = err.Error()
	}
	defer subcommon.RecordLog(c, common.SensitiveStrategyType, common.OperateDelete, strings.Join(req.Names, ","), errStr)
	common.Ok(c)
}

// UpdateStgList godoc
// @Summary 批量启用禁用敏感数据
// @Schemes
// @Description 批量启用禁用敏感数据
// @Tags        sensitive strategy
// @Produce     application/json
// @Success     200
// @Router      /v1/sensitive_strategy/batch [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateStgList(c *gin.Context) {
	var req subcommon.UpdateStgBatchReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	err := service.GetSensitiveStrategyService().UpdateEnableStgBatch(c, req)
	if err != nil {
		global.SysLog.Error("EnableBatchStg error:", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	errStr := ""
	if err != nil {
		errStr = err.Error()
	}
	var opener = common.OperateEnable
	if req.Enable == 2 {
		opener = common.OperateDisable
	}
	defer subcommon.RecordLog(c, common.SensitiveStrategyType, opener, strings.Join(req.Names, ","), errStr)
	common.Ok(c)
}
