#!/bin/bash

set -e

# 简单的JSON处理函数
json_set_value() {
    local file=$1
    local key=$2
    local value=$3

    # 创建目录和文件
    mkdir -p "$(dirname "$file")"
    [ ! -f "$file" ] && echo "{}" > "$file"

    # 如果字段已存在，更新它
    if grep -q "\"$key\"[[:space:]]*:" "$file"; then
        sed -i "s/\"$key\"[[:space:]]*:[[:space:]]*\"[^\"]*\"/\"$key\": \"$value\"/g" "$file"
    else
        # 添加新字段
        if grep -q '^[[:space:]]*{[[:space:]]*}[[:space:]]*$' "$file"; then
            echo "{\"$key\": \"$value\"}" > "$file"
        else
            sed -i "0,/{/{s/{/{\"$key\": \"$value\",/}" "$file"
        fi
    fi
}

if [ -z "$ASEC_RESTART_COUNT" ]; then
  export ASEC_RESTART_COUNT=0
fi

#获取当前文件路径
current_dir=$(cd "$(dirname "$0")"; pwd)
current_filepath="$current_dir/$(basename "$0")"

#命令行相关变量
cmd_list=$@
channel=""
# TODO 一键安装平台+网关场景
install_gateway=false
deploy_mode=""
slave=false
#命令行参数解析

ARGS=$(getopt -o h:c:t:s:g:o:s: --long help:channel:deploy_mode:gateway:offline:slave: -- "$@")
eval set -- "$ARGS"

while true; do
    case $1 in
        -h|--help)
            cat usage
            exit 0
            ;;
        -c|--channel)
            channel=$2
            shift 2
            ;;
        -t|--deploy_mode)
            deploy_mode=$2
            shift 2
            ;;
        -g|--gateway)
            install_gateway=$2
            shift 2
            ;;
        -o|--offline)
            offline=$2
            shift 2
            ;;
        -s|--slave)
            slave=$2
            shift 2
            ;;
        --)
            shift
            break
            ;;
        *)
            shift
            ;;
    esac
done

# 定义日志文件路径
LOG_DIR="/opt/platform/logs"
INSTALL_LOG_FILE="${LOG_DIR}/install_$(date +'%Y%m%d-%H%M%S').log"

# 创建日志目录
mkdir -p $LOG_DIR

# 函数用于记录日志
function log() {
    echo "[`date +'%Y-%m-%d %H:%M:%S'`] $1" | tee -a $INSTALL_LOG_FILE
}

log "Starting platform installation."

if [ -z "$channel" ];then
    echo "please use the option -c/--channel to provide the client download channel."
    exit 1
fi

if [ -z "$deploy_mode" ];then
    echo "please provide the option -t/--deploy_mode to specify the client deployment mode."
    exit 1
fi

if [ "$deploy_mode" != "public" ] && [ "$deploy_mode" != "private" ];then
    echo "$deploy_mode"
    echo "the client deployment mode should be either \"public\" or \"private\"."
    exit 1
fi

function pre_install() {
    log "Pre-installation steps..."
    #判断当前用户是否为root
    if [ $(id -u) -ne 0 ]; then
        echo "请使用 root 或 sudo 权限运行此脚本."
        exit 10
    fi
    #设置时区
    timedatectl set-timezone Asia/Shanghai
    # 初始化配置
    chmod +x $current_dir/init_conf.sh
    source $current_dir/init_conf.sh
}

#如果是从节点,写入slave标记
function slave_mark() {
    if [ "$slave" = "true" ]; then
        echo "Slave is true, write slave mark file..."
        touch /etc/asec/slave
    else
        rm -f /etc/asec/slave
    fi
}

function install_docker(){
    log "[install docker] start..."

    # 移除jq依赖，使用sed处理JSON

    # 检查Docker是否已安装
    if command -v docker &> /dev/null && docker --version &> /dev/null; then
        log "Docker已安装，版本信息: $(docker --version)"
    else
        log "Docker未安装，开始安装..."
        if [ ! -x "$current_dir"/dependency/install.sh ];then
            chmod +x "$current_dir"/dependency/install.sh
        fi
        "$current_dir"/dependency/install.sh "$offline" >> "${INSTALL_LOG_FILE}"
    fi

    # --- 配置Docker Bridge网络 ---
    # 从配置文件读取bridge网段，如果没有则使用默认值
    local docker_bridge_network=$(grep '^docker_bridge_network' /etc/asec/config.ini | cut -d '=' -f 2 | xargs)
    if [ -z "$docker_bridge_network" ]; then
        docker_bridge_network="************/24"
        log "警告: 未找到docker_bridge_network配置，使用默认值: $docker_bridge_network"
    fi

    local daemon_file="/etc/docker/daemon.json"

    log "配置Docker bridge网络, 网段为: $docker_bridge_network"

    # 使用sed处理JSON配置
    json_set_value "$daemon_file" "bip" "$docker_bridge_network"

    if [ $? -eq 0 ]; then
        log "成功更新 /etc/docker/daemon.json"
        log "重启Docker服务以应用网络配置..."
        systemctl daemon-reload
        systemctl restart docker
        log "Docker服务已重启."
    else
        log "错误: 更新 $daemon_file 失败。安装中止。"
        exit 1
    fi

    # --- 配置用户自定义的platform_network ---
    platform_network_subnet=$(grep '^platform_network_subnet' /etc/asec/config.ini | cut -d '=' -f 2 | xargs)
    platform_network_gateway=$(grep '^platform_network_gateway' /etc/asec/config.ini | cut -d '=' -f 2 | xargs)

    log "开始创建Docker网络 platform_network"
    log "使用配置网段: $platform_network_subnet, 网关: $platform_network_gateway"

    # 创建网络
    if ! docker network inspect platform_network > /dev/null 2>&1; then
        docker network create --subnet=$platform_network_subnet --gateway=$platform_network_gateway platform_network

        # 验证网络创建
        if [ $? -eq 0 ]; then
            log "网络创建成功!"
        else
            log "错误: 创建网络失败，将使用默认配置尝试创建"
            docker network create platform_network
        fi
    else
        log "platform_network网络已存在，配置信息:"
    fi

    # 显示网络信息
    network_info=$(docker network inspect platform_network | grep -A 5 "Config")
    subnet=$(echo "$network_info" | grep "Subnet" | awk -F '"' '{print $4}')
    gateway=$(echo "$network_info" | grep "Gateway" | awk -F '"' '{print $4}')
    log "网段: $subnet"
    log "网关: $gateway"

    log "[install docker] finished"

    # 尝试加载离线镜像，如果离线镜像不存在，则登录在线仓库
    if [ -f "$current_dir/asec_services_imgs.tar.gz" ]; then
      log "start load imgs for offline install"
      load_images
      load_middleware_images
    else
      log "start docker login"
      docker_login
    fi
}

function load_images() {
    mkdir -p /tmp/service_imgs
    rm -fr /tmp/service_imgs/*
    tar -xvf "$current_dir"/asec_services_imgs.tar.gz  -C /tmp/service_imgs
    for tarfile in /tmp/service_imgs/*.tar; do
      filename=$(basename "$tarfile" .tar)
      docker load < "$tarfile"
    done
}

function load_middleware_images() {
    mkdir -p /tmp/middleware_imgs
    rm -fr /tmp/middleware_imgs/*
    tar -xvf "$current_dir"/asec_middlewares_imgs.tar.gz  -C /tmp/middleware_imgs
    for tarfile in /tmp/middleware_imgs/*.tar; do
      filename=$(basename "$tarfile" .tar)
      docker load < "$tarfile"
    done
}

function docker_login() {
    log "[docker_login] start..."
    # 设置最大重试次数
    max_retries=5
    retry_count=0

    # 定义登录命令
    login_command="docker login --username=asdsec registry.cn-guangzhou.aliyuncs.com -p asd@1234!"

    # 循环重试直到成功或达到最大重试次数
    while true; do
      # 执行登录命令
      $login_command

      # 检查登录是否成功
      login_status=$?

      if [ $login_status -eq 0 ]; then
        log "登录成功"
        break
      elif [ $retry_count -lt $max_retries ]; then
        # 增加重试计数
        retry_count=$((retry_count+1))
        log "登录失败，重试 (${retry_count}/${max_retries})"
        sleep 1  # 可以增加一些延迟时间

        continue
      else
        log "达到最大重试次数，登录失败"
        break
      fi
    done
    log "[docker_login] finished..."
}


function install_kafka(){
  log "[install_kafka] start..."
  #安装kafka
  if [ ! -x "$parent_dir"/infra/kafka/install.sh ];then
      chmod +x "$parent_dir"/infra/kafka/install.sh
  fi
  "$parent_dir"/infra/kafka/install.sh  >> "${INSTALL_LOG_FILE}"
  log "[docker_login] finished..."
}

function install_openresty(){
  log "[install_openresty] start..."

  target_dir="/opt/asdsec-compose/openresty/ssl"
  src_dir="$current_dir/openresty/ssl"

  # 确保目标目录存在
  mkdir -p "$target_dir"

  # 判断文件是否存在，不存在则复制
  [ ! -f "$target_dir/server.crt" ] && cp "$src_dir/server.crt" "$target_dir/"
  [ ! -f "$target_dir/server.key" ] && cp "$src_dir/server.key" "$target_dir/"

  #安装openresty
  docker compose -f "$current_dir"/openresty/docker-compose.yml up -d
  log "[install_openresty] finished..."
}

function install_redis(){
  log "[install_redis] start..."
  #安装redis
  docker compose -f "$parent_dir"/infra/redis/docker-compose.yml up -d
  log "[install_redis] finished..."
}
function install_telemetry_services() {
  #!/bin/bash
  # 文件路径
  config_file="/etc/asec/config.ini"
  # 读取 corp_name
  corp_name=$(grep '^corp_name' "$config_file" | cut -d '=' -f 2 | xargs)
  # 读取 plat_ip
  plat_ip=$(grep '^plat_ip' "$config_file" | cut -d '=' -f 2 | xargs)
  echo "corp_name: $corp_name"
  echo "plat_ip: $plat_ip"

  log "[install_telemetry_services] start..."
  export MONITOR_LABEL="${corp_name}_${plat_ip}"
  envsubst < "$parent_dir"/infra/telemetry/prometheus/prometheus.yml.tpl > "$parent_dir"/infra/telemetry/prometheus/prometheus.yml
  docker compose -f "$parent_dir"/infra/telemetry/docker-compose.yml up -d prometheus loki promtail
  # 避免被镜像服务器限速报错
  sleep 5
  docker compose -f "$parent_dir"/infra/telemetry/docker-compose.yml up -d node-exporter alertmanager grafana
  sleep 5
  docker compose -f "$parent_dir"/infra/telemetry/docker-compose.yml up -d postgres-exporter kafka-exporter pushgateway
  log "[install_telemetry_services] finished..."
}

function install_pg(){
  log "[install_pg] start..."
  #创建卷
  if ! docker volume inspect postgres_vol >/dev/null 2>&1; then
      docker volume create --name=postgres_vol
  fi
  #创建postgres
  docker compose -f "$parent_dir"/infra/postgres/docker-compose.yml up -d
  log "[install_pg] finished..."
}


function install_ck(){
  log "[install_ck] start..."
  if ! docker volume inspect ck_vol >/dev/null 2>&1; then
    docker volume create --name=ck_vol
  fi
  #安装ClickHouse
  cd "$parent_dir"/infra/clickhouse
  docker compose -f docker-compose.yml up -d
  cd -
  log "[install_ck] finished..."
}

function install_minio(){
    # 按需安装 minio
    log "[install_minio] start..."
    docker compose -f "$parent_dir"/infra/minio/docker-compose.yml up -d
    if [[ $oss == "本地oss" ]]
    then
      cp "$parent_dir"/infra/minio/mc /usr/bin/
      chmod +x /usr/bin/mc
    fi
    log "[install_minio] finished..."
}
function install_timed(){
    # 按需安装 minio
    log "[install_timed] start..."
    docker compose -f "$parent_dir"/infra/timed/docker-compose.yml up -d
    log "[install_timed] finished..."
}

function db_init(){
  log "[db_init] start..."
  # 容器中执行该脚本，避免本机需要安装python依赖库
  cd "$current_dir"/upgrade
  # 这里提前创建一下文件，保证compose中mount的文件存在，避免compose自动创建为目录而不是文件
  touch /home/<USER>
  #初次部署需要给flink目录权限，不然写文件一直失败会导致flik一直重启
  mkdir -p /data/asec/flink/ueba_data
  chmod -R 777 /data/asec/flink/ueba_data
  chmod +x "$parent_dir"/infra/minio/mc
  #初始化clickhouse数据库。 不太优雅。。。
  docker exec -i clickhouse bash -c "echo CREATE DATABASE IF NOT EXISTS asec | clickhouse-client --user=asec --password=O1wq7pgl"
  cd -
  log "[db_init] finished..."
}

function do_upgrade(){
  log "[do_upgrade] start..."
  # 执行升级内容
  if [ ! -x "$current_dir"/upgrade.sh ];then
        chmod +x "$current_dir"/upgrade.sh
  fi
  cd "$current_dir"
  ./upgrade.sh skip
  init_jwt_key
  log "[do_upgrade] finished..."
  cd -
}

function init_jwt_key() {
  log "[init_jwt_key] start..."
  cd "$current_dir"/upgrade
  docker compose run upgrader python3 /opt/upgrader/a01-init_jwt_key_fix.py
  cd -
  log "[init_jwt_key] finished..."
  docker restart asec_console
  log "[asec_console] restart for memory variable initialization..."
}

function install_ueba_engine(){
  log "[install_ueba_engine] start..."
  #安装flink engine
  if [ ! -x "$current_dir"/ueba-engine/install.sh ];then
      chmod +x "$current_dir"/ueba-engine/install.sh
  fi
  "$current_dir"/ueba-engine/install.sh
  log "[install_ueba_engine] finished..."
}


function install_asec_services(){
  log "[install_asec_services] start..."
  #安装平台
  docker compose -f "$current_dir"/console/docker-compose.yml up -d
  # sleep一下,避免被阿里云限频率
  sleep 2
  docker compose -f "$current_dir"/appliance-center/docker-compose.yml up -d
  sleep 2
  docker compose -f "$current_dir"/log-center/docker-compose.yml up -d
  sleep 2
  docker compose -f "$current_dir"/auth/docker-compose.yml up -d
  docker restart asec_console
  log "[install_asec_services] finished..."
}

function install_frontend(){
  log "[install_frontend] start..."
  cd "$current_dir"/frontend
  if [ ! -x ./install.sh ];then
      chmod +x ./install.sh
  fi
  ./install.sh
  cd -
  log "[install_frontend] finished..."
}


function setup_firewall(){
  log "[setup_firewall] start..."
  if [ -f /etc/os-release ]; then
      source /etc/os-release
      if [[ "$ID" == "ubuntu" ]]; then
          chmod +x "$current_dir"/ufw-config.sh
          "$current_dir"/ufw-config.sh
      elif [[ "$ID" == "centos" ]]; then
          chmod +x "$current_dir"/firewalld-config.sh
          "$current_dir"/firewalld-config.sh
      fi
  fi
  log "[setup_firewall] finished..."
}

function write_conf(){
  log "[write_conf] start with args: channel:[$channel] deploy_mode:[$deploy_mode]..."
  docker exec -it postgres psql -U asec -d asec_platform -c "TRUNCATE TABLE tb_console_config;"
  docker exec -it postgres psql -U asec -d asec_platform -c "INSERT INTO public.tb_console_config (id,develop_mode,deploy_mode,create_time,update_time) values (1,'$channel','$deploy_mode',now(),now());"
  docker exec -it postgres psql -U asec -d asec_platform -c "ALTER SEQUENCE tb_console_config_id_seq RESTART WITH 2;"
  log "[write_conf] finished..."
}

function reserve_last(){
  if [ -e /opt/platform/current ]; then
      cp -fr /opt/platform/current /opt/platform/last
  else
      echo "current does not exist, skipping mv"
  fi
}

# 记录当前版本信息
function record_current_version() {
    if [ -f "/opt/platform/current/version.conf" ]; then
        log "Current version information:"
        cat /opt/platform/current/version.conf | tee -a $INSTALL_LOG_FILE
    else
        log "No current version information found."
    fi
}

 # 初始化网关包
function init_gateway_pkg(){
    if [ -d "/opt/gateway" ] && [ "$(ls -A /opt/gateway)" ]; then
        mkdir -p /opt/asdsec-compose/asec-platform/asec-console/data/asec/gateway
        cp -fr /opt/gateway/* /opt/asdsec-compose/asec-platform/asec-console/data/asec/gateway/
        log "[init_gateway_pkg] Gateway package copied successfully"
    else
        log "[init_gateway_pkg] Gateway directory not found or empty, skipping copy"
    fi
}

function main(){
  reserve_last

  current_dir=$(cd "$(dirname "$0")"; pwd)
  parent_dir=$(dirname "$current_dir")
  cd ../
  mkdir -p /opt/platform/current

  cp -fr "$parent_dir"/platform/* /opt/platform/current/
  cp -fr "$parent_dir"/infra /opt/platform/
  if [ -d "$parent_dir/gateway" ]; then
      cp -fr "$parent_dir"/gateway /opt
  fi

  cd /opt/platform/current
  log "go to $current_dir"

  record_current_version
  current_dir="/opt/platform/current"
  parent_dir=$(dirname "$current_dir")
  pre_install
  slave_mark

  # 在安装开始前，暂停 unattended-upgrades 和 apt 进程
  unattended_upgrades_was_active=false
  if systemctl is-active --quiet unattended-upgrades; then
      log "检测到 unattended-upgrades 服务正在运行。为保证安装顺利，将临时停止该服务..."
      systemctl stop unattended-upgrades
      unattended_upgrades_was_active=true
  fi
  if pgrep -f "apt" > /dev/null; then
      log "检测到有apt进程正在运行。为确保安装环境干净，将强制终止相关进程..."
      pkill -f "apt"
      sleep 2
  fi

  # 安装基础服务
  install_docker
  install_kafka
  install_telemetry_services
  install_ck
  install_minio
  install_pg
  install_redis
  install_timed

  # 等待pg，ck等启动完成再执行数据库升级
  sleep 15

  # 初始化ck数据库，初始化升级version文件等
  db_init
  # 执行服务&配置升级
  do_upgrade
  # 写入通道，部署模式等配置
  write_conf
  #openresty要在前端资源ready之后启动
  install_openresty

  # 配置防火墙放通端口。必要时可以去掉（云上部署或者客户端服务器防火墙有专门的管理工具）
  setup_firewall

  # 初始化网关包
  init_gateway_pkg

  # 在安装结束后，恢复 unattended-upgrades 服务
  if [ "$unattended_upgrades_was_active" = true ]; then
      log "安装流程执行完毕。正在重新启动 unattended-upgrades 服务..."
      systemctl start unattended-upgrades
  fi

  log "平台安装完成!"
  exit 0
}

main


