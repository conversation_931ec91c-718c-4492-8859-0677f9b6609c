#!/bin/bash

current_dir=$(pwd)
. "${current_dir}/utils.sh"

echo_logo
echo_green "感谢使用ASec产品,初次安装请按照提示进行平台初始设置"

# 网络地址校验函数
validate_platform_network() {
    local network=$1
    echo "校验platform_network地址: $network"

    # 检查格式：网段/掩码
    if ! echo "$network" | grep -qE '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/[0-9]{1,2}$'; then
        echo_red "错误: platform_network地址格式不正确，应为 网段/掩码 格式，如: ************/24"
        return 1
    fi

    # 提取网段和掩码
    local network_ip=$(echo "$network" | cut -d'/' -f1)
    local mask=$(echo "$network" | cut -d'/' -f2)

    # 检查掩码范围
    if [ "$mask" -lt 16 ] || [ "$mask" -gt 28 ]; then
        echo_red "错误: platform_network掩码应在16-28之间"
        return 1
    fi

    # 检查是否为网段地址（最后一位应为0）
    local last_octet=$(echo "$network_ip" | cut -d'.' -f4)
    if [ "$last_octet" -ne 0 ]; then
        echo_red "错误: platform_network应为网段地址，最后一位应为0，如: ************/24"
        return 1
    fi

    # 检查IP格式
    local IFS='.'
    local ip_parts=($network_ip)
    for part in "${ip_parts[@]}"; do
        if [ "$part" -lt 0 ] || [ "$part" -gt 255 ]; then
            echo_red "错误: 网络地址格式不正确"
            return 1
        fi
    done

    echo_green "platform_network地址校验通过"
    return 0
}

validate_docker_bridge() {
    local network=$1
    echo "校验Docker bridge地址: $network"

    # 检查格式：IP/掩码
    if ! echo "$network" | grep -qE '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/[0-9]{1,2}$'; then
        echo_red "错误: Docker bridge地址格式不正确，应为 IP/掩码 格式，如: ************/24"
        return 1
    fi

    # 提取IP和掩码
    local ip=$(echo "$network" | cut -d'/' -f1)
    local mask=$(echo "$network" | cut -d'/' -f2)

    # 检查掩码范围
    if [ "$mask" -lt 8 ] || [ "$mask" -gt 30 ]; then
        echo_red "错误: Docker bridge掩码应在8-30之间"
        return 1
    fi

    # 检查不能是网段地址（最后一位不能为0）
    local last_octet=$(echo "$ip" | cut -d'.' -f4)
    if [ "$last_octet" -eq 0 ]; then
        echo_red "错误: Docker bridge应为IP地址，不能是网段地址，如: ************/24"
        return 1
    fi

    # 检查IP格式
    local IFS='.'
    local ip_parts=($ip)
    for part in "${ip_parts[@]}"; do
        if [ "$part" -lt 0 ] || [ "$part" -gt 255 ]; then
            echo_red "错误: IP地址格式不正确"
            return 1
        fi
    done

    echo_green "Docker bridge地址校验通过"
    return 0
}

validate_gateway_ip() {
    local ip=$1
    echo "校验网关IP地址: $ip"

    # 检查IP格式
    if ! echo "$ip" | grep -qE '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$'; then
        echo_red "错误: 网关IP地址格式不正确，应为 IP 格式，如: *************"
        return 1
    fi

    # 检查IP各段范围
    local IFS='.'
    local ip_parts=($ip)
    for part in "${ip_parts[@]}"; do
        if [ "$part" -lt 0 ] || [ "$part" -gt 255 ]; then
            echo_red "错误: IP地址格式不正确"
            return 1
        fi
    done

    # 检查不能是网段地址（最后一位不能为0）
    local last_octet=$(echo "$ip" | cut -d'.' -f4)
    if [ "$last_octet" -eq 0 ]; then
        echo_red "错误: 网关不能是网段地址，最后一位不能为0"
        return 1
    fi

    # 检查不能是广播地址（最后一位不能为255）
    if [ "$last_octet" -eq 255 ]; then
        echo_red "错误: 网关不能是广播地址，最后一位不能为255"
        return 1
    fi

    echo_green "网关IP地址校验通过"
    return 0
}

function Get_Ip(){
    active_interface=$(ip route get ******* | awk 'NR==1 {print $5}')
    if [[ -z $active_interface ]]; then
        LOCAL_IP="127.0.0.1"
    else
        LOCAL_IP=`ip -4 addr show dev "$active_interface" | grep -oP '(?<=inet\s)\d+(\.\d+){3}'`
    fi
# 暂时注释掉获取公网IP，避免离线部署卡很久
#    PUBLIC_IP=`curl -s https://api64.ipify.org`
#    if [[ -z "$PUBLIC_IP" ]]; then
#        PUBLIC_IP="N/A"
#    fi
#    if echo "$PUBLIC_IP" | grep -q ":"; then
#        PUBLIC_IP=[${PUBLIC_IP}]
#    fi
}

Get_Ip
auto_detected_ip=${LOCAL_IP}

# 请求用户标识
read -e -p "请输入用户标识(仅限大小写英文字符): " corp_name

# 配置平台地址
read -e -p "请配置平台地址(******* 或 xx.com): " -i "${auto_detected_ip}" plat_ip

# 配置平台用户访问端口（非管理员）
read -e -p "请配置用户访问端口(缺省443): " -i "443" user_port

# 配置Docker网络
echo_green "Docker网络配置:"

# 配置platform_network网段
platform_network_subnet="************/24"
platform_network_gateway="************"
echo_green "默认platform_network网段: $platform_network_subnet"
echo_green "默认platform_network网关: $platform_network_gateway"

read -e -p "是否使用默认platform_network配置? (y/n): " -i "y" use_default_network
if [[ "$use_default_network" != "y" && "$use_default_network" != "Y" ]]; then
    # 让用户输入自定义网段和网关
    valid_input=false
    while [ "$valid_input" = false ]; do
        read -e -p "请输入自定义platform_network网段(例如: *************/24): " platform_network_subnet
        read -e -p "请输入自定义platform_network网关(例如: *************): " platform_network_gateway

        # 校验platform_network网段格式
        if validate_platform_network "$platform_network_subnet"; then
            # 校验网关IP格式
            if validate_gateway_ip "$platform_network_gateway"; then
                # 验证网段和网关是否匹配
                subnet_prefix=$(echo "$platform_network_subnet" | cut -d'/' -f1 | cut -d'.' -f1,2,3)
                gateway_prefix=$(echo "$platform_network_gateway" | cut -d'.' -f1,2,3)

                if [[ "$subnet_prefix" == "$gateway_prefix" ]]; then
                    echo_green "platform_network配置验证通过"
                    valid_input=true
                else
                    echo_red "错误: 网关IP必须在网段范围内，请重新输入"
                fi
            else
                echo_red "请重新输入正确的网关IP地址"
            fi
        else
            echo_red "请重新输入正确的platform_network网段"
        fi
    done
fi

# 配置Docker bridge网段
docker_bridge_network="************/24"
echo_green "默认Docker bridge网段: $docker_bridge_network"

read -e -p "是否使用默认Docker bridge网段配置? (y/n): " -i "y" use_default_bridge
if [[ "$use_default_bridge" != "y" && "$use_default_bridge" != "Y" ]]; then
    # 让用户输入自定义bridge网段
    valid_bridge_input=false
    while [ "$valid_bridge_input" = false ]; do
        read -e -p "请输入自定义Docker bridge地址(例如: ************/24): " docker_bridge_network

        # 使用校验函数验证Docker bridge格式
        if validate_docker_bridge "$docker_bridge_network"; then
            valid_bridge_input=true
        else
            echo_red "请重新输入正确的Docker bridge地址"
        fi
    done
fi

# 请求用户临时oss存储方案
echo_green "请选择临时oss存储方案:"
select oss in "阿里云" "本地oss" ; do
  if [[ -n $oss ]]; then
    break
  else
    echo_red "非法选择"
  fi
done

if [[ $oss == "本地oss" ]]
then
  read -p "请设置本地oss存储大小(G,纯数字): " oss_size
fi

# === spa-node-ebpf 配置（交互提前，写入延后） ===
spa_ebpf_selected="y"
spa_ebpf_iface=""
default_iface=$(ip -o link show | awk -F': ' '$2 != "lo" {print $2; exit}')
echo_green "配置用于spa的网卡"
read -e -p "请输入要用于spa的网卡名称(缺省$default_iface): " -i "$default_iface" spa_ebpf_iface

# 显示输出
if [[ $oss == "本地oss" ]]
then
  echo_green "你好 $corp_name, 你选择 $oss 临时存储方案, 平台地址为: $plat_ip 用户访问端口为：$user_port 存储的大小为 $oss_size G \n用于spa的网卡为 $spa_ebpf_iface \nDocker网络配置: bridge网段 $docker_bridge_subnet, platform_network网段 $docker_subnet, 网关 $docker_gateway"
else
  echo_green "你好 $corp_name, 你选择 $oss 临时存储方案, 平台地址为: $plat_ip 用户访问端口为：$user_port\n用于spa的网卡为 $spa_ebpf_iface \nDocker网络配置: bridge网段 $docker_bridge_subnet, platform_network网段 $docker_subnet, 网关 $docker_gateway"
fi



# 暂停脚本以显示输出
read -p "按回车确认配置,以完成初始化配置.如需修改 按(Ctrl+C)重新运行安装脚本"

# 将配置写入环境变量及配置文件中
mkdir -p /etc/asec/
rm -f /etc/asec/config.ini
echo "[default]" > /etc/asec/config.ini
echo "corp_name = $corp_name" >> /etc/asec/config.ini
echo "oss = $oss" >> /etc/asec/config.ini
echo "plat_ip = $plat_ip" >> /etc/asec/config.ini
echo "user_port = $user_port" >> /etc/asec/config.ini
echo "platform_network_subnet = $platform_network_subnet" >> /etc/asec/config.ini
echo "platform_network_gateway = $platform_network_gateway" >> /etc/asec/config.ini
echo "docker_bridge_network = $docker_bridge_network" >> /etc/asec/config.ini

# profile文件中写入的配置暂时没用。TODO，去掉ini，通过环境变量共享？

echo 'export corp_name='$corp_name >> /etc/profile
echo 'export oss='$oss >> /etc/profile
echo 'export plat_ip='$plat_ip >> /etc/profile
echo 'export platform_network_subnet='$platform_network_subnet >> /etc/profile
echo 'export platform_network_gateway='$platform_network_gateway >> /etc/profile
echo 'export docker_bridge_network='$docker_bridge_network >> /etc/profile

if [[ $oss == "本地oss" ]]
then
  echo "oss_size = $oss_size" >> /etc/asec/config.ini
  echo 'export oss_size='$oss_size >> /etc/profile
fi

# === spa-node-ebpf 配置写入 ===
echo "SPA_EBPF_SELECTED=$spa_ebpf_selected" > /etc/asec/spa_ebpf.conf
echo "SPA_EBPF_IFACE=$spa_ebpf_iface" >> /etc/asec/spa_ebpf.conf

source /etc/profile