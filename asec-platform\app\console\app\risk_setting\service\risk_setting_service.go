package service

import (
	"asdsec.com/asec/platform/app/console/app/risk_setting/model"
	repository1 "asdsec.com/asec/platform/app/console/app/risk_setting/repository"
	"context"
	"gorm.io/gorm"
	"sync"
)

var RiskSettingServiceImpl RiskSettingService

// RiskSettingServiceInit 单例对象
var RiskSettingServiceInit sync.Once

type RiskSettingService interface {
	GetRiskSettingTotal(ctx context.Context) ([]model.GetRiskSettingTotalRsp, error)
	GetRiskSettingList(ctx context.Context) (model.GetRiskSettingListResp, error)
	UpdateRiskSetting(ctx context.Context, req *model.UpdateRiskScoreSettingReq) error
	CascadeCreatRiskSetting(ctx context.Context, indicator string, indicatorType string, defaultScore int, corpId string, db *gorm.DB) error
	CascadeDeleteRiskSetting(ctx context.Context, indicatorType string, indicator string, db *gorm.DB) error
}

type riskService struct {
	db repository1.RiskSettingRepository
}

func (a *riskService) CascadeDeleteRiskSetting(ctx context.Context, indicatorType string, indicator string, db *gorm.DB) error {
	return a.db.CascadeDeleteRiskSetting(ctx, indicatorType, indicator, db)
}

func (a *riskService) CascadeCreatRiskSetting(ctx context.Context, indicator string, indicatorType string, defaultScore int, corpId string, db *gorm.DB) error {
	return a.db.CascadeCreatRiskSetting(ctx, indicator, indicatorType, defaultScore, corpId, db)
}

func (a *riskService) GetRiskSettingTotal(ctx context.Context) ([]model.GetRiskSettingTotalRsp, error) {
	return a.db.GetRiskSettingTotal(ctx)
}

func (a *riskService) GetRiskSettingList(ctx context.Context) (model.GetRiskSettingListResp, error) {
	return a.db.GetRiskSettingList(ctx)
}

func (a *riskService) UpdateRiskSetting(ctx context.Context, req *model.UpdateRiskScoreSettingReq) error {
	return a.db.UpdateRiskSetting(ctx, req)
}

func GetRiskSettingService() RiskSettingService {
	RiskSettingServiceInit.Do(func() {
		RiskSettingServiceImpl = &riskService{db: repository1.NewAppRepository()}
	})
	return RiskSettingServiceImpl
}
